<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ConsultationPrescription extends Model
{
    use HasFactory;

    protected $fillable = [
        'consultation_id',
        'medication_name',
        'dosage',
        'frequency',
        'duration',
        'instructions',
        'status',
        'created_by',
    ];

    /**
     * Get the consultation that owns the prescription.
     */
    public function consultation()
    {
        return $this->belongsTo(Consultation::class);
    }

    /**
     * Get the user who created the prescription.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope to filter by status.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }
}
