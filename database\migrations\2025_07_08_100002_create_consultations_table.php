<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('consultations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('appointment_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('patient_id')->constrained()->onDelete('cascade');
            $table->foreignId('provider_id')->constrained()->onDelete('cascade');
            $table->foreignId('clinic_id')->nullable()->constrained()->onDelete('set null');
            $table->string('consultation_type')->default('general'); // general, follow_up, emergency, etc.
            $table->enum('status', ['draft', 'in_progress', 'completed', 'cancelled'])->default('draft');
            $table->datetime('consultation_date');
            $table->integer('duration_minutes')->nullable();
            $table->string('consultation_mode')->default('in_person'); // in_person, video, phone
            $table->boolean('is_telemedicine')->default(false);

            // JSON columns for flexible data storage
            $table->json('vital_signs')->nullable(); // Temperature, BP, HR, Weight, etc.
            $table->json('main_tabs')->nullable(); // Present Concerns, Present History, Examination, Plan
            $table->json('additional_tabs')->nullable(); // Allergies, Family History, Social History, etc.

            $table->datetime('started_at')->nullable();
            $table->datetime('completed_at')->nullable();
            $table->timestamps();

            // Indexes for better performance
            $table->index(['patient_id', 'consultation_date']);
            $table->index(['provider_id', 'consultation_date']);
            $table->index(['clinic_id', 'consultation_date']);
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('consultations');
    }
};
