<template>
  <div>
    <form :id="appointmentFormObj.id !== undefined
      ? 'appointmentDataForm' + appointmentFormObj.id
      : 'appointmentDataForm'
      " :appointment-form-id="appointmentFormObj.id !== undefined ? appointmentFormObj.id : 0
        " enctype="multipart/form-data" @submit.prevent="handleFormSubmit" :novalidate="true">
      <div class="px-4 py-4">
        <div class="grid grid-cols-1 md:grid-cols-12 gap-6">
          <!-- Left Column -->
          <div class="md:col-span-5">
            <!-- Clinic Selection -->
            <div class="mb-4" v-if="
              userData.addOns.kiviPro == true &&
              (getUserRole() == 'administrator' ||
                getUserRole() == 'doctor' ||
                getUserRole() == 'patient')
            ">
              <label for="clinic_id" class="block text-sm font-medium text-gray-700 mb-1">
                {{ formTranslation.appointments.select_clinic }}
                <span class="text-red-500">*</span>
              </label>
              <multi-select deselect-label="" select-label="" @select="clinicChange" @remove="clinicChange"
                :disabled="disabledClinicField" :loading="clinicMultiselectLoader"
                v-model="appointmentFormObj.clinic_id" :tag-placeholder="formTranslation.appointments.select_clinic_plh
                  " id="clinic_id" :placeholder="formTranslation.appointments.search_plh" label="label" track-by="id"
                :options="clinics" class="w-full" />
              <p v-if="submitted && !$v.appointmentFormObj.clinic_id.required" class="mt-1 text-sm text-red-600">
                {{ formTranslation.appointments.clinic_is_required }}
              </p>
            </div>

            <!-- Doctor Selection -->
            <div class="mb-4" v-if="getUserRole() !== 'doctor'">
              <label for="doctor_id" class="block text-sm font-medium text-gray-700 mb-1">
                {{ formTranslation.common.doctor }}
                <span class="text-red-500">*</span>
              </label>
              <multi-select deselect-label="" select-label="" @select="handleDoctorChange"
                @remove="handleDoctorUnselect" v-model="appointmentFormObj.doctor_id"
                :tag-placeholder="formTranslation.appointments.doctor_plh"
                :placeholder="formTranslation.appointments.search_plh" :disabled="disabledDoctorField" id="doctor_id"
                label="label" track-by="id" :loading="doctorMultiselectLoader" :options="doctors" class="w-full" />
              <p v-if="submitted && !$v.appointmentFormObj.doctor_id.required" class="mt-1 text-sm text-red-600">
                {{ formTranslation.appointments.doc_required }}
              </p>
            </div>

            <!-- Service Selection -->
            <div class="mb-4">
              <div class="flex justify-between items-center mb-1">
                <label for="visit_type" class="block text-sm font-medium text-gray-700">
                  {{ formTranslation.common.service }}
                  <span class="text-red-500">*</span>
                </label>
                <router-link v-if="
                  (getUserRole() === 'administrator' ||
                    getUserRole() === 'clinic_admin' ||
                    getUserRole() === 'receptionist' ||
                    getUserRole() === 'doctor') &&
                  appointmentFormObj.id === undefined &&
                  kcCheckPermission('service_add')
                " :to="{ name: 'service' }" class="text-sm text-black hover:text-black">
                  <i class="fa fa-plus"></i>
                  {{ formTranslation.common.service_add }}
                </router-link>
              </div>
              <multi-select v-model="appointmentFormObj.visit_type" @select="appointmentTypeChangeSelect"
                @remove="appointmentTypeChangeUnselect" :tag-placeholder="formTranslation.appointments.tag_visit_type_plh
                  " id="visit_type" :placeholder="formTranslation.appointments.search_plh"
                :disabled="disabledServiceField" label="name" track-by="id" :options="appointmentTypes"
                :loading="serviceMultiselectLoader" :close-on-select="!appointmentTypeMultiselect" class="w-full" />
              <p v-if="submitted && !$v.appointmentFormObj.visit_type.required" class="mt-1 text-sm text-red-600">
                {{ formTranslation.patient_bill.service_required }}
              </p>
            </div>

            <!-- Date Picker -->
            <div class="mb-4">
              <label for="appointment_start_date" class="block text-sm font-medium text-gray-700 mb-1">
                {{ formTranslation.appointments.appointment_date }}
                <span class="text-red-500">*</span>
              </label>
              <vc-date-picker id="appointmentDate" title-position="left"
                v-model="appointmentFormObj.appointment_start_date" is-expanded
                :available-dates="{ weekdays: getAvailableDays() }"
                :min-date="new Date()" :max-date="maxDate"
                :popover="{ placement: 'bottom', visibility: 'click' }" @input="handleDateChange" :masks="masks"
                class="w-full">
                <template v-slot="{ inputValue, inputEvents }">
                  <input
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black"
                    readonly :value="inputValue" v-on="inputEvents" :placeholder="formTranslation.appointments.appointment_date_plh
                      " />
                </template>
              </vc-date-picker>
              <p v-if="
                submitted &&
                !$v.appointmentFormObj.appointment_start_date.required
              " class="mt-1 text-sm text-red-600">
                {{ formTranslation.appointments.appointment_date_required }}
              </p>
            </div>

            <!-- Patient Selection -->
            <div v-if="showPatientSelection" class="mb-4">
              <label class="block text-sm font-medium text-gray-700">
                {{ formTranslation.common.patient }}
                <span class="text-red-500">*</span>
                <span v-if="canAddPatient" class="float-right">
                  <router-link :to="{ name: 'patient.create' }" class="text-black text-sm">
                    <i class="fa fa-plus"></i>
                    {{ formTranslation.patient.add_patient }}
                  </router-link>
                </span>
              </label>
              <multi-select v-model="appointmentFormObj.patient_id" :disabled="disabledPatientField"
                :loading="patientMultiselectLoader" :options="patients" :tag-placeholder="formTranslation.appointments.tag_patient_type_plh
                  " :placeholder="formTranslation.appointments.search_plh" label="label" track-by="id"
                class="w-full" />
              <p v-if="submitted && !$v.appointmentFormObj.patient_id.required" class="text-sm text-red-500 mt-1">
                {{ formTranslation.appointments.patient_requires }}
              </p>
            </div>

            <!-- Status Selection -->
            <div class="mb-4" v-if="this.getUserRole() !== 'patient'">
              <label for="status" class="block text-sm font-medium text-gray-700 mb-1">
                {{ formTranslation.common.status }}
                <span class="text-red-500">*</span>
              </label>
              <select name="status" v-model="appointmentFormObj.status" id="status"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black">
                <option value="">
                  {{ formTranslation.appointments.select_status }}
                </option>
                <option selected="selected" value="1">
                  {{ formTranslation.appointments.booked }}
                </option>
                <option value="2">
                  {{ formTranslation.appointments.pending }}
                </option>
                <option value="3">
                  {{ formTranslation.appointments.check_out }}
                </option>
                <option value="4">
                  {{ formTranslation.appointments.check_in }}
                </option>
                <option value="0">
                  {{ formTranslation.appointments.cancelled }}
                </option>
              </select>
              <p v-if="submitted && !$v.appointmentFormObj.status.required" class="mt-1 text-sm text-red-600">
                {{ formTranslation.appointments.status_required }}
              </p>
            </div>

            <!-- File Upload Section -->
            <div v-if="fileUploadEnable === 'on'" class="mb-4">
              <div v-if="appointmentFormObj.id === undefined">
                <h5 class="text-lg font-medium mb-3">
                  {{ formTranslation.patient.add_medical_report }}
                </h5>
                <div class="flex">
                  <button
                    class="px-4 py-2 bg-black text-white rounded-md hover:bg-black focus:outline-none focus:ring-2 focus:ring-black focus:ring-offset-2"
                    type="button" id="appointmentreport" @click.prevent="multiUploadProfile">
                    {{ formTranslation.common.choose_file }}
                  </button>
                  <span class="ml-3 text-sm text-gray-600 self-center">
                    {{
                      upload_appointment_report.length > 0
                        ? upload_appointment_report.length + " File selected"
                        : formTranslation.common.no_file_chosen
                    }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- Right Column -->
          <div class="md:col-span-7">
            <!-- Time Slots -->
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-1">
                {{ formTranslation.appointments.available_slot }}
                <span class="text-red-500">*</span>
              </label>
              <div class="border border-gray-200 rounded-md p-4 overflow-y-auto" :class="getUserRole() !== 'doctor' && getUserRole() !== 'patient'
                ? 'h-96'
                : 'h-64'
                ">
                <div v-if="
                  timeSlots.length !== undefined &&
                  timeSlots.length > 0 &&
                  appointmentData.appointment_start_date !== null
                ">
                  <div v-for="(timeSlot, index) in timeSlots" class="mb-4" :key="index">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      {{ formTranslation.appointments.session }} {{ index + 1 }}
                    </label>
                    <div class="flex flex-wrap gap-2">
                      <template v-for="(slot, subIndex) in timeSlot">
                        <span v-if="slot.available === false" :key="subIndex"
                          class="px-3 py-1 text-red-600 bg-red-100 rounded line-through">
                          {{ slot.time }}
                        </span>
                        <button v-else :key="'timeSlot' + subIndex" type="button"
                          class="px-3 py-1 rounded transition-colors" :class="appointmentFormObj.appointment_start_time ===
                            slot.time
                            ? 'bg-black text-white'
                            : 'border border-black text-black hover:bg-black'
                            " @click="handleTimeChange(slot.time)">
                          {{ slot.time }}
                        </button>
                      </template>
                    </div>
                  </div>
                </div>
                <div v-else class="flex items-center justify-center h-full">
                  <p class="text-black text-sm font-medium">
                    {{ formTranslation.appointments.no_time_slots_found }}
                  </p>
                </div>
              </div>
              <p v-if="
                submitted &&
                !$v.appointmentFormObj.appointment_start_time.required
              " class="mt-1 text-sm text-red-600">
                {{ formTranslation.appointments.time_slot_required }}
              </p>
            </div>

            <!-- Service Details -->
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-1">
                {{ formTranslation.widgets.service_detail }}
              </label>
              <div class="border border-gray-200 rounded-md p-4 min-h-32 flex items-center justify-center">
                <div v-if="
                  appointmentFormObj.visit_type &&
                  appointmentFormObj.visit_type.length > 0
                ">
                  <div v-for="(service, index) in appointmentFormObj.visit_type" :key="index" class="text-center">
                    <span class="font-medium">{{ service.name }}</span>
                    <span class="ml-2">
                      {{
                        " - " +
                        (appointmentFormObj.id !== undefined ? prefix : "") +
                        service.charges +
                        (appointmentFormObj.id !== undefined ? postfix : "")
                      }}
                    </span>
                  </div>
                </div>
                <div v-else>
                  <p class="text-black text-sm font-medium">
                    {{ formTranslation.widgets.no_service_detail_found }}.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="w-full  py-2 bg-white  " v-if="appointmentFormObj.id === undefined">
          <h6 class="block text-sm font-medium text-gray-700 mb-1">Payment Method</h6>

          <div class=" grid grid-cols-2  gap-6">
            <!-- Stripe Payment Option -->
            <label :class="[
              'flex items-center p-4 border rounded-lg cursor-pointer transition-colors',
              appointmentFormObj.payment_mode === 'paymentStripepay'
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:bg-gray-50'
            ]">
              <input type="radio" name="paymentMethod" value="paymentStripepay"
                v-model="appointmentFormObj.payment_mode"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300" />
              <div class="ml-3 flex items-center">
                <div class="flex-shrink-0 mr-3">
                  <!-- Credit Card SVG Icon -->
                  <svg width="24" height="24" class="text-blue-800" xmlns="http://www.w3.org/2000/svg"
                    fill="currentColor" viewBox="0 0 384 512">
                    <path
                      d="M155.3 154.6c0-22.3 18.6-30.9 48.4-30.9 43.4 0 98.5 13.3 141.9 36.7V26.1C298.3 7.2 251.1 0 203.8 0 88.1 0 11 60.4 11 161.4c0 157.9 216.8 132.3 216.8 200.4 0 26.4-22.9 34.9-54.7 34.9-47.2 0-108.2-19.5-156.1-45.5v128.5a396.1 396.1 0 0 0 156 32.4c118.6 0 200.3-51 200.3-153.6 0-170.2-218-139.7-218-203.9z" />
                  </svg>
                </div>
                <div>
                  <p class="font-medium text-gray-900">Stripe</p>
                  <p class="text-sm text-gray-500">Pay securely online with credit card</p>
                </div>
              </div>
            </label>

            <!-- Offline Payment Option -->
            <label :class="[
              'flex items-center p-4 border rounded-lg cursor-pointer transition-colors',
              appointmentFormObj.payment_mode === 'paymentOffline'
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:bg-gray-50'
            ]">
              <input type="radio" name="paymentMethod" value="paymentOffline" v-model="appointmentFormObj.payment_mode"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300" />
              <div class="ml-3 flex items-center">
                <div class="flex-shrink-0 mr-3">
                  <!-- Dollar Sign SVG Icon -->
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="text-green-600">
                    <line x1="12" y1="2" x2="12" y2="22"></line>
                    <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                  </svg>
                </div>
                <div>
                  <p class="font-medium text-gray-900">Pay Offline</p>
                  <p class="text-sm text-gray-500">Pay with cash, bank transfer or Cheque</p>
                </div>
              </div>
            </label>
          </div>
          <p v-if="submitted && !$v.appointmentFormObj.payment_mode.required" class="text-sm text-red-500 mt-1">
            Please select a payment method to continue
          </p>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-4 mt-6">
          <button v-if="!loading"
            class="px-4 py-2 bg-black text-white rounded-md hover:bg-black focus:outline-none focus:ring-2 focus:ring-black focus:ring-offset-2"
            type="submit" :disabled="!disabledButton">
            <i class="fa fa-save mr-2"></i>{{ appointmentFormObj.payment_mode == "paymentStripepay" ? 'Send payment link to patient' : formTranslation.appointments.save_btn }}
          </button>
          <button v-else class="px-4 py-2 bg-black text-white rounded-md opacity-75 cursor-not-allowed" type="submit"
            disabled>
            <i class="fa fa-sync fa-spin mr-2"></i>{{ formTranslation.common.loading }}
          </button>
          <button type="button"
            class="px-4 py-2 border border-black text-black rounded-md hover:bg-black focus:outline-none focus:ring-2 focus:ring-black focus:ring-offset-2"
            @click="appointmentCloseForm">
            {{ formTranslation.common.cancel }}
          </button>
        </div>
      </div>
    </form>

    <!-- Modal -->
    <div v-if="appointmentModel" class="fixed inset-0 z-50 overflow-y-auto">
      <div class="flex items-center justify-center min-h-screen px-4">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
        <div class="relative bg-white rounded-lg shadow-xl max-w-4xl w-full mx-auto">
          <!-- Modal Header -->
          <div class="flex items-center justify-between p-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">
              {{ formTranslation.widgets.summary }}
            </h3>
            <button type="button" class="text-gray-400 hover:text-gray-500 focus:outline-none"
              @click="(appointmentModel = false), (loading = false)">
              <span class="sr-only">Close</span>
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <!-- Modal Body -->
          <div class="p-4">
            <input type="hidden" value="" id="payment_status_child" />
            <!-- Loader -->
            <div v-if="overlaySpinner" class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
              <loader-component-2></loader-component-2>
            </div>
            <!-- Appointment Detail Component -->
            <AppointmentDetail ref="appointment_detail" :appointment-data="appointmentFormObj" :user-data="userData"
              :prefix="prefix" :postfix="postfix" @bookAppointment="bookAppointmentHandle"
              @cancelAppointment="(appointmentModel = false), (loading = false)" :lazy="true" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { required } from "vuelidate/lib/validators";
import { validateForm } from "../../config/helper";
import { post, get } from "../../config/request";
import AppointmentDetail from "./AppointmentDetail";
import ModalPopup from "../Modal/Index";
import moment from "moment";

export default {
  name: "AppointmentForm",
  components: {
    AppointmentDetail,
    ModalPopup,
  },
  props: {
    appointmentData: {
      type: [Object, Array, Date],
      default() {
        return {};
      },
    },
    patient_profile_id: {
      type: [Number, String],
      default() {
        return "";
      },
    },
  },
  validations: {
    appointmentFormObj: {
      appointment_start_date: { required },
      appointment_start_time: { required },
      visit_type: { required },
      clinic_id: { required },
      doctor_id: { required },
      patient_id: { required },
      status: { required },
      payment_mode: { required },
    },
  },
  data: () => {
    return {
      appointmentModel: false,
      overlaySpinner: false,
      loading: false,
      prefix: "",
      postfix: "",
      formTitle: "Add appointment",
      buttonText: '<i class="fa fa-save"></i> Save',
      appointmentFormObj: {
        clinic_id: null,
        doctor_id: null,
        visit_type: [],
        appointment_start_date: null,
        appointment_start_time: null,
        patient_id: null,
        status: "1",
        description: "",
        custom_fields: [],
        payment_mode: "",
      },
      submitted: false,
      doctors: [],
      timer: "",
      appointmentTypes: [],
      showCustomField: false,
      componentKey: 0,
      p_uid: "",
      disabledDoctorField: false,
      disabledServiceField: false,
      disabledPatientField: false,
      disabledClinicField: false,
      DoctorWorkdays: [],
      holiday: {},
      restrictAppointment: {
        pre_book: "0",
        post_book: "365",
        only_same_day_book: "on",
      },
      requiredFields: [],
      patientRoleName: "patient",
      minDate: new Date(),
      maxDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 365),
      medicalReport: [],
      disabledButton: true,
      patients: [],
      clinicMultiselectLoader: true,
      doctorMultiselectLoader: true,
      patientMultiselectLoader: true,
      serviceMultiselectLoader: false,
      appointmentTypeMultiselect: true,
      upload_appointment_report: [],
      hideFormBtn: true,
      taxes: [],
      masks: {
        input: "DD/MM/YYYY",
      }
    };
  },
  mounted() {
    // Get appointment restriction settings
    this.getRestrictAppointmentDay();

    // Use $nextTick to ensure DOM is ready before initialization
    this.$nextTick(async () => {
      // If we have appointment data (edit mode), use initFormData
      if (this.appointmentData.id !== undefined) {
        await this.initFormData();
        this.setupEditMode();
      } else {
        // For new appointments
        await this.init();
      }

      // Special case for doctor role
      if (this.getUserRole() === "doctor" && this.userData.addOns.kiviPro != true) {
        await this.getDoctorActiveDays(this.userData.default_clinic, this.userData.ID);
      }
    });
  },
  methods: {
    // Get available days for the date picker (converting disabled days to available days)
    getAvailableDays() {
      // If DoctorWorkdays is empty, return all days as available
      if (!this.DoctorWorkdays || this.DoctorWorkdays.length === 0) {
        return [1, 2, 3, 4, 5, 6, 7]; // All days available
      }
      
      // Return the available days (1-7, where 1 is Monday)
      // The DoctorWorkdays array contains days that should be disabled,
      // so we need to return days that are NOT in that array
      const allDays = [1, 2, 3, 4, 5, 6, 7];
      return allDays.filter(day => !this.DoctorWorkdays.includes(day));
    },
    
    // Helper method to check if a date falls within any holiday period
    isHoliday(date) {
      if (!this.holiday || !this.holiday.length) {
        return false;
      }
      
      return this.holiday.some(holiday => {
        const fromDate = new Date(holiday.start_date);
        const toDate = new Date(holiday.end_date);
        return date >= fromDate && date <= toDate;
      });
    },
    
    customDisabledDates(date) {
      console.log("date", date);
      if (this.DoctorWorkdays.includes(date.date.getDay() + 1)) {
        return true;
      }
      if (this.holiday.length > 0) {
        let sample = false;
        this.holiday.map((holidays) => {
          const fromDate = new Date(holidays.start_date);
          const toDate = new Date(holidays.end_date);
          if (date.date >= fromDate && date.date <= toDate) {
            sample = true;
          }
        });
        return sample;
      }
    },
    async init() {
      // Set payment mode and initial values
      this.appointmentFormObj.payment_mode = "";

      
      // Handle edit mode
      if (this.appointmentData.id !== undefined) {
        this.setupEditMode();
      } else {
        this.setupAddMode();
      }

      // Load initial data based on user role
      await this.setupInitialDataByRole();

      // Load patients list if needed
      if (this.getUserRole() !== "patient") {
        await this.loadPatientsList();
      }
    },

    setupEditMode() {
      this.taxes = this.appointmentFormObj.tax || [];
      // this.hideFormBtn = this.appointmentFormObj.isEditAble;
      // this.formTitle = "Edit appointment";
      // this.buttonText =
      //   '<i class="fa fa-save"></i> ' + this.formTranslation.common.save;
      this.showCustomField = true;

      // Disable fields in edit mode
      this.disabledDoctorField = true;
      this.disabledServiceField = true;
      this.disabledClinicField = true;
      this.disabledPatientField = true;
      

      // Reset loaders
      this.clinicMultiselectLoader = false;
      this.doctorMultiselectLoader = false;
      this.patientMultiselectLoader = false;
      this.serviceMultiselectLoader = false;

      this.appointmentFormObj.visit_type = this.appointmentData.visit_type;
      if (
        this.appointmentFormObj.clinic_id &&
        this.appointmentFormObj.doctor_id
      ) {
        this.getDoctorActiveDays(
          this.appointmentFormObj.clinic_id.id,
          this.appointmentFormObj.doctor_id.id
        );
        this.dispatchTimeSlot();
      }
    },

    // Create a new method to handle role-specific initialization
    async setupInitialDataByRole() {
      if (this.getUserRole() === "doctor") {
        await this.setupDoctorRole();
      } else if (this.getUserRole() === "patient") {
        this.setupPatientRole();
      } else {
        await this.setupDefaultRole();
      }
    },

    setupAddMode() {
      this.appointmentFormObj.status = "1";
      const isOnlySameDay =
        this.restrictAppointment.only_same_day_book === "on";
      
      // Today's date (beginning of day)
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // Set minimum date to today
      this.minDate = today;

      // Calculate maximum date based on settings
      this.maxDate = isOnlySameDay
        ? today // Same day only
        : new Date(
            today.getTime() +
            1000 * 60 * 60 * 24 * 
            parseInt(this.restrictAppointment.post_book || 365)
          );

      // Set initial date to today or next available day if today is not available
      this.appointmentFormObj.appointment_start_date = today;
      
      // After doctor data is loaded, we'll update to the first available date in findNextAvailableDate()
    },

    async setupDoctorRole() {
      // Set the doctor ID
      const doctorId = this.userData;
      this.showCustomField = true;

      // Set doctor in form object if not already set
      if (!this.appointmentFormObj.doctor_id) {
        this.appointmentFormObj.doctor_id = {
          id: doctorId.ID,
          label: doctorId.display_name
        };
      }

      // Only load services once
      if (this.appointmentTypes.length === 0) {
        await this.getDoctorsServices(doctorId.ID);
      }

      // Initialize clinic if needed
      if (this.clinics && this.clinics.id && !this.appointmentFormObj.clinic_id) {
        await this.handleInitialClinicChange({ id: this.clinics.id });
      }
    },

    setupPatientRole() {
      if (!this.appointmentFormObj.patient_id) {
        this.appointmentFormObj.patient_id = {
          id: this.userData.ID,
          label: this.userData.display_name,
        };
      }
    },

    async setupDefaultRole() {
      if (
        this.$store.state.clinic.length > 0 &&
        this.$store.state.clinic[0].id !== undefined
      ) {
        // Only trigger clinic change if no appointment data exists
        if (!Object.keys(this.appointmentData).length) {
          await this.handleInitialClinicChange({
            id: this.$store.state.clinic[0].id,
          });
        }
      }
    },

    async handleInitialClinicChange(selectedClinic) {
      // This is a special version of clinicChange that doesn't clear form data
      if (this.getUserRole() !== "patient") {
        await this.loadPatientsList(selectedClinic.id);
      }

      if (this.getUserRole() === "doctor") {
        const doctorId = this.userData;
        await this.getDoctorsServices(doctorId.ID);
        await this.getDoctorActiveDays(selectedClinic.id, 1);
      } else {
        await this.loadDoctorsList(selectedClinic.id);
      }
    },

    async loadPatientsList(clinicId) {
      if (
        this.$store.state.userDataModule?.userDropDownData?.patients?.length > 0
      ) {
        this.patientMultiselectLoader = false;
        this.patients =
          this.$store.state.userDataModule.userDropDownData.patients;
        return;
      }

      try {
        this.patientMultiselectLoader = true;
        const response = await get("get_static_data", {
          data_type: "users",
          user_type: this.patientRoleName,
          request_clinic_id: clinicId,
        });

        if (response.data.status === true) {
          this.patients = response.data.data;
        }
      } finally {
      
        this.patientMultiselectLoader = false;
      }
    },

    async loadDoctorsList(clinicId) {
      try {
        this.doctorMultiselectLoader = true;
        const response = await get("get_static_data", {
          data_type: "get_users_by_clinic",
          clinic_id: clinicId,
          module_type: "appointment",
        });

        if (response.data.status === true) {
          this.doctors = response.data.data;
          this.postfix = response.data.postfix;
          this.prefix = response.data.prefix;
        }
      } finally {
        this.doctorMultiselectLoader = false;
        
      }
    },
    async init_delete() {
      // this.getUniqueId();
      this.appointmentFormObj.payment_mode = "paymentOffline";
      if (this.appointmentFormObj.id !== undefined) {
        // This is an edit/reschedule case
        this.taxes = this.appointmentFormObj.tax || [];
        this.hideFormBtn = this.appointmentFormObj.isEditAble;
        this.formTitle = "Edit appointment";
        this.buttonText =
          '<i class="fa fa-save"></i> ' + this.formTranslation.common.save;
        this.showCustomField = true;
        this.disabledDoctorField = true;
        this.disabledServiceField = true;
        this.disabledClinicField = true;
        this.disabledPatientField = true;
        this.clinicMultiselectLoader = false;
        this.doctorMultiselectLoader = false;
        this.patientMultiselectLoader = false;
        this.serviceMultiselectLoader = false;

        // Get doctor workdays and time slots
        if (
          this.appointmentFormObj.clinic_id &&
          this.appointmentFormObj.doctor_id
        ) {
          this.getDoctorActiveDays(
            this.appointmentFormObj.clinic_id.id,
            this.appointmentFormObj.doctor_id.id
          );
          this.dispatchTimeSlot();
        }

        if (
          this.appointmentFormObj?.restrictAppointment?.only_same_day_book ==
          "on"
        ) {
          this.minDate = new Date();
          this.maxDate = new Date();
        } else {
          this.minDate =
            new Date() ||
            new Date(
              Date.now() +
              1000 *
              60 *
              60 *
              24 *
              parseInt(this.restrictAppointment.pre_book) || 0
            );
          this.maxDate = new Date(
            Date.now() +
            1000 *
            60 *
            60 *
            24 *
            parseInt(this.restrictAppointment.post_book) || 365
          );
        }
      } else {
        this.appointmentFormObj.status = "1";
        if (this.restrictAppointment.only_same_day_book === "on") {
          this.minDate = new Date();
          this.maxDate = new Date();
        } else {
          this.minDate = new Date(
            Date.now() +
            1000 *
            60 *
            60 *
            24 *
            parseInt(this.restrictAppointment.pre_book) || 0
          );
          this.maxDate = new Date(
            Date.now() +
            1000 *
            60 *
            60 *
            24 *
            parseInt(this.restrictAppointment.post_book) || 365
          );
          this.appointmentFormObj.appointment_start_date = new Date(
            Date.now() +
            1000 *
            60 *
            60 *
            24 *
            parseInt(this.restrictAppointment.pre_book) || 0
          );
        }
      }

      if (this.getUserRole() !== "doctor") {
        this.getDoctorDropdown();
      } else {
        this.dispatchTimeSlot();
      }

      setTimeout(() => {
        if (
          this.getUserRole() === "doctor" &&
          this.userData.addOns.kiviPro != true
        ) {
          this.getDoctorActiveDays(
            this.userData.default_clinic,
            this.userData.ID
          );
        }
      }, 3000);

      if (
        typeof this.$store.state.userDataModule !== undefined &&
        typeof this.$store.state.userDataModule.userDropDownData !==
        undefined &&
        this.$store.state.userDataModule.userDropDownData.patients.length > 0
      ) {
        this.patientMultiselectLoader = false;
        this.patients =
          this.$store.state.userDataModule.userDropDownData.patients;
      } else {
        this.$store.dispatch("userDataModule/fetchUserForDropdown", {
          userRoleName: this.patientRoleName,
        });
        setTimeout(() => {
          this.patientMultiselectLoader = false;
          this.patients = Object.values(
            this.$store.state.userDataModule.userDropDownData.patients
          );
        }, 3000);
      }
    },
    getRestrictAppointmentDay: function () {
      get("restrict_appointment_edit", {})
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.restrictAppointment = response.data.data;
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(this.formTranslation.widgets.record_not_found);
        });
    },
    forceRerender() {
      this.componentKey += 1;
      this.showCustomField = true;
    },
    clearAppointmentData() {
      this.upload_appointment_report = [];
      // this.appointmentFormObj.appointment_start_date = ''
      this.appointmentFormObj.id !== undefined
        ? this.appointmentFormObj.id
        : "";
      // this.appointmentFormObj.clinic_id = ''
      this.appointmentFormObj.doctor_id = "";
      let patient_id = "";
      if (this.$route.params.patient_id !== undefined) {
        patient_id = this.$route.params.patient_id;
      } else if (this.patient_profile_id) {
        patient_id = this.patient_profile_id;
      }
      this.appointmentFormObj.patient_id = patient_id;
      this.appointmentFormObj.service_id = "";
      this.appointmentFormObj.visit_type = [];
      if (this.getUserRole() === "doctor") {
        this.appointmentFormObj.doctor_id = {
          id: this.userData.ID,
          label: this.userData.display_name,
        };
        let doctor_id = this.userData;
        this.showCustomField = true;
        // this.getDoctorsServices(doctor_id.ID);
      } else if (this.getUserRole() === "patient") {
        this.appointmentFormObj.patient_id = {
          id: this.userData.ID,
          label: this.userData.display_name,
        };
      }
      this.$store.commit("appointmentModule/RESET_TIME_SLOT");
    },
    clinicChange(selectedOption) {
      if (this.getUserRole() !== "patient") {
        this.patientMultiselectLoader = true;
        get("get_static_data", {
          data_type: "users",
          user_type: this.patientRoleName,
          request_clinic_id: selectedOption.id,
        })
          .then((response) => {
            this.patientMultiselectLoader = false;
            if (
              response.data.status !== undefined &&
              response.data.status === true
            ) {
              this.patients = response.data.data;
            }
            if(this.appointmentFormObj.id !== undefined){
              this.appointmentFormObj.patient_id = {
                  id: this.appointmentData.patient_id.id,
                  label: this.appointmentData.patient_name,
                };
            }
          })
          .catch((error) => {
            this.patientMultiselectLoader = false;
            console.log(error);
            displayErrorMessage("Internal server error");
          });
      }
      // reset appointment form data on clinic change
      this.clearAppointmentData();

      if (this.getUserRole() === "doctor") {
        let doctor_id = this.userData;
        this.getDoctorsServices(doctor_id.ID);
        this.getDoctorActiveDays(selectedOption.id, 1);
      } else {
        this.doctorMultiselectLoader = true;

        get("get_static_data", {
          data_type: "get_users_by_clinic",
          clinic_id: ["clinic_admin", "receptionist"].includes(this.getUserRole()) ? this.userData.user_clinic_id : selectedOption.id,
        })
          .then((response) => {
            this.doctorMultiselectLoader = false;
            if (
              response.data.status !== undefined &&
              response.data.status === true
            ) {
              this.doctors = response.data.data;
              // if(this.appointmentFormObj.id !== undefined){
              //   this.appointmentFormObj.doctor_id = this.appointmentData.doctor_id
              // }
              if (response.data.postfix !== undefined) {
                this.postfix = response.data.postfix;
              }
              if (response.data.prefix !== undefined) {
                this.prefix = response.data.prefix;
              }
            } else {
              displayErrorMessage(response.data.message);
            }
          })
          .catch((error) => {
            this.doctorMultiselectLoader = false;
            console.log(error);
            displayErrorMessage(
              this.formTranslation.common.internal_server_error
            );
          });
      }
      this.appointment_tax_data();
    },
    dispatchTimeSlot: function () {
      
      let clinic_id = ["clinic_admin", "doctor", "receptionist"].includes(this.getUserRole()) ? this.userData.user_clinic_id : (
        this.appointmentFormObj?.clinic_id !== undefined &&
          this.appointmentFormObj?.clinic_id?.id !== undefined
          ? this.appointmentFormObj?.clinic_id?.id
          : this.userData.default_clinic_id);
          
      this.getTimeSlot({
        date: moment(this.appointmentFormObj.appointment_start_date).format(
          "YYYY-MM-DD"
        ),
        appointment_id:
          this.appointmentFormObj.id !== undefined
            ? this.appointmentFormObj.id
            : "",
        clinic_id: clinic_id,
        doctor_id: this.appointmentFormObj.doctor_id.id,
        service: this.appointmentFormObj.visit_type
      });
    },
    appointmentCloseForm() {
      this.upload_appointment_report = [];
      this.$emit("closeAppointmentForm");
      this.appointmentModel = false;
      this.loading = false;
    },
    getTimeSlot: function (data) {
      data.service = this.appointmentFormObj.visit_type[0];
      this.$store.dispatch("appointmentModule/fetchAppointmentSlots", data);
    },
    handleTimeChange(time) {
      this.appointmentFormObj.appointment_start_time = time;
    },

    getDoctorDropdown: function () {
      this.doctorMultiselectLoader = true;
      let clinic_id = this.appointmentFormObj?.clinic_id;
      if (typeof clinic_id === "object") {
        clinic_id = this.appointmentFormObj?.clinic_id?.id;
      }
      get("get_static_data", {
        data_type: "clinic_doctors",
        clinic_id: clinic_id,
        module_type: "appointment",
      })
        .then((response) => {
          this.doctorMultiselectLoader = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.doctors = response.data.data;
            if (response.data.postfix !== undefined) {
              this.postfix = response.data.postfix;
            }
            if (response.data.prefix !== undefined) {
              this.prefix = response.data.prefix;
            }
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          this.doctorMultiselectLoader = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    appointmentTypeChangeSelect(selected) {
      // Check if service is already selected to prevent duplicates
      const isAlreadySelected = this.appointmentFormObj.visit_type.some(
        (service) => service.id === selected.id
      );

      if (isAlreadySelected) {
        return; // Don't add if already selected
      }

      if (
        selected.multiple !== undefined &&
        selected.multiple !== "" &&
        selected.multiple == "no"
      ) {
        // For single selection services, replace the entire array
        this.appointmentFormObj.visit_type = [selected];
        this.appointmentTypeMultiselect = false;
      } else {
        // For multiple selection, add to existing array
        this.appointmentTypeMultiselect = true;
        this.appointmentFormObj.visit_type.push(selected);
      }

      // Trigger updates
      this.$nextTick(() => {
        this.dispatchTimeSlot();
        this.appointment_tax_data();
      });
    },

    appointmentTypeChangeUnselect(selected) {
      // Remove the unselected service
      this.appointmentFormObj.visit_type =
        this.appointmentFormObj.visit_type.filter(
          (service) => service.id !== selected.id
        );

      if (
        selected.multiple !== undefined &&
        selected.multiple !== "" &&
        selected.multiple == "no"
      ) {
        this.appointmentTypeMultiselect = true;
      }

      // Trigger updates
      this.dispatchTimeSlot();
      this.appointment_tax_data();
    },
    handleDoctorChange: function (selectedOption) {
      this.DoctorWorkdays = [];
      let clinic_id = ["clinic_admin", "doctor", "receptionist"].includes(this.getUserRole()) ? this.userData.user_clinic_id : (
        this.appointmentFormObj?.clinic_id !== undefined &&
          this.appointmentFormObj?.clinic_id?.id !== undefined
          ? this.appointmentFormObj?.clinic_id?.id
          : this.userData.default_clinic_id);

      this.getTimeSlot({
        date: moment(this.appointmentFormObj.appointment_start_date).format(
          "YYYY-MM-DD"
        ),
        appointment_id:
          this.appointmentFormObj.id !== undefined
            ? this.appointmentFormObj.id
            : "",
        clinic_id: clinic_id,
        doctor_id: selectedOption.id,
        service: this.appointmentFormObj.visit_type
      });
      this.getDoctorActiveDays(clinic_id, selectedOption.id);
      this.appointmentFormObj.visit_type = [];
      this.getDoctorsServices(selectedOption.id);
      this.forceRerender();
      this.appointment_tax_data();
    },
    getDoctorActiveDays: function (clinic_id, id) {
      if(clinic_id?.id){
        clinic_id = clinic_id.id;
      }
      if(id?.id){
        id = id.id;
      }
      get("get_doctor_workdays", {
        clinic_id: clinic_id,
        doctor_id: id,
      })
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.holiday = response.data.holiday;
            
            // Store the doctor's workdays (these are days the doctor DOESN'T work)
            // The API returns the days that should be disabled
            this.DoctorWorkdays = response.data.data;
            
            // Force the date picker to refresh
            this.$nextTick(() => {
              // If there's a selected date but it's not valid anymore, reset it
              if (this.appointmentFormObj.appointment_start_date) {
                const day = this.appointmentFormObj.appointment_start_date.getDay() + 1;
                if (this.DoctorWorkdays.includes(day)) {
                  // Current selected day is not available, reset it to the first available day
                  this.appointmentFormObj.appointment_start_date = this.findNextAvailableDate();
                }
              }
            });
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    
    // Find the next available date for appointment
    findNextAvailableDate() {
      const currentDate = new Date();
      // Set time to beginning of day for consistent comparison
      currentDate.setHours(0, 0, 0, 0);
      
      // Try the next 30 days
      for (let i = 0; i < 30; i++) {
        const testDate = new Date(currentDate);
        testDate.setDate(currentDate.getDate() + i);
        
        // Check if this day is available (not in DoctorWorkdays and not a holiday)
        const dayOfWeek = testDate.getDay() + 1; // Convert to 1-7 format
        if (!this.DoctorWorkdays.includes(dayOfWeek) && !this.isHoliday(testDate)) {
          return testDate;
        }
      }
      
      // If no available date found in the next 30 days, return today
      // (this case should be rare unless the doctor has no available days)
      return currentDate;
    },
    handleDateChange: function (selectedOption) {
      this.appointmentFormObj.appointment_start_time = "";
      let clinic_id = ["clinic_admin", "doctor", "receptionist"].includes(this.getUserRole()) ? this.userData.user_clinic_id : (
        this.appointmentFormObj?.clinic_id !== undefined &&
          this.appointmentFormObj?.clinic_id?.id !== undefined
          ? this.appointmentFormObj?.clinic_id?.id
          : this.userData.default_clinic_id);
      this.getTimeSlot({
        date: moment(selectedOption).format("YYYY-MM-DD"),
        appointment_id:
          this.appointmentFormObj.id !== undefined
            ? this.appointmentFormObj.id
            : "",
        clinic_id: clinic_id,
        doctor_id: this.appointmentFormObj.doctor_id.id,
        service: this.appointmentFormObj.visit_type
      });
    },
    handleDoctorUnselect: function (id) {
      this.clearAppointmentData();
      this.$store.commit("appointmentModule/RESET_TIME_SLOT");
      this.appointment_tax_data();
    },
    handleFormSubmit: function () {
      this.loading = true;
      this.submitted = true;
      if (this.userData.addOns.kiviPro != true) {
        let clinic_id = ["clinic_admin", "doctor", "receptionist"].includes(this.getUserRole()) ? this.userData.user_clinic_id : (
          this.appointmentFormObj?.clinic_id !== undefined &&
            this.appointmentFormObj?.clinic_id?.id !== undefined
            ? this.appointmentFormObj?.clinic_id?.id
            : this.userData.default_clinic_id);

        this.appointmentFormObj.clinic_id = clinic_id;
      }
      // stop here if form is invalid
      let patient_id = false;
      if (this.$route.params.patient_id !== undefined) {
        patient_id = this.$route.params.patient_id;
      } else if (this.patient_profile_id) {
        patient_id = this.patient_profile_id;
      }

      if (patient_id) {
        this.appointmentFormObj.patient_id = {
          id: patient_id,
        };
      }
      if(this.appointmentFormObj.id !== undefined){
        this.appointmentFormObj.patient_id = {
          id: this.appointmentData.patient_id,
        };
      }
      
      this.$v.$touch();
      if (this.$v.appointmentFormObj.$invalid) {
        this.loading = false;
        return;
      }

      if (this.requiredFields !== undefined && this.requiredFields.length > 0) {
        this.loading = false;
        displayErrorMessage(
          this.formTranslation.common.all_required_field_validation
        );
        return;
      }
      if (
        Object.keys(this.userData.all_payment_method).length > 0 &&
        this.appointmentFormObj.id === undefined && this.appointmentFormObj.payment_mode === undefined
      ) {
        this.loading = false;
        return;
      }
      if (validateForm("appointmentDataForm")) {
        this.bookAppointmentHandle();
      }
    },
    closeAppointmentModal() {
      this.appointmentModel = false;
      this.loading = false;
      this.overlaySpinner = false;
    },
    bookAppointmentHandle: function () {
      let appointmentData = Object.assign({}, this.appointmentFormObj);
      appointmentData.appointment_start_date = moment(
        appointmentData.appointment_start_date
      ).format("YYYY-MM-DD");
      appointmentData.tax = this.taxes;
      post("appointment_save", appointmentData)
        .then((response) => {
          this.loading = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            let checkWoocommerceCart = response.data;
            if (
              checkWoocommerceCart.woocommerce_cart_data !== undefined &&
              checkWoocommerceCart.woocommerce_cart_data != null
            ) {
              if (
                checkWoocommerceCart.woocommerce_cart_data
                  .woocommerce_redirect !== undefined
              ) {
                if (this.appointmentFormObj.payment_mode === "paymentPaypal") {
                  kiviOpenPaymentWindow(
                    checkWoocommerceCart.woocommerce_cart_data
                      .woocommerce_redirect
                  );
                  this.overlaySpinner = true;
                  this.timer = setInterval(this.checkChildWindow, 500);
                  return;
                } else {
                  location.href =
                    checkWoocommerceCart.woocommerce_cart_data.woocommerce_redirect;
                  return;
                }
              }
            } else {
              if (this.appointmentFormObj.payment_mode === "paymentRazorpay") {
                if (response.data.checkout_detail) {
                  kivicareCreateRazorpayCheckoutPage(
                    response.data.checkout_detail
                  );
                  this.overlaySpinner = true;
                  this.timer = setInterval(this.checkChildWindow, 500);
                } else {
                  displayErrorMessage(response.data.message);
                }
              } else {
                displayMessage(response.data.message);
                this.$store.commit("appointmentModule/RESET_TIME_SLOT");
                this.$emit("appointmentSaved", response.data.data);
                if (this.patient_profile_id) {
                  this.$store.dispatch(
                    "appointmentModule/fetchAppointmentEncounterCount",
                    { id: this.patient_profile_id }
                  );
                }
                this.overlaySpinner = false;
                this.appointmentModel = false;
                this.loading = false;
              }
            }
          } else {
            displayErrorMessage(response.data.message);
            this.overlaySpinner = false;
            this.appointmentModel = false;
            this.loading = false;
          }
        })
        .catch((error) => {
          this.appointmentModel = false;
          console.log(error);
          this.overlaySpinner = false;
          this.loading = false;
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    checkChildWindow() {
      let ele = document.getElementById("payment_status_child");
      if (ele !== null && ele.value !== "") {
        clearInterval(this.timer);
        if (ele.value === "failed") {
          displayErrorMessage(
            this.formTranslation.common.payment_transaction_failed
          );
          this.$refs.appointment_detail.loading = false;
          ele.value = "";
        } else if (ele.value === "approved") {
          ele.value = "";
          displayMessage(this.formTranslation.common.payment_transaction_saved);
          this.appointmentModel = false;
          this.$store.commit("appointmentModule/RESET_TIME_SLOT");
          this.$emit("appointmentSaved", {});
        } else {
          ele.value = "";
        }
        if (this.patient_profile_id) {
          this.$store.dispatch(
            "appointmentModule/fetchAppointmentEncounterCount",
            { id: this.patient_profile_id }
          );
        }
        this.overlaySpinner = false;
        this.loading = false;
      }
    },
    getDoctorsServices: function (doctorId) {
      // Skip if services are already loaded for this doctor
      if (this.serviceMultiselectLoader) {
        return; // Already loading
      }

      this.serviceMultiselectLoader = true;
      this.appointmentTypes = [];

      let clinic_id = ["clinic_admin", "doctor", "receptionist"].includes(this.getUserRole())
        ? this.userData.user_clinic_id
        : (this.appointmentFormObj?.clinic_id !== undefined &&
          this.appointmentFormObj?.clinic_id?.id !== undefined
          ? this.appointmentFormObj?.clinic_id?.id
          : this.userData.default_clinic_id);

      get("get_clinic_service", {
        module_type: "appointment_form",
        limit: 0,
        doctor_id: doctorId,
        clinic_id: clinic_id,
      })
        .then((response) => {
          this.serviceMultiselectLoader = false;
          this.appointmentTypes = JSON.parse(JSON.stringify(response.data.data));

          // Only set default service if we're in add mode and no service is selected yet
          if (this.appointmentTypes.length > 0 &&
            this.appointmentFormObj.id === undefined &&
            this.appointmentFormObj.visit_type.length === 0) {

            this.appointmentFormObj.visit_type.push(this.appointmentTypes[0]);

            if (this.appointmentTypes[0].multiple !== undefined &&
              this.appointmentTypes[0].multiple == "no") {
              this.appointmentTypeMultiselect = false;
            }
          }

          // Only dispatch time slot if we have a date and doctor
          if (this.appointmentFormObj.appointment_start_date &&
            this.appointmentFormObj.doctor_id) {
            this.dispatchTimeSlot();
          }

          this.appointment_tax_data();
        })
        .catch((error) => {
          this.serviceMultiselectLoader = false;
          console.log(error);
          displayErrorMessage(this.formTranslation.common.internal_server_error);
        });
    },
    getCustomFieldsValues: function (fieldsObj) {
      if (!fieldsObj || fieldsObj === undefined) {
        return false;
      }

      for (
        let index = 0;
        index < this.appointmentFormObj.custom_fields.length;
        index++
      ) {
        const customFielsElement = Object.assign(
          {},
          this.appointmentFormObj.custom_fields[index]
        );
        const customFielsElementValue = Object.assign({}, fieldsObj);
        customFielsElement.field_data =
          customFielsElementValue["custom_field_" + customFielsElement.id];
        this.appointmentFormObj.custom_fields[index] = customFielsElement;
      }

      //  previous version dead code
      // this.appointmentFormObj.custom_fields = fieldsObj;
      this.appointmentFormObj.custom_fields_data = fieldsObj;
    },
    getRequireFields: function (validateRequired) {
      this.requiredFields = validateRequired;
    },
    async initFormData() {
      if (Object.keys(this.appointmentData).length > 0) {
        try {
          // Set basic form data first
          this.appointmentFormObj = {
            ...this.appointmentFormObj,
            id: this.appointmentData.id,
            status: this.appointmentData.status?.toString(),
            description: this.appointmentData.description || "",
            custom_fields: this.appointmentData.custom_fields || [],
            payment_mode: this.appointmentData.payment_mode || "",
          };

          // Handle clinic selection first
          if (this.appointmentData.clinic_id) {
            // Set initial clinic data
            this.appointmentFormObj.clinic_id = {
              id: this.appointmentData.clinic_id,
              label: this.appointmentData.clinic_name,
            };
          }

          // Set doctor data
          if (this.appointmentData.doctor_id) {
            this.appointmentFormObj.doctor_id = {
              id: this.appointmentData.doctor_id,
              label: this.appointmentData.doctor_name,
            };

            // After doctor is set, get their services (once)
            if (this.appointmentTypes.length === 0) {
              await this.getDoctorsServices(this.appointmentData.doctor_id);
            }

            // Get doctor's working days
            await this.getDoctorActiveDays(
              this.appointmentData.clinic_id,
              this.appointmentData.doctor_id
            );
          }

          // Set patient data
          if (this.appointmentData.patient_id) {
            this.appointmentFormObj.patient_id = {
              id: this.appointmentData.patient_id,
              label: this.appointmentData.patient_name,
            };
          }
          console.log(this.appointmentData.patient_id,'ritesh');
          

          // Set appointment date and time
          if (this.appointmentData.appointment_start_date) {
            this.appointmentFormObj.appointment_start_date = new Date(
              this.appointmentData.appointment_start_date
            );
          }

          if (this.appointmentData.appointment_start_time) {
            this.appointmentFormObj.appointment_start_time =
              this.appointmentData.appointment_start_time;
          }

          // Set visit type/service
          if (this.appointmentData.visit_type && Array.isArray(this.appointmentData.visit_type)) {
            this.appointmentFormObj.visit_type = this.appointmentData.visit_type.map((service) => ({
              id: service.service_id,
              service_id: service.service_id,
              name: service.name,
              charges: service.charges,
              multiple: service.multiple,
            }));
          }

          // Set other data
          if (this.appointmentData.tax) {
            this.taxes = this.appointmentData.tax;
          }

          if (this.appointmentData.file && Array.isArray(this.appointmentData.file)) {
            this.upload_appointment_report = this.appointmentData.file.map(
              (file) => ({
                name: file.name,
                url: file.url,
              })
            );
          }
          
          
          // Update UI and fetch time slots
          this.$nextTick(() => {
            this.dispatchTimeSlot();
            this.appointment_tax_data();
          });
        } catch (error) {
          console.error("Error initializing form data:", error);
          displayErrorMessage(this.formTranslation.common.internal_server_error);
        }
      }
    },
    multiUploadProfile: function () {
      let _this = this;
      var custom_uploader = kivicareCustomImageUploader(
        this.formTranslation,
        "report",
        this.userData.addOns.kiviPro == true
      );

      custom_uploader.on("select", function () {
        var attachment = custom_uploader
          .state()
          .get("selection")
          .map((item) => {
            item.toJSON();
            return item;
          });
        if (_this.appointmentData.file == undefined) {
          _this.appointmentData.file = [];
        }
        attachment.map((report) => {
          _this.upload_appointment_report.push({
            name: report.attributes.filename,
            url: report.attributes.url,
          });
          _this.appointmentData.file.push(report.attributes.id);
        });
      });

      //Open the uploader dialog
      custom_uploader.open();
    },
    appointment_tax_data() {
      if (this.userData.addOns.kiviPro === false) {
        return;
      }
      this.taxes = [];
      post("tax_calculated_data", this.appointmentData)
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.taxes = response.data.data;
            this.appointmentFormObj.tax = response.data.tax_total;
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage("Internal server error");
        });
    },
  },
  computed: {
    canAddPatient() {
      return (
        ["administrator", "clinic_admin", "receptionist", "doctor"].includes(
          this.getUserRole()
        ) &&
        this.appointmentFormObj.id === undefined &&
        this.kcCheckPermission("patient_add")
      );
    },

    canShowClinicSelection() {
      return (
        this.userData.addOns.kiviPro === true &&
        ["administrator", "doctor", "patient"].includes(this.getUserRole())
      );
    },

    canAddService() {
      return (
        ["administrator", "clinic_admin", "receptionist", "doctor"].includes(
          this.getUserRole()
        ) &&
        this.appointmentFormObj.id === undefined &&
        this.kcCheckPermission("service_add")
      );
    },

    showPatientSelection() {
      return (
        this.getUserRole() !== "patient" &&
        this.$route.params.patient_id === undefined &&
        !this.patient_profile_id
      );
    },
    timeSlots() {
      return this.$store.state.appointmentModule.timeSlots;
    },
    // patients() {
    //   return this.$store.state.userDataModule.userDropDownData.patients;
    // },
    clinics() {
      this.clinicMultiselectLoader = false;
      if (this.$store.state.clinic.length > 0) {
        if (this.appointmentFormObj.id === undefined) {
          this.appointmentFormObj.clinic_id = this.$store.state.clinic[0];
          if (this.getUserRole() !== "doctor") {
            this.clinicChange(this.$store.state.clinic[0]);
          }
          // this.clinicChange(this.$store.state.clinic[0] );
        }
        return this.$store.state.clinic;
      } else {
        return [];
      }
    },
    userData() {
      if (
        this.$store.state.userDataModule !== undefined &&
        this.$store.state.userDataModule.user !== undefined
      ) {
        return this.$store.state.userDataModule.user;
      } else {
        return [];
      }
    },
    teleMedEn() {
      return this.userData.addOns.telemed;
    },
    customFieldsData() {
      return this.appointmentFormObj.custom_fields
        ? this.appointmentFormObj.custom_fields
        : [];
    },
    appointmentDoctorId() {
      if (this.appointmentFormObj.doctor_id !== undefined) {
        if (typeof this.appointmentFormObj.doctor_id == "object") {
          return this.appointmentFormObj.doctor_id.id;
        }
        return this.appointmentFormObj.doctor_id;
      }
    },
    deleteAppointmentReport: function (appointmentData) { },
    fileUploadEnable() {
      return this.$store.state.appointmentModule.file_upload_status;
    },
    enableDisableAppointmentDescriptionStatus() {
      return this.$store.state.appointmentModule.description_status;
    },
  },
  watch: {
    appointmentFormObj: {
      handler(newData) {
        this.appointmentFormObj.is_dashboard = newData.payment_mode == 'paymentStripepay';
      },
      deep: true,
    },
  },
};
</script>
<style scoped>
.appointment-widget-service-list {
  border: 1px solid #d0cece;
  padding: 10px 12px;
  border-radius: 0.25rem;
}

.badge {
  text-transform: unset !important;
}
</style>
