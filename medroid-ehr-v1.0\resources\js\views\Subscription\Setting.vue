<template>
    <div class="w-full min-h-screen">
      <div class="max-w-7xl mx-auto p-4 md:p-6">
        <!-- Main Card Container -->
        <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
          <!-- Header -->
          <div class="p-6 border-b border-gray-100">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <h1 class="text-2xl font-semibold text-gray-900">
                  Subscription Settings
                </h1>
                <span class="hidden md:inline-flex px-4 py-1.5 bg-blue-50 text-blue-700 rounded-full text-sm font-medium">
                  Settings
                </span>
              </div>
            </div>
          </div>
  
          <!-- Content Container -->
          <div class="p-6">
            <!-- Price Settings Section -->
            <div class="relative bg-white rounded-3xl shadow-sm overflow-hidden">
              <!-- Decorative Corner Elements -->
              <div class="absolute top-0 right-0 w-40 h-40 bg-blue-50 rounded-full -mr-20 -mt-20 opacity-70"></div>
              <div class="absolute bottom-0 left-0 w-32 h-32 bg-blue-50 rounded-full -ml-16 -mb-16 opacity-70"></div>
  
              <div class="p-8 relative z-10">
                <!-- Section Header -->
                <div class="flex items-center gap-4 mb-8">
                  <div class="w-12 h-12 bg-violet-100 rounded-2xl flex items-center justify-center">
                    <svg class="w-6 h-6 text-violet-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <h2 class="text-xl font-semibold text-gray-900">Additional Price Settings</h2>
                    <p class="text-sm text-gray-500">Configure additional pricing per doctor</p>
                  </div>
                </div>
  
                <!-- Form Content -->
                <div class="bg-gray-50 rounded-2xl p-6 max-w-xl">
                  <div class="space-y-6">
                    <!-- Price Input Group -->
                    <div>
                      <label for="input-live" class="flex items-center text-sm font-medium text-gray-900 mb-2">
                        <svg class="w-4 h-4 mr-2 text-violet-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Additional Price per Doctor:
                      </label>
                      
                      <div class="relative flex rounded-xl shadow-sm">
                        <!-- Prefix -->
                        <span 
                          v-if="$store.state.userDataModule.user.clinic_currency_detail.prefix" 
                          class="inline-flex items-center px-4 rounded-l-xl border border-r-0 border-gray-200 bg-gray-100 text-gray-600 text-sm"
                        >
                          {{ $store.state.userDataModule.user.clinic_currency_detail.prefix }}
                        </span>
                        
                        <!-- Input -->
                        <input
                          id="input-live"
                          type="number"
                          min="0"
                          v-model="setting.doctorPrice"
                          :placeholder="formTranslation.patient_bill.plh_price"
                          required
                          name="first_name"
                          class="flex-1 min-w-0 block w-full px-4 py-3 text-sm border border-gray-200 focus:ring-2 focus:ring-violet-500 focus:border-violet-500 transition-colors duration-200"
                          oninput="validity.valid||(value='');"
                          :class="{
                            'rounded-l-xl': !$store.state.userDataModule.user.clinic_currency_detail.prefix, 
                            'rounded-r-xl': !$store.state.userDataModule.user.clinic_currency_detail.postfix
                          }"
                        />
                        
                        <!-- Postfix -->
                        <span 
                          v-if="$store.state.userDataModule.user.clinic_currency_detail.postfix"
                          class="inline-flex items-center px-4 rounded-r-xl border border-l-0 border-gray-200 bg-gray-100 text-gray-600 text-sm"
                        >
                          {{ $store.state.userDataModule.user.clinic_currency_detail.postfix }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
  
                <!-- Save Button -->
                <div class="flex justify-end mt-6">
                  <button 
                    @click="saveCommonSettings"
                    class="inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl font-medium hover:from-blue-700 hover:to-blue-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-sm hover:shadow"
                    v-html="saveSettingBtn"
                  ></button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>

<script>
import { post, get } from "../../config/request";

export default {
  data: () => ({
      saveSettingBtn: '',
      setting: {
          doctorPrice: 0,
      },
      clinic_currency_prefix: '',
      clinic_currency_postfix: 'asda',
  }),
  mounted() {
      if (!['administrator'].includes(this.getUserRole())) {
          this.$router.push({ name: "403" });
      }
      this.saveSettingBtn = '<i class="fa fa-save"></i> ' + this.formTranslation.common.save;
      this.init();
  },
  methods: {
      init() {
           this.setting.doctorPrice = this.$store.state.userDataModule.user.subscription_settings.doctorPrice;
      },
      saveCommonSettings() {
          post('save_subscription_settings', { settings: this.setting }).then(res => {
              this.$store.dispatch("userDataModule/fetchUserData");
              displayMessage(res.data.message);
          });
      }
  }
}
</script>