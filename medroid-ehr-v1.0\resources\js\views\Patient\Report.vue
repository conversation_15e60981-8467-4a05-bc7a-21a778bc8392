<template>
  <div class="min-h-screen p-6 bg-gradient-to-br from-pink-50 to-purple-50">
    <!-- Header Section -->
    <div class="mb-8 flex justify-between items-center">
      <div class="flex items-center gap-4">
        <button
          v-if="showBackButton"
          @click="$router.go(-1)"
          class="flex items-center gap-2 px-4 py-2 bg-black text-white rounded-lg shadow-sm hover:bg-gray-800"
        >
          <i class="fa fa-angle-left mr-2"></i>
          <span>Back</span>
        </button>
        <h1 class="text-2xl font-semibold text-gray-800">
          {{ formTranslation?.patient?.medical_report }}
        </h1>
      </div>

      <div class="flex gap-2">
        <button
          v-if="medicalReportList.length > 0"
          @click="handlePrescriptionMail"
          class="flex items-center gap-2 px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800"
        >
          <i class="fas fa-paper-plane mr-2"></i>
          <span>{{
            getUserRole() === "patient" &&
            $route.params.patient_id !== undefined
              ? formTranslation?.doctor?.dt_lbl_email
              : formTranslation?.clinic?.dt_lbl_email
          }}</span>
        </button>

        <button
          v-if="
            encounterData.status != '0' &&
            kcCheckPermission('patient_report_add')
          "
          @click="openAddModal"
          class="flex items-center gap-2 px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800"
        >
          <i class="fa fa-plus mr-2"></i>
          <span>{{ formTranslation?.patient?.add_medical_report }}</span>
        </button>
      </div>
    </div>

    <!-- Email Form Section -->
    <div
      v-if="showEmailAddForm && medicalReportList.length > 0"
      class="mb-6 bg-white rounded-lg shadow-sm p-6"
    >
      <form id="sendMedicalReportDataForm" @submit.prevent="handleEmailSubmit">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              {{ formTranslation?.reports?.plh_select }}
              {{ formTranslation?.reports?.reports }}
              <span class="text-red-500">*</span>
            </label>
            <multi-select
              v-model="emailReportSelect"
              :options="medicalReportList"
              :multiple="true"
              label="name"
              track-by="id"
              :placeholder="formTranslation?.patient?.search_placeholder"
              class="w-full"
            >
            </multi-select>
          </div>
        </div>

        <div class="mt-4 flex justify-end gap-2">
          <button
            type="button"
            @click="closeEmail"
            class="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
          >
            {{ formTranslation?.common?.cancel }}
          </button>
          <button
            type="submit"
            class="flex items-center gap-2 px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 email-send-button"
          >
            <i class="fas fa-paper-plane mr-2"></i>
            <span>{{ formTranslation?.clinic?.dt_lbl_email }}</span>
          </button>
        </div>
      </form>
    </div>

    <!-- Reports Table -->
    <div
      v-if="kcCheckPermission('patient_report')"
      class="bg-white rounded-lg shadow-sm"
    >
      <div class="p-4 space-y-4">
        <!-- Loading State -->
        <div v-if="isLoading" class="flex justify-center py-8">
          <loader-component-2></loader-component-2>
        </div>

        <!-- Report List -->
        <table v-else class="w-full">
          <thead>
            <tr class="text-left border-b">
              <th class="pb-3 font-semibold text-gray-600">
                {{ formTranslation?.common?.name }}
              </th>
              <th class="pb-3 font-semibold text-gray-600">
                {{ formTranslation?.common?.date }}
              </th>
              <th
                v-if="getUserRole() !== 'patient'"
                class="pb-3 font-semibold text-gray-600"
              >
                {{ formTranslation?.common?.action }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="(medical, index) in medicalReportList"
              :key="index"
              class="border-b last:border-0"
            >
              <td class="py-4">
                <h3 class="text-lg font-medium text-blue-600">
                  {{ medical.name }}
                </h3>
              </td>
              <td class="py-4 text-gray-600">{{ medical.medical_date }}</td>
              <td v-if="getUserRole() !== 'patient'" class="py-4">
                <div class="flex space-x-2">
                  <button
                    v-if="kcCheckPermission('patient_report_edit')"
                    @click="editReportData(medical)"
                    class="p-2 text-blue-600 hover:bg-blue-100 rounded-md"
                    :title="formTranslation?.common?.edit"
                  >
                    <i class="fa fa-pen-alt"></i>
                  </button>
                  <button
                    v-if="kcCheckPermission('patient_report_view')"
                    @click="viewReportData(medical.patient_id, medical.id)"
                    class="p-2 text-blue-600 hover:bg-blue-100 rounded-md"
                    :title="formTranslation?.appointments?.view_report"
                  >
                    <i class="fa fa-eye"></i>
                  </button>
                  <button
                    v-if="
                      getUserRole() != 'patient' &&
                      kcCheckPermission('patient_report_delete') &&
                      ($route.params.encounter_id === undefined ||
                        ($route.params.encounter_id !== undefined &&
                          encounterData.status === '1'))
                    "
                    @click="deletemedicalReportData(index)"
                    class="p-2 text-red-600 hover:bg-red-100 rounded-md"
                    :title="formTranslation?.clinic_schedule?.dt_lbl_dlt"
                  >
                    <i class="fa fa-trash"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>

        <!-- Empty State -->
        <div
          v-if="!isLoading && !medicalReportList.length"
          class="py-8 text-center"
        >
          <h4 class="text-red-600 text-lg">
            {{ formTranslation?.patient_encounter?.no_patient_report_found }}
          </h4>
        </div>
      </div>
    </div>

    <!-- Add/Edit Report Modal -->
    <div
      v-if="showAddReportModal"
      class="fixed inset-0 z-50 overflow-y-auto bg-gray-500 bg-opacity-75 transition-opacity"
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
    >
      <div class="min-h-screen px-4 text-center">
        <!-- Background overlay -->
        <div class="fixed inset-0 transition-opacity" @click="closeModal"></div>

        <!-- Modal panel -->
        <div
          class="inline-block w-full max-w-3xl my-8 text-left align-middle transition-all transform bg-white shadow-xl rounded-lg"
        >
          <!-- Header -->
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-xl font-semibold text-gray-900" id="modal-title">
              {{ mutation_medical_report_label }}
            </h3>
          </div>

          <!-- Body -->
          <div class="px-6 py-4">
            <form
              id="medicalReportDataForm"
              @submit.prevent="handleSubmit"
              class="space-y-6"
            >
              <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Name Input -->
                <div class="space-y-1">
                  <label class="block text-sm font-medium text-gray-700">
                    {{ formTranslation?.common?.name }}
                    <span class="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    v-model="medicalReportData.name"
                    :placeholder="formTranslation?.patient?.plh_enter_report"
                    class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p
                    v-if="submitted && !$v.medicalReportData.name.required"
                    class="text-sm text-red-600"
                  >
                    {{ formTranslation?.common?.name_required }}
                  </p>
                </div>

                <!-- Date Picker -->
                <div class="space-y-1">
                  <label class="block text-sm font-medium text-gray-700">
                    {{ formTranslation?.common?.date }}
                    <span class="text-red-500">*</span>
                  </label>
                  <vc-date-picker
                    mode="date"
                    v-model="medicalReportData.date"
                    :max-date="new Date()"
                    :placeholder="formTranslation?.patient?.welcome_date_plh"
                    class="w-full"
                  >
                    <template v-slot="{ inputValue }">
                      <input
                        readonly
                        :value="inputValue"
                        :placeholder="
                          formTranslation?.patient?.welcome_date_plh
                        "
                        class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </template>
                  </vc-date-picker>
                  <p
                    v-if="submitted && !$v.medicalReportData.date.required"
                    class="text-sm text-red-600"
                  >
                    {{ formTranslation?.widgets?.date_required }}
                  </p>
                </div>

                <!-- File Upload -->
                <div class="space-y-1">
                  <label class="block text-sm font-medium text-gray-700">
                    {{ formTranslation?.patient?.upload_report }}
                    <span class="text-red-500">*</span>
                  </label>
                  <div class="flex items-center gap-3">
                    <button
                      type="button"
                      :disabled="isEditForm"
                      @click.prevent="uploadProfile"
                      class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {{ formTranslation?.common?.choose_file }}
                    </button>
                    <span class="text-sm text-gray-600 truncate">{{
                      new_report
                    }}</span>
                  </div>
                  <p v-if="upload_report_required" class="text-sm text-red-600">
                    {{ formTranslation?.widgets?.file_required }}
                  </p>
                </div>
              </div>
            </form>
          </div>

          <!-- Footer -->
          <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
            <div class="flex justify-end gap-3">
              <button
                type="button"
                @click="closeModal"
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                {{ formTranslation?.common?.cancel }}
              </button>

              <button
                v-if="!loading"
                type="submit"
                @click="handleSubmit"
                class="flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <i class="fa fa-save mr-2"></i>
                {{ formTranslation?.common?.save }}
              </button>

              <button
                v-else
                disabled
                type="button"
                class="flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg opacity-75 cursor-not-allowed"
              >
                <i class="fa fa-sync fa-spin mr-2"></i>
                {{ formTranslation?.common?.loading }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { required } from "vuelidate/lib/validators";
import { post, get } from "../../config/request";

export default {
  name: "MedicalReportComponent",

  props: {
    patient_profile_id: {
      type: [String, Number],
      default: "",
    },
  },

  data() {
    return {
      showAddReportModal: false,
      patient_id: 0,
      showAddForm: false,
      medicalReportList: [],
      medicalReportData: {},
      encounterData: {
        status: 1,
      },
      isLoading: true,
      showBackButton: true,
      buttonText: '<i class="fa fa-save"></i> Save',
      loading: false,
      submitted: false,
      showEmailAddForm: false,
      emailReportSelect: [],
      new_report: "",
      upload_report_required: false,
      mutation_medical_report_label: "",
      isEditForm: false,
    };
  },

  validations: {
    medicalReportData: {
      name: { required },
      date: { required },
    },
  },

  mounted() {
    this.new_report =
      this.formTranslation?.common?.no_file_chosen || "No file chosen";
    if (this.$route.params.encounter_id !== undefined) {
      this.encounterDataDetails();
    }
    if (this.$route.params.patient_id !== undefined) {
      this.patient_id = this.$route.params.patient_id;
      this.getReport();
      if (this.getUserRole() === "patient") {
        this.showBackButton = false;
      }
    }
    if (this.patient_profile_id) {
      this.patient_id = this.patient_profile_id;
      this.showBackButton = false;
      this.getReport();
    }
    this.medicalReportData = this.defaultmedicalReportData();
  },

  methods: {
    openAddModal() {
      this.showAddReportModal = true;
      this.isEditForm = false;
      this.resetForm();
      this.mutation_medical_report_label =
        this.formTranslation?.patient?.add_medical_report;
    },

    closeModal() {
      this.showAddReportModal = false;
      this.resetForm();
    },

    resetForm() {
      this.medicalReportData = this.defaultmedicalReportData();
      this.submitted = false;
      this.upload_report_required = false;
      this.new_report =
        this.formTranslation?.common?.no_file_chosen || "No file chosen";
    },

    encounterDataDetails() {
      if (this.$route.params.encounter_id !== undefined) {
        get("patient_encounter_details", {
          id: this.$route.params.encounter_id,
        })
          .then((data) => {
            if (data.data?.status === true) {
              this.encounterData = data.data.data;
              this.patient_id = this.encounterData.patient_id;
              this.getReport();
            }
          })
          .catch((error) => {
            console.error(error);
            if (typeof displayErrorMessage === "function") {
              displayErrorMessage(
                this.formTranslation?.common?.internal_server_error
              );
            }
          });
      }
    },

    defaultmedicalReportData() {
      return {
        name: "",
        date: new Date(),
        upload_report: "",
        patient_id: 0,
      };
    },

    getReport() {
      this.isLoading = true;
      get("get_patient_report", { patinet: this.patient_id })
        .then((response) => {
          if (response.data?.status) {
            this.medicalReportList = response.data.data;
          }
        })
        .catch((error) => {
          console.error(error);
          if (typeof displayErrorMessage === "function") {
            displayErrorMessage(
              this.formTranslation?.common?.internal_server_error
            );
          }
        })
        .finally(() => {
          this.isLoading = false;
        });
    },

    uploadProfile() {
      const custom_uploader = kivicareCustomImageUploader(
        this.formTranslation,
        "report"
      );

      custom_uploader.on("select", () => {
        const attachment = custom_uploader
          .state()
          .get("selection")
          .first()
          .toJSON();
        this.medicalReportData.upload_report = attachment.id;
        this.new_report = attachment.filename;
        this.upload_report_required = false;
      });

      custom_uploader.open();
    },

    editReportData(medical) {
      this.mutation_medical_report_label =
        this.formTranslation?.patient?.edit_medical_report;
      this.showAddReportModal = true;
      this.isEditForm = true;
      this.medicalReportData = {
        ...medical,
        date: new Date(medical.date),
      };
    },

    handlePrescriptionMail() {
      this.showEmailAddForm = true;
    },

    closeEmail() {
      $(".email-send-button").html(
        `<i class="fas fa-paper-plane"></i> ${this.formTranslation?.clinic?.dt_lbl_email}`
      );
      this.emailReportSelect = [];
      this.showEmailAddForm = false;
    },

    handleEmailSubmit() {
      if (this.emailReportSelect.length <= 0) {
        if (typeof displayErrorMessage === "function") {
          displayErrorMessage(
            this.formTranslation?.medical_records?.medical_record_not_found
          );
        }
        return;
      }

      $(".email-send-button").html(
        `<i class="fa fa-spinner fa-spin"></i> ${this.formTranslation?.common?.loading}`
      );

      post("patient_report_mail", {
        data: this.emailReportSelect,
        patient_id: this.patient_id,
      })
        .then((response) => {
          $(".email-send-button").html(
            `<i class="fas fa-paper-plane"></i> ${this.formTranslation?.clinic?.dt_lbl_email}`
          );

          if (response.data?.status === true) {
            if (typeof displayMessage === "function") {
              displayMessage(response.data.message);
            }
            this.showEmailAddForm = false;
          } else {
            if (typeof displayErrorMessage === "function") {
              displayErrorMessage(response.data.message);
            }
          }
        })
        .catch((error) => {
          console.error(error);
          if (typeof displayErrorMessage === "function") {
            displayErrorMessage(
              this.formTranslation?.common?.internal_server_error
            );
          }
        });
    },

    handleSubmit() {
      this.submitted = true;
      this.$v.$touch();

      if (this.$v.medicalReportData.$invalid) {
        this.loading = false;
        return;
      }

      if (!this.medicalReportData.upload_report) {
        this.upload_report_required = true;
        this.loading = false;
        return;
      }

      this.loading = true;
      this.submitted = false;
      this.medicalReportData.patient_id = this.patient_id;
      this.medicalReportData.date = this.medicalReportData.date.getTime();

      if (
        typeof validateForm === "function" &&
        validateForm("medicalReportDataForm")
      ) {
        post(
          this.isEditForm ? "edit_patient_report" : "upload_patient_report",
          this.medicalReportData
        )
          .then((response) => {
            const result = this.isEditForm ? response.data : response;
            this.loading = false;
            this.upload_report_required = false;

            if (result.data?.status) {
              if (typeof displayMessage === "function") {
                displayMessage(result.data.message);
              }
              this.closeModal();
              this.getReport();
            } else {
              if (typeof displayErrorMessage === "function") {
                displayErrorMessage(result.data.message);
              }
              this.closeModal();
              this.getReport();
            }
          })
          .catch((error) => {
            this.loading = false;
            this.upload_report_required = false;
            console.error(error);
            if (typeof displayErrorMessage === "function") {
              displayErrorMessage(
                this.formTranslation?.common?.internal_server_error
              );
            }
          });
      }
    },

    viewReportData(id, docId) {
      get("view_patient_report", {
        patient_id: id,
        doc_id: docId,
      })
        .then((response) => {
          if (response.data?.status) {
            setTimeout(() => {
              window.open(response.data.data, "_blank");
            });
          }
        })
        .catch((error) => {
          console.error(error);
          if (typeof displayErrorMessage === "function") {
            displayErrorMessage(
              this.formTranslation?.common?.internal_server_error
            );
          }
        });
    },

    deletemedicalReportData(index) {
      if (this.medicalReportList[index] !== undefined) {
        this.$swal
          .fire({
            title: this.formTranslation?.clinic_schedule?.dt_are_you_sure,
            text: this.formTranslation?.common?.py_delete_report,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#d33",
            cancelButtonColor: "#3085d6",
            confirmButtonText: this.formTranslation?.common?.yes,
            cancelButtonText: this.formTranslation?.common?.cancel,
          })
          .then((result) => {
            if (result.isConfirmed) {
              get("delete_patient_report", {
                id: this.medicalReportList[index].id,
              })
                .then(() => {
                  this.medicalReportList.splice(index, 1);

                  // Optional: Show a success message
                  this.$swal.fire({
                    icon: "success",
                    title: "Deleted!",
                    text: "The medical report has been deleted.",
                    showConfirmButton: false,
                    timer: 1500,
                  });
                })
                .catch((error) => {
                  console.error(error);

                  // Use Sweet Alert for error message
                  this.$swal.fire({
                    icon: "error",
                    title: "Error",
                    text: this.formTranslation?.common?.internal_server_error,
                  });
                });
            }
          });
      }
    },
  },

  computed: {
    userData() {
      return this.$store.state.userDataModule.user;
    },
  },
};
</script>
