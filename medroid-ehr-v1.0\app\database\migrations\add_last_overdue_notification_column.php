<?php
/**
 * Migration: Add last_overdue_notification column to tasks table
 * File: add_last_overdue_notification_column.php
 */

if (!defined('ABSPATH')) {
    exit;
}

class AddLastOverdueNotificationColumn {
    /**
     * Run the migration - adds the last_overdue_notification column
     */
    public function up() {
        global $wpdb;

        error_log("[Migration] Adding last_overdue_notification column to kc_tasks table");

        $tasks_table = $wpdb->prefix . 'kc_tasks';

        // Check if table exists
        $table_exists = $wpdb->get_var($wpdb->prepare("SHOW TABLES LIKE %s", $tasks_table));

        if (!$table_exists) {
            error_log("[Migration] Table kc_tasks does not exist, skipping migration.");
            return false;
        }

        // Check if column exists
        $column_exists = $wpdb->get_var($wpdb->prepare("SHOW COLUMNS FROM {$tasks_table} LIKE %s", 'last_overdue_notification'));

        if (!$column_exists) {
            $wpdb->query("ALTER TABLE {$tasks_table} ADD COLUMN last_overdue_notification DATETIME DEFAULT NULL AFTER updated_at");

            if ($wpdb->last_error) {
                error_log("[Migration] Error adding column: " . $wpdb->last_error);
                return false;
            }

            error_log("[Migration] Column last_overdue_notification added successfully.");
            return true;
        }

        error_log("[Migration] Column last_overdue_notification already exists, skipping.");
        return true;
    }

    /**
     * Reverse the migration - removes the last_overdue_notification column
     */
    public function down() {
        global $wpdb;

        error_log("[Migration] Removing last_overdue_notification column from kc_tasks table");

        $tasks_table = $wpdb->prefix . 'kc_tasks';

        // Check if table exists
        $table_exists = $wpdb->get_var($wpdb->prepare("SHOW TABLES LIKE %s", $tasks_table));

        if (!$table_exists) {
            error_log("[Migration] Table kc_tasks does not exist, skipping rollback.");
            return false;
        }

        // Check if column exists
        $column_exists = $wpdb->get_var($wpdb->prepare("SHOW COLUMNS FROM {$tasks_table} LIKE %s", 'last_overdue_notification'));

        if ($column_exists) {
            $wpdb->query("ALTER TABLE {$tasks_table} DROP COLUMN last_overdue_notification");

            if ($wpdb->last_error) {
                error_log("[Migration] Error removing column: " . $wpdb->last_error);
                return false;
            }

            error_log("[Migration] Column last_overdue_notification removed successfully.");
            return true;
        }

        error_log("[Migration] Column last_overdue_notification does not exist, skipping.");
        return true;
    }
}
