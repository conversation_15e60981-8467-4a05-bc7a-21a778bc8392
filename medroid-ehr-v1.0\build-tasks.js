const webpack = require('webpack');
const path = require('path');

// Create a minimal webpack configuration just for the task components
const config = {
  mode: 'development',
  entry: {
    'task-components': [
      './resources/js/components/Task/TaskCalendar.vue',
      './resources/js/components/Task/TaskKanban.vue',
      './resources/js/components/Task/TaskList.vue',
      './resources/js/components/Task/TaskCard.vue'
    ]
  },
  output: {
    path: path.resolve(__dirname, 'assets/js'),
    filename: 'task-components.min.js'
  },
  module: {
    rules: [
      {
        test: /\.vue$/,
        loader: 'vue-loader'
      },
      {
        test: /\.js$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: ['@babel/preset-env']
          }
        }
      }
    ]
  },
  resolve: {
    extensions: ['.js', '.vue', '.json'],
    alias: {
      'vue$': 'vue/dist/vue.common.js'
    }
  }
};

// Run webpack
webpack(config, (err, stats) => {
  if (err) {
    console.error(err.stack || err);
    if (err.details) {
      console.error(err.details);
    }
    return;
  }

  const info = stats.toJson();

  if (stats.hasErrors()) {
    console.error(info.errors);
  }

  if (stats.hasWarnings()) {
    console.warn(info.warnings);
  }

  console.log(stats.toString({
    chunks: false,  // Makes the build much quieter
    colors: true    // Shows colors in the console
  }));
});