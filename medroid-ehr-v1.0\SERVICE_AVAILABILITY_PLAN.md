# Service-Based Availability Implementation Plan

## Overview
This plan outlines the implementation of service-based availability for the KiviCare clinic management system. This feature allows specific services to have their own availability schedules that override the doctor's general availability.

## Completed Tasks
- [x] Created database table for service sessions (`kc_service_sessions`)
- [x] Created PHP model class `KCServiceSession`
- [x] Created controller `KCServiceSessionController` for CRUD operations
- [x] Created Vue component `ServiceSessionsTab.vue` for UI management
- [x] Completed updating `kvGetTimeSlots()` function to check for service-specific sessions
- [x] Added permissions for service session management
- [x] Added API routes for the service session controller
- [x] Integrated `ServiceSessionsTab.vue` component into service detail view

## Pending Tasks
- [ ] Implement validation for service session time slots
- [ ] Add logic to handle overlapping service sessions
- [ ] Test service-specific availability in appointment booking flow
- [ ] Modify appointment creation process to respect service availability
- [ ] Update documentation with the new feature details

## Current Status
Basic implementation is complete. The service-specific availability feature is now integrated into the service detail view. The `kvGetTimeSlots()` function has been updated to check for service-specific sessions before falling back to doctor sessions. Next steps involve testing and refinement of the feature.

## Implementation Details (Updated)

### API Endpoints
The following API routes have been added to handle service session management:
- `GET /service_session_list` - List all service sessions for a specific service
- `POST /save_service_session` - Create or update service sessions
- `POST /delete_service_session` - Delete a service session

### User Interface
Added a ServiceSessionsTab component to the service edit page that allows:
- Viewing existing service-specific availability sessions
- Adding new service sessions with multiple time slots per day
- Editing existing service sessions
- Deleting service sessions

### Time Slot Generation
Updated the time slot generation logic in `kvGetTimeSlots()` to:
1. Check if service-specific sessions exist
2. Use service-specific sessions if available
3. Fall back to doctor's general availability if no service-specific sessions exist

### Permissions
Added the following permissions to control access to service session management:
- `service_session_list` - View service sessions
- `service_session_add` - Add new service sessions
- `service_session_edit` - Edit existing service sessions
- `service_session_delete` - Delete service sessions

These permissions are granted to all roles except patients by default.

## Implementation Details

### Database Schema
Created a new table `kc_service_sessions` with the following structure:
- id (Primary Key)
- service_id (Foreign Key to services table)
- doctor_id (Foreign Key to users table)
- clinic_id (Foreign Key to clinics table)
- day (Weekday, 1-7)
- start_time (Time format)
- end_time (Time format)
- created_at
- updated_at

### Code Structure
- **Model**: `app/models/KCServiceSession.php`
- **Controller**: `app/controllers/KCServiceSessionController.php`
- **Database**: `app/database/kc-service-session-db.php`
- **Frontend**: `resources/js/components/service/ServiceSessionsTab.vue`
- **Helper Functions**: Modified `utils/kc_helpers.php`

### API Endpoints (To be implemented)
- `GET /service-sessions` - List all service sessions
- `GET /service-sessions/{id}` - Get a specific service session
- `POST /service-sessions` - Create a new service session
- `PUT /service-sessions/{id}` - Update a service session
- `DELETE /service-sessions/{id}` - Delete a service session

### Integration Points
- Service detail view: Add tab for managing service sessions
- Time slot calculation: Check service sessions before doctor sessions
- Appointment booking: Use service-specific availability if available