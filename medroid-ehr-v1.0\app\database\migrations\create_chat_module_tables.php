<?php
/**
 * Migration: Create Chat Module Feature Tables
 * File: create_chat_module_tables.php
 */

if (!defined('ABSPATH')) {
    exit;
}

class CreateChatModuleTables {
    /**
     * Run the migration - creates all tables for chat module
     */
    public function up() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // Define table names
        $conversations_table = $wpdb->prefix . 'md_chat_conversations';
        $members_table = $wpdb->prefix . 'md_chat_members';
        $messages_table = $wpdb->prefix . 'md_chat_messages';
        $message_read_table = $wpdb->prefix . 'md_chat_message_read';
        $settings_table = $wpdb->prefix . 'md_chat_settings';

        // 1. Conversations Table
        $conversations_sql = "CREATE TABLE IF NOT EXISTS {$conversations_table} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            name varchar(255) DEFAULT NULL,
            type varchar(20) NOT NULL DEFAULT 'direct',
            created_by bigint(20) NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            last_activity datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) {$charset_collate};";

        // 2. Members Table
        $members_sql = "CREATE TABLE IF NOT EXISTS {$members_table} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            conversation_id bigint(20) NOT NULL,
            user_id bigint(20) NOT NULL,
            joined_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY conv_user_unique (conversation_id, user_id)
        ) {$charset_collate};";

        // 3. Messages Table
        $messages_sql = "CREATE TABLE IF NOT EXISTS {$messages_table} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            conversation_id bigint(20) NOT NULL,
            user_id bigint(20) NOT NULL,
            message text,
            file_url varchar(255) DEFAULT NULL,
            file_type varchar(100) DEFAULT NULL,
            file_name varchar(255) DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) {$charset_collate};";

        // 4. Message Read Status Table
        $message_read_sql = "CREATE TABLE IF NOT EXISTS {$message_read_table} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            message_id bigint(20) NOT NULL,
            user_id bigint(20) NOT NULL,
            read_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY msg_user_unique (message_id, user_id)
        ) {$charset_collate};";

        // 5. Settings Table
        $settings_sql = "CREATE TABLE IF NOT EXISTS {$settings_table} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            clinic_id bigint(20) NOT NULL,
            allow_patient_doctor_chat varchar(10) NOT NULL DEFAULT 'no',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY clinic_unique (clinic_id)
        ) {$charset_collate};";

        // Execute all CREATE TABLE queries
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

        dbDelta($conversations_sql);
        dbDelta($members_sql);
        dbDelta($messages_sql);
        dbDelta($message_read_sql);
        dbDelta($settings_sql);

        // Update version tracking
        update_option('kivicare_chat_tables_version', KIVI_CARE_VERSION);

        error_log("[Migration] Chat module tables created successfully");
        return true;
    }

    /**
     * Reverse the migration - drops all chat module tables
     */
    public function down() {
        global $wpdb;

        error_log("[Migration] Dropping chat module tables");

        // Drop tables in reverse order to handle dependencies
        $wpdb->query("DROP TABLE IF EXISTS {$wpdb->prefix}md_chat_settings");
        $wpdb->query("DROP TABLE IF EXISTS {$wpdb->prefix}md_chat_message_read");
        $wpdb->query("DROP TABLE IF EXISTS {$wpdb->prefix}md_chat_messages");
        $wpdb->query("DROP TABLE IF EXISTS {$wpdb->prefix}md_chat_members");
        $wpdb->query("DROP TABLE IF EXISTS {$wpdb->prefix}md_chat_conversations");

        // Remove version tracking
        delete_option('kivicare_chat_tables_version');

        error_log("[Migration] Chat module tables dropped successfully");
        return true;
    }
}