// resources/js/utils/message.js

/**
 * Displays a success message using available notification methods
 * @param {string} message - The message to display
 * @param {Object} options - Additional options for the notification
 */
export const displayMessage = (message, options = {}) => {
  try {
    if (!message) return;
    
    // Use window.displayMessage if available (global function)
    if (typeof window.displayMessage === 'function') {
      window.displayMessage(message, options);
    } 
    // Check for toastify library
    else if (typeof window.Toastify === 'function') {
      window.Toastify({
        text: message,
        duration: options.duration || 3000,
        close: true,
        gravity: "top",
        position: "right",
        backgroundColor: "#2ecc71",
      }).showToast();
    } 
    // Check for jQuery alert
    else if (window.$ && window.$.alert) {
      window.$.alert(message);
    } 
    // Fallback to basic alert
    else {
      console.log(message);
      alert(message);
    }
  } catch (error) {
    console.error('Error displaying message:', error);
    console.log('Original message:', message);
    // Ultimate fallback
    alert(message);
  }
};

/**
 * Displays an error message using available notification methods
 * @param {string} message - The error message to display
 */
export const displayErrorMessage = (message) => {
  try {
    if (!message) return;
    
    // Use window.displayErrorMessage if available (global function)
    if (typeof window.displayErrorMessage === 'function') {
      window.displayErrorMessage(message);
    } 
    // Check for toastify library
    else if (typeof window.Toastify === 'function') {
      window.Toastify({
        text: message,
        duration: 3000,
        close: true,
        gravity: "top",
        position: "right",
        backgroundColor: "#e74c3c",
      }).showToast();
    } 
    // Check for jQuery alert
    else if (window.$ && window.$.alert) {
      window.$.alert('Error: ' + message);
    } 
    // Fallback to basic alert
    else {
      console.error(message);
      alert('Error: ' + message);
    }
  } catch (error) {
    console.error('Error displaying error message:', error);
    console.error('Original error message:', message);
    // Ultimate fallback
    alert('Error: ' + message);
  }
};

/**
 * Logs an error to console with context
 * @param {Error} error - The error object
 * @param {string} context - Context information about where the error occurred
 * @returns {string} - The error message
 */
export const logError = (error, context = '') => {
  console.error(`Error in ${context}:`, error);
  return error.message || 'An unexpected error occurred.';
};