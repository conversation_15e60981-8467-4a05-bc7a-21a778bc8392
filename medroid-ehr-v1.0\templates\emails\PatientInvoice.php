<?php 
if(!defined('ABSPATH')) exit; // Exit if accessed directly

ob_start(); // Start output buffering
?>
<style>
    /* Add print-specific styles */
    @media print {
        * {
            -webkit-print-color-adjust: exact !important;   /* Chrome, Safari */
            color-adjust: exact !important;                 /* Firefox */
            print-color-adjust: exact !important;           /* Future standard */
        }
        
        body {
            background-color: #f9f9f9 !important;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
        }
        
        .invoice-header {
            background-color: #4a77e5 !important;
            color: white !important;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
        }
        
        th {
            background-color: #f2f5ff !important;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
        }
        
        .footer {
            background-color: #f2f5ff !important;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
        }
        
        .status {
            background-color: #4caf50 !important;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
        }
        
        .status.unpaid {
            background-color: #f44336 !important;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
        }
    }

    section {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 20px;
        color: #333;
        background-color: #f9f9f9;
    }

    .invoice-container {
        max-width: 800px;
        margin: 0 auto;
        background-color: white;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        overflow: hidden;
    }

    .invoice-header {
        background-color: #4a77e5;
        color: white;
        padding: 20px 30px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .invoice-header h1 {
        margin: 0;
        font-size: 28px;
        font-weight: 600;
    }

    .invoice-body {
        padding: 30px;
    }

    .invoice-info {
        display: flex;
        justify-content: space-between;
        margin-bottom: 30px;
    }

    .invoice-info-left h2 {
        margin-top: 0;
        color: #4a77e5;
    }

    .invoice-details {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
    }

    .patient-details {
        border-top: 1px solid #eee;
        border-bottom: 1px solid #eee;
        padding: 20px 0;
        margin-bottom: 30px;
    }

    .patient-info {
        display: flex;
        justify-content: space-between;
    }

    .patient-info-item {
        flex: 1;
    }

    .services {
        margin-bottom: 30px;
    }

    table {
        width: 100%;
        border-collapse: collapse;
    }

    th {
        background-color: #f2f5ff;
        color: #4a77e5;
        text-align: left;
        padding: 12px;
    }

    td {
        padding: 12px;
        border-bottom: 1px solid #eee;
    }

    .total-row td {
        border-top: 2px solid #4a77e5;
        border-bottom: none;
        padding-top: 15px;
        font-weight: 600;
    }

    .text-right {
        text-align: right;
    }

    .status {
        background-color: #4caf50;
        color: white;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 500;
    }
    .status.unpaid {
        background-color: #f44336;
    }

    .footer {
        padding: 20px 30px;
        background-color: #f2f5ff;
        text-align: center;
        font-size: 14px;
        color: #666;
    }
</style>
<section>
<div class="invoice-container">
    <div class="invoice-header">
        <h1>{{clinic_name}}</h1>
        <div class="contact-info">
            <div>{{clinic_address}}</div>
            <div>Phone: {{clinic_phone}}</div>
            <div>{{clinic_email}}</div>
            <div>medroid.ai</div>
        </div>
    </div>

    <div class="invoice-body">
        <div class="invoice-details">
            <div class="invoice-info-left">
                <h2>Invoice #{{invoice_number}}</h2>
                <div>Created At: {{invoice_date}}</div>
            </div>
            <div class="invoice-info-right">
                <div class="text-right">Payment Status: <span class="status {{payment_status}}">{{payment_status}}</span></div>
            </div>
        </div>

        <div class="patient-details">
            <h3>Patient Details</h3>
            <div class="patient-info">
                <div class="patient-info-item">
                    <strong>Patient Name</strong>
                    <div>{{patient_name}}</div>
                </div>
                <div class="patient-info-item">
                    <strong>Gender</strong>
                    <div>{{patient_gender}}</div>
                </div>
                <div class="patient-info-item">
                    <strong>Date of Birth</strong>
                    <div>{{patient_dob}}</div>
                </div>
            </div>
        </div>

        <div class="services">
            <h3>Services</h3>
            <table>
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Item Name</th>
                        <th>Price</th>
                        <th>Quantity</th>
                        <th class="text-right">Total</th>
                    </tr>
                </thead>
                <tbody>
                    {{service_items}}
                    <tr class="total-row">
                        <td colspan="4" class="text-right">Total:</td>
                        <td class="text-right">{{total_amount}}</td>
                    </tr>
                    <tr>
                        <td colspan="4" class="text-right">Discount:</td>
                        <td class="text-right">{{discount_amount}}</td>
                    </tr>
                    <tr class="total-row">
                        <td colspan="4" class="text-right">Amount Due:</td>
                        <td class="text-right">{{amount_due}}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="footer">
        <p>Thank you for choosing {{clinic_name}} for your healthcare needs.</p>
    </div>
</div>
</section>

<?php
return ob_get_clean(); // Return the buffered content and clean the buffer