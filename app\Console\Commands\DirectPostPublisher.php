<?php

namespace App\Console\Commands;

use App\Models\AutomatedPost;
use App\Models\SocialContent;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DirectPostPublisher extends Command
{
    protected $signature = 'bot:direct-publish {--limit=20 : Number of posts to publish}';
    protected $description = 'Publish ready bot posts directly to social feed';

    public function handle()
    {
        $limit = (int) $this->option('limit');
        
        $this->info("📤 Publishing up to {$limit} ready posts...");
        
        // Get ready posts
        $readyPosts = AutomatedPost::where('status', 'ready')
            ->with(['botPersona', 'botUser'])
            ->orderBy('scheduled_at')
            ->limit($limit)
            ->get();
            
        if ($readyPosts->count() === 0) {
            $this->info("✅ No posts ready for publishing");
            return 0;
        }
        
        $this->info("Found {$readyPosts->count()} ready posts");
        
        $published = 0;
        $errors = 0;
        
        foreach ($readyPosts as $post) {
            try {
                $this->info("Publishing post by {$post->botPersona->first_name} {$post->botPersona->last_name}...");
                
                if ($this->publishPost($post)) {
                    $published++;
                    $this->line("  ✅ Published successfully");
                } else {
                    $errors++;
                    $this->line("  ❌ Failed to publish");
                }
                
            } catch (\Exception $e) {
                $errors++;
                $this->error("  ❌ Error: " . $e->getMessage());
            }
        }
        
        $this->info("🎯 Publishing complete:");
        $this->info("  ✅ Successfully published: {$published} posts");
        $this->info("  ❌ Errors: {$errors}");
        
        return 0;
    }
    
    private function publishPost(AutomatedPost $post): bool
    {
        try {
            DB::beginTransaction();
            
            // Create social content entry
            $socialContent = SocialContent::create([
                'source' => 'bot',
                'source_id' => "bot_post_{$post->id}",
                'content_type' => 'image',
                'media_url' => $post->image_url,
                'caption' => $post->caption,
                'health_topics' => $this->extractHealthTopics($post->caption),
                'relevance_score' => 8.5,
                'engagement_metrics' => [
                    'likes' => rand(15, 45),
                    'comments' => rand(2, 8),
                    'shares' => rand(1, 5)
                ],
                'filtered_status' => 'approved',
                'user_id' => $post->botUser->user_id ?? null,
                'published_at' => now()
            ]);
            
            // Update automated post status
            $post->update([
                'status' => 'published',
                'posted_at' => now()
            ]);
            
            // Add fake engagement
            $this->addFakeEngagement($socialContent);
            
            DB::commit();
            return true;
            
        } catch (\Exception $e) {
            DB::rollback();
            
            // Mark post as failed
            $post->update([
                'status' => 'failed',
                'error_message' => $e->getMessage()
            ]);
            
            return false;
        }
    }
    
    private function extractHealthTopics(string $caption): array
    {
        $topics = [];
        
        $keywords = [
            'yoga' => ['yoga', 'meditation', 'mindful', 'breath', 'zen', 'peace'],
            'fitness' => ['workout', 'exercise', 'fitness', 'strong', 'muscle', 'cardio'],
            'nutrition' => ['nutrition', 'healthy eating', 'food', 'diet', 'nourish', 'fuel'],
            'mental_health' => ['mental health', 'self-care', 'wellness', 'stress', 'anxiety', 'mood'],
            'lifestyle' => ['lifestyle', 'habits', 'routine', 'balance', 'health', 'wellbeing']
        ];
        
        $lowerCaption = strtolower($caption);
        
        foreach ($keywords as $topic => $words) {
            foreach ($words as $word) {
                if (strpos($lowerCaption, $word) !== false) {
                    $topics[] = $topic;
                    break;
                }
            }
        }
        
        return array_unique($topics) ?: ['wellness'];
    }
    
    private function addFakeEngagement(SocialContent $content): void
    {
        // Add some realistic engagement metrics
        $metrics = $content->engagement_metrics ?? [];
        
        $metrics['initial_engagement'] = [
            'timestamp' => now()->toISOString(),
            'likes_rate' => rand(70, 95) / 100,
            'comment_rate' => rand(5, 15) / 100,
            'share_rate' => rand(2, 8) / 100
        ];
        
        $content->update(['engagement_metrics' => $metrics]);
    }
}