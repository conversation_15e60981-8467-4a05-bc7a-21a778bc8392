<template>
  <Card>
    <CardHeader>
      <CardTitle class="text-lg">Quick Actions</CardTitle>
    </CardHeader>
    <CardContent class="space-y-2">
      <!-- Add Prescription -->
      <Button 
        variant="outline" 
        class="w-full justify-start" 
        @click="$emit('add-prescription')"
      >
        <Pill class="w-4 h-4 mr-2" />
        Add Prescription
      </Button>

      <!-- Add Document -->
      <Button 
        variant="outline" 
        class="w-full justify-start" 
        @click="$emit('add-document')"
      >
        <FileText class="w-4 h-4 mr-2" />
        Add Document
      </Button>

      <!-- Take Photo -->
      <Button 
        variant="outline" 
        class="w-full justify-start" 
        @click="takePhoto"
      >
        <Camera class="w-4 h-4 mr-2" />
        Take Photo
      </Button>

      <!-- Voice Note -->
      <Button 
        variant="outline" 
        class="w-full justify-start" 
        @click="toggleVoiceRecording"
        :class="{ 'bg-red-50 border-red-200': isRecording }"
      >
        <Mic class="w-4 h-4 mr-2" :class="{ 'text-red-500': isRecording }" />
        {{ isRecording ? 'Stop Recording' : 'Voice Note' }}
      </Button>

      <!-- Generate Letter -->
      <Button 
        variant="outline" 
        class="w-full justify-start" 
        @click="generateLetter"
      >
        <FileType class="w-4 h-4 mr-2" />
        Generate Letter
      </Button>

      <!-- AI Summary -->
      <Button 
        variant="outline" 
        class="w-full justify-start" 
        @click="generateAISummary"
        :disabled="aiLoading"
      >
        <Loader2 v-if="aiLoading" class="w-4 h-4 mr-2 animate-spin" />
        <Brain v-else class="w-4 h-4 mr-2" />
        AI Summary
      </Button>

      <!-- Print -->
      <Button 
        variant="outline" 
        class="w-full justify-start" 
        @click="printConsultation"
      >
        <Printer class="w-4 h-4 mr-2" />
        Print Record
      </Button>

      <!-- Export -->
      <Button 
        variant="outline" 
        class="w-full justify-start" 
        @click="exportConsultation"
      >
        <Download class="w-4 h-4 mr-2" />
        Export PDF
      </Button>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  Pill, FileText, Camera, Mic, FileType, Brain, Printer, 
  Download, Loader2 
} from 'lucide-vue-next'

interface Props {
  consultationId?: string
}

interface Emits {
  (e: 'add-prescription'): void
  (e: 'add-document'): void
  (e: 'photo-taken', photo: Blob): void
  (e: 'voice-recorded', audio: Blob): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Component state
const isRecording = ref(false)
const aiLoading = ref(false)
const mediaRecorder = ref<MediaRecorder | null>(null)
const recordedChunks = ref<Blob[]>([])

// Methods
const takePhoto = async () => {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ 
      video: { facingMode: 'environment' } 
    })
    
    // Create a video element to capture the photo
    const video = document.createElement('video')
    video.srcObject = stream
    video.play()
    
    // Wait for video to load
    video.addEventListener('loadedmetadata', () => {
      const canvas = document.createElement('canvas')
      canvas.width = video.videoWidth
      canvas.height = video.videoHeight
      
      const ctx = canvas.getContext('2d')
      ctx?.drawImage(video, 0, 0)
      
      canvas.toBlob((blob) => {
        if (blob) {
          emit('photo-taken', blob)
        }
      }, 'image/jpeg', 0.8)
      
      // Stop the stream
      stream.getTracks().forEach(track => track.stop())
    })
  } catch (error) {
    console.error('Error accessing camera:', error)
    alert('Unable to access camera. Please check permissions.')
  }
}

const toggleVoiceRecording = async () => {
  if (isRecording.value) {
    stopRecording()
  } else {
    startRecording()
  }
}

const startRecording = async () => {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
    mediaRecorder.value = new MediaRecorder(stream)
    recordedChunks.value = []
    
    mediaRecorder.value.ondataavailable = (event) => {
      if (event.data.size > 0) {
        recordedChunks.value.push(event.data)
      }
    }
    
    mediaRecorder.value.onstop = () => {
      const blob = new Blob(recordedChunks.value, { type: 'audio/webm' })
      emit('voice-recorded', blob)
      
      // Stop all tracks
      stream.getTracks().forEach(track => track.stop())
    }
    
    mediaRecorder.value.start()
    isRecording.value = true
  } catch (error) {
    console.error('Error accessing microphone:', error)
    alert('Unable to access microphone. Please check permissions.')
  }
}

const stopRecording = () => {
  if (mediaRecorder.value && isRecording.value) {
    mediaRecorder.value.stop()
    isRecording.value = false
  }
}

const generateLetter = () => {
  // Navigate to letter generation
  if (props.consultationId) {
    window.open(`/consultations/${props.consultationId}/letter`, '_blank')
  }
}

const generateAISummary = async () => {
  try {
    aiLoading.value = true
    // Call AI service to generate summary
    // This would integrate with your AI processing
    console.log('Generating AI summary for consultation:', props.consultationId)
    
    // Simulate AI processing
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    alert('AI summary generated successfully!')
  } catch (error) {
    console.error('Error generating AI summary:', error)
    alert('Failed to generate AI summary. Please try again.')
  } finally {
    aiLoading.value = false
  }
}

const printConsultation = () => {
  if (props.consultationId) {
    window.open(`/consultations/${props.consultationId}/print`, '_blank')
  } else {
    window.print()
  }
}

const exportConsultation = () => {
  if (props.consultationId) {
    window.open(`/consultations/${props.consultationId}/export`, '_blank')
  }
}
</script>
