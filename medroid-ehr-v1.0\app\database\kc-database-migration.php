<?php
/**
 * @package KiviCarePlugin
 * Simple Database Migration System
 */

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Class KCDatabaseMigration
 * 
 * Simple migration system for KiviCare plugin
 * Reads migration files from a flat directory structure
 */
class KCDatabaseMigration {
    
    // Directory where migrations are stored
    private $migrations_dir;
    
    // Table name to track migrations
    private $migration_table = 'kc_migrations';
    
    // Option name for last check time
    private $last_check_option = 'kivicare_migration_last_check';
    
    // How often to check migrations (in seconds) - default 1 day
    private $check_interval = 86400;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->migrations_dir = KIVI_CARE_DIR . 'app/database/migrations';
        
        // Register hooks
        add_action('kcActivatePlugin', array($this, 'initialize'), 10);
        add_action('admin_init', array($this, 'maybe_check_migrations'), 10);
        
        // Set up hooks for admin notice
        add_action('admin_notices', array($this, 'migration_admin_notice'));
        add_action('wp_ajax_kc_run_migrations', array($this, 'ajax_run_migrations'));
    }
    
    /**
     * Initialize the migration system
     */
    public function initialize() {
        // Create migrations directory if it doesn't exist
        if (!file_exists($this->migrations_dir)) {
            wp_mkdir_p($this->migrations_dir);
        }
        
        // Create migration tracking table
        $this->create_migrations_table();
        
        // Run all pending migrations
        $this->run_migrations();
    }
    
    /**
     * Create migrations tracking table
     */
    public function create_migrations_table() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . $this->migration_table;
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE IF NOT EXISTS {$table_name} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            migration_name varchar(255) NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY  (id),
            UNIQUE KEY migration_name (migration_name)
        ) {$charset_collate};";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Get applied migrations
     */
    private function get_applied_migrations() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . $this->migration_table;
        
        // Check if table exists before querying
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
        
        if (!$table_exists) {
            return array();
        }
        
        $results = $wpdb->get_col("SELECT migration_name FROM {$table_name}");
        
        return is_array($results) ? $results : array();
    }
    
    /**
     * Get migration files
     */
    private function get_migration_files() {
        $files = array();
        
        if (!file_exists($this->migrations_dir)) {
            return $files;
        }
        
        $scan_files = scandir($this->migrations_dir);
        
        foreach ($scan_files as $file) {
            if ($file === '.' || $file === '..') {
                continue;
            }
            
            // Only consider PHP files
            if (pathinfo($file, PATHINFO_EXTENSION) !== 'php') {
                continue;
            }
            
            $files[] = array(
                'name' => $file,
                'path' => $this->migrations_dir . '/' . $file
            );
        }
        
        return $files;
    }
    
    /**
     * Check if a table exists
     */
    private function table_exists($table_name) {
        global $wpdb;
        
        // Make sure to add prefix if not already included
        if (strpos($table_name, $wpdb->prefix) !== 0) {
            $table_name = $wpdb->prefix . $table_name;
        }
        
        return $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
    }

    /**
     * Run pending migrations
     */
    public function run_migrations() {
        global $wpdb;
        
        // Get applied migrations
        $applied_migrations = $this->get_applied_migrations();
        
        // Get migration files
        $files = $this->get_migration_files();
        
        $migrations_executed = false;
        
        // Begin transaction
        $wpdb->query('START TRANSACTION');
        
        foreach ($files as $file) {
            $file_name = $file['name'];
            
            // Skip if already applied
            if (in_array($file_name, $applied_migrations)) {
                continue;
            }
            
            error_log("Attempting to run migration: " . $file_name);
            
            // Include the file in a function to limit scope
            $success = $this->execute_migration_file($file['path']);
            
            // Mark migration as executed if successful
            if ($success) {
                $wpdb->insert(
                    $wpdb->prefix . $this->migration_table,
                    array(
                        'migration_name' => $file_name,
                        'created_at' => current_time('mysql')
                    )
                );
                
                $migrations_executed = true;
                error_log("Successfully executed migration: {$file_name}");
            } else {
                // Log the error but don't mark as executed to allow retry
                error_log("Migration {$file_name} failed to execute");
                // Rollback this specific migration attempt
                $wpdb->query('ROLLBACK');
                $wpdb->query('START TRANSACTION');
            }
        }
        
        // Commit transaction
        $wpdb->query('COMMIT');
        
        // Update last check time
        update_option($this->last_check_option, time());
        
        return $migrations_executed;
    }

    /**
     * Execute a migration file
     */
    private function execute_migration_file($file_path) {
        global $wpdb;
        
        try {
            // Get the filename for better error messages
            $file_name = pathinfo($file_path, PATHINFO_FILENAME);
            
            // Store the functions that exist before including this file
            $functions_before = get_defined_functions();
            $user_functions_before = isset($functions_before['user']) ? $functions_before['user'] : array();
            
            // Include the file
            include_once($file_path);
            
            // Check for new functions after including the file
            $functions_after = get_defined_functions();
            $user_functions_after = isset($functions_after['user']) ? $functions_after['user'] : array();
            
            // Get functions defined in this file
            $new_functions = array_diff($user_functions_after, $user_functions_before);
            
            // Find if this file defined an 'up' function
            $has_up_function = in_array('up', $new_functions);
            
            // Simple approach: directly call the up() function 
            // that should be defined in each migration file
            if ($has_up_function) {
                error_log("Running up() function from migration: " . $file_name);
                $result = up();
                return $result !== false;
            } 
            
            // If no up function was found, try the filename-specific up function
            $specific_up_function = $file_name . '_up';
            if (in_array($specific_up_function, $new_functions)) {
                error_log("Running {$specific_up_function}() function from migration");
                $result = call_user_func($specific_up_function);
                return $result !== false;
            }
            
            // Backwards compatibility: try class-based approach as fallback
            $class_name = str_replace(' ', '', ucwords(str_replace('_', ' ', $file_name)));
            
            if (class_exists($class_name) && method_exists($class_name, 'up')) {
                $migration_instance = new $class_name();
                $migration_instance->up();
                return true;
            }
            
            error_log("No migration functions found in file: " . $file_name);
            return false;
        } catch (Exception $e) {
            error_log("Error executing migration: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return false;
        } catch (Error $e) {
            error_log("PHP Error executing migration: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return false;
        }
    }
    
    /**
     * Check if migrations need to be run, but only at specific intervals
     * This is now only used for showing admin notices
     */
    public function maybe_check_migrations() {
        // Only run this check for admin users
        if (!current_user_can('manage_options')) {
            return;
        }

        $this->check_migrations();
    }
    
    /**
     * Check if there are pending migrations
     */
    public function check_migrations() {
        $applied_migrations = $this->get_applied_migrations();
        $files = $this->get_migration_files();
        
        $pending_migrations = false;
        
        foreach ($files as $file) {
            if (!in_array($file['name'], $applied_migrations)) {
                $pending_migrations = true;
                break;
            }
        }
        
        // Update the last check time
        update_option($this->last_check_option, time());
        
        // Set flag if there are pending migrations
        if ($pending_migrations) {
            update_option('kivicare_migrations_needed', 'yes');
        } else {
            delete_option('kivicare_migrations_needed');
        }
        
        return $pending_migrations;
    }
    
    /**
     * Display admin notice if migrations need to be run
     */
    public function migration_admin_notice() {
        // Only show to admin users
        if (!current_user_can('manage_options')) {
            return;
        }
        
        // Check if migrations are needed
        $migrations_needed = get_option('kivicare_migrations_needed', '');
        
        if ($migrations_needed === 'yes') {
            ?>
            <div class="notice notice-warning is-dismissible">
                <p><strong>KiviCare Database Update Required</strong></p>
                <p>Your database needs to be updated to work with the latest version of KiviCare.</p>
                <p>
                    <a href="#" class="button button-primary" id="kc-run-migrations">Update Database Now</a>
                </p>
            </div>
            <script>
                jQuery(document).ready(function($) {
                    $('#kc-run-migrations').on('click', function(e) {
                        e.preventDefault();
                        
                        $(this).text('Updating database...').prop('disabled', true);
                        
                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'kc_run_migrations',
                                nonce: '<?php echo wp_create_nonce('kc_run_migrations'); ?>'
                            },
                            success: function(response) {
                                if (response.success) {
                                    $('.notice').removeClass('notice-warning').addClass('notice-success')
                                        .html('<p><strong>Database update complete!</strong></p>');
                                    
                                    // Refresh the page after a short delay
                                    setTimeout(function() {
                                        window.location.reload();
                                    }, 1500);
                                } else {
                                    $('.notice').removeClass('notice-warning').addClass('notice-error')
                                        .html('<p><strong>Error updating database.</strong> Please try again or contact support.</p>');
                                }
                            },
                            error: function() {
                                $('.notice').removeClass('notice-warning').addClass('notice-error')
                                    .html('<p><strong>Error updating database.</strong> Please try again or contact support.</p>');
                            }
                        });
                    });
                });
            </script>
            <?php
        }
    }
    
    /**
     * AJAX handler to run migrations
     */
    public function ajax_run_migrations() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'kc_run_migrations')) {
            wp_send_json_error('Invalid nonce');
            return;
        }
        
        // Verify user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        // Run the migrations
        $this->run_migrations();
        
        // Clear the migrations needed flag
        delete_option('kivicare_migrations_needed');
        
        wp_send_json_success();
    }
    
    /**
     * Utility function to check if a table exists
     * Made public to be used in migration files
     */
    public function check_table_exists($table_name) {
        return $this->table_exists($table_name);
    }
}

// Initialize the migration system
$kc_migration = new KCDatabaseMigration();

// Function to run migrations manually if needed
function kivicare_run_migrations() {
    global $kc_migration;
    return $kc_migration->run_migrations();
}

// Add action to check and run migrations whenever the plugin loads
add_action('plugins_loaded', function() {
    global $kc_migration;
    
    // Create migration tracking table if needed
    $kc_migration->create_migrations_table();
    
    // Run all pending migrations
    $kc_migration->run_migrations();
}, 20);