<template>
  <div class="min-h-screen p-6 bg-gradient-to-br from-pink-50 to-purple-50">
    <!-- Header Section -->
    <div class="mb-8 flex justify-between items-center">
      <div class="flex items-center gap-4">
        <button class="flex items-center gap-2 px-4 py-2 bg-black text-white rounded-lg shadow-sm hover:bg-gray-800">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
            <path d="m12 19-7-7 7-7"/>
            <path d="M19 12H5"/>
          </svg>
          <span>Back</span>
        </button>
        <h1 class="text-2xl font-semibold text-gray-800">{{ formTranslation.clinic_schedule.holiday_list }}</h1>
      </div>
      <button 
        v-if="kcCheckPermission('clinic_schedule_add')"
        @click="openCreateModal"
        class="px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 flex items-center gap-2"
      >
        <span>{{ formTranslation.clinic_schedule.add_holiday_btn }}</span>
      </button>
    </div>

    <!-- Search Bar -->
    <div class="relative mb-6">
      <svg xmlns="http://www.w3.org/2000/svg" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5">
        <circle cx="11" cy="11" r="8"/>
        <path d="m21 21-4.3-4.3"/>
      </svg>
      <input 
        type="text"
        v-model="serverParams.searchTerm"
        @input="globalFilter"
        placeholder="Search holiday data..."
        class="w-full pl-10 pr-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
      >
    </div>

    <!-- Filters Grid -->
    <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">
      <input 
        v-model="serverParams.columnFilters.id"
        placeholder="ID" 
        class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
      >
      <input 
        type="date"
        v-model="serverParams.columnFilters.start_date"
        class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
      >
      <input 
        type="date"
        v-model="serverParams.columnFilters.end_date"
        class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
      >
    </div>

    <!-- Table Section -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-x-auto">
      <table class="w-full">
        <thead class="bg-gray-50">
          <tr>
            <th v-for="column in clinicScheduleList.column" 
                :key="column.field"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              {{ column.label }}
            </th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
          <tr v-for="(row, index) in clinicScheduleList.data" 
              :key="index"
              class="hover:bg-gray-50"
          >
            <td class="px-6 py-4 whitespace-nowrap">{{ row.id }}</td>
            <td class="px-6 py-4 whitespace-nowrap">{{ row.module_type }}</td>
            <td class="px-6 py-4 whitespace-nowrap">{{ row.name }}</td>
            <td class="px-6 py-4 whitespace-nowrap">{{ row.start_date }}</td>
            <td class="px-6 py-4 whitespace-nowrap">{{ row.end_date }}</td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex gap-2">
                <button 
                  v-if="kcCheckPermission('clinic_schedule_edit')"
                  @click="editClinicSchedule(row, row.id)"
                  class="p-1 hover:bg-gray-100 rounded"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-gray-600">
                    <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"/>
                  </svg>
                </button>
                <button 
                  v-if="kcCheckPermission('clinic_schedule_delete')"
                  @click="deleteClinicSchedule(index + 1)"
                  class="p-1 hover:bg-gray-100 rounded"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-red-500">
                    <path d="M3 6h18"/>
                    <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/>
                    <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>
                  </svg>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- Pagination -->
      <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
        <div class="flex items-center gap-2">
          <span class="text-sm text-gray-700">Rows per page:</span>
          <select 
            v-model="serverParams.perPage"
            @change="onPerPageChange"
            class="border border-gray-300 rounded-md text-sm p-1"
          >
            <option>10</option>
            <option>25</option>
            <option>50</option>
          </select>
        </div>
        <div class="flex items-center gap-4">
          <span class="text-sm text-gray-700">
            Page {{ serverParams.page }} of {{ Math.ceil(totalRows / serverParams.perPage) }}
          </span>
          <div class="flex gap-2">
            <button 
              @click="onPageChange(serverParams.page - 1)"
              :disabled="serverParams.page === 1"
              class="p-1 rounded hover:bg-gray-100 disabled:opacity-50"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-gray-600">
                <path d="m15 18-6-6 6-6"/>
              </svg>
            </button>
            <button 
              @click="onPageChange(serverParams.page + 1)"
              :disabled="serverParams.page >= Math.ceil(totalRows / serverParams.perPage)"
              class="p-1 rounded hover:bg-gray-100 disabled:opacity-50"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-gray-600">
                <path d="m9 18 6-6-6-6"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Create/Edit Modal -->
    <div v-if="showModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4">
        <div class="p-6">
          <div class="flex justify-between items-center mb-6">
            <h2 class="text-xl font-semibold">
              {{ editMode ? 'Edit Holiday' : formTranslation.clinic_schedule.add_holiday_btn }}
            </h2>
            <button @click="closeModal" class="text-gray-500 hover:text-gray-700">
              <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6">
                <path d="M18 6L6 18M6 6l12 12"/>
              </svg>
            </button>
          </div>
          <!-- Modal Content -->
          <Create 
            v-if="showModal"
            :holidayId="holidayId" 
            :holidayDetail="editHolidayDetail" 
            @getClinicScheduleList="getClinicScheduleList" 
            @closeForm="closeModal"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { post, get } from "../../config/request";
import Create from "./Create";
import _ from 'lodash';

export default {
  name: 'HolidaySchedule',
  components: { Create },
  data() {
    return {
      showModal: false,
      editMode: false,
      holidayId: -1,
      editHolidayDetail: [],
      pageLoader: true,
      clinicScheduleList: {
        column: [],
        data: []
      },
      serverParams: {
        columnFilters: {
          service_type: ''
        },
        sort: [{
          field: '',
          type: ''
        }],
        page: 1,
        perPage: 10,
        searchTerm: '',
        type: 'list'
      },
      totalRows: 0,
      request_status: 'off'
    };
  },

  mounted() {
    if(['patient'].includes(this.getUserRole())) {
      this.$router.push({ name: "403"});
    }
    this.init();
  },

  methods: {
    openCreateModal() {
      this.editMode = false;
      this.holidayId = -1;
      this.editHolidayDetail = [];
      this.showModal = true;
    },

    closeModal() {
      this.showModal = false;
      this.editMode = false;
      this.holidayId = -1;
      this.editHolidayDetail = [];
    },

    editClinicSchedule(data, id) {
      this.editMode = true;
      this.editHolidayDetail = data;
      this.holidayId = id;
      this.showModal = true;
    },

    init() {
      this.getClinicScheduleList();
    },

    async getClinicScheduleList() {
      try {
        this.pageLoader = true;
        const response = await get('clinic_schedule_list', this.serverParams);
        if (response.data.status) {
          this.clinicScheduleList.data = response.data.data;
          this.totalRows = response.data.total_rows;
        }
      } catch (error) {
        console.error(error);
      } finally {
        this.pageLoader = false;
      }
    },

    onPageChange(page) {
      this.serverParams.page = page;
      this.getClinicScheduleList();
    },

    onPerPageChange() {
      this.serverParams.page = 1;
      this.getClinicScheduleList();
    },

    globalFilter: _.debounce(function() {
      this.serverParams.page = 1;
      this.getClinicScheduleList();
    }, 300),

    async deleteClinicSchedule(index) {
      if (!this.clinicScheduleList.data[index - 1]) return;
      
      if (confirm(this.formTranslation.clinic_schedule.dt_are_you_sure)) {
        try {
          const response = await get('clinic_schedule_delete', {
            id: this.clinicScheduleList.data[index - 1].id
          });
          if (response.data.status) {
            this.getClinicScheduleList();
          }
        } catch (error) {
          console.error(error);
        }
      }
    }
  }
};
</script>