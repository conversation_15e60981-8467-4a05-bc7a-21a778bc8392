<template>
  <main class="p-6">
    <!-- AI Health Assistant Card -->
    <div
      v-if="
        userData.telemedConfigOn &&
        userData.is_enable_doctor_zoom_telemed == 'off'
      "
      class="bg-white text-card-foreground rounded-xl border shadow mb-6"
    >
      <div class="flex flex-col space-y-1.5 p-6">
        <h3
          class="font-semibold leading-none tracking-tight flex items-center space-x-2"
        >
          <i class="fas fa-exclamation-triangle text-purple-600 w-5 h-5"></i>
          <span>{{ formTranslation.zoom_telemed.deprecated_notice }}</span>
        </h3>
      </div>
      <div class="p-6 pt-0">
        <div class="flex items-start space-x-4">
          <div
            class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center"
          >
            <i class="fas fa-video w-5 h-5 text-purple-600"></i>
          </div>
          <div class="flex-1 p-4 bg-gray-50 rounded-lg">
            <div class="flex justify-between items-center">
              <p class="text-gray-600">
                {{ formTranslation.zoom_telemed.deprecated_notice }}
              </p>
              <button
                @click="$router.push('setting/telemed')"
                class="px-4 py-2 text-sm text-purple-600 bg-purple-50 rounded-lg hover:bg-purple-100"
              >
                {{ formTranslation.zoom_telemed.deprecated_notice_btn }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Stats Grid -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <!-- Total Patients Card -->
      <div v-if="kcCheckPermission('dashboard_total_patient')" class="w-full">
        <router-link :to="{ name: 'patient' }" class="block">
          <div
            class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300"
          >
            <div class="p-4">
              <div class="flex justify-between items-center">
                <div>
                  <h5 class="text-sm text-gray-600 uppercase mb-1">
                    {{ formTranslation.dashboard.total_patients }}
                  </h5>
                  <span v-if="isdataLoading" class="text-2xl font-bold">
                    <Loader2Icon class="animate-spin" />
                  </span>
                  <span v-else class="text-2xl font-bold">
                    {{ dashboardData.patient_count }}
                  </span>
                </div>
                <div class="flex-shrink-0">
                  <div
                    class="p-3 bg-gradient-to-r from-red-500 to-red-600 rounded-full text-white shadow-md"
                  >
                  <i class="fas fa-user-injured text-xl"></i>
                  </div>
                </div>
              </div>
              <p class="mt-3 text-sm text-gray-600">
                {{ formTranslation.dashboard.total_visited_patients }}
              </p>
            </div>
          </div>
        </router-link>
      </div>

      <!-- Total Appointments Card -->
      <div
        v-if="kcCheckPermission('dashboard_total_appointment')"
        class="w-full"
      >
        <router-link :to="{ name: 'appointment-list.index' }" class="block">
          <div
            class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300"
          >
            <div class="p-4">
              <div class="flex justify-between items-center">
                <div>
                  <h5 class="text-sm text-gray-600 uppercase mb-1">
                    {{ formTranslation.dashboard.total_appointments }}
                  </h5>
                  <span v-if="isdataLoading" class="text-2xl font-bold">
                    <Loader2Icon class="animate-spin" />
                  </span>
                  <span v-else class="text-2xl font-bold">
                    {{ dashboardData.appointment_count }}
                  </span>
                </div>
                <div class="flex-shrink-0">
                  <div
                    class="p-3 bg-gradient-to-r from-green-500 to-green-600 rounded-full text-white shadow-md"
                  >
                  <i class="fas fa-calendar-check text-xl"></i>
                  </div>
                </div>
              </div>
              <p class="mt-3 text-sm text-gray-600">
                {{ formTranslation.dashboard.total_appointments }}
              </p>
            </div>
          </div>
        </router-link>
      </div>

      <!-- Today's Appointments Card -->
      <div
        v-if="kcCheckPermission('dashboard_total_today_appointment')"
        class="w-full"
      >
        <router-link :to="{ name: 'appointment-list.index' }" class="block">
          <div
            class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300"
          >
            <div class="p-4">
              <div class="flex justify-between items-center">
                <div>
                  <h5 class="text-sm text-gray-600 uppercase mb-1">
                    {{ formTranslation.dashboard.total_today_appointments }}
                  </h5>
                  <span v-if="isdataLoading" class="text-2xl font-bold">
                    <Loader2Icon class="animate-spin" />
                  </span>
                  <span v-else class="text-2xl font-bold">
                    {{ dashboardData.today_count }}
                  </span>
                </div>
                <div class="flex-shrink-0">
                  <div
                    class="p-3 bg-gradient-to-r from-green-500 to-green-600 rounded-full text-white shadow-md"
                  >
                  <i class="fas fa-calendar-check text-xl"></i>
                  </div>
                </div>
              </div>
              <p class="mt-3 text-sm text-gray-600">
                {{ formTranslation.dashboard.total_today_appointments }}
              </p>
            </div>
          </div>
        </router-link>
      </div>

      <!-- Total Services Card -->
      <div v-if="kcCheckPermission('dashboard_total_service')" class="w-full">
        <router-link :to="{ name: 'service' }" class="block">
          <div
            class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300"
          >
            <div class="p-4">
              <div class="flex justify-between items-center">
                <div>
                  <h5 class="text-sm text-gray-600 uppercase mb-1">
                    {{ formTranslation.dashboard.total_service }}
                  </h5>
                  <span v-if="isdataLoading" class="text-2xl font-bold">
                    <Loader2Icon class="animate-spin" />
                  </span>
                  <span v-else class="text-2xl font-bold">
                    {{ dashboardData.service }}
                  </span>
                </div>
                <div class="flex-shrink-0">
                  <div
                    class="p-3 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full text-white shadow-md"
                  >
                  <i class="fas fa-money-check-alt text-xl"></i>
                  </div>
                </div>
              </div>
              <p class="mt-3 text-sm text-gray-600">
                {{ formTranslation.dashboard.total_service }}
              </p>
            </div>
          </div>
        </router-link>
      </div>
    </div>

    <!-- Bottom Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-1 gap-6">
      <!-- Appointment Calendar -->
      <div class="bg-white text-card-foreground rounded-xl border shadow">
        <div class="flex flex-col space-y-1.5 p-6">
          <h3 class="font-semibold leading-none tracking-tight">
            Appointments Calendar
          </h3>
        </div>
        <div class="p-6 pt-0">
          <appointment-calender v-if="reloadCalender" @reloadAppointment="init">
          </appointment-calender>
        </div>
      </div>
    </div>
  </main>
</template>

<script>
import AppointmentList from "../../components/appointment/AppointmentList";
import { get, post } from "../../config/request";
import VueApexCharts from "vue-apexcharts";

export default {
  components: {
    AppointmentList,
    apexcharts: VueApexCharts,
  },
  data: () => ({
    isLoading: false,
    dashboardData: {},
    isAppointmentReload: false,
    appointmentRequest: {},
    reloadCalender: true,
    isdataLoading: true,
    totalCardEnable: 0,
  }),
  mounted() {
    this.init();
    if (this.kcCheckPermission("dashboard_total_patient"))
      this.totalCardEnable++;
    if (this.kcCheckPermission("dashboard_total_appointment"))
      this.totalCardEnable++;
    if (this.kcCheckPermission("dashboard_total_today_appointment"))
      this.totalCardEnable++;
    if (this.kcCheckPermission("dashboard_total_service"))
      this.totalCardEnable++;
  },
  methods: {
    init() {
      this.getDashboardData();
      this.dashboardData = this.defaultDashboardData();
      this.appointmentRequest = this.defaultAppointmentRequest();
      this.reloadCalender = false;
      this.$nextTick(() => {
        this.reloadCalender = true;
      });
    },
    defaultDashboardData() {
      return {
        appointment_count: 0,
        doctor_count: 0,
        patient_count: 0,
        today_count: 0,
        revenue: 0,
        change_log: true,
        telemed_log: false,
      };
    },
    getDashboardData() {
      get("get_dashboard", {})
        .then((response) => {
          this.isdataLoading = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.dashboardData = response.data.data;
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(this.formTranslation.widgets.record_not_found);
        });
    },
    defaultAppointmentRequest() {
      return {
        date: new Date(),
      };
    },
    appointmentReload() {
      this.isLoading = false;
    },
  },
  computed: {
    userData() {
      return this.$store.state.userDataModule.user;
    },
  },
};
</script>
