<template>
  <Card>
    <CardHeader>
      <CardTitle class="text-lg flex items-center justify-between">
        <div class="flex items-center">
          <Calendar class="w-5 h-5 mr-2" />
          Previous Visits
        </div>
        <Button variant="ghost" size="sm" @click="showAll = !showAll">
          {{ showAll ? 'Show Less' : 'View All' }}
        </Button>
      </CardTitle>
    </CardHeader>
    <CardContent>
      <div v-if="loading" class="flex items-center justify-center py-6">
        <Loader2 class="w-6 h-6 animate-spin text-gray-400" />
      </div>
      
      <div v-else-if="visits.length" class="space-y-3">
        <div 
          v-for="visit in displayedVisits" 
          :key="visit.id"
          class="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
          @click="viewVisit(visit.id)"
        >
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <div class="flex items-center space-x-2">
                <h4 class="font-medium text-sm text-gray-900">
                  {{ visit.consultation_type || 'General Visit' }}
                </h4>
                <Badge :variant="getStatusVariant(visit.status)" class="text-xs">
                  {{ formatStatus(visit.status) }}
                </Badge>
              </div>
              
              <p class="text-xs text-gray-600 mt-1">
                {{ formatDate(visit.consultation_date) }}
              </p>
              
              <div v-if="visit.chief_complaint" class="mt-2">
                <p class="text-xs text-gray-500 line-clamp-2">
                  {{ visit.chief_complaint }}
                </p>
              </div>
              
              <div class="flex items-center justify-between mt-2">
                <p class="text-xs text-gray-500">
                  Dr. {{ visit.provider?.user?.name || 'Unknown' }}
                </p>
                <div class="flex items-center space-x-1">
                  <FileText v-if="visit.documents_count" class="w-3 h-3 text-gray-400" />
                  <Pill v-if="visit.prescriptions_count" class="w-3 h-3 text-gray-400" />
                </div>
              </div>
            </div>
            
            <ChevronRight class="w-4 h-4 text-gray-400 ml-2" />
          </div>
        </div>
        
        <!-- Load More Button -->
        <div v-if="!showAll && visits.length > 3" class="text-center pt-2">
          <Button variant="ghost" size="sm" @click="showAll = true">
            <Plus class="w-4 h-4 mr-1" />
            {{ visits.length - 3 }} more visits
          </Button>
        </div>
      </div>
      
      <div v-else class="text-center py-6 text-gray-500">
        <Calendar class="w-8 h-8 mx-auto mb-2 text-gray-300" />
        <p class="text-sm">No previous visits</p>
        <p class="text-xs text-gray-400 mt-1">
          This is the patient's first consultation
        </p>
      </div>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { router } from '@inertiajs/vue3'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Calendar, ChevronRight, FileText, Pill, Plus, Loader2 
} from 'lucide-vue-next'
import axios from 'axios'

interface Visit {
  id: number
  consultation_type: string
  status: string
  consultation_date: string
  chief_complaint?: string
  provider?: {
    user?: {
      name: string
    }
  }
  documents_count?: number
  prescriptions_count?: number
}

interface Props {
  patientId?: number
}

const props = defineProps<Props>()

// Component state
const visits = ref<Visit[]>([])
const loading = ref(false)
const showAll = ref(false)

// Computed
const displayedVisits = computed(() => {
  if (showAll.value) {
    return visits.value
  }
  return visits.value.slice(0, 3)
})

// Methods
const loadPreviousVisits = async () => {
  if (!props.patientId) return
  
  try {
    loading.value = true
    
    // For now, use mock data
    const mockVisits: Visit[] = [
      {
        id: 1,
        consultation_type: 'Follow-up',
        status: 'completed',
        consultation_date: '2024-01-10T10:00:00Z',
        chief_complaint: 'Chest pain follow-up, feeling better with medication',
        provider: { user: { name: 'Dr. Smith' } },
        documents_count: 2,
        prescriptions_count: 1
      },
      {
        id: 2,
        consultation_type: 'General',
        status: 'completed',
        consultation_date: '2024-01-05T14:30:00Z',
        chief_complaint: 'Chest pain and shortness of breath',
        provider: { user: { name: 'Dr. Johnson' } },
        documents_count: 1,
        prescriptions_count: 3
      },
      {
        id: 3,
        consultation_type: 'Emergency',
        status: 'completed',
        consultation_date: '2023-12-20T09:15:00Z',
        chief_complaint: 'Severe headache and dizziness',
        provider: { user: { name: 'Dr. Brown' } },
        documents_count: 0,
        prescriptions_count: 2
      },
      {
        id: 4,
        consultation_type: 'General',
        status: 'completed',
        consultation_date: '2023-11-15T11:00:00Z',
        chief_complaint: 'Annual check-up',
        provider: { user: { name: 'Dr. Smith' } },
        documents_count: 3,
        prescriptions_count: 0
      }
    ]
    
    visits.value = mockVisits
    
    // In a real implementation:
    // const response = await axios.get(`/api/patients/${props.patientId}/consultations`)
    // visits.value = response.data.filter(visit => visit.status === 'completed')
  } catch (error) {
    console.error('Error loading previous visits:', error)
  } finally {
    loading.value = false
  }
}

const viewVisit = (visitId: number) => {
  router.visit(`/consultations/${visitId}`)
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const formatStatus = (status: string) => {
  return status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ')
}

const getStatusVariant = (status: string) => {
  switch (status) {
    case 'completed':
      return 'default'
    case 'in_progress':
      return 'secondary'
    case 'cancelled':
      return 'destructive'
    default:
      return 'outline'
  }
}

// Watch for patient changes
watch(() => props.patientId, () => {
  if (props.patientId) {
    loadPreviousVisits()
  }
}, { immediate: true })

// Initialize
onMounted(() => {
  loadPreviousVisits()
})
</script>
