<template>
  <div>
    <component 
      :is="useModal ? 'b-modal' : 'div'" 
      no-close-on-esc 
      no-close-on-backdrop
      id="custom-form-modal"
      centered
      size="xl"
      v-model="customFormModal"
      v-if="customFormModal"
      :hide-footer="true"
      class="relative"
    >
      <template class="p-4" v-slot:modal-header="{ close }">
        <span v-html="formTitle" class="w-full"></span>
        <button 
          type="button" 
          aria-label="Close" 
          class="p-1 hover:bg-gray-100 rounded-full" 
          @click="closeModal"
        >×</button>
      </template>

      <div class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-50" v-show="overlay">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>

      <form 
        id="customFormDataForm" 
        @submit.prevent="handleSubmit" 
        :novalidate="true" 
        v-show="!overlay"
        class="p-4"
      >
        <span v-html="formTitle" class="w-full block mb-4" v-if="!useModal"></span>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div 
            :class="['hr','heading'].includes(field.type.id) ? 'col-span-2' : ''" 
            v-for="(field, index) in data.fields" 
            :key="index"
          >
            <div class="space-y-2">
              <label 
                :for="field.name" 
                v-if="!['heading','hr'].includes(field.type.id)"
                class="block text-sm font-medium text-gray-700"
              >
                {{ field.label }}
                <span class="text-red-500">
                  {{ field.is_required && ['1','true'].includes(field.is_required.toString()) ? '*' : '' }}
                </span>
              </label>

              <!-- Text and Number inputs -->
              <input
                v-if="field.type.id === 'text' || field.type.id === 'number'"
                :id="field.name"
                v-model="formData[field.name]"
                :placeholder="field.placeholder"
                :type="field.type.id"
                :disabled="viewMode"
                class="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
                :class="field.class"
              />

              <!-- Heading -->
              <div v-else-if="field.type.id === 'heading'">
                <component 
                  :is="field.tag ? field.tag : 'h2'" 
                  :class="['text-xl font-bold', field.class]"
                >
                  {{field.label}}
                </component> 
              </div>

              <!-- Horizontal Rule -->
              <div v-else-if="field.type.id === 'hr'">
                <hr :class="['border-t border-gray-200', field.class]">
              </div>

              <!-- File Upload -->
              <div v-else-if="field.type.id === 'file_upload'">
                <div class="flex items-center space-x-2">
                  <button 
                    class="px-4 py-2 text-white bg-blue-500 rounded-md hover:bg-blue-600 disabled:bg-blue-300" 
                    :class="field.class"
                    :disabled="viewMode" 
                    type="button" 
                    :id="field.name" 
                    @click.prevent="fileUpload(field)"
                  >
                    {{ formTranslation.common.choose_file }}
                  </button>
                  <label class="text-sm text-gray-600" :for="field.name">
                    <a 
                      class="text-blue-500 hover:text-blue-600" 
                      :id="'custom_field_'+field.name+'_file_upload'" 
                      :href="formData[field.name] && formData[field.name].url ? formData[field.name].url : ''" 
                      target="_blank"
                    >
                      {{formData[field.name] && formData[field.name].name ? formData[field.name].name : formTranslation.common.no_file_chosen}}
                    </a>
                  </label>
                </div>
              </div>

              <!-- Select -->
              <b-select
                v-else-if="field.type.id === 'select'"
                class="w-full px-3 py-2 border rounded-md capitalize focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
                :class="field.class"
                :id="field.name"
                v-model="formData[field.name]"
                :disabled="viewMode"
              >
                <option value="">{{formTranslation.common.select_option}}</option>
                <option 
                  v-for="(option, index) in field.options" 
                  :value="option.id" 
                  :key="index"
                >
                  {{ option.text }}
                </option>
              </b-select>

              <!-- Radio -->
              <div v-else-if="field.type.id === 'radio'" class="space-x-4">
                <div 
                  v-for="(option, key) in field.options" 
                  class="inline-flex items-center" 
                  :key="key"
                >
                  <input
                    type="radio"
                    :id="field.name+ '_' +key"
                    v-model="formData[field.name]"
                    :class="['form-radio text-blue-500 focus:ring-blue-500', field.class]"
                    :disabled="viewMode"
                    :name="field.name"
                    :value="option.text.replace(' ','-')"
                  >
                  <label 
                    class="ml-2 text-sm text-gray-700" 
                    :for="field.name + '_' +key"
                  >
                    {{ option.text }}
                  </label>
                </div>
              </div>

              <!-- Checkbox -->
              <div v-else-if="field.type.id === 'checkbox'" class="space-y-2">
                <b-form-checkbox-group
                  :id="field.name"
                  v-model="formData[field.name]"
                  :disabled="viewMode"
                  :class="field.class"
                >
                  <b-form-checkbox 
                    v-for="(option,key) in field.options" 
                    :key="key"
                    :value="option.id"
                    class="form-checkbox text-blue-500 focus:ring-blue-500"
                  >
                    {{option.text}}
                  </b-form-checkbox>
                </b-form-checkbox-group>
              </div>

              <!-- Calendar -->
              <div v-else-if="field.type.id === 'calendar'">
                <input 
                  type="date"
                  :class="['w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100', field.class]"
                  :id="'doc_birthdate'"
                  v-model="formData[field.name]"
                  :disabled="viewMode"
                >
              </div>

              <!-- Textarea -->
              <textarea
                v-else-if="field.type.id === 'textarea'"
                :id="field.name"
                :placeholder="field.placeholder"
                v-model="formData[field.name]"
                :disabled="viewMode"
                class="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
                :class="field.class"
              ></textarea>

              <!-- Multi Select -->
              <multi-select
                v-else-if="field.type.id === 'multi_select'"
                :id="field.name + '_' + index"
                :placeholder="field.placeholder"
                :name="'custom_field_'+field.id"
                :class="['multiselect-blue', field.class]"
                label="text"
                track-by="id"
                :options="field.options"
                :multiple="true"
                v-model="formData[field.name]"
                :disabled="viewMode"
              ></multi-select>

              <!-- Validation Message -->
              <div  
                class="hidden text-sm text-red-500" 
                :id="field.name+'_invalid-feedback'" 
                v-if="field.is_required && ['1','true'].includes(field.is_required.toString()) && !['hr','heading'].includes(field.type.id)"
              >
                {{ field.label + ' ' + formTranslation.common.required}}
              </div>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-2 mt-6">
          <div v-if="!viewMode" class="space-x-2">
            <button 
              v-if="!formLoading" 
              class="px-4 py-2 text-white bg-blue-500 rounded-md hover:bg-blue-600 flex items-center space-x-2" 
              type="submit"
            >
              <i class="fa fa-save"></i>
              <span>{{ formTranslation.common.save }}</span>
            </button>
            <button 
              v-else 
              class="px-4 py-2 text-white bg-blue-500 rounded-md hover:bg-blue-600 disabled:bg-blue-300 flex items-center space-x-2" 
              type="submit" 
              disabled
            >
              <i class="fa fa-sync fa-spin"></i>
              <span>{{formTranslation.common.loading}}</span>
            </button>
          </div>
          <button 
            type="button" 
            class="px-4 py-2 text-red-500 border border-red-500 rounded-md hover:bg-red-50" 
            @click="closeModal"
          >
            {{formTranslation.common.cancel}}
          </button>
        </div>
      </form>
    </component>
  </div>
</template>
  
  <script>
  
  import {post, get} from "../../config/request";
  
  export default {
    name: "CustomForm",
    props: {
        customFormModal:{
        type:[Boolean],
        default(){
          return false;
        }
      },
      useModal:{
        type:[Boolean],
        default(){
          return true;
        }
      },
      data: {
        type: [Object, Array],
        default() {
          return [
            
          ]
        }
      },
      viewMode:{
        type:[Boolean],
        default(){
          return false;
        }
      },
      fields:{
        type:[Object,Array],
        default(){
          return [];
        }
      }
    },
    data: () => {
      return {
        formLoading:false,
        overlay:true,
        formData:{

        },
        requiredFields:[]
      }
    },
    mounted() {
      console.log(this.fields)
      if(!this.fields || !this.fields.length){
        this.init();
      }else{
        this.data.fields = this.fields
        this.overlay = false;
      }
    },
    methods: {
      init: function () {
        this.data.fields.map((index) => {
            if(index.is_required && ['1','true'].includes(index.is_required.toString())){
                this.requiredFields.push(index.name)
            }
        })
        this.overlay = true;
        get('custom_form_data_get', {form_id: this.data.id, module_id: this.data.module_id})
            .then((response) => {
              if (response.data.status && response.data.status === true ) {
                this.formData = response.data.data
              }
              this.overlay = false;
            })
            .catch((error) => {
                this.overlay = false;
                console.log(error);
            })
      },
      closeModal(){
        this.$emit('closeModal')
      },
      handleSubmit(){
        this.submit = true;
        let validationFalse = false;
        let firstScroll = true;
        if(this.requiredFields.length){
            this.requiredFields.map((index,key) =>  {
                if(!this.formData[index]){
                    validationFalse = true;
                    $('#'+index+'_invalid-feedback').removeClass('d-none');
                    if(firstScroll){
                        document.querySelector('#'+index+'_invalid-feedback').scrollIntoView({block: "center", behavior: "smooth"})
                    }else{
                        firstScroll = false;
                    }
                }else{
                    $('#'+index+'_invalid-feedback').addClass('d-none');
                }
            })
        }
        if(validationFalse){
            displayErrorMessage(this.formTranslation.common.please_fill_all_required_fields);
            return;
        }
        this.formLoading = true;
        post('custom_form_data_save', {form_id: this.data.id, module_id: this.data.module_id,form_data:this.formData})
            .then((response) => {
              if (response.data.status && response.data.status === true ) {
                displayMessage(response.data.message)
                this.closeModal();
              } else {
                displayErrorMessage(response.data.message)
              }
              this.formLoading = false;
            })
            .catch((error) => {
                this.formLoading = false;
                console.log(error);
            })

      },
      fileUpload(data){
            let mediaType = data.file_upload_type.map((index) => {
                return index.id;
            })
            let custom_uploader = kivicareCustomImageUploader(this.formTranslation,'custom_field',false,{
                mediaType:mediaType
            })
            custom_uploader.on('select', () => {
                let attachment = custom_uploader.state().get('selection').first().toJSON();
                $('#custom_field_'+data.name+'_file_upload').attr('href',attachment.url).html(attachment.name)
                  // Use $set to update this.formData[data.name]
                this.$set(this.formData, data.name, {
                    id: attachment.id,
                    url: attachment.url,
                    name: attachment.filename
                });
            })
            //Open the uploader dialog
            custom_uploader.open();
        }
    },
    computed:{
        formTitle(){
            if(this.data.name && this.data.name.text){
                return `<h2 class="${this.data.name.color} ${this.data.name.align}">${this.data.name.text}</h2>`
            }
            return '';
        }
    }
  }
  </script>
  
  <style scoped>
  #doc_birthdate {
  border: 1px solid #c4c4c4;
  border-radius: 5px;
  background-color: #fff;
  padding: 3px 5px;
  box-shadow: inset 0 3px 6px rgba(0,0,0,0.1);
  /* width: 190px; */
  width: 100%;
  height: 45px;
  color:#8c9cad
}
#doc_birthdate ::placeholder{
  color:#8c9cad
}
#customFormDataForm .invalid-feedback-new{
    width: 100%;
    margin-top: 0.25rem;
    font-size: 100%;
    color: #f6993f;
}
  </style>