<!-- HealthProfile.vue -->
<template>
  <div class="space-y-6">
    <div
      v-for="(group, index) in groupedFields"
      :key="index"
      class="bg-white text-card-foreground rounded-xl border shadow"
    >
      <div class="flex flex-col space-y-1.5 p-6">
        <h3 class="font-semibold leading-none tracking-tight">
          {{ group.title }}
        </h3>
      </div>
      <div class="p-6 pt-0">
        <!-- Two column grid layout -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div 
            v-for="field in group.fields" 
            :key="field.id" 
            class="space-y-2"
          >
            <label class="block text-sm font-medium text-gray-700">
              {{ field.label }}
              <span v-if="field.isRequired === '1'" class="text-red-500">*</span>
            </label>

            <!-- Text, Number, Calendar -->
            <input
              v-if="['text', 'number', 'calendar'].includes(field.type)"
              v-model="localData[field.name]"
              :type="field.type === 'calendar' ? 'date' : field.type"
              :placeholder="field.placeholder"
              :disabled="!isEditMode"
              class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 disabled:bg-gray-100 disabled:cursor-not-allowed"
              :class="{ 
                'kivicare-required': field.isRequired === '1',
                'border-red-500': customFieldsData[field.id]?.hasError
              }"
              @input="handleFieldChange(field, $event.target.value)"
            />

            <!-- Textarea -->
            <textarea
              v-else-if="field.type === 'textarea'"
              v-model="localData[field.name]"
              :placeholder="field.placeholder"
              :disabled="!isEditMode"
              class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 disabled:bg-gray-100 disabled:cursor-not-allowed"
              :class="{ 
                'kivicare-required': field.isRequired === '1',
                'border-red-500': customFieldsData[field.id]?.hasError
              }"
              @input="handleFieldChange(field, $event.target.value)"
            ></textarea>

            <!-- Select -->
            <select
              v-else-if="field.type === 'select'"
              v-model="localData[field.name]"
              :disabled="!isEditMode"
              class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 disabled:bg-gray-100 disabled:cursor-not-allowed"
              :class="{ 
                'kivicare-required': field.isRequired === '1',
                'border-red-500': customFieldsData[field.id]?.hasError
              }"
              @change="handleFieldChange(field, $event.target.value)"
            >
              <option value="">Select Option</option>
              <option
                v-for="option in field.options"
                :key="option.id"
                :value="option.id"
              >
                {{ option.text }}
              </option>
            </select>

            <!-- Multiselect -->
            <multi-select
              v-else-if="field.type === 'multiselect'"
              v-model="localData[field.name]"
              :options="field.options"
              :disabled="!isEditMode"
              label="text"
              track-by="id"
              :multiple="true"
              class="multiselect-purple"
              :class="{ 
                'kivicare-required': field.isRequired === '1',
                'border-red-500': customFieldsData[field.id]?.hasError,
                'opacity-75 cursor-not-allowed': !isEditMode
              }"
              @input="handleFieldChange(field, $event)"
            />

            <!-- Radio -->
            <div v-else-if="field.type === 'radio'" class="space-y-2">
              <div
                v-for="option in field.options"
                :key="option.id"
                class="flex items-center space-x-2"
              >
                <input
                  type="radio"
                  :id="field.name + '_' + option.id"
                  v-model="localData[field.name]"
                  :value="option.id"
                  :disabled="!isEditMode"
                  class="disabled:cursor-not-allowed"
                  :class="{ 
                    'kivicare-required': field.isRequired === '1',
                    'border-red-500': customFieldsData[field.id]?.hasError
                  }"
                  @change="handleFieldChange(field, option.id)"
                />
                <label 
                  :for="field.name + '_' + option.id"
                  :class="{ 'opacity-75': !isEditMode }"
                >
                  {{ option.text }}
                </label>
              </div>
            </div>

            <!-- Checkbox -->
            <div v-else-if="field.type === 'checkbox'" class="space-y-2">
              <div
                v-for="option in field.options"
                :key="option.id"
                class="flex items-center space-x-2"
              >
                <input
                  type="checkbox"
                  :id="field.name + '_' + option.id"
                  v-model="localData[field.name]"
                  :value="option.id"
                  :disabled="!isEditMode"
                  class="disabled:cursor-not-allowed"
                  :class="{ 
                    'kivicare-required': field.isRequired === '1',
                    'border-red-500': customFieldsData[field.id]?.hasError
                  }"
                  @change="handleFieldChange(field, $event.target.checked ? option.id : null)"
                />
                <label 
                  :for="field.name + '_' + option.id"
                  :class="{ 'opacity-75': !isEditMode }"
                >
                  {{ option.text }}
                </label>
              </div>
            </div>

            <!-- Error Message -->
            <div 
              v-if="customFieldsData[field.id]?.hasError" 
              class="text-red-500 text-sm mt-1"
            >
              This field is required
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Form Actions -->
    <div v-if="isEditMode" class="flex justify-end mt-6 space-x-4">
      <button
        type="button"
        @click="cancelEdit"
        class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
      >
        Cancel
      </button>
      <button
        type="submit"
        :disabled="loading"
        class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 flex items-center"
        @click="saveChanges"
      >
        <i v-if="loading" class="fa fa-sync fa-spin mr-2"></i>
        {{ loading ? "Saving..." : "Save Changes" }}
      </button>
    </div>
  </div>
</template>

<script>
import { get, post } from "../../config/request";

export default {
  name: "HealthProfile",
  props: {
    isEditMode: {
      type: Boolean,
      default: false,
    },
    patientData: {
      type: Object,
      required: true,
    },
    customFields: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      localData: {},
      customFieldsData: {}, // Separate storage for custom fields
      loading: false,
      submitted: false,
    };
  },

  created() {
    this.initializeLocalData();
  },

  computed: {
    groupedFields() {
      const groups = {};

      this.customFields.forEach((field) => {
        const type = field.module_sub_type || "Other Details";
        if (!groups[type]) {
          groups[type] = {
            title: type
              .split("_")
              .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
              .join(" "),
            fields: [],
          };
        }
        groups[type].fields.push(field);
      });

      return Object.values(groups);
    },
  },

  methods: {
    // Update the initializeLocalData method
    initializeLocalData() {
      this.localData = {};
      this.customFieldsData = {}; // Reset custom fields data

      // Initialize custom fields with their existing values
      this.customFields.forEach((field) => {
        // Store custom field values separately
        this.customFieldsData[field.id] = {
          id: field.id,
          name: field.name,
          value: field.field_data,
          type: field.type,
        };

        // Handle different field types for initialization
        if (field.type === "multiselect") {
          // If field_data is a string, try to parse it as JSON
          try {
            const parsedData = field.field_data
              ? typeof field.field_data === "string"
                ? JSON.parse(field.field_data)
                : field.field_data
              : [];

            this.localData[field.name] = Array.isArray(parsedData)
              ? parsedData
              : parsedData
              ? [parsedData]
              : [];
          } catch (e) {
            console.warn(
              `Error parsing multiselect data for ${field.name}:`,
              e
            );
            this.localData[field.name] = [];
          }
        } else if (field.type === "checkbox") {
          // For checkbox, convert field_data to array if needed
          try {
            const parsedData = field.field_data
              ? typeof field.field_data === "string"
                ? JSON.parse(field.field_data)
                : field.field_data
              : [];

            this.localData[field.name] = Array.isArray(parsedData)
              ? parsedData
              : parsedData
              ? [parsedData]
              : [];
          } catch (e) {
            console.warn(`Error parsing checkbox data for ${field.name}:`, e);
            this.localData[field.name] = [];
          }
        } else {
          // For all other field types (text, number, select, radio, etc.)
          this.localData[field.name] = field.field_data || "";
        }

        // If field is required and has no value, mark it for validation
        if (field.isRequired === "1" && !field.field_data) {
          this.customFieldsData[field.id].hasError = true;
        }
      });
    },

    // Update handleFieldChange to maintain validation state
    handleFieldChange(field, value) {
      // Update the custom fields data structure
      this.customFieldsData[field.id] = {
        ...this.customFieldsData[field.id],
        value: value,
        hasError: field.isRequired === "1" && !value,
      };

      // Update localData for v-model binding
      if (field.type === "checkbox") {
        // Initialize as array if undefined
        if (!Array.isArray(this.localData[field.name])) {
          this.localData[field.name] = [];
        }

        // Handle checkbox arrays
        if (value !== null) {
          if (!this.localData[field.name].includes(value)) {
            this.localData[field.name].push(value);
          }
        } else {
          const index = this.localData[field.name].indexOf(field.id);
          if (index > -1) {
            this.localData[field.name].splice(index, 1);
          }
        }
      } else {
        this.localData[field.name] = value;
      }
    },

    // Add validation method
    validateFields() {
      let isValid = true;

      this.customFields.forEach((field) => {
        if (field.isRequired === "1") {
          const value = this.localData[field.name];
          const isEmpty =
            value === null ||
            value === undefined ||
            value === "" ||
            (Array.isArray(value) && value.length === 0);

          this.customFieldsData[field.id].hasError = isEmpty;

          if (isEmpty) {
            isValid = false;
          }
        }
      });

      return isValid;
    },

    // Update saveChanges to include validation
    async saveChanges() {
      if (!this.validateFields()) {
        if (typeof displayErrorMessage === "function") {
          displayErrorMessage("Please fill in all required fields");
        }
        return;
      }

      this.loading = true;
      try {
        const formData = new FormData();
        formData.append("ID", this.patientData.ID);

        const customFieldsData = this.prepareCustomFieldsData();
        Object.entries(customFieldsData).forEach(([key, value]) => {
          formData.append(`custom_fields[${key}]`, value);
        });

        formData.append("route_name", "patient_save_custom_field");

        if (window.kiviCareNonce) {
          formData.append("_ajax_nonce", window.kiviCareNonce);
        }

        const response = await post("patient_save_custom_field", formData);

        if (response.data.status) {
          this.$emit("saved", this.localData);
          if (typeof displayMessage === "function") {
            displayMessage(response.data.message);
          }
        } else {
          throw new Error(response.data.message);
        }
      } catch (error) {
        console.error("Error saving changes:", error);
        if (typeof displayErrorMessage === "function") {
          displayErrorMessage(error.message || "Error saving changes");
        }
      } finally {
        this.loading = false;
      }
    },

    // Fix prepareCustomFieldsData method
    prepareCustomFieldsData() {
      const customFields = {};

      // Handle regular inputs (text, number, calendar, textarea, radio, select)
      Object.entries(this.localData).forEach(([name, value]) => {
        const field = this.customFields.find((f) => f.name === name);
        if (field && value !== null && value !== undefined && value !== "") {
          if (field.type !== "checkbox" && field.type !== "multiselect") {
            customFields[`custom_field_${field.id}`] = value;
          }
        }
      });

      // Handle checkboxes - group values into arrays
      const checkboxFields = this.customFields.filter(
        (f) => f.type === "checkbox"
      );
      checkboxFields.forEach((field) => {
        const values = this.localData[field.name];
        if (Array.isArray(values) && values.length > 0) {
          customFields[`custom_field_${field.id}`] = values;
        }
      });

      // Handle multiselect - transform to {id: value, text: value} format
      const multiselectFields = this.customFields.filter(
        (f) => f.type === "multiselect"
      );
      multiselectFields.forEach((field) => {
        const values = this.localData[field.name];
        if (Array.isArray(values) && values.length > 0) {
          customFields[`custom_field_${field.id}`] = values.map((value) => ({
            id: value,
            text: value,
          }));
        }
      });

      return customFields;
    },

    cancelEdit() {
      this.initializeLocalData();
      this.$emit("cancel");
    },
  },
};
</script>

<style scoped>
.multiselect-purple {
  --ms-tag-bg: rgb(147, 51, 234);
  --ms-tag-color: white;
  --ms-option-bg-selected: rgb(147, 51, 234);
}
</style>
