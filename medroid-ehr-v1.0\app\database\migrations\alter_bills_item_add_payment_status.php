<?php
/**
 * Migration: Alter bills table to add payment_status column
 * File: alter_bills_add_payment_status.php
 */
if (!defined('ABSPATH')) {
    exit;
}

class AlterBillsAddBillingDataId {
    /**
     * Run the migration - adds payment_status column to kc_bills table
     */
    public function up() {
        global $wpdb;
        error_log("[Migration] Altering kc_bills table to add payment_status column");
        
        $table = $wpdb->prefix . 'kc_bill_items';
        
        // Check if table exists
        $table_exists = $wpdb->get_var($wpdb->prepare("SHOW TABLES LIKE %s", $table));
        if (!$table_exists) {
            error_log("[Migration] Table kc_bills does not exist, skipping migration.");
            return false;
        }
        
        // Check if column already exists
        $column_exists = $wpdb->get_var($wpdb->prepare("SHOW COLUMNS FROM {$table} LIKE %s", 'payment_status'));
        if ($column_exists) {
            error_log("[Migration] Column payment_status already exists, skipping addition.");
            
            // Check if index exists
            $index_exists = $wpdb->get_var("SHOW INDEX FROM {$table} WHERE Column_name = 'payment_status'");
            if (!$index_exists) {
                // Add only the index
                $wpdb->query("ALTER TABLE {$table} ADD KEY payment_status (payment_status)");
                if ($wpdb->last_error) {
                    error_log("[Migration] Error adding index: " . $wpdb->last_error);
                    return false;
                }
                error_log("[Migration] Index on payment_status added successfully.");
            } else {
                error_log("[Migration] Index on payment_status already exists, skipping.");
            }
            
            return true;
        }
        
        // Add column and index
        $wpdb->query("ALTER TABLE {$table} 
                     ADD COLUMN payment_status bigint(20) DEFAULT NULL,
                     ADD KEY payment_status (payment_status)");
        
        if ($wpdb->last_error) {
            error_log("[Migration] Error altering table: " . $wpdb->last_error);
            return false;
        }
        
        error_log("[Migration] Column payment_status and index added successfully.");
        return true;
    }
    
    /**
     * Reverse the migration - removes the payment_status column
     */
    public function down() {
        global $wpdb;
        error_log("[Migration] Removing payment_status column from kc_bills table");
        
        $table = $wpdb->prefix . 'kc_bills';
        
        // Check if table exists
        $table_exists = $wpdb->get_var($wpdb->prepare("SHOW TABLES LIKE %s", $table));
        if (!$table_exists) {
            error_log("[Migration] Table kc_bills does not exist, skipping rollback.");
            return false;
        }
        
        // Check if column exists
        $column_exists = $wpdb->get_var($wpdb->prepare("SHOW COLUMNS FROM {$table} LIKE %s", 'payment_status'));
        if (!$column_exists) {
            error_log("[Migration] Column payment_status does not exist, skipping removal.");
            return true;
        }
        
        // Check if index exists and drop it first
        $index_exists = $wpdb->get_var("SHOW INDEX FROM {$table} WHERE Column_name = 'payment_status'");
        if ($index_exists) {
            $wpdb->query("ALTER TABLE {$table} DROP INDEX payment_status");
            if ($wpdb->last_error) {
                error_log("[Migration] Error dropping index: " . $wpdb->last_error);
                // Continue with column drop attempt even if index drop fails
            } else {
                error_log("[Migration] Index on payment_status dropped successfully.");
            }
        }
        
        // Drop column
        $wpdb->query("ALTER TABLE {$table} DROP COLUMN payment_status");
        
        if ($wpdb->last_error) {
            error_log("[Migration] Error dropping column: " . $wpdb->last_error);
            return false;
        }
        
        error_log("[Migration] Column payment_status removed successfully.");
        return true;
    }
}