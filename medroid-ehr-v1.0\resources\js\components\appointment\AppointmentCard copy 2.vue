<template>
  <div class="w-full">
    <!-- Table Component -->
    <table class="w-full">
      <!-- Table Header -->
      <thead>
        <tr class="bg-gray-50/50 border-b border-gray-200">
          <th class="px-4 py-3 text-left text-sm font-medium text-gray-600">
            {{ formTranslation.patient.patient_name }}
          </th>
          <th class="px-4 py-3 text-left text-sm font-medium text-gray-600">
            {{ formTranslation.common.services }}
          </th>
          <th class="px-4 py-3 text-left text-sm font-medium text-gray-600">
            {{ formTranslation.service.charges }}
          </th>
          <th class="px-4 py-3 text-left text-sm font-medium text-gray-600">
            {{ formTranslation.common.status }}
          </th>
          <th class="px-4 py-3 text-left text-sm font-medium text-gray-600">
            {{ formTranslation.common.action }}
          </th>
        </tr>
      </thead>

      <!-- Table Body -->
      <tbody>
        <template v-if="!isLoading && appointmentList.length > 0">
          <tr
            v-for="(appointment, index) in appointmentList"
            :key="index"
            class="group hover:bg-gray-50/50"
          >
            <!-- Patient Info Column -->
            <td class="px-4 py-3">
              <div class="relative border-l-4 border-indigo-400 pl-4">
                <div class="flex flex-col gap-3">
                  <div class="space-y-1">
                    <h3 class="text-lg font-semibold text-gray-900">
                      {{ appointment.patient_name }}
                    </h3>
                    <!-- Doctor and Clinic Info -->
                    <div class="flex gap-4 text-sm text-gray-600">
                      <div class="flex items-center">
                        <span class="mr-1">Doctor:</span>
                        <span class="font-medium">{{
                          appointment.doctor_name
                        }}</span>
                      </div>
                      <div class="flex items-center">
                        <span class="mr-1">Clinic:</span>
                        <span class="font-medium">{{
                          appointment.clinic_name
                        }}</span>
                      </div>
                    </div>
                  </div>
                  <!-- Time and Date -->
                  <div class="flex items-center gap-6">
                    <div class="flex items-center gap-2">
                      <span class="font-medium text-gray-800">
                        {{ appointment.appointment_start_time }} -
                        {{ appointment.appointment_end_time }}
                      </span>
                    </div>
                    <div class="flex items-center gap-2">
                      <span class="text-gray-800">
                        {{ appointment.appointment_formated_start_date }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </td>

            <!-- Services Column -->
            <td class="px-4 py-3">
              <div
                class="px-3 py-2 bg-blue-50 border border-blue-100 rounded-lg"
              >
                <div class="text-blue-700 font-medium">
                  {{
                    serviceTypeFormat(
                      appointment.all_services,
                      appointment.visit_type_old
                    )
                  }}
                </div>
              </div>
            </td>

            <!-- Charges Column -->
            <td class="px-4 py-3">
              <span class="text-xl font-semibold text-gray-900">
                {{ appointment.clinic_prefix
                }}{{ appointment.all_service_charges || "0"
                }}{{ appointment.clinic_postfix }}
              </span>
            </td>

            <!-- Status Column -->
            <td class="px-4 py-3">
              <div
                class="inline-flex items-center rounded-md px-2.5 py-0.5 text-xs font-semibold"
                :class="getStatusClass(appointment.status)"
              >
                {{ getStatusText(appointment.status) }}
              </div>
            </td>

            <!-- Actions Column -->
            <td class="px-4 py-3">
              <button
                @click="handleAction(appointment)"
                class="inline-flex items-center justify-center rounded-md text-sm font-medium h-8 px-3 text-xs bg-black text-white hover:bg-gray-800"
              >
                Actions
              </button>
              <!-- Actions Dropdown -->
              <div
                v-if="showActionsMenu === appointment.id"
                class="absolute right-0 mt-2 w-48 rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-10"
              >
                <!-- View Button -->
                <button
                  v-if="kcCheckPermission('appointment_view')"
                  @click="
                    appointmentModalData = appointment;
                    appointmentDetailModal = true;
                  "
                  class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  <i class="fa fa-eye w-4 h-4 mr-2"></i>
                  {{ formTranslation.common.view }}
                </button>

                <!-- Edit Button -->
                <button
                  v-if="
                    !['3', '4', '0'].includes(appointment.status) &&
                    appointment.isEditAble &&
                    kcCheckPermission('appointment_edit') &&
                    (getUserRole() !== 'patient' ||
                      (getUserRole() === 'patient' &&
                        appointment.cancellation_buffer))
                  "
                  @click="
                    handleAppointmentEdit(appointment, 'accordion_' + index)
                  "
                  class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  <i class="fa fa-edit w-4 h-4 mr-2"></i>
                  {{ formTranslation.common.edit }}
                </button>

                <!-- Print Button -->
                <button
                  @click="handleAppointmentPrint(appointment.id)"
                  class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  <i class="fa fa-print w-4 h-4 mr-2"></i>
                  {{ formTranslation.widget_setting.print }}
                </button>

                <!-- Additional action buttons... -->
                <!-- [Previous action buttons converted to dropdown items] -->
              </div>
            </td>
          </tr>
        </template>

        <!-- Loading and Empty States -->
        <tr v-if="!isLoading && appointmentList.length === 0">
          <td colspan="5" class="px-4 py-8 text-center">
            <h4 class="text-red-600 font-medium">
              {{ formTranslation.common.no_appointments }}
            </h4>
          </td>
        </tr>
        <tr v-if="isLoading">
          <td colspan="5" class="px-4 py-8">
            <loader-component-2></loader-component-2>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
import { post, get } from "../../config/request";

export default {
  props: {
    appointmentData: {
      type: Array,
      default: () => [],
    },
    filterStatus: {
      type: String,
      default: "1",
    },
  },

  data() {
    return {
      isLoading: true,
      appointmentList: [],
      showActionsMenu: null,
    };
  },

  methods: {
    serviceTypeFormat(newVal, oldVal) {
      if (!oldVal) return newVal;
      return oldVal.replace(/_/g, " ");
    },

    handleAction(appointment) {
      this.showActionsMenu = appointment.id;
    },

    getStatusClass(status) {
      const statusClasses = {
        1: "bg-green-100 text-green-700",
        0: "bg-red-100 text-red-700",
        2: "bg-yellow-100 text-yellow-700",
        4: "bg-gray-100 text-gray-700",
        3: "bg-blue-100 text-blue-700",
      };
      return statusClasses[status] || "bg-gray-100 text-gray-700";
    },

    getStatusText(status) {
      const statusTexts = {
        1: this.formTranslation.appointments.booked,
        0: this.formTranslation.appointments.cancelled,
        2: this.formTranslation.appointments.pending,
        4: this.formTranslation.appointments.check_in,
        3: this.formTranslation.appointments.check_out,
      };
      return statusTexts[status] || "";
    },
  },

  watch: {
    appointmentData: {
      handler(newVal) {
        this.appointmentList = newVal;
        this.isLoading = false;
        this.$emit("updateAppointmentList", this.appointmentList);
      },
      immediate: true,
    },
  },
};
</script>
