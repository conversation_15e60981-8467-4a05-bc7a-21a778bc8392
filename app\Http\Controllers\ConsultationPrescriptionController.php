<?php

namespace App\Http\Controllers;

use App\Models\ConsultationPrescription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ConsultationPrescriptionController extends Controller
{
    /**
     * Store a new prescription.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'consultation_id' => 'required|exists:consultations,id',
            'medication_name' => 'required|string|max:255',
            'dosage' => 'nullable|string|max:100',
            'frequency' => 'nullable|string|max:100',
            'duration' => 'nullable|string|max:100',
            'instructions' => 'nullable|string',
            'status' => 'required|in:active,discontinued,completed',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $prescription = ConsultationPrescription::create([
            ...$request->validated(),
            'created_by' => auth()->id(),
        ]);

        return response()->json([
            'message' => 'Prescription added successfully',
            'data' => $prescription->load('creator')
        ], 201);
    }

    /**
     * Get prescriptions for a consultation.
     */
    public function index(Request $request)
    {
        $consultationId = $request->get('consultation_id');

        if (!$consultationId) {
            return response()->json([
                'message' => 'Consultation ID is required'
            ], 400);
        }

        $prescriptions = ConsultationPrescription::where('consultation_id', $consultationId)
            ->with('creator')
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'data' => $prescriptions
        ]);
    }

    /**
     * Update a prescription.
     */
    public function update(Request $request, $id)
    {
        $prescription = ConsultationPrescription::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'medication_name' => 'sometimes|string|max:255',
            'dosage' => 'nullable|string|max:100',
            'frequency' => 'nullable|string|max:100',
            'duration' => 'nullable|string|max:100',
            'instructions' => 'nullable|string',
            'status' => 'sometimes|in:active,discontinued,completed',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $prescription->update($request->validated());

        return response()->json([
            'message' => 'Prescription updated successfully',
            'data' => $prescription->load('creator')
        ]);
    }

    /**
     * Delete a prescription.
     */
    public function destroy($id)
    {
        $prescription = ConsultationPrescription::findOrFail($id);
        $prescription->delete();

        return response()->json([
            'message' => 'Prescription deleted successfully'
        ]);
    }
}
