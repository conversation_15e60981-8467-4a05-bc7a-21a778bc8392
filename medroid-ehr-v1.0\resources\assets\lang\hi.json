{"common": {"kivicare_Management_system": "KiviCare - क्लिनिक और रोगी प्रबंधन प्रणाली (EHR)", "no_appointments": "कोई नियुक्ति नहीं मिली", "loading": "लोड हो रहा है ...", "cancel": "रद्द", "date": "दिना<PERSON><PERSON>", "close": "बंद", "closed": "बंद", "select_option": "- चयन विकल्प -", "all": "सभी", "back_to_wordpress": "वापस Wordpress के लिए ", "update": "अपडेट करें", "my_profile": "मेरी प्रोफाइल", "change_password": "पासवर्ड बदलें", "logout": "लॉग आउट", "full_screen": "पूर्ण स्क्रीन", "warning_zoom_configuration": "चेतावनी: कृपया ज़ूम कॉन्फ़िगरेशन को सहेजें", "zoom_configuration_link": "ज़ूम कॉन्फ़िगरेशन लिंक", "dob": "जन्म तिथि", "dob_required": "जन्म तिथि की आवश्यकता है", "gender": "लिंग", "gender_required": "लिंग की आवश्यकता होती है", "male": "पुरुष", "female": "महिला", "other": "अन्य", "service": "सर्विस", "services": "सेवाएं", "sr_no": "क्रमांक", "item_name": "आइटम नाम", "price": "कीमत", "quantity": "मात्रा", "total": "संपूर्ण", "no_records_found": "कोई रिकॉर्ड नहीं मिला", "_note": "ध्यान दें", "note": "ध्यान दें: नई सेवा बनाने के लिए टाइप और एंटर दबाएं", "status": "स्थिति", "action": "कार्य", "title": "शीर्षक", "name": "नाम", "doctor": "डॉक्टर", "doctors": "डॉक्टरों", "patient": "मरीज़", "fname": "पहला नाम", "fname_required": "पहला नाम आवश्यक है।", "lname": "उपनाम", "lname_required": "उपनाम आवश्यक है।", "email": "ईमेल", "email_required": "ईमेल आवश्यक है।", "password": "पासवर्ड", "pwd_required": "पासवर्ड की आवश्यकता है", "repeat_pwd": "पासवर्ड दोहराएं", "repeat_password_required": "पासवर्ड दोहराना आवश्यक है", "pwd_not_match": "नया पासवर्ड और पुष्टिकरण पासवर्ड मेल नहीं खाते हैं", "login_btn": "लॉग इन करें", "sign_up": "साइन अप करें", "no": "संख्या:#", "dr": "डॉ।", "filters": "फ़िल्टर", "back": "पीछे", "save": "सहेजें", "invalid_email": "अमान्य ईमेल प्रारूप", "active": "सक्रिय", "inactive": "निष्क्रिय", "name_required": "नाम की आवश्यकता है", "email_address": "ईमेल पता", "contact_info": "संपर्क जानकारी", "settings": "सेटिंग्स", "fname_validation_1": "पहला नाम केवल अक्षर मान देता है (स्थान की अनुमति नहीं है)", "fname_validation_2": "पहले नाम की लंबाई 2 से 15 के बीच होनी चाहिए", "lname_validation_1": "अंतिम नाम केवल अल्फ़ाबेटिक मान की अनुमति देता है (स्थान की अनुमति नहीं)", "lname_validation_2": "अंतिम नाम की लंबाई 2 से 15 के बीच होनी चाहिए", "contact": "संपर्क नंबर", "contact_required": "संपर्क आवश्यक है", "contact_validation_1": "संपर्क संख्या की लंबाई 6 से 15 अंकों के बीच होनी चाहिए", "contact_validation_2": "अमान्य संपर्क नंबर प्रारूप", "telemed": "टेलीमेड", "to": "से", "time": "समय", "contact_no": "संपर्क नंबर", "contact_num_required": "संपर्क नंबर आवश्यक है", "city": "<PERSON><PERSON><PERSON>", "city_required": "शहर की आवश्यकता है", "city_validation_1": "शहर का नाम केवल अल्फाबेटिक वैल्यू देता है", "city_validation_2": "शहर की अधिकतम लंबाई 30 वर्ण होनी चाहिए", "state": "राज<PERSON>य", "state_validation_1": "राज्य का नाम केवल वर्णानुक्रमिक मूल्य की अनुमति देता है", "state_validation_2": "राज्य की अधिकतम लंबाई 30 वर्ण होनी चाहिए", "country": "देश", "country_required": "देश की आवश्यकता है", "country_validation_1": "देश का नाम केवल वर्णमाला मान देता है", "country_validation_2": "देश की अधिकतम लंबाई 30 वर्ण होनी चाहिए", "address": "पता", "address_required": "पता आवश्यक है", "postal_code": "डाक कोड", "postal_code_required": "डाक कोड की आवश्यकता है", "postal_code_validation_1": "अमान्य पोस्टल कोड प्रारूप", "postal_code_validation_2": "पोस्टल कोड अधिकतम 12 अंक होना चाहिए", "profile": "प्रोफ़ाइल", "static_data": "स्थैतिक डेटा", "handle_request": "अनुरोध का अनुरोध करें", "email_to_get_help": "इसके अलावा कई और बेहतरीन ट्यूनिंग और ट्वीक किए जाते हैं। यदि आप अपडेट के साथ किसी भी समस्या का सामना करते हैं तो कृपया <EMAIL> पर ईमेल करें", "note_options": "नोट: विकल्प का नाम टाइप करें और नया विकल्प जोड़ने के लिए एंटर दबाएं", "note_1": "1) यदि आप किसी भी मुद्दे का सामना करते हैं तो प्लगइन को निष्क्रिय करने और सक्रिय करने का प्रयास करें या हमसे संपर्क करें", "note_2": "2) यदि आप पुराने संस्करण को वापस करना चाहते हैं। कृपया स्थापित करें", "wp_rollback": "wp-rollback", "plugin": "प्लगइन", "keep_improving": "हम आपके सहयोग से सुधार करते रहेंगे! जी शुक्रिया!", "currency_setting": "मुद्रा सेटिंग", "module": "मॉड्यूल", "i_understand": "मै समझता/समझती हुँ", "version": "महत्वपूर्ण! प्रमुख संस्करण अद्यतन !! (V2.0.0)", "read_notice": "आगे बढ़ने से पहले कृपया इस नीचे दिए गए लॉग को पढ़ें:", "faced_issue": "सामना किए गए मुद्दे?", "if_use_older_version": "यदि आप इस संस्करण के साथ समस्याओं का सामना करते हैं और आप पुराने संस्करण के साथ जारी रखना चाहते हैं तो कृपया स्थापित करें और उपयोग करें", "check_video": "नए संस्करण के लिए चिकनी प्रवास के लिए निम्नलिखित वीडियो गाइड देखें: ", "kivicare_v2": "किवीकेयर अपग्रेड V2.0.0", "appointment_flow": "अपॉइंटमेंट फ्लो", "basic_details": "बेसिक डिटेल्स"}, "dashboard": {"dashboard": "डैशबोर्ड", "total_patients": "क<PERSON><PERSON> मरीज", "total_visited_patients": "कुल मरीजों का दौरा", "total_doctors": "कुल डॉक्टर", "total_clinic_doctors": "कुल चिकित्सक", "total_appointments": "आज की नियुक्ति सूची", "total_clinic_appointments": "कुल क्लिनिक नियुक्तियाँ", "latest_appointments": "नवीनतम नियुक्तियाँ", "reload": "पुनः लोड करें", "view_all": "सभी को देखें", "weekly_appointments": "साप्ताहिक नियुक्तियों", "weekly_total_appointments": "साप्ताहिक कुल नियुक्तियाँ", "today_appointment_list": "आज की नियुक्ति सूची", "total_revenue": "कुल मुनाफा", "total_clinic_revenue": "कुल क्लिनिक मुनाफा", "total_generated_revenue": "कुल उत्पन्न मुनाफा", "filter": "फ़िल्टर", "reset": "रीसेट", "total_today_appointments": "टोटल टुडे अपॉइंटमेंट्स", "total_service": "कुल सेवा", "patients": "मरी<PERSON><PERSON>ं", "medical_dashboard": "मेडिकल डैशबोर्ड"}, "doctor": {"doctor_name": "डॉक्टर का नाम", "doctor_specialization_required": "डॉक्टर की विशेषज्ञता आवश्यक है", "experience_year": "अनुभव (वर्ष में)", "address_details": "पता विवरण", "degree": "डिग्री", "degree_required": "डिग्री की आवश्यकता है", "degree_validation_1": "डिग्री केवल चरित्र मूल्य की अनुमति दें", "university": "विश्वविद्यालय", "university_required": "विश्वविद्यालय की आवश्यकता है", "university_validation": "विश्वविद्यालय केवल चरित्र मूल्य की अनुमति देते हैं ", "year": "डॉक्टर की विशेषज्ञता आवश्यक है0", "select_year": "डॉक्टर की विशेषज्ञता आवश्यक है1", "year_required": "डॉक्टर की विशेषज्ञता आवश्यक है2", "college_university": "डॉक्टर की विशेषज्ञता आवश्यक है3", "api_key": "डॉक्टर की विशेषज्ञता आवश्यक है4", "api_secret": "डॉक्टर की विशेषज्ञता आवश्यक है5", "api_secret_required": "डॉक्टर की विशेषज्ञता आवश्यक है6", "api_key_required": "डॉक्टर की विशेषज्ञता आवश्यक है7", "zoom_configuration_guide": "डॉक्टर की विशेषज्ञता आवश्यक है8", "zoom_step1": "चरण १: साइन अप करें या यहाँ साइन इन करें:", "zoom_step2": "चरण २: नेविगेशन बार में दाईं ओर विकसित बटन पर क्लिक / हॉवर पर क्लिक करें और बिल्ड ऐप पर क्लिक करें", "zoom_step3": "चरण ३: जेडब्ल्यूटी के लिए अपना ऐप प्रकार चुनें", "zoom_step4": "चरण ४: भरें अनिवार्य जानकारी और ऐप क्रेडेंशियल्स टैग में आप एपीआई कुंजी और एपीआई सीक्रेट देख सकते हैं।", "zoom_step5": "चरण ५: यहां कॉपी और पेस्ट एपीआई कुंजी और एपीआई रहस्य को सेव करें और सेव बटन पर क्लिक करें और आप जाने के लिए तैयार हैं।", "other_detail": "अन्य विवरण", "consultation_fees": "परामर्श शुल्क", "video_consultation_fees": "वीडियो परामर्श शुल्क", "doctor_fees_required": "डॉक्टर की फीस आवश्यक है", "zoom_market_place_portal": "ज़ूम मार्केट प्लेस पोर्टल", "create_app": "ऐप बनाएं", "doctors_list": "डॉक्टर्स लिस्ट", "other_details": "अन्य डिटेल्स", "extra_detail": "अतिरिक्त विस्तार", "add_doctor": "डॉक्टर जोड़ें", "edit_profile": "प्रोफ़ाइल संपादित करें", "basic_information": "बुनिया<PERSON>ी जानकारी", "basic_settings": "मूल सेटिंग्स", "type": "प्र<PERSON><PERSON>र", "type_required": "प्रकार की आवश्यकता है", "fees_type": "शुल्क प्रकार", "range": "रे<PERSON>ज", "fixed": "तय", "fees": "शुल्क", "fees_type_required": "शुल्क प्रकार की आवश्यकता है", "doc_fee_required": "डॉक्टर की फीस आवश्यक है", "doc_fee_validation_1": "डॉक्टर की फीस शून्य से अधिक होनी चाहिए", "doc_fee_validation_2": "डॉक्टर की फीस 0 से 1000000000000000000 के बीच होनी चाहिए", "doc_fee_validation_3": "डॉक्टर की फीस न्यूनतम फीस और अधिकतम शुल्क की आवश्यकता है", "doc_fee_validation_4": "डॉक्टर की फीस न्यूनतम फीस और अधिकतम फीस शून्य से अधिक होनी चाहिए", "doc_fee_validation_5": "डॉक्टर की फीस न्यूनतम मान फीस मूल्य से अधिक होना चाहिए।", "doc_fee_validation_6": "डॉक्टर की फीस अधिकतम फीस और न्यूनतम फीस 0 से 1000000000000000000 के बीच होनी चाहिए", "qualification_information": "योग्यता की जानकारी", "qualification_speciality_details": "योग्यता / विशेषता विवरण", "doctor_working_days_sessions": "डॉक्टर के कार्य दिवस और सत्र", "charge_n_doc_selection": "जोड़े गए शुल्क और डॉक्टर का चयन", "doc_field_customization": "व्यक्तिगत चिकित्सक क्षेत्र अनुकूलन"}, "patient": {"patient_name": "रोगी का नाम", "add_patient": "रोगी जोड़ें", "patients_lists": "मरीजों की सूची", "medical_report": "चिकित्सा रिपोर्ट", "add_medical_report": "चिकित्सा रिपोर्ट जोड़ें", "upload_report": "रिपोर्ट अपलोड करें"}, "clinic": {"clinic": "क्लिनिक", "receptionist": "रिसेप्शनिस्ट", "receptionists_list": "रिसेप्शनिस्ट सूची", "add_receptionist": "रिसेप्शनिस्ट जोड़ें", "clinic_name": "क्लिनिक का नाम", "clinic_info": "क्लिनिक की जानकारी", "clinic_profile": "क्लिनिक प्रोफाइल", "add_clinic": "क्लिनिक जोड़ें", "admin_profile": "एडमिन प्रोफाइल", "clinic_admin_detail": "क्लिनिक का विस्तार", "clinic_name_validation_1": "रिसेप्शनिस्ट0", "clinic_name_validation_2": "रिसेप्शनिस्ट1", "select_clinic": "रिसेप्शनिस्ट2", "speciality": "रिसेप्शनिस्ट3", "specialties": "रिसेप्शनिस्ट4", "specialities": "रिसेप्शनिस्ट5", "note_specialization": "रिसेप्शनिस्ट6", "clinic_specialities_required": "रिसेप्शनिस्ट7", "currency_prefix": "रिसेप्शनिस्ट8", "currency_postfix": "रिसेप्शनिस्ट9", "currency_decimals": "रिसेप्शनिस्ट सूची0", "profile_img": "रिसेप्शनिस्ट सूची1", "doctor_record_not_found": "रिसेप्शनिस्ट सूची2", "blood_group": "रिसेप्शनिस्ट सूची3", "select_blood_group": "रिसेप्शनिस्ट सूची4", "update_profile": "रिसेप्शनिस्ट सूची5"}, "appointments": {"appointment": "नियुक्ति", "appointments": "नियुक्ति", "description": "विवरण", "booked": "बुक किया गया", "cancelled": "रद्द", "arrived": "आगमन", "check_in": "में जाँच करें", "check_out": "बाहर शुरू करें", "start": "शामिल हों", "join": "डॉक्टर की आवश्यकता है", "doc_required": "नियुक्ति0", "visit_type_required": "नियुक्ति1", "appointment_date": "नियुक्ति2", "appointment_date_required": "नियुक्ति3", "select_status": "नियुक्ति4", "status_required": "नियुक्ति5", "available_slot": "नियुक्ति6", "session": "नियुक्ति7", "no_time_slots_found": "नियुक्ति8", "time_slot_required": "नियुक्ति9", "appointment_details": "विवरण0", "appointment_type": "विवरण1", "completed": "विवरण2", "appointment_time": "विवरण3", "appointment_time_required": "विवरण4", "book_appointment": "विवरण5", "today_appointment": "विवरण6", "tomorrow_appointment": "विवरण7", "appointment_booking": "विवरण8", "available_appointments_on": "विवरण9", "appointment_visit_type_required": "बुक किया गया0", "appointment_detail": "बुक किया गया1", "save_appointment": "बुक किया गया2", "appointment_list": "बुक किया गया3"}, "clinic_schedule": {"schedule": "अनुसूची", "holiday_of": "छुट्टी का", "module_type_required": "मॉड्यूल प्रकार की आवश्यकता है", "schedule_date": "अनुसूची की तारीख", "schedule_date_required": "अनुसूची की तारीख की आवश्यकता है", "holiday_list": "छुट्टी की सूची"}, "doctor_session": {"doc_sessions": "डॉक्टर सत्र", "session_doc_required": "सत्र चिकित्सक की आवश्यकता है", "doc_already_added": "चयनित चिकित्सक पहले से ही दूसरे सत्र में जोड़ा जाता है", "week_days": "सप्ताह के दिनों", "days_required": "दिनों की आवश्यकता है", "days_already_exist": "चयनित दिन पहले से ही दूसरे सत्र में मौजूद हैं", "morning_session": "सुबह का सत्र", "start_time_required": "प्रारंभ समय आवश्यक है", "start_time_smaller_then_end": "प्रारंभ समय छोटा होना चाहिए फिर अंत समय", "end_time_required": "समाप्ति समय आवश्यक है", "end_time_bigger_then_start": "सत्र चिकित्सक की आवश्यकता है0", "evening_session": "सत्र चिकित्सक की आवश्यकता है1", "start_time_smaller_then_first_session_end_time": "सत्र चिकित्सक की आवश्यकता है2", "set_session_for_doc": "सत्र चिकित्सक की आवश्यकता है3"}, "patient_encounter": {"encounters": "एनकाउंटर", "encounter_dashboard": "एनकाउंटर डैशबोर्ड", "is_required": "आवश्यक है", "note_prescription": "नोट: टाइप करें और नई प्रिस्क्रिप्शन बनाने के लिए एन्टर प्रेस करें", "frequency": "फ्रीक्वेंसी", "frequency_required": "फ्रीक्वेंसी की आवश्यकता है", "duration_Days": "अवधि (दिनों में)", "duration_required": "अवधि आवश्यक है", "instruction": "निर्देश", "duration": "अवधि", "no_prescription_found": "एनकाउंटर डैशबोर्ड0", "add_prescription": "एनकाउंटर डैशबोर्ड1", "encounter_date": "एनकाउंटर डैशबोर्ड2", "encounter_date_required": "एनकाउंटर डैशबोर्ड3", "encounter_module": "एनकाउंटर डैशबोर्ड4", "prescription": "एनकाउंटर डैशबोर्ड5", "encounter_details": "एनकाउंटर डैशबोर्ड6"}, "medical_records": {"problem_type": "समस्या का प्रकार", "problem_start_date_required": "समस्या प्रारंभ तिथि आवश्यक है", "problem_start_date": "समस्या प्रारंभ तिथि", "problem_end_date": "समस्या समाप्ति तिथि", "problem_outcome": "समस्या परिणाम", "medical_records": "चिकित्सा रिकॉर्ड", "add_medical_problems": "समस्याएँ जोड़ें"}, "reports": {"reports": "रिपोर्ट", "filter_by": "फ़िल्टर द्वारा", "clinic_revenue_overall": "क्लिनिक राजस्व (कुल मिलाकर)", "clinic_revenue_detail": "क्लिनिक राजस्व (विस्तार)", "clinic_doctor_revenue": "क्लिनिक डॉक्टर राजस्व", "prescription_module": "प्रिस्क्रिप्शन मॉड्यूल", "report_required": "रिपोर्ट की आवश्यकता है"}, "patient_front_widget": {"specialization": "विशेषज्ञता", "username_email": "उपयोगकर्ता नाम या ईमेल", "fill_form": "कृपया खाता बनाने के लिए इस फॉर्म को भरें।"}, "service": {"service_list": "सेवा सूची", "service_category": "सेवा श्रेणी", "service_category_required": "सेवा श्रेणी आवश्यक है", "note_category": "नोट: नई श्रेणी जोड़ने के लिए टाइप करें और एंटर करें", "service_name": "सेवा का नाम", "service_name_required": "सेवा का नाम आवश्यक है", "service_validation": "सेवा का नाम लंबाई २ से १०० के बीच होना चाहिए", "charges": "शुल्क] ", "service_charge": "सेवा मॉड्यूल से सेवा शुल्क। ", "service_charges_required": "सेवा शुल्क आवश्यक है", "service_charge_length": "सेवा श्रेणी0", "select_all": "सेवा श्रेणी1"}, "patient_bill": {"invoice_id": "चालान आईडी:", "created_at": "पर बनाया गया:", "payment_status": "भुगतान की स्थिति:", "paid": "अदा", "unpaid": "अवैतनिक", "patient_details": "रोगी विवरण", "amount_due": "दे<PERSON> राशि", "print": "प्रिंट", "service_required": "सेवा की आवश्यकता है", "price_required": "मूल्य आवश्यक है", "prize_greater_then_0": "पर बनाया गया:0", "prize_between_number": "पर बनाया गया:1", "quantity_required": "पर बनाया गया:2", "please_add_bill_items": "पर बनाया गया:3", "bill_total_required": "पर बनाया गया:4", "discount": "पर बनाया गया:5", "discount_amount": "पर बनाया गया:6", "discount_required": "पर बनाया गया:7", "discount_greater_then_0": "पर बनाया गया:8", "discount_less_then_total_bill_amount": "पर बनाया गया:9", "payable_amount": "भुगतान की स्थिति:0", "bill_title": "भुगतान की स्थिति:1", "bill_title_required": "भुगतान की स्थिति:2", "bill_items": "भुगतान की स्थिति:3", "grand_total": "भुगतान की स्थिति:4", "grand_total_required": "भुगतान की स्थिति:5", "print_bill": "भुगतान की स्थिति:6", "billing_records": "भुगतान की स्थिति:7", "add_bill": "भुगतान की स्थिति:8", "patient_required": "भुगतान की स्थिति:9", "encounter_close": "अदा0", "bill_details": "अदा1", "other_info": "अदा2", "patients_encounter_list": "अदा3", "bills": "अदा4", "payment_setting": "अदा5", "woocommerce_payment_gateway": "अदा6", "amount": "अदा7", "items": "अदा8", "notes": "अदा9", "invoice_n_payment": "अवैतनिक0", "currency": "अवैतनिक1"}, "settings": {"general": "सामान्य", "holidays": "छुट्टियाँ", "configurations": "विन्यास", "email_template": "ईमेल टेम्पलेट", "sms_template": "एसएमएस टेम्पलेट", "listings": "लिस्टिंग", "custom_field": "कस्टम क्षेत्र", "payment": "भुगतान", "new_setting": "ईमेल, चालान, मुद्रा, आदि जैसे विभिन्न सेटिंग्स के साथ नई परिष्कृत सेटिंग्स]", "pro_settings": "प्रो सेटिंग्स", "language_settings": "भाषा सेटिंग्स"}, "pro_setting": {"set_site_logo": "सेट साइट का लोगो", "set_language": "भाषा सेट करें", "set_theme_color": "थीम रंग सेट करें", "rtl_mode": "RTL मोड", "on": "पर", "twilo_sms_configration": "ट्विलो एसएमएस कन्फ़ेक्शन", "account_sid": "ACID SID", "auth_token": "AUTH TOKEN", "phone_number": "PHONE NUMBER (वैकल्पिक)", "twilo_sms_guide": "T<PERSON><PERSON> एसएमएस गाइड", "twilio_step_1": "भाषा सेट करें0", "twilo_sms_portal": "भाषा सेट करें1", "twilio_step_2": "भाषा सेट करें2", "get_console": "भाषा सेट करें3", "unique_sid": "भाषा सेट करें4", "twilio_step_3": "भाषा सेट करें5", "twilio_step_4": "भाषा सेट करें6", "head_on_console": "भाषा सेट करें7", "phone_msg_sid": "भाषा सेट करें8"}, "custom_field": {"label_name_required": "लेबल नाम की आवश्यकता है", "label_name_validation": "लेबल का नाम केवल अल्फ़ाबेटिक मान की अनुमति देता है", "where_it_look_like": "जहाँ पर यह दिखता है", "shows_in_doc_creation_form": "यह डॉक्टर निर्माण फॉर्म में दिखाता है", "shows_in_patient_encounter_dashboard": "यह रोगी मुठभेड़ डैशबोर्ड में दिखाता है", "shows_in_patient_creation_form": "यह रोगी निर्माण फॉर्म में दिखाता है", "filed_name": "दायर नाम:] अमान्य लेबल नाम", "invalid_label_name": "लेबल आवश्यक है", "label_required": "फ़ील्ड नाम का उपयोग पहले ही किया जा चुका है", "field_name_used": "इनपुट प्रकार", "input_type": "लेबल का नाम केवल अल्फ़ाबेटिक मान की अनुमति देता है0", "input_type_required": "लेबल का नाम केवल अल्फ़ाबेटिक मान की अनुमति देता है1", "placeholder": "लेबल का नाम केवल अल्फ़ाबेटिक मान की अनुमति देता है2", "options": "लेबल का नाम केवल अल्फ़ाबेटिक मान की अनुमति देता है3", "validation": "लेबल का नाम केवल अल्फ़ाबेटिक मान की अनुमति देता है4", "mandatory_field": "लेबल का नाम केवल अल्फ़ाबेटिक मान की अनुमति देता है5", "custom_field_list": "लेबल का नाम केवल अल्फ़ाबेटिक मान की अनुमति देता है6", "add_custom_field": "लेबल का नाम केवल अल्फ़ाबेटिक मान की अनुमति देता है7"}, "setup_wizard": {"previous": "पिछला", "add_session_details": "सत्र विवरण जोड़ें", "session_doctors": "सत्र डॉक्टरों", "days": "दिन", "no_sessions_found": "कोई सत्र नहीं मिला", "time_slot_minute": "समय स्लॉट (मिनट में)", "open_time": "खुला समय", "close_time": "बंद समय", "session_demo": "सत्र डेमो", "invalid_time_slot": "अमान्य समय स्लॉट मिला। अमान्य स्लॉट समय है", "doctor_list": "सत्र विवरण जोड़ें0", "kivicare_ehr": "सत्र विवरण जोड़ें1", "prev": "सत्र विवरण जोड़ें2"}, "notification": {"notification": "परीक्षण ईमेल भेजें", "test_sender_email_required": "परीक्षण प्रेषक ईमेल की आवश्यकता है", "test_content": "परीक्षण सामग्री", "test_content_required": "परीक्षण सामग्री की आवश्यकता है", "email_notification": "ईमेल अधिसूचना सक्षम / अक्षम करें।", "forbidden_403": "४०३ | मना किया हुआ"}, "static_data": {"listing_data": "लिस्टिंग डेटा", "terms_n_condition": "नियम और शर्त", "version_update": "संस्करण अद्यतन (V2.0.0)", "new_filters_n_view": "नई बढ़ाया फिल्टर और देखें", "booking_widget_updated": "बुकिंग विजेट अद्यतन किया गया है", "visit_type_replaced": "विजिटिंग प्रकार सेवाओं के साथ बदल दिया गया है (कृपया सेवा टैब की जांच करें)", "appointment_flow_update": "नियुक्ति की जांच- में और चेक-आउट प्रवाह अद्यतन"}, "widgets": {"doc_not_found": "डॉक्टर नहीं मिला", "zoom_config": "ज़ूम कॉन्फ़िगरेशन", "terms_condition": "नियम और शर्त", "date_required": "तिथि आवश्यक है", "current_pwd": "करंट पासवर्ड", "current_pwd_required": "करंट पासवर्ड की आवश्यकता है", "new_pwd": "नया पासवर्ड", "appointment_info": "अपॉइंटमेंट की जानकारी", "available_slots": "उपलब्ध स्लॉट", "service_detail": "सर्विस डिटेल", "no_service_detail_found": "ज़ूम कॉन्फ़िगरेशन0", "book_now": "ज़ूम कॉन्फ़िगरेशन1", "registration_success": "ज़ूम कॉन्फ़िगरेशन2", "more_detail": "ज़ूम कॉन्फ़िगरेशन3", "username_email_required": "ज़ूम कॉन्फ़िगरेशन4", "new_pwd_required": "ज़ूम कॉन्फ़िगरेशन5", "confirm_pwd": "ज़ूम कॉन्फ़िगरेशन6", "confirm_pwd_required": "ज़ूम कॉन्फ़िगरेशन7", "pwd_validation": "ज़ूम कॉन्फ़िगरेशन8", "home": "ज़ूम कॉन्फ़िगरेशन9", "change_pwd": "नियम और शर्त0", "logging_out": "नियम और शर्त1", "total_visits": "नियम और शर्त2", "upcoming_visits": "नियम और शर्त3", "example_component": "नियम और शर्त4", "email_to_get_help_1": "नियम और शर्त5", "email_to_get_help_2": "नियम और शर्त6", "email_to_get_help_3": "नियम और शर्त7", "feedback_note": "नियम और शर्त8", "imp_version_update": "नियम और शर्त9", "replace_appointment": "तिथि आवश्यक है0", "option_as": "तिथि आवश्यक है1", "service_type": "तिथि आवश्यक है2", "add_charges": "तिथि आवश्यक है3", "manage_doctor": "तिथि आवश्यक है4", "send_test_email": "तिथि आवश्यक है5"}}