<div class="flex items-center">
    <div class="flex items-center text-gray-400" id="categoryTab">
        <div
            class="flex items-center justify-center w-8 h-8 rounded-full transition-colors duration-300 text-gray-400 bg-gray-50">
            <svg class="flex items-center justify-center w-4 h-4 rounded-full transition-colors duration-300 "xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 24 24">
                <rect x="4" y="4" width="6" height="6" rx="1" fill="none"  stroke="currentColor" stroke-width="2"/>
                <rect x="14" y="4" width="6" height="6" rx="1" fill="none" stroke="currentColor"  stroke-width="2"/>
                <rect x="4" y="14" width="6" height="6" rx="1" fill="none" stroke="currentColor"  stroke-width="2"/>
                <rect x="14" y="14" width="6" height="6" rx="1" fill="none"  stroke="currentColor" stroke-width="2"/>
            </svg>
        </div>
        <span class="hidden md:inline-block ml-2 text-sm font-medium transition-colors duration-300 text-gray-400">Category</span>
    </div>
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
        class="lucide lucide-chevron-right w-4 h-4 mx-3 text-gray-300">
        <path d="m9 18 6-6-6-6"></path>
    </svg>
</div>