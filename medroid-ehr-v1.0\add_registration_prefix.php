<?php
/**
 * Add Registration Prefix to Doctors
 * 
 * This script adds the registration_prefix field to all doctors' basic_data
 * It must be accessed directly through a web browser while logged in as admin
 */

// Load WordPress
require_once(dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-load.php');

// Security check - only allow admins
if (!current_user_can('manage_options')) {
    die('You do not have sufficient permissions to access this page.');
}

global $wpdb;

// Start HTML output
?>
<!DOCTYPE html>
<html>
<head>
    <title>Add Registration Prefix</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 800px; margin: 0 auto; }
        h1 { color: #2c3e50; border-bottom: 1px solid #eee; padding-bottom: 10px; }
        .card { background: #fff; border: 1px solid #ddd; padding: 20px; margin-bottom: 20px; border-radius: 4px; }
        .success { color: green; }
        .error { color: red; }
        pre { background: #f5f5f5; padding: 10px; overflow: auto; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Add Registration Prefix to Doctors</h1>
<?php

// Function to safely get JSON value with fallback
function safe_json_value($json, $path, $default = '') {
    if (empty($json)) return $default;
    
    try {
        $data = json_decode($json, true);
        if (!is_array($data)) return $default;
        
        return isset($data[$path]) ? $data[$path] : $default;
    } catch (Exception $e) {
        return $default;
    }
}

// Get doctors
$doctor_role = 'kiviCare_doctor';
$doctor_query = "
    SELECT u.ID, u.display_name, um.umeta_id, um.meta_value
    FROM {$wpdb->users} u 
    JOIN {$wpdb->usermeta} um_role ON u.ID = um_role.user_id 
    LEFT JOIN {$wpdb->usermeta} um ON u.ID = um.user_id AND um.meta_key = 'basic_data'
    WHERE um_role.meta_key = '{$wpdb->prefix}capabilities' 
    AND um_role.meta_value LIKE '%{$doctor_role}%'
";

$doctors = $wpdb->get_results($doctor_query);

if (empty($doctors)) {
    echo '<div class="card error"><p>No doctors found in the database.</p></div>';
    exit;
}

echo '<div class="card">';
echo '<h2>Found ' . count($doctors) . ' doctors</h2>';

$updated = 0;
$errors = 0;
$results = [];

foreach ($doctors as $doctor) {
    $doctor_id = $doctor->ID;
    $doctor_name = $doctor->display_name;
    $meta_value = $doctor->meta_value;
    
    // Create result entry
    $result = [
        'id' => $doctor_id,
        'name' => $doctor_name,
        'status' => '',
        'message' => '',
        'before' => $meta_value,
        'after' => ''
    ];
    
    try {
        if (!empty($meta_value)) {
            // Parse existing JSON
            $data = json_decode($meta_value, true);
            
            if (is_array($data)) {
                // Check if registration_prefix already exists
                if (!isset($data['registration_prefix'])) {
                    // Add registration_prefix field
                    $data['registration_prefix'] = '';
                    
                    // Update meta value
                    $new_meta_value = json_encode($data, JSON_UNESCAPED_UNICODE);
                    update_user_meta($doctor_id, 'basic_data', $new_meta_value);
                    
                    $result['status'] = 'success';
                    $result['message'] = 'Added registration_prefix field';
                    $result['after'] = $new_meta_value;
                    $updated++;
                } else {
                    $result['status'] = 'info';
                    $result['message'] = 'registration_prefix field already exists';
                    $result['after'] = $meta_value;
                }
            } else {
                // Invalid JSON, create new structure
                $data = [
                    'mobile_number' => '',
                    'gender' => '',
                    'dob' => '',
                    'address' => '',
                    'city' => '',
                    'state' => '',
                    'country' => '',
                    'postal_code' => '',
                    'gmc_no' => '',
                    'registration_prefix' => ''
                ];
                
                $new_meta_value = json_encode($data, JSON_UNESCAPED_UNICODE);
                update_user_meta($doctor_id, 'basic_data', $new_meta_value);
                
                $result['status'] = 'success';
                $result['message'] = 'Created new basic_data with registration_prefix';
                $result['after'] = $new_meta_value;
                $updated++;
            }
        } else {
            // No basic_data exists, create new one
            $data = [
                'mobile_number' => '',
                'gender' => '',
                'dob' => '',
                'address' => '',
                'city' => '',
                'state' => '',
                'country' => '',
                'postal_code' => '',
                'gmc_no' => '',
                'registration_prefix' => ''
            ];
            
            $new_meta_value = json_encode($data, JSON_UNESCAPED_UNICODE);
            update_user_meta($doctor_id, 'basic_data', $new_meta_value);
            
            $result['status'] = 'success';
            $result['message'] = 'Created new basic_data with registration_prefix';
            $result['after'] = $new_meta_value;
            $updated++;
        }
    } catch (Exception $e) {
        $result['status'] = 'error';
        $result['message'] = 'Error: ' . $e->getMessage();
        $errors++;
    }
    
    $results[] = $result;
}

echo '<p>Updated ' . $updated . ' doctors. Encountered ' . $errors . ' errors.</p>';
echo '</div>';

// Show results table
if (!empty($results)) {
    echo '<div class="card">';
    echo '<h2>Results</h2>';
    echo '<table>';
    echo '<tr><th>ID</th><th>Name</th><th>Status</th><th>Message</th></tr>';
    
    foreach ($results as $result) {
        $status_class = ($result['status'] === 'success') ? 'success' : (($result['status'] === 'error') ? 'error' : '');
        
        echo '<tr>';
        echo '<td>' . esc_html($result['id']) . '</td>';
        echo '<td>' . esc_html($result['name']) . '</td>';
        echo '<td class="' . $status_class . '">' . esc_html($result['status']) . '</td>';
        echo '<td>' . esc_html($result['message']) . '</td>';
        echo '</tr>';
    }
    
    echo '</table>';
    echo '</div>';
}

// Show detailed first errors if any
$detailed_errors = array_filter($results, function($r) { return $r['status'] === 'error'; });
if (!empty($detailed_errors)) {
    echo '<div class="card error">';
    echo '<h2>Error Details</h2>';
    
    foreach (array_slice($detailed_errors, 0, 3) as $error) {
        echo '<h3>Doctor ID: ' . esc_html($error['id']) . '</h3>';
        echo '<p>' . esc_html($error['message']) . '</p>';
    }
    
    echo '</div>';
}

// Show SQL to fix manually
echo '<div class="card">';
echo '<h2>SQL Query to Fix Manually</h2>';
echo '<p>If the automatic fix didn\'t work, you can run this SQL query in your database:</p>';

$table_prefix = $wpdb->prefix;
$sql = "UPDATE {$table_prefix}usermeta um
JOIN {$table_prefix}usermeta um_role ON um.user_id = um_role.user_id
SET um.meta_value = JSON_SET(
    IF(
        JSON_VALID(um.meta_value), 
        um.meta_value, 
        JSON_OBJECT('registration_prefix', '', 'mobile_number', '', 'gender', '')
    ),
    '$.registration_prefix', 
    IF(
        JSON_EXTRACT(um.meta_value, '$.registration_prefix') IS NULL,
        '',
        JSON_UNQUOTE(JSON_EXTRACT(um.meta_value, '$.registration_prefix'))
    )
)
WHERE um.meta_key = 'basic_data'
AND um_role.meta_key = '{$table_prefix}capabilities'
AND um_role.meta_value LIKE '%kiviCare_doctor%';";

echo '<pre>' . esc_html($sql) . '</pre>';
echo '</div>';

// Security warning
echo '<div class="card error">';
echo '<h2>Security Warning</h2>';
echo '<p><strong>Important:</strong> Please delete this file after use to prevent security risks.</p>';
echo '</div>';

?>
    </div>
</body>
</html>
<?php
// End of file