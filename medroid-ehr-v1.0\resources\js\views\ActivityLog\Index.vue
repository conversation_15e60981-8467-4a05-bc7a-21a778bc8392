<template>
  <div class="px-6 py-8 max-w-full">
    <!-- Header Section -->
    <div class="flex items-center justify-between mb-8">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Activity Logs</h1>
        <p class="mt-1 text-sm text-gray-500">Track and monitor all system activities for compliance and security</p>
      </div>
      <div class="flex space-x-2">
        <button @click="resetFilters"
          class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-gray-500 hover:bg-gray-100 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24"
            stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          Reset
        </button>
      </div>
    </div>

    <!-- Filter Section (Redesigned to be more compact) -->
    <div class="bg-white rounded-xl shadow-sm p-5 mb-6">
      <!-- Filter Form -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-5">
        <!-- Column 1: Activity & User Type -->
        <div class="space-y-4">
          <!-- Activity Type Filter -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Activity Type</label>
            <select v-model="filters.activity_type"
              class="block w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 shadow-sm">
              <option value="">All Types</option>
              <option v-for="(label, type) in activityTypes" :key="type" :value="type">{{ label }}</option>
            </select>
          </div>

          <!-- User Type Filter -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">User Type</label>
            <select v-model="filters.user_type"
              class="block w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 shadow-sm">
              <option value="">All User Types</option>
              <option value="administrator">Administrator</option>
              <option value="kivicare_clinic_admin">Clinic Admin</option>
              <option value="kivicare_doctor">Doctor</option>
              <option value="kivicare_receptionist">Admin Staff</option>
              <option value="kivicare_patient">Patient</option>
            </select>
          </div>
        </div>

        <!-- Column 2: Date Range -->
        <div class="space-y-4">
          <!-- Date From -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">From Date</label>
            <vc-date-picker v-model="filters.date_from" :max-date="new Date()" class="w-full">
              <template v-slot="{ inputValue, inputEvents }">
                <div class="relative">
                  <input
                    class="block w-full pl-10 pr-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 shadow-sm"
                    :value="inputValue" v-on="inputEvents" placeholder="Select start date" />
                  <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none"
                      viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                </div>
              </template>
            </vc-date-picker>
          </div>

          <!-- Date To -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">To Date</label>
            <vc-date-picker v-model="filters.date_to" :max-date="new Date()" class="w-full">
              <template v-slot="{ inputValue, inputEvents }">
                <div class="relative">
                  <input
                    class="block w-full pl-10 pr-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 shadow-sm"
                    :value="inputValue" v-on="inputEvents" placeholder="Select end date" />
                  <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none"
                      viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                </div>
              </template>
            </vc-date-picker>
          </div>
        </div>

        <!-- Column 3: Search Fields -->
        <div class="space-y-4">
          <!-- User Search -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">User Name</label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24"
                  stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
              <input type="text" v-model="filters.user_name" placeholder="Enter user name"
                class="block w-full pl-10 pr-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 shadow-sm" />
            </div>
          </div>

          <!-- Clinic Filter -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Clinic</label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24"
                  stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <input type="text" v-model="filters.clinic_name" placeholder="Enter clinic name"
                class="block w-full pl-10 pr-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 shadow-sm" />
            </div>
          </div>
        </div>
      </div>

      <!-- Action Buttons - Centered at the bottom with spacing -->
      <div class="flex justify-center mt-6 space-x-4">
        <button @click="getActivityLogs"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24"
            stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
          </svg>
          Apply Filters
        </button>
        <button @click="resetFilters"
          class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24"
            stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          Reset Filters
        </button>
      </div>
    </div>

    <!-- Activity Log Table -->
    <div class="bg-white shadow-sm rounded-xl overflow-hidden">
      <!-- Loading State -->
      <div v-if="isLoading" class="flex justify-center items-center py-40">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        <span class="sr-only">Loading</span>
      </div>

      <!-- Empty State -->
      <div v-else-if="!activityData.logs || activityData.logs.length === 0" class="py-20 text-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-gray-300" fill="none" viewBox="0 0 24 24"
          stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1"
            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <h3 class="mt-4 text-lg font-medium text-gray-900">No activity logs found</h3>
        <p class="mt-1 text-sm text-gray-500">Try adjusting your filters or come back later.</p>
      </div>

      <!-- Activity Table -->
      <div v-else>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  ID
                </th>
                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  User
                </th>
                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Role
                </th>
                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Activity Type
                </th>
                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Description
                </th>
                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  IP Address
                </th>
                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date/Time
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="log in activityData.logs" :key="log.id" class="hover:bg-gray-50">
                <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                  {{ log.id }}
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                  {{ log.user_name }}
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                    {{getUserRoleLabel(log.user_type) }}
                  </span>
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                  <span v-if="activityTypes[log.activity_type]"
                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                    :class="getActivityTypeClass(log.activity_type)">
                    {{ activityTypes[log.activity_type] }}
                  </span>
                  <span v-else
                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                    {{ log.activity_type }}
                  </span>
                </td>
                <td class="px-4 py-3 text-sm text-gray-500 max-w-md truncate">
                  {{ log.activity_description }}
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                  <span class="font-mono">{{ log.ip_address || 'N/A' }}</span>
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(log.created_at) }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700">
                Showing <span class="font-medium">{{ activityData.logs ? activityData.logs.length : 0 }}</span> of <span
                  class="font-medium">{{ activityData.total || 0 }}</span> entries
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <a href="#" @click.prevent="goToPage(currentPage - 1)"
                  :class="[currentPage === 1 ? 'cursor-not-allowed opacity-50' : 'hover:bg-gray-50']"
                  class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500">
                  <span class="sr-only">Previous</span>
                  <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"
                    aria-hidden="true">
                    <path fill-rule="evenodd"
                      d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                      clip-rule="evenodd" />
                  </svg>
                </a>

                <a href="#" v-for="page in paginationPages" :key="page" @click.prevent="goToPage(page)" :class="[
                  currentPage === page ? 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600' : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50',
                  'relative inline-flex items-center px-4 py-2 border text-sm font-medium'
                ]">
                  {{ page }}
                </a>

                <a href="#" @click.prevent="goToPage(currentPage + 1)"
                  :class="[currentPage === activityData.total_pages ? 'cursor-not-allowed opacity-50' : 'hover:bg-gray-50']"
                  class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500">
                  <span class="sr-only">Next</span>
                  <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"
                    aria-hidden="true">
                    <path fill-rule="evenodd"
                      d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                      clip-rule="evenodd" />
                  </svg>
                </a>
              </nav>
            </div>
          </div>

          <!-- Mobile pagination -->
          <div class="flex items-center justify-between w-full sm:hidden">
            <a href="#" @click.prevent="goToPage(currentPage - 1)"
              :class="[currentPage === 1 ? 'cursor-not-allowed opacity-50' : '']"
              class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              Previous
            </a>
            <div class="text-sm text-gray-500">
              Page {{ currentPage }} of {{ activityData.total_pages || 1 }}
            </div>
            <a href="#" @click.prevent="goToPage(currentPage + 1)"
              :class="[currentPage === activityData.total_pages ? 'cursor-not-allowed opacity-50' : '']"
              class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              Next
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment';
import { mapGetters } from 'vuex';
import { post, get } from "../../config/request";

export default {
  name: 'ActivityLogs',
  components: {
  },
  data() {
    return {
      isLoading: true,
      activityData: {
        logs: [],
        total: 0,
        per_page: 10,
        current_page: 1,
        total_pages: 1
      },
      currentPage: 1,
      filters: {
        activity_type: '',
        date_from: '',
        date_to: '',
        user_type: '',
        clinic_name: '',
        user_name: ''
      },
      activityTypes: {
        '1': 'Login', // Numeric keys mapped to proper names
        '215': 'Logout',
        'login': 'Login',
        'logout': 'Logout',
        'appointment_created': 'Appointment Created',
        'appointment_updated': 'Appointment Updated',
        'appointment_deleted': 'Appointment Deleted',
        'appointment_status_changed': 'Appointment Status Changed',
        'encounter_created': 'Encounter Created',
        'encounter_updated': 'Encounter Updated',
        'file_uploaded': 'File Uploaded',
        'file_downloaded': 'File Downloaded',
        'file_deleted': 'File Deleted',
        'prescription_created': 'Prescription Created',
        'prescription_updated': 'Prescription Updated',
        'prescription_sent': 'Prescription Sent',
        'prescription_downloaded': 'Prescription Downloaded',
        'patient_created': 'Patient Created',
        'patient_updated': 'Patient Updated',
        'patient_deleted': 'Patient Deleted',
        'summary_generated': 'Summary Generated',
        'report_generated': 'Report Generated',
        'data_shared': 'Data Shared',
        'bill_generated': 'Bill Generated',
        'bill_updated': 'Bill Updated',
        'payment_completed': 'Payment Completed',
        'communication_sent': 'Communication Sent',
        'profile_updated': 'Profile Updated',
        'mobile_upload_created': 'Mobile Upload Created',
        'ai_transcription_completed': 'AI Transcription Completed',
        'ai_analysis_completed': 'AI Analysis Completed',
        'service_created': 'Service Created',
        'service_updated': 'Service Updated',
        'service_deleted': 'Service Deleted',
        'session_created': 'Session Created',
        'session_updated': 'Session Updated',
        'session_deleted': 'Session Deleted',
        'absence_created': 'Absence Created',
        'absence_updated': 'Absence Updated',
        'absence_deleted': 'Absence Deleted',
        'consultation_shared': 'Consultation Shared',
        'document_prescription': 'Prescription Document',
        'shared_document': 'Shared Document',
        'update_patient': 'Update Patient',
        'create_consultation': 'Create Consultation',
        'delete_patient': 'Delete Patient',
      }
    };
  },
  computed: {
    ...mapGetters({
      getRoleWisePermission: 'getRoleWisePermission'
    }),
    paginationPages() {
      let pages = [];
      if (this.activityData.total_pages <= 5) {
        for (let i = 1; i <= this.activityData.total_pages; i++) {
          pages.push(i);
        }
      } else {
        if (this.currentPage <= 3) {
          for (let i = 1; i <= 5; i++) {
            pages.push(i);
          }
        } else if (this.currentPage >= this.activityData.total_pages - 2) {
          for (let i = this.activityData.total_pages - 4; i <= this.activityData.total_pages; i++) {
            pages.push(i);
          }
        } else {
          for (let i = this.currentPage - 2; i <= this.currentPage + 2; i++) {
            pages.push(i);
          }
        }
      }
      return pages;
    }
  },
  created() {
    // Get initial activity logs
    this.getActivityLogs();

    // We won't fetch activity types since we have them hardcoded above
    // this.getActivityTypes();
  },
  methods: {
    // Fetch activity logs directly
    getActivityLogs() {
  this.isLoading = true;

  // Create request parameters with pagination and filters
  const params = {
    page: this.currentPage,
    per_page: this.activityData.per_page,
    filters: {}
  };
  
  // Only add defined filters
  if (this.filters.activity_type && this.filters.activity_type !== '') {
    params.filters.activity_type = this.filters.activity_type;
  }
  
  if (this.filters.user_type && this.filters.user_type !== '') {
    params.filters.user_type = this.filters.user_type;
  }
  
  if (this.filters.clinic_name && this.filters.clinic_name.trim() !== '') {
    params.filters.clinic_name = this.filters.clinic_name.trim();
  }
  
  if (this.filters.user_name && this.filters.user_name.trim() !== '') {
    params.filters.user_name = this.filters.user_name.trim();
  }
  
  if (this.filters.date_from) {
    params.filters.date_from = moment(this.filters.date_from).format('YYYY-MM-DD');
  }
  
  if (this.filters.date_to) {
    params.filters.date_to = moment(this.filters.date_to).format('YYYY-MM-DD');
  }
  
  // Make the API call with params
  get('activity_logs', params)
    .then(response => {
      if (response.data && response.data.status) {
        console.log('Activity logs response:', response.data);
        
        // Get data directly from the response
        const apiData = response.data.data || {};
        
        // Update activity data from the API response
        this.activityData = {
          logs: apiData.logs || [],
          total: parseInt(apiData.total) || 0,
          per_page: parseInt(apiData.per_page) || 10,
          current_page: parseInt(apiData.current_page) || 1,
          total_pages: parseInt(apiData.total_pages) || 1
        };
        
        console.log('Pagination information:', {
          total: this.activityData.total,
          per_page: this.activityData.per_page,
          current_page: this.activityData.current_page,
          total_pages: this.activityData.total_pages
        });
        
        // If logs are empty, show a notification
        if (!this.activityData.logs || this.activityData.logs.length === 0) {
          console.log('No activity logs found with the current filters');
        }
      } else {
        this.showError((response.data && response.data.message) || 'Failed to fetch activity logs');
      }
    })
    .catch(error => {
      console.error('Error fetching activity logs:', error);
      this.showError('Error fetching activity logs. Check console for details.');
    })
    .finally(() => {
      this.isLoading = false;
    });
},

    // Get activity types for filtering - not used now since we hardcoded the types
    getActivityTypes() {
      // Keeping this for reference, but not using it
      // Instead using hardcoded values to avoid API issues
      /*
      const baseUrl = window.ajaxurl + '?action=ajax_post&route_name=activity_types';
      axios.get(baseUrl)
        .then(response => {
          if (response.data.status) {
            this.activityTypes = response.data.data;
          }
        })
        .catch(error => {
          console.error('Error fetching activity types:', error);
        });
      */
    },

    // Reset all filters
    resetFilters() {
      this.filters = {
        activity_type: '',
        date_from: '',
        date_to: '',
        user_type: '',
        clinic_name: '',
        user_name: ''
      };
      this.currentPage = 1;
      this.getActivityLogs();
    },

    // Format date for display
    formatDate(dateString) {
      return moment(dateString).format('MMMM D, YYYY h:mm A');
    },

    // Get appropriate CSS class based on activity type
    getActivityTypeClass(activityType) {
      const typeColorMap = {
        // Authentication activities
        '1': 'bg-blue-100 text-blue-800', // Numeric ID mapping
        '215': 'bg-gray-100 text-gray-800', // Numeric ID mapping
        'login': 'bg-blue-100 text-blue-800',
        'logout': 'bg-gray-100 text-gray-800',

        // Appointment activities
        'appointment_created': 'bg-green-100 text-green-800',
        'appointment_updated': 'bg-yellow-100 text-yellow-800',
        'appointment_deleted': 'bg-red-100 text-red-800',
        'appointment_status_changed': 'bg-yellow-100 text-yellow-800',

        // File activities
        'file_uploaded': 'bg-teal-100 text-teal-800',
        'file_downloaded': 'bg-teal-100 text-teal-800',
        'file_deleted': 'bg-red-100 text-red-800',

        // Document/record activities
        'prescription_created': 'bg-indigo-100 text-indigo-800',
        'prescription_updated': 'bg-indigo-100 text-indigo-800',
        'prescription_sent': 'bg-purple-100 text-purple-800',
        'prescription_downloaded': 'bg-purple-100 text-purple-800',
        'summary_generated': 'bg-violet-100 text-violet-800',
        'report_generated': 'bg-violet-100 text-violet-800',

        // Patient activities
        'patient_created': 'bg-emerald-100 text-emerald-800',
        'patient_updated': 'bg-emerald-100 text-emerald-800',
        'patient_deleted': 'bg-red-100 text-red-800',

        // Encounter activities
        'encounter_created': 'bg-blue-100 text-blue-800',
        'encounter_updated': 'bg-yellow-100 text-yellow-800',
        'consultation_shared': 'bg-pink-100 text-pink-800',

        // Sharing/communication activities
        'data_shared': 'bg-pink-100 text-pink-800',
        'communication_sent': 'bg-pink-100 text-pink-800',

        // Payment activities
        'bill_generated': 'bg-amber-100 text-amber-800',
        'bill_updated': 'bg-amber-100 text-amber-800',
        'payment_completed': 'bg-lime-100 text-lime-800',

        // AI/ML activities
        'mobile_upload_created': 'bg-cyan-100 text-cyan-800',
        'ai_transcription_completed': 'bg-fuchsia-100 text-fuchsia-800',
        'ai_analysis_completed': 'bg-fuchsia-100 text-fuchsia-800',

        // Service management
        'service_created': 'bg-green-100 text-green-800',
        'service_updated': 'bg-yellow-100 text-yellow-800',
        'service_deleted': 'bg-red-100 text-red-800',

        // Session management
        'session_created': 'bg-green-100 text-green-800',
        'session_updated': 'bg-yellow-100 text-yellow-800',
        'session_deleted': 'bg-red-100 text-red-800',
        'absence_created': 'bg-orange-100 text-orange-800',
        'absence_updated': 'bg-orange-100 text-orange-800',
        'absence_deleted': 'bg-red-100 text-red-800',

        // Default
        'default': 'bg-gray-100 text-gray-800'
      };

      return typeColorMap[activityType] || typeColorMap.default;
    },

    // Pagination navigation
    goToPage(page) {
      if (page < 1 || page > this.activityData.total_pages) {
        return;
      }
      this.currentPage = page;
      this.getActivityLogs();
    },

    // Show error message
    showError(message) {
      // Check if swal is available as a method or a constructor
      if (typeof this.$swal === 'function') {
        // Use directly if it's a function
        this.$swal({
          title: 'Error',
          text: message,
          icon: 'error',
          confirmButtonText: 'OK'
        });
      } else {
        // Use console.error as fallback
        console.error('Error:', message);
        // Display an alert as fallback
        alert('Error: ' + message);
      }
    }
  }
};
</script>