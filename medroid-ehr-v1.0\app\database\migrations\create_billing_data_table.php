<?php
/**
 * Migration: Create billing data table
 * File: create_billing_data_table.php
 */
if (!defined('ABSPATH')) {
    exit;
}

class CreateBillingDataTable {
    /**
     * Run the migration - creates the kc_billing_data table
     */
    public function up() {
        global $wpdb;
        error_log("[Migration] Creating kc_billing_data table");
        
        $table = $wpdb->prefix . 'kc_billing_data';
        $charset_collate = $wpdb->get_charset_collate();
        
        // Check if table already exists
        $table_exists = $wpdb->get_var($wpdb->prepare("SHOW TABLES LIKE %s", $table));
        if ($table_exists) {
            error_log("[Migration] Table kc_billing_data already exists, skipping creation.");
            return true;
        }
        
        // Create the table
        $sql = "CREATE TABLE {$table} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            billing_id bigint(20) NOT NULL,
            patient_id bigint(20) NOT NULL,
            doctor_id bigint(20) NOT NULL,
            clinic_id bigint(20) NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        
        if ($wpdb->last_error) {
            error_log("[Migration] Error creating table: " . $wpdb->last_error);
            return false;
        }
        
        error_log("[Migration] Table kc_billing_data created successfully.");
        return true;
    }
    
    /**
     * Reverse the migration - drops the kc_billing_data table
     */
    public function down() {
        global $wpdb;
        error_log("[Migration] Dropping kc_billing_data table");
        
        $table = $wpdb->prefix . 'kc_billing_data';
        
        // Check if table exists
        $table_exists = $wpdb->get_var($wpdb->prepare("SHOW TABLES LIKE %s", $table));
        if (!$table_exists) {
            error_log("[Migration] Table kc_billing_data doesn't exist, skipping drop.");
            return true;
        }
        
        // Drop the table
        $sql = "DROP TABLE IF EXISTS {$table}";
        $wpdb->query($sql);
        
        if ($wpdb->last_error) {
            error_log("[Migration] Error dropping table: " . $wpdb->last_error);
            return false;
        }
        
        error_log("[Migration] Table kc_billing_data dropped successfully.");
        return true;
    }
}