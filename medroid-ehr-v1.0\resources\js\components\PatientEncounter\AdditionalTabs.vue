<!-- 
<template>
  <div class="tab-manager">
    <b-alert
      v-if="errorMessage"
      variant="danger"
      dismissible
      class="mb-3"
      @dismissed="errorMessage = ''"
    >
      {{ errorMessage }}
    </b-alert>

    <div class="tab-buttons mb-3">
      <b-button
        v-for="tab in availableTabs"
        :key="tab.type"
        variant="outline-primary"
        class="mr-2 mb-2"
        :disabled="loading || hasTabType(tab.type)"
        @click="addNewTab(tab)"
      >
        <i :class="tab.icon" class="mr-2"></i>
        {{ tab.title }}
      </b-button>
    </div>

    <div v-if="activeTabs.length === 0" class="text-center py-4">
      <i class="fas fa-plus-circle fa-3x text-muted mb-2"></i>
      <p class="text-muted">Click a button above to add a section</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Tab<PERSON>anager',
  
  props: {
    encounterId: {
      type: [String, Number],
      required: true
    }
  },

  data() {
    return {
      loading: false,
      errorMessage: '',
      activeTabs: [],
      availableTabs: [
        { type: 'problems', title: 'Problems', icon: 'fas fa-exclamation-circle' },
        { type: 'observations', title: 'Observations', icon: 'fas fa-eye' },
        { type: 'examination', title: 'Examination', icon: 'fas fa-stethoscope' },
        { type: 'allergies', title: 'Allergies', icon: 'fas fa-allergies' },
        { type: 'family_history', title: 'Family History', icon: 'fas fa-users' }
      ]
    };
  },

  methods: {
    hasTabType(type) {
      return this.activeTabs.some(tab => tab.type === type);
    },

    addNewTab(tabInfo) {
      const newTab = {
        id: `${tabInfo.type}-${Date.now()}`,
        type: tabInfo.type,
        title: tabInfo.title,
        content: '',
        allergyType: 'food',
        severity: 'mild',
        examType: ''
      };
      
      this.activeTabs.push(newTab);
      this.$emit('section-added', newTab);
    },

    removeTab(tabId) {
      const index = this.activeTabs.findIndex(t => t.id === tabId);
      if (index !== -1) {
        this.activeTabs.splice(index, 1);
        this.$emit('tab-removed', tabId);
      }
    }
  }
};
</script>

<style scoped>
.tab-manager {
  position: relative;
}
.tab-buttons .btn {
  transition: all 0.2s;
}
.tab-buttons .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style> -->
<template>
  <div class="tab-manager">
    <b-alert
      v-if="errorMessage"
      variant="danger"
      dismissible
      class="mb-3"
      @dismissed="errorMessage = ''"
    >
      {{ errorMessage }}
    </b-alert>

    <!-- Horizontal Tabs -->
    <div class="horizontal-tabs">
      <!-- Tab Navigation -->

      <!-- Horizontal Options -->
      <b-row class="tab-options mb-3">
        <b-col 
          v-for="tab in availableTabs" 
          :key="tab.type"
          cols="12" 
          sm="6" 
          md="4" 
          lg="3" 
          xl="2"
          class="mb-2"
        >
          <b-button
            block
            variant="outline-primary"
            class="btn btn-sm mb-1 d-flex align-items-center"
            :disabled="loading"
            @click="addNewTab(tab)"
          >
            <i :class="tab.icon" class="mr-2"></i>
            {{ tab.title }}
          </b-button>
        </b-col>
      </b-row>
    </div>
  </div>
</template>


<script>
export default {
  name: 'TabManager',
  
  props: {
    encounterId: {
      type: [String, Number],
      required: true
    }
  },

  data() {
    return {
      loading: false,
      errorMessage: '',
      activeTabs: [],
      availableTabs: [
        { type: 'safeguarding_concerns', title: 'Safeguarding Concerns', icon: 'fas fa-exclamation-circle' },
        { type: 'comments', title: 'Comments', icon: 'fas fa-eye' },
        { type: 'examination', title: 'Examination', icon: 'fas fa-stethoscope' },
        { type: 'allergies', title: 'Allergies', icon: 'fas fa-allergies' },
        { type: 'family_history', title: 'Family History', icon: 'fas fa-users' }
      ]
    };
  },

  methods: {
    hasTabType(type) {
      return this.activeTabs.some(tab => tab.type === type);
    },

    addNewTab(tabInfo) {
      const newTab = {
        id: `${tabInfo.type}-${Date.now()}`,
        type: tabInfo.type,
        title: tabInfo.title,
        content: '',
        allergyType: 'food',
        severity: 'mild',
        examType: ''
      };
      
      this.activeTabs.push(newTab);
      this.$emit('section-added', newTab);
    },

    removeTab(tabId) {
      const index = this.activeTabs.findIndex(t => t.id === tabId);
      if (index !== -1) {
        this.activeTabs.splice(index, 1);
        this.$emit('tab-removed', tabId);
      }
    }
  }
};
</script>

<style scoped>
.tab-manager {
  position: relative;
}

.horizontal-tabs {
  width: 100%;
  overflow-x: auto;
}

.tab-nav {
  white-space: nowrap;
  flex-wrap: nowrap;
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 0.5rem;
}

/* Custom styling for nav items */
.nav-item {
  margin-bottom: -1px;
}

.nav-item .nav-link {
  border-radius: 0.25rem;
  padding: 0.5rem 1rem;
  display: flex;
  align-items: center;
}

.nav-item .nav-link.active {
  background-color: #007bff;
  color: white;
}

/* Hover effect */
.nav-item .nav-link:hover:not(.active) {
  background-color: #f8f9fa;
}

/* Close button styling */
.nav-item .btn-link {
  opacity: 0.5;
}

.nav-item .btn-link:hover {
  opacity: 1;
}

/* Active tab close button */
.nav-item .nav-link.active .btn-link {
  color: white;
}

/* New styles for tab options */
.tab-options {
  margin-top: 1rem;
}

.tab-options .btn {
  height: 100%;
  min-height: 40px;
  transition: all 0.2s ease;
}

.tab-options .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.tab-options .btn i {
  font-size: 1.1em;
}
</style>
