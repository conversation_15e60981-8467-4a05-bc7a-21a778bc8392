<template>
  <div class="w-full bg-white">
    <div class="p-6">
      <div class="flex justify-between mb-6">
        <h2 class="text-xl font-semibold">Documents</h2>
      </div>

      <!-- Loading State -->
      <div v-if="loading" class="flex justify-center items-center py-8">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>

      <!-- Documents List -->
      <div v-else-if="documents.length" class="space-y-2">
        <div 
          v-for="doc in documents" 
          :key="`${doc.type}-${doc.id}`"
          class="flex items-center p-4 border rounded-lg mb-2 hover:bg-gray-50"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" 
               stroke-width="2" stroke-linecap="round" stroke-linejoin="round" 
               class="lucide lucide-file-text w-6 h-6 text-gray-500 mr-4">
            <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
            <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
            <path d="M10 9H8"></path>
            <path d="M16 13H8"></path>
            <path d="M16 17H8"></path>
          </svg>
          
          <div class="flex-1">
            <h3 class="font-medium">{{ doc.name }}</h3>
            <p class="text-sm text-gray-500">Added on {{ formatDate(doc.created_at) }}</p>
          </div>

          <document-viewer :document="doc"/>

          <!-- <a v-if="doc.document_url" 
             :href="doc.document_url"
             target="_blank" 
             class="px-4 py-2 text-blue-600 hover:bg-blue-50 rounded">
            View
          </a> -->
        </div>
      </div>

      <!-- Empty State -->
      <div 
        v-else 
        class="flex flex-col items-center justify-center py-12 px-4 border-2 border-dashed border-gray-200 rounded-lg"
      >
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          width="24" 
          height="24" 
          viewBox="0 0 24 24" 
          fill="none" 
          stroke="currentColor"
          stroke-width="2" 
          stroke-linecap="round" 
          stroke-linejoin="round"
          class="w-12 h-12 text-gray-400 mb-4"
        >
          <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
          <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
          <path d="M10 9H8"></path>
          <path d="M16 13H8"></path>
          <path d="M16 17H8"></path>
        </svg>
        <p class="text-lg font-medium text-gray-900">No documents found</p>
        <p class="text-sm text-gray-500 mt-1">No documents have been uploaded yet</p>
      </div>
    </div>
  </div>
</template>

<script>
import { get } from "../../config/request";
import { displayErrorMessage } from "../../utils/message";
import { formatDate } from "../../utils/helper";

export default {
  name: "DocumentsTab",
  props: {
    patientId: {
      type: [String, Number],
    },
    encounterId: {
      type: [String, Number],
    },
  },

  data() {
    return {
      loading: false,
      documents: [],
    };
  },

  methods: {
    formatDate,
    login_id() {
      return this.$store.state.userDataModule.user.ID;
    },

    async getDocuments() {
      this.loading = true;
      this.$emit("loading", true);

      try {
        const response = await get("get_patient_document", {
          patient_id: this.patientId,
          encounter_id: this.encounterId,
        });

        if (response.data.status) {
          this.documents = response.data.data;
        }
      } catch (error) {
        console.error("Error fetching documents:", error);
        displayErrorMessage("Internal server error");
      } finally {
        this.loading = false;
        this.$emit("loading", false);
      }
    },
  },

  watch: {
    patientId: {
      immediate: true,
      handler() {
        this.getDocuments();
      },
    },
  },
};
</script>
