import taskTranslations from '../locales/en/task.js';

// Initialize the translations once when this file is imported
const initTaskTranslations = () => {
    // Make sure window.__kivicarelang exists
    if (!window.__kivicarelang) {
        window.__kivicarelang = {};
    }
    
    // Make sure the task namespace exists
    if (!window.__kivicarelang.task) {
        window.__kivicarelang.task = {};
    }
    
    // Add all task translations to the global translation object
    Object.keys(taskTranslations).forEach(key => {
        window.__kivicarelang.task[key] = taskTranslations[key];
    });
    
    // Also add translations to formTranslation for components that use it directly
    if (!window.formTranslation) {
        window.formTranslation = {};
    }
    
    if (!window.formTranslation.task) {
        window.formTranslation.task = {};
    }
    
    // Copy translations to the formTranslation object as well
    Object.keys(taskTranslations).forEach(key => {
        window.formTranslation.task[key] = taskTranslations[key];
    });
};

// Run the initialization
initTaskTranslations();

export default taskTranslations;