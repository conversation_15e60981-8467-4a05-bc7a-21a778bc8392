<!-- components/BillDetailsModal.vue -->
<template>
  <div
    v-if="showBillDetailsModal"
    class="overflow-y-auto overflow-x-hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 py-8""
  >
    <div class="bg-white rounded-lg w-full max-w-4xl m-4">
      <!-- <PERSON><PERSON> Header -->
      <div class="border-b px-4 py-3 flex justify-between items-center">
        <h3 class="text-lg font-semibold">Bill <PERSON></h3>
        <button @click="closeModal" class="text-gray-500 hover:text-gray-700">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>

      <!-- Modal Body -->
      <div class="px-4 py-4">
        <div id="bill" class="mb-6">
          <!-- Clinic Details -->
          <div class="flex flex-wrap">
            <div class="w-full md:w-1/2">
              <h2 class="text-2xl">{{ patientBillData.clinic?.name || "" }}</h2>
              <h3 class="font-bold">
                Invoice ID: <span class="text-blue-600">#{{ patientBillData.id }}</span>
              </h3>
              <h4>
                <span class="font-bold">Created At:</span>
                {{ patientBillData.created_at }}
              </h4>
            </div>
            <div class="w-full md:w-1/2 text-right">
              <p>
                {{ patientBillData.clinic.address }},
                {{ patientBillData.clinic.city }},
                {{ patientBillData.clinic.postal_code }}
              </p>
              <p>{{ patientBillData.clinic.email }}</p>
              <p class="mt-2">
                Payment Status:
                <span
                  :class="[
                    'px-2 py-1 text-sm text-white rounded',
                    patientBillData.payment_status === 'paid' 
                      ? 'bg-green-500' 
                      : 'bg-red-500'
                  ]"
                >
                  {{ patientBillData.payment_status?.toUpperCase() }}
                </span>
              </p>
            </div>
          </div>

          <!-- Patient Details -->
          <div class="my-6">
            <h3 class="text-xl mb-4">Patient Details</h3>
            <table class="w-full">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-4 py-2 text-left">Patient Name</th>
                  <th class="px-4 py-2 text-left">Gender</th>
                  <th class="px-4 py-2 text-left">Date of Birth</th>
                </tr>
              </thead>
              <tbody>
                <tr class="border-b">
                  <td class="px-4 py-2">{{ patientBillData.patient.display_name }}</td>
                  <td class="px-4 py-2 capitalize">{{ patientBillData.patient.gender }}</td>
                  <td class="px-4 py-2">{{ patientBillData.patient.dob }}</td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Services -->
          <div class="my-6">
            <h3 class="text-xl mb-4">Services</h3>
            <table class="w-full">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-4 py-2 text-left">#</th>
                  <th class="px-4 py-2 text-left">Item Name</th>
                  <th class="px-4 py-2 text-left">Price</th>
                  <th class="px-4 py-2 text-left">Quantity</th>
                  <th class="px-4 py-2 text-left">Total</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(item, index) in patientBillData.billItems" :key="index" class="border-b">
                  <td class="px-4 py-2">{{ index + 1 }}</td>
                  <td class="px-4 py-2">{{ item.item_id.label }}</td>
                  <td class="px-4 py-2">{{ formatCurrency(item.price) }}</td>
                  <td class="px-4 py-2">{{ item.qty }}</td>
                  <td class="px-4 py-2">{{ formatCurrency(item.price * item.qty) }}</td>
                </tr>
              </tbody>
              <tfoot>
                <tr class="border-t">
                  <th colspan="4" class="px-4 py-2 text-right">Total:</th>
                  <th class="px-4 py-2">{{ formatCurrency(patientBillData.total_amount) }}</th>
                </tr>
                <tr>
                  <th colspan="4" class="px-4 py-2 text-right">Discount:</th>
                  <th class="px-4 py-2">{{ formatCurrency(patientBillData.discount) }}</th>
                </tr>
                <tr>
                  <th colspan="4" class="px-4 py-2 text-right">Amount Due:</th>
                  <th class="px-4 py-2">{{ formatCurrency(patientBillData.actual_amount) }}</th>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>

        <!-- Modal Footer -->
        <div class="flex justify-end gap-3">
          <button
            v-if="userData?.addOns?.kiviPro"
            @click="sendToPatient"
            class="px-4 py-2 text-white bg-blue-500 rounded hover:bg-blue-600"
            :disabled="isSending"
          >
            <i :class="[isSending ? 'fa fa-spinner fa-spin' : 'fa fa-paper-plane']"></i>
            Send to Patient
          </button>

          <button
            v-if="userData?.addOns?.kiviPro"
            @click="printData"
            class="px-4 py-2 text-white bg-blue-500 rounded hover:bg-blue-600"
            :disabled="isPrinting"
          >
            <i :class="[isPrinting ? 'fa fa-spinner fa-spin' : 'fa fa-print']"></i>
            Print
          </button>

          <button
            @click="closeModal"
            class="px-4 py-2 border rounded text-gray-600 hover:bg-gray-50"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { post, get } from "../../config/request";

export default {
  name: "BillDetailsModal",
  
  props: {
    showBillDetailsModal: {
      type: Boolean,
      required: true
    },
    encounterId: {
      type: [String, Number],
      required: true
    },
    clinic_extra: {
      type: Object,
      default: () => ({})
    },
    biilingId:{
      type: [String, Number],
      default: null
    }
  },

  data() {
    return {
      isSending: false,
      isPrinting: false,
      patientBillData: {
        clinic: {
          name: "",
          address: "",
          city: "",
          postal_code: "",
          email: ""
        },
        patient: {
          display_name: "",
          gender: "",
          dob: ""
        },
        billItems: [],
        total_amount: 0,
        discount: 0,
        actual_amount: 0,
        payment_status: "",
        created_at: ""
      },
      clinic_currency_prefix: "",
      clinic_currency_postfix: ""
    };
  },

  computed: {
    userData() {
      return this.$store.state.userDataModule.user;
    }
  },

  watch: {
    showBillDetailsModal: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.fetchBillDetails();
        }
      }
    },
    
    clinic_extra: {
      immediate: true,
      handler(newVal) {
        this.setCurrencyFormat(newVal);
      }
    }
  },

  methods: {
    setCurrencyFormat(clinicExtra) {
      this.clinic_currency_prefix = clinicExtra?.currency_prefix || clinicExtra?.prefix || "";
      this.clinic_currency_postfix = clinicExtra?.currency_postfix || clinicExtra?.postfix || "";
    },

    formatCurrency(amount) {
      return `${this.$store.state.userDataModule.user.clinic_currency_detail.prefix}${amount}${this.$store.state.userDataModule.user.clinic_currency_detail.postfix}`;
    },

    async fetchBillDetails() {
      try {
        const response = await get("patient_bill_detail", {
          encounter_id: this.encounterId,
          id: this.biilingId
        });

        if (response?.data?.status && response?.data?.data) {
          this.patientBillData = response.data.data;
        }
      } catch (error) {
        console.error("Error fetching bill details:", error);
        this.$toast?.error("Failed to fetch bill details");
      }
    },

    async sendToPatient() {
      this.isSending = true;
      console.log(this.encounterId,'ritesh');
      
      try {
        const response = await get("print_encounter_bill_detail", {
          id: this.biilingId,
          data: this.patientBillData,
          type: "sendBill"
        });

        if (response?.data?.status) {
          this.$toast?.success(response.data.message || "Bill sent successfully");
        }
      } catch (error) {
        console.error("Error sending bill:", error);
        this.$toast?.error("Failed to send bill");
      } finally {
        this.isSending = false;
      }
    },

    async printData() {
      this.isPrinting = true;
      try {
        const response = await get("print_encounter_bill_detail", {
          id: this.biilingId,
          data: this.patientBillData,
          type: "printBill"
        });

        if (response?.data?.status) {
          // Create a new window
          const newWindow = window.open('', 'self');
          
          // Write the HTML content to the new window
          newWindow.document.write(response?.data?.data);
          
          // Close the document for writing to ensure it renders properly
          newWindow.document.close();
          
          // Trigger the print dialog (optional)
          newWindow.print();
          newWindow.close();
          
        }
      } catch (error) {
        console.error("Error printing bill:", error);
        this.$toast?.error("Failed to print bill");
      } finally {
        this.isPrinting = false;
      }
    },

    closeModal() {
      this.$emit("update:showBillDetailsModal", false);
    }
  }
};
</script>