<template>
  <div class="space-y-3">
    <!-- Main Tabs - KiviCare Style -->
    <div v-for="(field, key) in sortedMainTabs" :key="key">
      <div class="bg-white rounded border p-3">
        <div class="flex justify-between items-center mb-2">
          <div class="flex items-center gap-2">
            <h2 class="font-medium">{{ field.label }}</h2>
          </div>
          <div class="flex gap-2">
            <!-- Clone button -->
            <button @click="addEntry(key)" class="text-blue-500 hover:text-blue-600">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
                <path d="M5 12h14"/>
                <path d="M12 5v14"/>
              </svg>
            </button>
          </div>
        </div>

        <div class="flex flex-col gap-2">
          <!-- Existing Entries -->
          <div v-if="mainTabs[key]?.length" class="space-y-2">
            <div v-for="(entry, index) in mainTabs[key]" :key="entry.id" class="relative">
              <textarea
                :value="entry.content"
                @input="updateEntry(key, entry.id, $event.target.value)"
                class="w-full h-28 text-sm resize-none focus:outline-none border-b"
                :placeholder="'Enter ' + field.label.toLowerCase() + '...'"
              ></textarea>
              <div class="flex justify-between items-center mt-1">
                <div class="text-xs text-gray-500">{{ formatDate(entry.created_at) }}</div>
                <button
                  @click="removeEntry(key, entry.id)"
                  class="text-red-500 hover:text-red-600 text-xs"
                >
                  Remove
                </button>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div v-else>
            <textarea
              @input="addEntry(key, $event.target.value)"
              class="w-full h-28 text-sm resize-none focus:outline-none border-b"
              :placeholder="'Enter ' + field.label.toLowerCase() + '...'"
            ></textarea>
          </div>

          <div class="flex justify-between items-center w-full">
            <!-- Prefill Button -->
            <button class="text-sm text-gray-500 hover:text-gray-600">
              Prefill
            </button>

            <!-- Templates for Examination -->
            <div v-if="key === 'examination' && field.templates" class="flex gap-1">
              <button
                v-for="template in field.templates.slice(0, 3)"
                :key="template"
                @click="addTemplateEntry(key, template)"
                class="px-2 py-1 text-xs bg-gray-100 rounded hover:bg-gray-200"
              >
                {{ template }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface TabEntry {
  id: number
  content: string
  created_at: string
}

interface Props {
  mainTabs: Record<string, TabEntry[]>
  template: Record<string, any>
}

interface Emits {
  (e: 'update', mainTabs: Record<string, TabEntry[]>): void
  (e: 'add-entry', tabKey: string, content: string): void
  (e: 'remove-entry', tabKey: string, entryId: number): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Computed
const sortedMainTabs = computed(() => {
  if (!props.template) return {}
  
  return Object.fromEntries(
    Object.entries(props.template).sort(([, a], [, b]) => (a.order || 0) - (b.order || 0))
  )
})

// Methods
const addEntry = (tabKey: string) => {
  emit('add-entry', tabKey, '')
}

const removeEntry = (tabKey: string, entryId: number) => {
  emit('remove-entry', tabKey, entryId)
}

const updateEntry = (tabKey: string, entryId: number, content: string) => {
  const updatedMainTabs = { ...props.mainTabs }
  if (updatedMainTabs[tabKey]) {
    const entry = updatedMainTabs[tabKey].find(e => e.id === entryId)
    if (entry) {
      entry.content = content
      emit('update', updatedMainTabs)
    }
  }
}

const addTemplateEntry = (tabKey: string, template: string) => {
  const templateContent = getTemplateContent(template)
  emit('add-entry', tabKey, templateContent)
}

const getTemplateContent = (template: string) => {
  const templates = {
    'CVS': 'Cardiovascular System:\n- Heart rate: \n- Blood pressure: \n- Heart sounds: \n- Peripheral pulses: \n- Edema: ',
    'Resp': 'Respiratory System:\n- Respiratory rate: \n- Oxygen saturation: \n- Chest inspection: \n- Auscultation: \n- Percussion: ',
    'Gastro': 'Gastrointestinal System:\n- Abdomen inspection: \n- Palpation: \n- Bowel sounds: \n- Liver: \n- Spleen: ',
    'Neuro': 'Neurological System:\n- Mental status: \n- Cranial nerves: \n- Motor function: \n- Sensory function: \n- Reflexes: ',
    'MSK': 'Musculoskeletal System:\n- Joint inspection: \n- Range of motion: \n- Muscle strength: \n- Deformities: \n- Gait: ',
    'Derm': 'Dermatological System:\n- Skin inspection: \n- Color: \n- Texture: \n- Lesions: \n- Rashes: ',
    'ENT': 'ENT System:\n- Ears: \n- Nose: \n- Throat: \n- Lymph nodes: \n- Thyroid: '
  }
  
  return templates[template] || `${template} examination findings:`
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString()
}
</script>
