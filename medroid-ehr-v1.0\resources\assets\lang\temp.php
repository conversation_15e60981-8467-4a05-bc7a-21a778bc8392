<?php

use App\baseClasses\KCBase;

$kcBase = KCBase::get_instance();
$data = [
    "common" => [
        "add" => __("Add", "kc-lang"),
        "edit" => __("Edit", "kc-lang"),
        "delete" => __("Delete", "kc-lang"),
        "update" => __("Update", "kc-lang"),
        "choose_language" => __("Choose Language", "kc-lang"),
        "form_setting" => __("Form setting", "kc-lang"),
        "plh_choose_language" => __("Choose Language", "kc-lang"),
        "kivicare_Management_system" => __("Medroid (EHR)", "kc-lang"),
        'encounter_body_chart' => esc_html__('Consultation body chart', 'kc-lang'),
        'theme_mode' => esc_html__('Theme mode', 'kc-lang'),
        'menu_bar_position' => esc_html__('Menu bar position', 'kc-lang'),
        'menu_items' => esc_html__('Menu items', 'kc-lang'),
        'select_menu_items' => esc_html__('Select menu items', 'kc-lang'),
        'image_template' => esc_html__('Image template', 'kc-lang'),
        'image' => esc_html__('Image', 'kc-lang'),
        'default' => esc_html__('Default', 'kc-lang'),
        'add_new_image' => esc_html__('Add new image', 'kc-lang'),
        'replace' => esc_html__('Replace', 'kc-lang'),
        'new' => esc_html__('New', 'kc-lang'),
        'file' => esc_html__('File', 'kc-lang'),
        'body_chart' => esc_html__('Body chart', 'kc-lang'),
        'clone' => esc_html__('Clone', 'kc-lang'),
        'image_handle_preference' => esc_html__('Image Handling Preference (when image save after editing)', "kc-lang"),
        'body_chart_delete_confirm_message' => esc_html__( 'Press yes to delete body chart details.', 'kc-lang' ),
		'image_delete_confirm_message' => esc_html__( 'Press yes to delete image details', 'kc-lang' ),
		'create_new_image' => esc_html__( 'Create New image', 'kc-lang' ),
		'replace_orginal_image'  => esc_html__( 'Replace orginal image', 'kc-lang' ),
		'same_image_exist_in_template' => esc_html__( 'The selected image is already set as the template. Please choose a different image.', 'kc-lang' ),
        'stripe_payment_currency_match' => __('Note: The Stripe payment currency must be the same as the service price currency', 'kc-lang'),
        'stripe_payment' => __('Stripe payment', 'kc-lang'),
        'stripe_publishable_key' => __('Publishable key', 'kc-lang'),
        'stripe_secret_key' => __('API secret key', 'kc-lang'),
        'enter_publishable_key' => __('Enter publishable key', 'kc-lang'),
        'enter_stripe_secret_key' => __('Enter API secret key', 'kc-lang'),
        'publishable_key_required' => __('Stripe Publishable key is required', 'kc-lang'),
        'stripe_currency_required' => __('Stripe currency is required', 'kc-lang'),
        "tax_rate" => __("Tax Rate", "kc-lang"),
        "tax_value" => __("Tax Value", "kc-lang"),
        "add_tax" => __("Add Tax", "kc-lang"),
        "new_tax" => __("New Tax", "kc-lang"),
        "nhs" => __("NHS Number", "kc-lang"),
        "tax_doctor_note" => __("Note: To apply tax to all doctors, please leave the selection blank.", "kc-lang"),
        "tax_service_note" => __("Note: To apply tax to all services, please leave the selection blank.", "kc-lang"),
        "tax_clinic_note" => __("Note: To apply tax to all clinics, please leave the selection blank.", "kc-lang"),
        "tax_greater_then_0" => __("Tax rate must be greater than zero", "kc-lang"),
        "custom_notification" => __("Custom notification", "kc-lang"),
        "custom_form" => __("Custom Form", "kc-lang"),
        "no_form_field_available" => __("No Form Field Available", "kc-lang"),
        "please_fill_all_required_fields" => __("Please fill all required fields", "kc-lang"),
        "custom_form_list" => __("Custom forms", "kc-lang"),
        "add_form" => __("Add form", "kc-lang"),
        "search_custom_form_data_global_placeholder" => __("Search by name,module type and id", "kc-lang"),
        "form_title" => __("Form title", "kc-lang"),
        "form_title_color" => __("Form title color", "kc-lang"),
        "form_title_alignment" => __("Form title alignment", "kc-lang"),
        "add_field_classes" => __("Add field classes", "kc-lang"),
        "class" => __("Class", "kc-lang"),
        "heading_title" => __("Heading title", "kc-lang"),
        "heading_tag" => __("Heading tag", "kc-lang"),
        "module_type" => __("Module type", "kc-lang"),
        "form_icon" => __("Form icon", "kc-lang"),
        "form_icon_placeholder" => __("Enter font icon class", "kc-lang"),
        "show_if_appointment_status" => __("Show if Appointment status", "kc-lang"),
        "show_in" => __("Show in", "kc-lang"),
        "custom_form_appointment_note" => __("Note: If consultation is selected, form will only be displayed if there has been an appointment encounter", "kc-lang"),
        "custom_form_clinic_note" => __("Note: To show form to all clinics, please leave the selection blank.", "kc-lang"),
        "custom_form_role_note" => __("Note: To show form to all roles, please leave the selection blank.", "kc-lang"),
        "please_add_form_fields" => __("Please add form fields", "kc-lang"),
        "please_select_form_status" => __("Please select form status", "kc-lang"),
        "please_select_module_type" => __("Please select module type", "kc-lang"),
        "please_enter_field_label" => __("Please enter field label", "kc-lang"),
        "please_select_field_type" => __("Please select field type", "kc-lang"),
        "please_select_file_types" => __("Please select file types", "kc-lang"),
        "please_enter_options" => __("Please enter options", "kc-lang"),
        "get" => __("GET", "kc-lang"),
        "post" => __("POST", "kc-lang"),
        "headers" => __("Headers", "kc-lang"),
        "enter_key" => __("Enter key", "kc-lang"),
        "enter_value" => __("Enter value", "kc-lang"),
        "value" => __("Value", "kc-lang"),
        "enabling_twilio_whatsApp_will_disable_custom_notification_WhatsApp" => __("Enabling Twilio WhatsApp will disable custom notification WhatsApp", "kc-lang"),
        "enabling_twilio_sms_will_disable_custom_notification_sms" => __("Enabling Twilio SMS will disable custom notification SMS", "kc-lang"),
        "enabling_custom_notification_whatsApp_will_disable_twillo_WhatsApp" => __("Enabling custom notification WhatsApp will disable Twilio WhatsApp", "kc-lang"),
        "enabling_custom_notification_sms_will_disable_twillo_sms" => __("Enabling custom notification SMS will disable Twillo SMS", "kc-lang"),
        "enable_sms" => __("Enable SMS", "kc-lang"),
        "enable_whatsapp" => __("Enable WhatsApp", "kc-lang"),
        "add_header" => __("Add header", "kc-lang"),
        "add_query_parameter" => __("Add query parameter", "kc-lang"),
        "add_dynamic_key" => __("Add dynamic key", "kc-lang"),
        "query_parameters" => __("Query Parameters", "kc-lang"),
        "send_request" => __("Send Request", "kc-lang"),
        "save_custom_notification" => __("Save custom notification", "kc-lang"),
        "enter_name" => __("Enter name", "kc-lang"),
        "notification_type" => __("Notification Type", "kc-lang"),
        "sms" => __("SMS", "kc-lang"),
        "whatsapp" => __("Whatsapp", "kc-lang"),
        "status_code" => __("Status Code", "kc-lang"),
        "form" => __("Form", "kc-lang"),
        "response" => __("Response", "kc-lang"),
        "dynamic_keys" => __("Dynamic keys", "kc-lang"),
        "collections" => __("Collections", "kc-lang"),
        "tax" => __("Tax", "kc-lang"),
        "tax_name" => __("Tax Name", "kc-lang"),
        "tax_list" => __("Taxes", "kc-lang"),
        "delete_tax" => __("Press yes to delete tax", "kc-lang"),
        "no_tax_found" => __("No tax found", "kc-lang"),
        "search_tax_data_global_placeholder" => __("Search by name,status....", "kc-lang"),
        "no_appointments" => __("No Appointments Found", "kc-lang"),
        "loading" => __("Loading...", "kc-lang"),
        "cancel" => __("Cancel", "kc-lang"),
        "clear" => __("Clear", "kc-lang"),
        "undo" => __("Undo", "kc-lang"),
        "encounter_id" => __("Consultation ID", "kc-lang"),
        "total_rows" => __("Total Rows", "kc-lang"),
        "rows_selected" => __("Rows selected", "kc-lang"),
        "apply" => __("Apply", "kc-lang"),
        "total_rows_inserted" => __("Total Rows Inserted", "kc-lang"),
        "import_more_file" => __("Import More File", "kc-lang"),
        "appointment_module" => __("Appointment Module", "kc-lang"),
        "encounter_module" => __("Consultation Module", "kc-lang"),
        "patient_encounter_module" => __("Patient Consultation Module", "kc-lang"),
        "encounters_template_module" => __("Consultation Template Module", "kc-lang"),
        "select_allowed_file_type" => __("Select allowed file type", "kc-lang"),
        "accept_all_file_type" => __("To accept all type of file select all option from dropdown option", "kc-lang"),
        "clinical_detail_module" => __("Clinical Detail Module", "kc-lang"),
        "prescription_module" => __("Prescription Module", "kc-lang"),
        "clinic_module" => __("Clinic Module", "kc-lang"),
        "patient_module" => __("Patient Module", "kc-lang"),
        "doctor_module" => __("Doctor Module", "kc-lang"),
        "receptionist_module" => __("Receptionist Module", "kc-lang"),
        "service_module" => __("Service Module", "kc-lang"),
        "session_module" => __("Session Module", "kc-lang"),
        "billing_module" => __("Billing Module", "kc-lang"),
        "holiday_module" => __("Absence Module", "kc-lang"),
        "dashboard_module" => __("Dashboard Module", "kc-lang"),
        "custom_field_module" => __("Custom field Module", "kc-lang"),
        "static_data_module" => __("Static/Listing data Module", "kc-lang"),
        "other_module" => __("Other Module", "kc-lang"),
        "patient_report_module" => __("Patient Report Module", "kc-lang"),
        "import" => __("Import", "kc-lang"),
        "file_type" => __("File type", "kc-lang"),
        "upload_file" => __("Upload File", "kc-lang"),
        "clinic_admin" => __("Clinic admin", "kc-lang"),
        "removed_clinics_doctor_msg" => __("Removed clinics will delete service and session data of this doctor and removed clinics.", "kc-lang"),
        "razorpay" => __("Razorpay", "kc-lang"),
        "razorpay_currency_notice" => __("Note: The Razorpay currency must be the same as the service price currency", "kc-lang"),
        "send_notification_when_user_register" => __("Send Notification when user register", "kc-lang"),
        "import_data" => __("Import data", "kc-lang"),
        "signature" => __("Signature", "kc-lang"),
        "registration_shortcode_setting" => __("Registration Shortcode Setting", "kc-lang"),
        "default_status_when_doctor_register" => __("Default status when doctor register", "kc-lang"),
        "default_status_when_receptionist_register" => __("Default status when receptionist register", "kc-lang"),
        "default_status_when_patient_register" => __("Default status when patient register", "kc-lang"),
        "encounter_not_close" => __("Please close the Consultation to checkout patient", "kc-lang"),
        "clinic_appointment_count" => __("Clinic Appointment Count", "kc-lang"),
        "doctor_appointment_count" => __("Doctor Appointment Count", "kc-lang"),
        "message" => __("SMS/Whatsapp", "kc-lang"),
        "note_notification" => __("Note : If notification is enable, demo import will take time.", "kc-lang"),
        "add_session" => __("Add Availability", "kc-lang"),
        "detail" => __("Detail", "kc-lang"),
        "please_share_your_experience" => __("Please Share Your Experience", "kc-lang"),
        "not_verified" => __("Not Verified", "kc-lang"),
        "verify" => __("Verify", "kc-lang"),
        "verified" => __("Verified", "kc-lang"),
        "demo_user" => __("Create Selected Demo User", "kc-lang"),
        "date" => __("Date", "kc-lang"),
        "save_changes" => __("Save Changes", "kc-lang"),
        "close" => __("Close", "kc-lang"),
        "closed" => __("Closed", "kc-lang"),
        "test" => __("Test", "kc-lang"),
        "select_option" => __("-- Select option --", "kc-lang"),
        "all" => __("All", "kc-lang"),
        "back_to_wordpress" => __("Back To WordPress", "kc-lang"),
        "update" => __("Update", "kc-lang"),
        "my_profile" => __("My Profile", "kc-lang"),
        "change_password" => __("Change Password", "kc-lang"),
        "logout" => __("Logout", "kc-lang"),
        "choose_file" => __("Choose file", "kc-lang"),
        "no_file_chosen" => __("No file Chosen", "kc-lang"),
        "full_screen" => __("Full Screen", "kc-lang"),
        "warning_zoom_configuration" => __("Please save your Zoom configuration", "kc-lang"),
        "zoom_configuration_link" => __("Zoom configuration link", "kc-lang"),
        "dob" => __("DOB", "kc-lang"),
        "dob_required" => __("Date of birth is required", "kc-lang"),
        "gender" => __("Gender", "kc-lang"),
        "gender_required" => __("Gender is required", "kc-lang"),
        "male" => __("Male", "kc-lang"),
        "female" => __("Female", "kc-lang"),
        "other" => __("Other", "kc-lang"),
        "service" => __("Service", "kc-lang"),
        "services" => __("Services", "kc-lang"),
        "service_add" => __("Add Service", "kc-lang"),
        "sr_no" => __("Sr no", "kc-lang"),
        "item_name" => __("Item name", "kc-lang"),
        "price" => __("Price", "kc-lang"),
        "quantity" => __("Quantity", "kc-lang"),
        "total" => __("Total", "kc-lang"),
        "no_records_found" => __("No records found", "kc-lang"),
        "no_records_selected" => __("No record Selected", "kc-lang"),
        "_note" => __("Note", "kc-lang"),
        "note" => __("Type and press enter to create new service", "kc-lang"),
        "status" => __("Status", "kc-lang"),
        "change_status" => __("Change Status", "kc-lang"),
        "action" => __("Action", "kc-lang"),
        "title" => __("Title", "kc-lang"),
        "name" => __("Name", "kc-lang"),
        "doctor" => __("Doctor", "kc-lang"),
        "receptionist" => __("Receptionist", "kc-lang"),
        "doctors" => __("Doctors", "kc-lang"),
        "patient" => __("Patient", "kc-lang"),
        "fname" => __("First Name", "kc-lang"),
        "choose_image" => __("Choose Image", "kc-lang"),
        "enter_fname" => __("Enter your first name", "kc-lang"),
        "required" => __("Required.", "kc-lang"),
        "invalid" => __("Invalid.", "kc-lang"),
        "fname_required" => __("First name is required.", "kc-lang"),
        "lname" => __("Last Name", "kc-lang"),
        "enter_lname" => __("Enter your last name", "kc-lang"),
        "lname_required" => __("Last name is required.", "kc-lang"),
        "email" => __("Email", "kc-lang"),
        "enter_email" => __("Enter your email", "kc-lang"),
        "email_required" => __("Email is required.", "kc-lang"),
        "password" => __("Password", "kc-lang"),
        "pwd_required" => __("Password is required.", "kc-lang"),
        "repeat_pwd" => __("Repeat Password", "kc-lang"),
        "repeat_password_required" => __("Repeat Password is required.", "kc-lang"),
        "pwd_not_match" => __("New password and confirm password does not match.", "kc-lang"),
        "login_btn" => __("Login", "kc-lang"),
        "sign_up" => __("Sign Up", "kc-lang"),
        "no" => __("No", "kc-lang"),
        "dr" => __("Dr.", "kc-lang"),
        "filters" => __("Filters", "kc-lang"),
        "add_filter" => __("Apply filters", "kc-lang"),
        "close_filter" => __("Close filter", "kc-lang"),
        "back" => __("Back", "kc-lang"),
        "add_encounter_template" => __("Add Consultation Template", "kc-lang"),
        "switch_to_encounter_temp" => __("Consultation Template", "kc-lang"),
        "switch_to_encounter" => __("Patient Consultation", "kc-lang"),
        "encounter_template" => __("Consultation Templates", "kc-lang"),
        "save" => __("Save", "kc-lang"),
        "invalid_email" => __("Invalid email format", "kc-lang"),
        "active" => __("Active", "kc-lang"),
        "inactive" => __("Inactive", "kc-lang"),
        "name_required" => __("Name is required", "kc-lang"),
        "date_required" => __("Date is required", "kc-lang"),
        "email_address" => __("Email address", "kc-lang"),
        "registered_gp_name" => __("Registered GP Name", "kc-lang"),
        "registered_gp_address" => __("Registered GP Address", "kc-lang"),
        "contact_info" => __("Contact information", "kc-lang"),
        "insurance_info" => __("Insurance information", "kc-lang"),
        "settings" => __("Settings", "kc-lang"),
        "signaturerx_prescriptions" => __("Signature Rx", "kc-lang"),
        "audit_logs" => __("Audit Logs", "kc-lang"),
        "contact_list" => __("Contacts", "kc-lang"),
        "en_dis_module" => __("Enable/Disable Modules", "kc-lang"),
        "fname_validation_1" => __("First name allows only alphabetic value (spaces are not allowed)", "kc-lang"),
        "fname_validation_2" => __("First name length should be between 2 to 15 character", "kc-lang"),
        "lname_validation_1" => __("Last name allows only alphabetic value (spaces are not allowed)", "kc-lang"),
        "lname_validation_2" => __("Last name length should be between 2 to 15 character", "kc-lang"),
        "contact" => __("Contact", "kc-lang"),
        "enter_contact" => __("Enter your contact number", "kc-lang"),
        "contact_required" => __("Contact is required.", "kc-lang"),
        "contact_validation_1" => __("Contact number length should be between 4 to 15 digits", "kc-lang"),
        "contact_validation_2" => __("Invalid contact number format", "kc-lang"),
        "telemed" => __("Telemed", "kc-lang"),
        "to" => __("To", "kc-lang"),
        "time" => __("Time", "kc-lang"),
        "insurance_provider" => __("Insurance Provider", "kc-lang"),
        "insurance_no" => __("Insurance Number", "kc-lang"),
        "insurance_auth_code" => __("Insurance Auth Code", "kc-lang"),
        "contact_no" => __("Contact no", "kc-lang"),
        "contact_num_required" => __("Contact number is required", "kc-lang"),
        "city" => __("City", "kc-lang"),
        "city_required" => __("City is required", "kc-lang"),
        "city_validation_1" => __("City name allows only alphabetic value", "kc-lang"),
        "city_validation_2" => __("City maximum length should be 30 character", "kc-lang"),
        "state" => __("State", "kc-lang"),
        "state_validation_1" => __("State name allows only alphabetic value", "kc-lang"),
        "state_validation_2" => __("State maximum length should be 30 character", "kc-lang"),
        "country" => __("Country", "kc-lang"),
        "country_required" => __("Country is required", "kc-lang"),
        "country_validation_1" => __("Country name allows only alphabetic value", "kc-lang"),
        "country_validation_2" => __("Country maximum length should be 30 character", "kc-lang"),
        "address" => __("Address", "kc-lang"),
        "address_required" => __("Address is required", "kc-lang"),
        "postal_code" => __("Postal code", "kc-lang"),
        "postal_code_required" => __("Postal code is required", "kc-lang"),
        "postal_code_validation_1" => __("Invalid postal code format", "kc-lang"),
        "postal_code_validation_2" => __("Postal code should be maximum 12 digits", "kc-lang"),
        "profile" => __("Profile", "kc-lang"),
        "static_data" => __("Static Data", "kc-lang"),
        "handle_request" => __("Handle Request", "kc-lang"),
        "email_to_get_help" => __("Other than this many more fine-tunings and tweaks are done. Please <NAME_EMAIL> if you face any issues with the update.", "kc-lang"),
        "note_options" => __("Type option name and press enter to add new option", "kc-lang"),
        "note_1" => __("1) If you face any issue then try deactivating and activating the plugin or contact us.", "kc-lang"),
        "note_2" => __("2) If you want to revert old version. Please install", "kc-lang"),
        "wp_rollback" => __("wp-rollback", "kc-lang"),
        "plugin" => __("plugin.", "kc-lang"),
        "keep_improving" => __("We will keep improving with your support! Thank You!", "kc-lang"),
        "currency_setting" => __("Currency Setting", "kc-lang"),
        "i_understand" => __("I Understand", "kc-lang"),
        "version" => __("Important! Major Version update!! (V2.0.0)", "kc-lang"),
        "microsoft" => __("Microsoft", "kc-lang"),
        "google" => __("Google", "kc-lang"),
        "outlook" => __("Outlook", "kc-lang"),
        "yahoo" => __("Yahoo", "kc-lang"),
        "read_notice" => __("Please read this below log before moving forward", "kc-lang"),
        "faced_issue" => __("Faced issues ?", "kc-lang"),
        "if_use_older_version" => __("If you face problems with this version and you want to continue with old version then please install and use", "kc-lang"),
        "check_video" => __("For smooth migration to new version check following Video guide", "kc-lang"),
        "kivicare_v2" => __("Medroid Upgrade V2.0.0", "kc-lang"),
        "appointment_flow" => __("Appointment Flow", "kc-lang"),
        "basic_details" => __("Basic details", "kc-lang"),
        "close_form_btn" => __("Close form", "kc-lang"),
        "add" => __("Add", "kc-lang"),
        "edit" => __("Edit", "kc-lang"),
        "url" => __("url", "kc-lang"),
        "icon" => __("Icon", "kc-lang"),
        "clinic_admin_email" => __("Clinic Admin Email", "kc-lang"),
        "questions" => __("Health Question", "kc-lang"),
        "enabled_medroid_episode_mail" => __("Patient Consultation Mail Template", "kc-lang"),
        "disabled_medroid_episode_mail" => __("Patient Consultation Mail Template", "kc-lang"),
        "enabled_kivicare_patient_report" => __("Patient Report Template", "kc-lang"),
        "disabled_kivicare_patient_report" => __("Disabled Patient Report Template", "kc-lang"),
        "enabled_kivicare_patient_invoice" => __("Patient Invoice Template", "kc-lang"),
        "disabled_kivicare_patient_invoice" => __("Disabled Patient Invoice Template", "kc-lang"),
        "enabled_kivicare_user_verified" => __("User Verified Acknowledgement notification Template", "kc-lang"),
        "disabled_kivicare_user_verified" => __("Disabled User Verified Template", "kc-lang"),
        "enabled_kivicare_admin_new_user_register" => __("New Admin User Registration Notification", "kc-lang"),
        "disabled_kivicare_admin_new_user_register" => __("Disabled Admin New User Register", "kc-lang"),
        "enabled_kivicare_book_prescription" => __("Patient Prescription Notification Template", "kc-lang"),
        "disabled_kivicare_book_prescription" => __("Disabled Patient Prescription Template", "kc-lang"),
        "enabled_kivicare_book_appointment_reminder" => __("Patient  Appointment Reminder Notification Template", "kc-lang"),
        "disabled_kivicare_book_appointment_reminder" => __("Disabled  Patient  Appointment ReminderTemplate", "kc-lang"),
        "enabled_kivicare_book_appointment_reminder_for_doctor" => __("Patient  Appointment Reminder Notification Template for Doctor", "kc-lang"),
        "disabled_kivicare_book_appointment_reminder_for_doctor" => __("Disabled  Patient  Appointment ReminderTemplate for Doctor", "kc-lang"),
        "enabled_kivicare_clinic_book_appointment" => __("New Appointment Notification to Clinic", "kc-lang"),
        "disabled_kivicare_clinic_book_appointment" => __("Disabled  Clinic Booked Appointment Template", "kc-lang"),
        "enabled_kivicare_add_appointment" => __("New Appointment SMS Template", "kc-lang"),
        "disabled_kivicare_add_appointment" => __("Disabled - Appointment Add SMS Template", "kc-lang"),
        "enabled_kivicare_encounter_close" => __("Consultation Closed SMS Notify to User", "kc-lang"),
        "disabled_kivicare_encounter_close" => __("Disabled - Consultation Close Add SMS Template", "kc-lang"),
        "enabled_kivicare_patient_payment_link"=> __( "Send Appointment Payment Link to Patient",'kc-lang'),
        "disabled_kivicare_patient_payment_link"=> __( "Disabled - Send Appointment Payment Link to Patient",'kc-lang'),
        "enabled_kivicare_patient_bill_payment_link"=> __( "Send Billing Payment Link to Patient",'kc-lang'),
        "disabled_kivicare_patient_bill_payment_link"=> __( "Disabled - Send Billing Payment Link to Patient",'kc-lang'),
        "enabled_kivicare_patient_payment_invoice"=> __( "Send Invoice to Patient",'kc-lang'),
        "disabled_kivicare_patient_payment_invoice"=> __( "Disabled - Send Invoice to Patient",'kc-lang'),
        "enabled_kivicare_receptionist_register" => __("Medroid Receptionist Registration", "kc-lang"),
        "disabled_kivicare_receptionist_register" => __("Disabled Medroid Receptionist Register", "kc-lang"),
        "enabled_kivicare_doctor_registration" => __("Medroid Doctor Registration", "kc-lang"),
        "disabled_kivicare_doctor_registration" => __("Disabled Medroid Doctor Registration", "kc-lang"),
        "enabled_kivicare_book_appointment" => __("Medroid Book Appointment", "kc-lang"),
        "disabled_kivicare_book_appointment" => __("Disabled Medroid Book Appointment", "kc-lang"),
        "enabled_kivicare_resend_user_credential" => __("Resend user credentials", "kc-lang"),
        "disabled_kivicare_resend_user_credential" => __("Disabled Resend user credentials", "kc-lang"),
        "enabled_kivicare_cancel_appointment" => __("Allow Cancel appointments", "kc-lang"),
        "disabled_kivicare_cancel_appointment" => __("Disabled Cancel appointment", "kc-lang"),
        "enabled_kivicare_patient_register" => __("Registration Notification to Patient template", "kc-lang"),
        "disabled_kivicare_patient_register" => __("Disabled Patient Registration Template", "kc-lang"),
        "enabled_kivicare_zoom_link" => __("Video conference appointment booking notification template", "kc-lang"),
        "disabled_kivicare_zoom_link" => __("Disabled Video Conference appointment Template", "kc-lang"),
        "enabled_kivicare_doctor_book_appointment" => __("New Appointment Notification to Doctor Template", "kc-lang"),
        "disabled_kivicare_doctor_book_appointment" => __("Disabled Doctor Booked Appointment Template", "kc-lang"),
        "enabled_kivicare_add_doctor_zoom_link" => __("New Zoom video appointment notification to doctor template", "kc-lang"),
        "disabled_kivicare_add_doctor_zoom_link" => __("Doctor Zoom Video Conference appointment Template", "kc-lang"),
        "enabled_kivicare_add_doctor_meet_link" => __("New Google Meet video appointment notification to doctor template", "kc-lang"),
        "disabled_kivicare_add_doctor_meet_link" => __("Disabled Google Meet Video Conference appointment Template", "kc-lang"),
        "enabled_kivicare_meet_link" => __("New Google Meet video appointment email to patient template", "kc-lang"),
        "disabled_kivicare_meet_link" => __("Disabled Google Meet Video Conference appointment Template", "kc-lang"),
        "enabled_kivicare_clinic_admin_registration" => __("Clinic Admin registration notification to user", "kc-lang"),
        "disabled_kivicare_clinic_admin_registration" => __("Disabled Clinic Admin Registration", "kc-lang"),
        "enabled_kivicare_payment_pending" => __("Payment pending notification to user template", "kc-lang"),
        "disabled_kivicare_payment_pending" => __("Disabled Payment Pending Template", "kc-lang"),
        "enabled_kivicare_patient_clinic_check_in_check_out" => __("Patient clinic Check In notify template", "kc-lang"),
        "disabled_kivicare_patient_clinic_check_in_check_out" => __("Disabled Patient Clinic Check In  Template", "kc-lang"),
        "google_event_template" => __("Google Event Template", "kc-lang"),
        "booked" => __("Booked", "kc-lang"),
        "pending" => __("Pending", "kc-lang"),
        "upcoming" => __("Upcoming", "kc-lang"),
        "completed" => __("Completed (checked-out)", "kc-lang"),
        "cancelled" => __("Cancelled", "kc-lang"),
        "accepted" => __("Accepted", "kc-lang"),
        "action_delete_appointment_doctor" => __("This action may delete your doctor's appointments, sessions, and holidays.", "kc-lang"),
        "action_delete_appointment_patient" => __("This action may delete patient's appointment, history, and encounters.", "kc-lang"),
        "actual_amount" => __("Actual Amount", "kc-lang"),
        "add_service" => __("Add Service", "kc-lang"),
        "add_static_data" => __("Add Static Data", "kc-lang"),
        "add_to_calender" => __("Add to Calendar", "kc-lang"),
        "add_to_cart" => __("Add To Cart", "kc-lang"),
        "address_info" => __("Address info", "kc-lang"),
        "booking_successful" => __("Booking successful", "kc-lang"),
        "cancel_date" => __("All the appointment on selected date will be cancelled.", "kc-lang"),
        "clinic" => __("Clinic", "kc-lang"),
        "color_green" => __("green", "kc-lang"),
        "color_red" => __("red", "kc-lang"),
        "confirm_booking" => __("Confirm booking", "kc-lang"),
        "connected_with_google_calender" => __("You are connected with the google calender.", "kc-lang"),
        "connected_with_google_meet" => __("You are connected with the google meet.", "kc-lang"),
        "create_add" => __("Create Add", "kc-lang"),
        "dates" => __("DATE", "kc-lang"),
        "deleting_services" => __("Deleting service may affects your existing appointments data.", "kc-lang"),
        "desc" => __("desc", "kc-lang"),
        "disconnect" => __("Disconnect", "kc-lang"),
        "disconnected" => __("Disconnected", "kc-lang"),
        "edit_bill_items" => __("Edit bill item", "kc-lang"),
        "edit_clinic" => __("Edit Clinic", "kc-lang"),
        "edit_encounter" => __("Edit consultation", "kc-lang"),
        "edit_receptionist" => __("Edit receptionist", "kc-lang"),
        "edit_service" => __("Edit service", "kc-lang"),
        "edit_staic_data" => __("Edit Static Data", "kc-lang"),
        "english" => __("English", "kc-lang"),
        "folder_permission_msg" => __("Please give permission to your language folder", "kc-lang"),
        "google_calendar" => __("Google Calendar", "kc-lang"),
        "google_calendar_client_id" => __("Google Calendar Client ID", "kc-lang"),
        "google_calendar_client_secret" => __("Google Calendar Client Secret", "kc-lang"),
        "google_calendar_configuration" => __("Google Calendar Configuration", "kc-lang"),
        "app_name" => __("App Name", "kc-lang"),
        "app_name_required" => __("App Name Required", "kc-lang"),
        "google_calendar_integration" => __("Google Calendar Integration", "kc-lang"),
        "guide_to_setup_google_calender" => __("Guide to setup google calender.", "kc-lang"),
        "important_note" => __("Important! Note ", "kc-lang"),
        "in_resources" => __("in resources.", "kc-lang"),
        "internal_server_error" => __("Internal server error", "kc-lang"),
        "key" => __("Key", "kc-lang"),
        "KiviCare_lang_folder" => __("KiviCare_lang folder", "kc-lang"),
        "lang_folder" => __("lang folder", "kc-lang"),
        "media_uploads_folder" => __("in media uploads folder and", "kc-lang"),
        "monthly" => __("Monthly", "kc-lang"),
        "yearly" => __("Yearly", "kc-lang"),
        "no_data_found" => __("No Data Found", "kc-lang"),
        "notes" => __("NOTES", "kc-lang"),
        // "patient_setting"=>__( "Patient Setting","kc-lang"),
        "please_connect_google_calendar_automatically" => __("Please connect with your Google Account to get appointments in Google Calendar automatically.", "kc-lang"),
        "please_connect_google_meet_automatically" => __("Please connect your Google Account for Google Meet.", "kc-lang"),
        "please_enable_google_meet" => __("Note: Please enable Google Meet from admin panel.", "kc-lang"),
        "press_yes_delete_billitems" => __("Press yes to delete bill item", "kc-lang"),
        "press_yes_to_delete_static_data" => __("Press yes to delete static data", "kc-lang"),
        "py_delete" => __("Press yes to delete", "kc-lang"),
        "py_delete_appointment" => __("Press yes to delete appointment", "kc-lang"),
        "py_cancel_appointment" => __("Press yes to cancel appointment", "kc-lang"),
        "py_delete_clinic" => __("Press yes to delete clinic", "kc-lang"),
        "py_delete_report" => __("Press yes to delete Report", "kc-lang"),
        "py_delete_clinic_session" => __("Press yes to delete clinic session", "kc-lang"),
        "py_delete_field" => __("Press yes to delete Field", "kc-lang"),
        "py_delete_prescription" => __("Press yes to delete prescription", "kc-lang"),
        "py_delete_receptionist" => __("Press yes to delete receptionist", "kc-lang"),
        "receptionist_list" => __("Clinic Admins", "kc-lang"),
        "remove_from_calender" => __("Remove from Calender", "kc-lang"),
        "reset_appointment_slot" => __("Following action will reset your current appointment session slot.", "kc-lang"),
        "save_and_close_checkout" => __("Save and Close Checkout", "kc-lang"),
        "save_and_close_encounters" => __("Save and Close Consultation", "kc-lang"),
        "save_item" => __("Save item", "kc-lang"),
        "save_profile" => __("Save profile", "kc-lang"),
        "server_error" => __("Server Error", "kc-lang"),
        "setting_for_add_event_in_calendar_for_patient" => __("Setting for add event in calendar for patient.", "kc-lang"),
        "start_date" => __("START DATE", "kc-lang"),
        "static_data_list" => __("Static Data", "kc-lang"),
        "terms_condition" => __("Terms and Condition", "kc-lang"),
        "total_amount" => __("Total AMOUNT", "kc-lang"),
        "total_appointment" => __("Total Appointment", "kc-lang"),
        "update_appointment_status" => __("Press yes to update appointment status", "kc-lang"),
        "update_system" => __("Press yes to update payment status", "kc-lang"),
        "video_consultation" => __("Video Consultation", "kc-lang"),
        "weekly" => __("Weekly", "kc-lang"),
        "yes" => __("Yes", "kc-lang"),
        "checkin" => __("CheckIn", "kc-lang"),
        "loco_translate" => __("Loco Translate", "kc-lang"),
        "view" => __("View", "kc-lang"),
        "doctor_not_available_for_this_clinic" => __("Selected Doctor or Clinic is not Available", "kc-lang"),
        "request_features" => __("Request Features", "kc-lang"),
        "hide_request_features" => __("Hide all utility links : Request features | Get Support | Documentation.", "kc-lang"),
        "remove_request_features" => __("Remove all utility links permanently : Request features | Get Support | Documentation.", "kc-lang"),
        "remove_links" => __("Remove Utility Links", "kc-lang"),
        "age" => __("Age", "kc-lang"),
        "clinic_admin_info" => __("Clinic Admin Information", "kc-lang"),
        "clinic_currency" => __("Clinic currency prefix and postfix ", "kc-lang"),
        "enable_wordpress_logo_status" => __("Enable Wordpress Logo", "kc-lang"),
        "clinic_is_required" => __("Clinic is required", "kc-lang"),
        'google_recaptcha' => __("Google Recaptcha V3", "kc-lang"),
        'enable_google_recaptcha' => __("Enable Google Recaptcha V3", "kc-lang"),
        'site_key' => __("Google Recaptcha Site Key", "kc-lang"),
        'secret_key' => __("Google Recaptcha Secret Key", "kc-lang"),
        "google_recaptcha_refer_link"  => __("Please Click Here To Create New Site and a Secret Key", "kc-lang"),
        "fullcalendar_setting"  => __("Fullcalendar Setting", "kc-lang"),
        "fullcalendar_license_key"  => __("License Key", "kc-lang"),
        "logout_redirect"  => __("Logout Redirect To", "kc-lang"),
        "include_in_multiservice"  => __("Allow multi selection while booking?", "kc-lang"),
        "payment_transaction_failed" => __("Payment Transaction Failed. Please Try Again", "kc-lang"),
        "payment_transaction_saved" => __("Appointment successfully booked, Please check your email for Confirmation Mail", "kc-lang"),
        "more" =>  __("More", "kc-lang"),
        "telemed_link" => __("Telemed Meeting Link", "kc-lang"),
        "qrcode" =>  __("Qrcode", "kc-lang"),
        "copy" =>  __("Copy", "kc-lang"),
        "remove" =>  __("Remove", "kc-lang"),
        "upload" =>  __("upload", "kc-lang"),
        "send_email" =>  __("Send email", "kc-lang"),
        "refresh_list" =>  __("Refresh list", "kc-lang"),
        "shortcodes" =>  __("Shortcodes", "kc-lang"),
        "update_payment_status_to_paid" =>  __("Update payment status to paid", "kc-lang"),
        "date_format_refer_link" => __("Please Click Here To Know more about date Format", "kc-lang"),
        "date_format_setting" => __("Date Format Setting", "kc-lang"),
        "custom_date_format" => __("Enter Custom Date Format ", "kc-lang"),
        "export_pdf" => __("Export PDF", "kc-lang"),
        "select_doctor_or_clinic_is_not_available" => __('Select Doctor or Clinic is not Available', 'kc-lang'),
        "test_and_save" => __("Test & Save", "kc-lang"),
        "search_patient_global_placeholder" => __("Search patient by ID,name, email and status(0 or 1)", 'kc-lang'),
        "search_doctor_global_placeholder" => __("Search doctor by ID,name, email and status(0 or 1)", 'kc-lang'),
        "search_receptionist_global_placeholder" => __("Search receptionist by ID,name, email and status(0 or 1)", 'kc-lang'),
        "search_listing_data_global_placeholder" => __("Search listing-data by name, type and status(0 or 1)", 'kc-lang'),
        "delete_data_kivicare_plugin" => __("Delete All Data Of Medroid Plugin", 'kc-lang'),
        "search_custom_field_data_global_placeholder" => __("Search custom field data by id,field,type and status(0 or 1)", 'kc-lang'),
        "search_holiday_data_global_placeholder" => __("Search absence data by id,schedule of,name", 'kc-lang'),
        "search_service_field_data_global_placeholder" => __("Search services data by id, doctor, name, category, price  and status(0 or 1)", 'kc-lang'),
        "search_bills_data_global_placeholder" => __("Search bills data by id,consultation_id, clinic, doctor, patient, total amount, discount ,amount due  and status(0 or 1)", 'kc-lang'),
        "search_encounter_field_data_global_placeholder" => __("Search consultation data by id, doctor, clinic, patient, date  and status(0 or 1)", 'kc-lang'),
        "search_encounter_template_field_data_global_placeholder" => __("Search consultation Template data by id, name date and status(0 or 1)", 'kc-lang'),
        "search_clinic_field_data_global_placeholder" => __("Search clinic data by id, name, email, admin-email, speciality,address and status(0 or 1)", 'kc-lang'),
        "id" => __("ID", 'kc-lang'),
        "reset_plugin_data" => __("Reset Plugin Data", "kc-lang"),
        "reset_appointments_and_encounter" => __("Reset Appointments And Consultations", "kc-lang"),
        "reset_doctors" => __("Reset Doctors", "kc-lang"),
        "reset_receptionists" => __("Reset Receptionists", "kc-lang"),
        "reset_patients" => __("Reset Patients", "kc-lang"),
        "reset_revenue" => __("Reset Total Revenue", "kc-lang"),
        "reset_clinic" => __("Reset Clinic", "kc-lang"),
        "delete_all_reset_plugin" => __("Delete All Data And Reset Plugin", "kc-lang"),
        "action_reset_plugin_data" => __("This Will Delete All Data", "kc-lang"),
        "action_reset_plugin_user_data" => __("This Will Also delete appointments and consultation related with the selected user type. Are you sure?", "kc-lang"),
        "customField" => __("Custom Field", "kc-lang"),
        "module" => __(" Module", "kc-lang"),
        "label" => __(" Label", "kc-lang"),
        "input_type" => __(" Input Type", "kc-lang"),
        "options" => __(" Options", "kc-lang"),
        "get_pro_now" => __("Get Pro Now", "kc-lang"),
        "prescription" => __("Prescription", "kc-lang"),
        "prescription_name" => __("name", "kc-lang"),
        "prescription_frequency" => __("frequency", "kc-lang"),
        "prescription_duration" => __("duration", "kc-lang"),
        "dashboard_sidebar_setting" => __("Dashboard sidebar setting", "kc-lang"),
        "sidebar_setting" => __("Sidebar Setting", "kc-lang"),
        "country_code" => __("Country Code", "kc-lang"),
        "clinic_admin_country_code" => __("Clinic Admin Country Code", "kc-lang"),
        "country_calling_code" => __("Country Calling Code", "kc-lang"),
        "clinic_admin_country_calling_code" => __("Clinic Admin Country Calling Code", "kc-lang"),
        "default_country_code" => __("Default Country Code For Contact", "kc-lang"),
        "select_country_code" => __("Select Country Code", "kc-lang"),
        "full_calender_validation" => __("Required Fullcalendar License Key", "kc-lang"),
        "all_required_field_validation" => __("Please fill all required fields", "kc-lang"),
        "py_resend_credential" => __("Press yes to resend credential", "kc-lang"),
        "py_status" => __("Press yes to change status", "kc-lang"),
        "user_registration_form_setting" => __("User Registration Form Setting", "kc-lang"),
        "show_other_option_in_gender" => __("Show \"Other\" option in gender", "kc-lang"),
        "dismiss" => __("DISMISS", "kc-lang"),
        "encounter_setting" => __("Consultation Setting", "kc-lang"),
        "allow_encounter_edit_after_close" => __("Allow Consultation Edit After Close", "kc-lang"),
        "deprecated" => __("(Deprecated)", "kc-lang"),
        "smtp_setup_msg" => __("Please make sure your server has Email Server SMTP setup !", "kc-lang"),
        "lbl_download_sample_file" => __("Click here to download sample file", "kc-lang"),
        "lbl_required_field" => __("Following field is required in", "kc-lang"),
        "lbl_file" => __("file", "kc-lang"),
        "documentation" => __("Documentation", "kc-lang"),
        "get_help" => __("Get help", "kc-lang"),
        "get_support" => __("Get support", "kc-lang"),
        "lbl_telemed_pro_pro" => __("Telemed (Pro)", "kc-lang"),
        "lbl_woocommerce_pro" => __("Woocommerce (Pro)", "kc-lang"),
        "lbl_google_calendar_pro" => __("Google Calendar (Pro)", "kc-lang"),
        "lbl_sms_pro" => __("SMS (Pro)", "kc-lang"),
        "holiday" => __("Absence", "kc-lang"),
        "lbl_date_validation" => __("date(date should be less than current date)", "kc-lang"),
        "allow_user_role" => __("Allowed User Role", "kc-lang"),
        "patient_role" => __("Patient Role", "kc-lang"),
        "doctor_role" => __("Doctor Role", "kc-lang"),
        "receptionist_role" => __("Receptionist Role", "kc-lang"),
        "copied" => __("copied!", "kc-lang"),
        "failed_to_copy" => __("Receptionist Role", "kc-lang"),
        "emptyTimeSlots"=> __("All time slots must have start and end times.",'kc-lang'),
        "invalidTimeRange"=> __("Start time must be before end time.",'kc-lang'),
        "overlappingSlots"=> __("Time slots must not overlap.",'kc-lang'),
        "encounter_template_setting"=> __("Consultations Template Settings",'kc-lang'),
        "remove_header"=> __("Remove",'kc-lang'),
        "remove_footer"=> __("Remove",'kc-lang'),
        "upload_header"=> __("Upload Header",'kc-lang'),
        "upload_footer"=> __("Upload Footer",'kc-lang'),
    ],
    "paypal" => [
        "paypal_setting" => __("Paypal Settings", "kc-lang"),
        "paypal_account_setting" => __("Paypal Account Settings", "kc-lang"),
        "paypal_status" => __("Paypal Status", "kc-lang"),
        "paypal_configration" => __("Paypal Configration", "kc-lang"),
        "client_id" => __("Client ID", "kc-lang"),
        "client_secret" => __("Client Secret", "kc-lang"),
        "mode" => __("Mode", "kc-lang"),
        "plh_enter_client_id" => __("Enter Your Client Id", "kc-lang"),
        "plh_enter_client_secret" => __("Enter Your Client Secret", "kc-lang"),
        "sandbox" => __("Sandbox", "kc-lang"),
        "live" => __("Live", "kc-lang"),
        "paypal_mode_required" => __("Payment mode is required", "kc-lang"),
        "paypal_client_id_required" => __("Client id is required", "kc-lang"),
        "paypal_client_secret_required" => __("Client secret is required", "kc-lang"),
        "currency" => __("Currency", "kc-lang"),
        "currency_symbol" => __("Currency symbol", "kc-lang"),
        "currency_format" => __("Currency format", "kc-lang"),
        "paypal_currency_required" => __("Currency is required", "kc-lang"),
        "price_number_format" => __("Price number format", "kc-lang"),
        "price_number_decimals" => __("Price number of decimals", "kc-lang"),
        "paypal_currency_notice" => __("Note: The PayPal currency must be the same as the service price currency", "kc-lang"),
    ],
    "dashboard" => [
        "dashboard" => __("Cockpit", "kc-lang"),
        "total_patients" => __("Total Patients", "kc-lang"),
        "total_visited_patients" => __("Total visited patients", "kc-lang"),
        "total_doctors" => __("Total Doctors", "kc-lang"),
        "total_clinic_doctors" => __("Total clinic doctors", "kc-lang"),
        "total_appointments" => __("Total Appointments", "kc-lang"),
        "total_clinic_appointments" => __("Total clinic appointments", "kc-lang"),
        "latest_appointments" => __("Latest appointments", "kc-lang"),
        "reload" => __("Reload", "kc-lang"),
        "view_all" => __("View All", "kc-lang"),
        "weekly_appointments" => __("Weekly appointments", "kc-lang"),
        "weekly_total_appointments" => __("Weekly total appointments", "kc-lang"),
        "monthly_appointments" => __("Monthly appointments", "kc-lang"),
        "monthly_total_appointments" => __("Monthly total appointments", "kc-lang"),
        "today_appointment_list" => __("todays appointment", "kc-lang"),
        "all_upcoming_appointment" => __("All upcoming appointments", "kc-lang"),
        "total_revenue" => __("Total revenue", "kc-lang"),
        "total_clinic_revenue" => __("Total clinic revenue", "kc-lang"),
        "total_generated_revenue" => __("Total generated revenue", "kc-lang"),
        "filter" => __("Filter", "kc-lang"),
        "reset" => __("Reset", "kc-lang"),
        "total_today_appointments" => __("Total Today's Appointments", "kc-lang"),
        "total_service" => __("Total services", "kc-lang"),
        "patients" => __("Patients", "kc-lang"),
        "medical_dashboard" => __("Medical dashboard", "kc-lang")
    ],
    "doctor" => [
        "doctor_name" => __("Doctor name", "kc-lang"),
        "first_name" => __(" first name", "kc-lang"),
        "last_name" => __(" last name", "kc-lang"),
        "email" => __(" email", "kc-lang"),
        "doctor_contact" => __(" contact", "kc-lang"),
        "gender" => __(" gender", "kc-lang"),
        "specilization" => __(" specilization", "kc-lang"),
        "doctor_specialization_required" => __("Doctor specialization is required", "kc-lang"),
        "experience_year" => __("Experience(In Year)", "kc-lang"),
        "address_details" => __("Address details", "kc-lang"),
        "degree" => __("Degree", "kc-lang"),
        "degree_required" => __("Degree is required", "kc-lang"),
        "degree_validation_1" => __("Only alphanumeric characters are allowed for Degree", "kc-lang"),
        "university" => __("University", "kc-lang"),
        "university_required" => __("University is required", "kc-lang"),
        "university_validation" => __("Only alphanumeric characters are allowed for University", "kc-lang"),
        "year" => __("Year", "kc-lang"),
        "select_year" => __("-- Select year --", "kc-lang"),
        "year_required" => __("Year is required", "kc-lang"),
        "college_university" => __("College/University", "kc-lang"),
        "api_key" => __("API key", "kc-lang"),
        "api_secret" => __("API secret", "kc-lang"),
        "api_secret_required" => __("API secret is required", "kc-lang"),
        "api_key_required" => __("API Key is required", "kc-lang"),
        "zoom_configuration_guide" => __("Zoom Configuration guide", "kc-lang"),
        "zoom_step1" => __("Step 1: Sign up or Sign in here", "kc-lang"),
        "zoom_step2" => __("Step 2: Click/Hover on Develop button at the right in navigation bar and click on build app", "kc-lang"),
        "zoom_step3" => __("Step 3: Select JWT and click Create", "kc-lang"),
        "zoom_oauth_step3" => __("Step 3: Select OAuth and click Create", "kc-lang"),
        "zoom_step4" => __("Step 4: Fill the mandatory information and In the App credentials tag you can see API key and API Secret.", "kc-lang"),
        "zoom_oauth_step4" => __("Step 4: Fill the mandatory information and In the App credentials tag you can see Client ID,Client secret And Redirect URL for OAuth.", "kc-lang"),
        "zoom_step5" => __("Step 5: Copy and Paste API key and API secret here and click on save button and you are ready to go.", "kc-lang"),
        "zoom_oauth_step5" => __("Step 5: Copy and Paste Client ID,Client secret And Redirect URL here and click on save button and you are ready to go.", "kc-lang"),
        "other_detail" => __("Other detail", "kc-lang"),
        "consultation_fees" => __("Consultation Fees", "kc-lang"),
        "video_consultation_fees" => __("Video consultation fees", "kc-lang"),
        "doctor_fees_required" => __("Doctors fees is required", "kc-lang"),
        "zoom_market_place_portal" => __("Zoom market Place portal", "kc-lang"),
        "create_app" => __("Create app", "kc-lang"),
        "doctors_list" => __("Doctors", "kc-lang"),
        "other_details" => __("Other details", "kc-lang"),
        "extra_detail" => __("Extra detail", "kc-lang"),
        "add_doctor" => __("Add doctor", "kc-lang"),
        "edit_doctor" => __("Edit doctor", "kc-lang"),
        "edit_profile" => __("Edit profile", "kc-lang"),
        "basic_information" => __("Basic information", "kc-lang"),
        "basic_settings" => __("Basic Settings", "kc-lang"),
        "type" => __("Type", "kc-lang"),
        "type_required" => __("Type is required", "kc-lang"),
        "fees_type" => __("Fees type", "kc-lang"),
        "range" => __("Range", "kc-lang"),
        "fixed" => __("Fixed", "kc-lang"),
        "fees" => __("Fees", "kc-lang"),
        "fees_type_required" => __("Fees type is required", "kc-lang"),
        "doc_fee_required" => __(" Doctors fees is required", "kc-lang"),
        "doc_fee_validation_1" => __("Doctors fees must be greater than zero", "kc-lang"),
        "doc_fee_validation_2" => __("Doctors fees must be between 0 to 1000000000000000000", "kc-lang"),
        "doc_fee_validation_3" => __("Doctors fees minimum fees and maximum fees are required", "kc-lang"),
        "doc_fee_validation_4" => __("Doctors fees minimum fees and maximum fees must be greater than zero", "kc-lang"),
        "doc_fee_validation_5" => __("Doctors to fees minimum value must be greater than from fees value.", "kc-lang"),
        "doc_fee_validation_6" => __("Doctors fees minimum fees and minimum fees must be between 0 to 1000000000000000000", "kc-lang"),
        "qualification_information" => __("Qualification Information", "kc-lang"),
        "qualification_speciality_details" => __("Qualification/Speciality details", "kc-lang"),
        "doctor_working_days_sessions" => __("Doctors working days and sessions", "kc-lang"),
        "doctor_working_days" => __("Working days", "kc-lang"),
        "charge_n_doc_selection" => __("Added charges and doctor selection", "kc-lang"),
        "doc_field_customization" => __("Individual doctor field customization", "kc-lang"),
        "save_btn" => __("Save", "kc-lang"),
        "fname_plh" => __("Enter first name", "kc-lang"),
        "lname_placeholder" => __("Enter last name", "kc-lang"),
        "email_placeholder" => __("Enter email", "kc-lang"),
        "address_placeholder" => __("Enter address", "kc-lang"),
        "country_placeholder" => __("Enter country", "kc-lang"),
        "search_placeholder" => __("Search", "kc-lang"),
        "tag_select_clinic_plh" => __("Select clinic", "kc-lang"),
        "contact_placeholder" => __("Enter contact", "kc-lang"),
        "welcome_date_plh" => __("welcome date", "kc-lang"),
        "tag_doc_sp_plh" => __("Doctor specialization", "kc-lang"),
        "add_sp_plh" => __("Add Specialization", "kc-lang"),
        "experience_plh" => __("Enter experience", "kc-lang"),
        "pcode_placeholder" => __("Enter pin code", "kc-lang"),
        "degree_plh" => __("Enter degree", "kc-lang"),
        "university_plh" => __("Enter university name", "kc-lang"),
        "API_key_plh" => __("Enter your API key", "kc-lang"),
        "API_secret_plh" => __("Enter your API secret", "kc-lang"),
        "dt_lbl_sr" => __("Sr.", "kc-lang"),
        "dt_lbl_name" => __("Name", "kc-lang"),
        "dt_name_filter_plh" => __("Filter doctor by name", "kc-lang"),
        "dt_lbl_clinic_name" => __("Clinic", "kc-lang"),
        "dt_lbl_email" => __("Email", "kc-lang"),
        "dt_email_fltr_plh" => __("Filter doctor by email", "kc-lang"),
        "dt_lbl_mobile_number" => __("Mobile", "kc-lang"),
        "dt_mobile_fltr_number" => __("Filter doctor by mobile no", "kc-lang"),
        "dt_lbl_specialties" => __("Specialization", "kc-lang"),
        "dt_specialities_filter_plh" => __("Filter doctor by specialties", "kc-lang"),
        "dt_lbl_actions" => __("Action", "kc-lang"),
        "add_qualification" => __("Add Qualification", "kc-lang"),
        "plh_clinic_address" => __("Clinic address", "kc-lang"),
        "plh_enter_city" => __("Enter city", "kc-lang"),
        "plh_enter_state" => __("Enter state", "kc-lang"),
        "plh_enter_country_name" => __("Enter country name", "kc-lang"),
        "plh_enter_postal_code" => __("Enter postal code", "kc-lang"),
        "plh_enter_fees" => __("Enter fees", "kc-lang"),
        "plh_min_price_range" => __("Min price range", "kc-lang"),
        "plh_max_price_range" => __("Max price range", "kc-lang"),
        "plh_enter_degree" => __("Enter degree", "kc-lang"),
        "plh_enter_university" => __("Enter university name", "kc-lang"),
        "city_placeholder" => __("Enter city", "kc-lang"),
        "add_qualifiaction" => __("Add qualifiaction", "kc-lang"),
        "edit_qualification" => __("Edit qualification", "kc-lang"),
        "lbl_calender_not_connected" => __("Google Calender is not connected please", "kc-lang"),
        "lbl_important_note" => __("Important! Note", "kc-lang"),
        "lbl_sign_in" => __("Sign in", "kc-lang"),
        "lbl_sign_in_google" => __("Sign in with Google.", "kc-lang"),
        "save_qualification" => __("Save qualification", "kc-lang"),
        "no_doctor_found" => __("No Doctor Found", "kc-lang")
    ],
    "patient" => [
        "patient_name" => __(" Patient name", "kc-lang"),
        "first_name" => __(" first name", "kc-lang"),
        "last_name" => __(" last name", "kc-lang"),
        "email" => __(" email", "kc-lang"),
        "patient_contact" => __(" contact", "kc-lang"),
        "gender" => __(" gender", "kc-lang"),
        "add_patient" => __("Add patient", "kc-lang"),
        "patients_lists" => __("Patients", "kc-lang"),
        "medical_report" => __("Medical Report", "kc-lang"),
        "add_medical_report" => __("Add Medical Report", "kc-lang"),
        "edit_medical_report" => __("Edit Medical Report", "kc-lang"),
        "upload_report" => __("Upload Report", "kc-lang"),
        "select_clinic" => __("Select Clinic", "kc-lang"),
        "fname_plh" => __("Enter first name", "kc-lang"),
        "lname_placeholder" => __("Enter last name", "kc-lang"),
        "email_placeholder" => __("Enter email", "kc-lang"),
        "contact_placeholder" => __("Enter contact", "kc-lang"),
        "address_placeholder" => __("Enter address", "kc-lang"),
        "city_placeholder" => __("Enter city", "kc-lang"),
        "state_plh" => __("Enter state", "kc-lang"),
        "country_placeholder" => __("Enter country", "kc-lang"),
        "pcode_placeholder" => __("Enter pin code", "kc-lang"),
        "sr" => __("Sr.", "kc-lang"),
        "name" => __("Name", "kc-lang"),
        "name_placeholder" => __("Filter patient by name", "kc-lang"),
        "clinic" => __("Clinic", "kc-lang"),
        "clinic_placeholder" => __("Filter clinic name", "kc-lang"),
        "email" => __("Email", "kc-lang"),
        "filter_email_placeholder" => __("Filter patient by email", "kc-lang"),
        "contact" => __("Mobile No.", "kc-lang"),
        "filter_contact_placeholder" => __("Filter patient by contact", "kc-lang"),
        "blood" => __("Blood Group", "kc-lang"),
        "blood_placeholder" => __("ARALL", "kc-lang"),
        "reg_date" => __("Registered On.", "kc-lang"),
        "reg_date_placeholder" => __("Filter patient by Reg. date", "kc-lang"),
        "action" => __("Action", "kc-lang"),
        "search_placeholder" => __("Search", "kc-lang"),
        "save_btn" => __("Save", "kc-lang"),
        "tag_select_clinic_plh" => __("Select clinic", "kc-lang"),
        "welcome_date_plh" => __("welcome date", "kc-lang"),
        "tag_doc_sp_plh" => __("Doctor specialization", "kc-lang"),
        "add_sp_plh" => __("Add Specialization", "kc-lang"),
        "experience_plh" => __("Enter experience", "kc-lang"),
        "degree_plh" => __("Enter degree", "kc-lang"),
        "university_plh" => __("Enter university name", "kc-lang"),
        "API_key_plh" => __("Enter your API key", "kc-lang"),
        "API_secret_plh" => __("Enter your API secret", "kc-lang"),
        "edit_bill" => __("Edit Bill", "kc-lang"),
        "edit_patient" => __("Edit patient", "kc-lang"),
        "edit_profile" => __("Edit profile", "kc-lang"),
        "exports_CSV" => __("Export CSV", "kc-lang"),
        "exports_excel" => __("Export Excel", "kc-lang"),
        "lbl_patient_unique_id" => __("Patient Unique ID", "kc-lang"),
        "unique_id" => __("Unique ID", "kc-lang"),
        "lbl_postfix" => __("Postfix", "kc-lang"),
        "lbl_prefix" => __("Prefix", "kc-lang"),
        "patient_unique_setting" => __("Patient unique Id Setting", "kc-lang"),
        "print" => __("Print", "kc-lang"),
        "dt_plh_all" => __("All", "kc-lang"),
        "dt_lbl_sr" => __("Sr.", "kc-lang"),
        "dt_lbl_name" => __("Name", "kc-lang"),
        "dt_lbl_clinic_name" => __("Clinic Name", "kc-lang"),
        "dt_lbl_action" => __("Action", "kc-lang"),
        "dt_lbl_email" => __("Email", "kc-lang"),
        "dt_lbl_mobile_number" => __("Mobile", "kc-lang"),
        "dt_lbl_specialties" => __("Specialization", "kc-lang"),
        "dt_lbl_registered" => __("Registered ON", "kc-lang"),
        "dt_lbl_blood_group" => __("Blood Group", "kc-lang"),
        "dt_plh_name_filter" => __("Filter patient by name", "kc-lang"),
        "dt_plh_email_fltr" => __("Filter patient by email", "kc-lang"),
        "dt_plh_mobile_fltr_number" => __("Filter patient by mobile no", "kc-lang"),
        "dt_plh_date" => __("Filter Patient by Date", "kc-lang"),
        "dt_plh_specialities_filter" => __("Filter doctor by specialties", "kc-lang"),
        "plh_username" => __("Enter Username", "kc-lang"),
        "plh_pwd" => __("Enter Password", "kc-lang"),
        "plh_repeat_pwd" => __("Repeat Password", "kc-lang"),
        "plh_enter_report" => __("Enter report name", "kc-lang"),
        "plh_enter_title" => __("Enter title", "kc-lang"),
        "plh_enter_notes" => __("Enter notes", "kc-lang"),
        "clinic_check_out_in" => __("Check Out or Check In Clinic", "kc-lang"),
        "only_number_in_patient_unique_id" => __("Show only numbers in patient unique id", "kc-lang"),
        "nhs" => __("NHS number", "kc-lang"),
        "nhs_placeholder" => __("Enter NHS number", "kc-lang"),
    ],
    "receptionist" => [
        "fname_plh" => __("Enter first name", "kc-lang"),
        "name_plh" => __("Name", "kc-lang"),
        "save_btn" => __("Save", "kc-lang"),
        "lname_plh" => __("Enter last name", "kc-lang"),
        "email_plh" => __("Enter email", "kc-lang"),
        "first_name" => __(" first name", "kc-lang"),
        "last_name" => __(" last name", "kc-lang"),
        "email" => __(" email", "kc-lang"),
        "receptionist_contact" => __(" contact", "kc-lang"),
        "gender" => __(" gender", "kc-lang"),
        "contact_plh" => __("Enter contact number", "kc-lang"),
        "welcome_date_plh" => __("welcome date", "kc-lang"),
        "search_placeholder" => __("Search", "kc-lang"),
        "select_clinic" => __("Select Clinic", "kc-lang"),
        "address_plh" => __("Enter address", "kc-lang"),
        "country_plh" => __("Enter country name", "kc-lang"),
        "city_plh" => __("Enter city", "kc-lang"),
        "plh_enter_state" => __("Enter state", "kc-lang"),
        "pcode_plh" => __("Enter postal code", "kc-lang"),
        "dt_lbl_sr" => __("Sr.", "kc-lang"),
        "dt_lbl_name" => __("Name", "kc-lang"),
        "dt_lbl_clinic_name" => __("Clinic Name", "kc-lang"),
        "dt_lbl_email" => __("Email", "kc-lang"),
        "dt_lbl_mobile" => __("Mobile No", "kc-lang"),
        "dt_lbl_status" => __("Status", "kc-lang"),
        "dt_lbl_action" => __("Action", "kc-lang"),
        "dt_plh_name_fltr" => __("Filter receptionist by name", "kc-lang"),
        "dt_plh_email_fltr" => __("Filter receptionist by email", "kc-lang"),
        "dt_plh_mobilr_fltr" => __("Filter receptionist by mobile no", "kc-lang"),
        "dt_all" => __("All", "kc-lang"),
        "dt_active" => __("Active", "kc-lang"),
        "dt_inactive" => __("In Active", "kc-lang"),
        "plh_clinic_add" => __("Clinic address", "kc-lang"),
        "login_user_not_found" => __("Login user not found", "kc-lang"),
        "press_yes_to_delete_receptionist" => __("Press yes to delete receptionist", "kc-lang"),
        "resend_credential" => __("Resend credential", "kc-lang"),
        "upload_profile" => __("Upload Proifle", "kc-lang")
    ],
    "clinic" => [
        "clinic" => __("Clinic", "kc-lang"),
        "first_name" => __(" first name", "kc-lang"),
        "last_name" => __(" last name", "kc-lang"),
        "email" => __(" email", "kc-lang"),
        "clinic_contact" => __(" contact", "kc-lang"),
        "gender" => __(" gender", "kc-lang"),
        "receptionist" => __("Admin Staff", "kc-lang"),
        "receptionists_list" => __("Admin Staff", "kc-lang"),
        "add_receptionist" => __("Add Admin Staff", "kc-lang"),
        "clinic_name" => __("Clinic name", "kc-lang"),
        "clinic_info" => __("Clinic information", "kc-lang"),
        "clinic_profile" => __("Clinic Profile", "kc-lang"),
        "add_clinic" => __("Add clinic", "kc-lang"),
        "edit_clinic" => __("Edit clinic", "kc-lang"),
        "admin_profile" => __("Admin Profile", "kc-lang"),
        "clinic_admin_detail" => __("Clinic Admin Detail", "kc-lang"),
        "clinic_name_validation_1" => __("invalid clinic name format only allow alphabetic value", "kc-lang"),
        "clinic_name_validation_2" => __("Clinic name length must be between 2 to 35 characters", "kc-lang"),
        "select_clinic" => __("Select Clinic", "kc-lang"),
        "speciality" => __("Speciality", "kc-lang"),
        "specialties" => __("Specialties", "kc-lang"),
        "specialities" => __("Specialities", "kc-lang"),
        "note_specialization" => __("Note: Type and press enter to add new specialization", "kc-lang"),
        "clinic_specialities_required" => __("Clinic specialities is required", "kc-lang"),
        "product_country" => __("Product Country", "kc-lang"),
        "currency_prefix" => __("Currency prefix", "kc-lang"),
        "currency_postfix" => __("Currency postfix", "kc-lang"),
        "currency_decimals" => __("Currency decimals", "kc-lang"),
        "profile_img" => __("Profile image", "kc-lang"),
        "edit_profile_img" => __("Edit profile image", "kc-lang"),
        "doctor_record_not_found" => __("Doctor record not found ", "kc-lang"),
        "blood_group" => __("Blood group", "kc-lang"),
        "select_blood_group" => __("-- Select blood group --", "kc-lang"),
        "update_profile" => __("Update Profile", "kc-lang"),
        "clinic_list" => __("Clinics", "kc-lang"),
        "clinic_name_plh" => __("Enter clinic name", "kc-lang"),
        "email_plh" => __("Enter email address", "kc-lang"),
        "telephone_plh" => __("Enter telephone number", "kc-lang"),
        "search_placeholder" => __("Search", "kc-lang"),
        "clinic_sp_plh" => __("Clinic specialization", "kc-lang"),
        "product_country_plh" => __("Enter Product Country", "kc-lang"),
        "currency_prefix_plh" => __("Enter currency prefix", "kc-lang"),
        "currency_postfix_plh" => __("Enter currency postfix", "kc-lang"),
        "address_plh" => __("Enter address", "kc-lang"),
        "city_plh" => __("Enter city", "kc-lang"),
        "country_plh" => __("Enter country name", "kc-lang"),
        "pcode_plh" => __("Enter postal code", "kc-lang"),
        "fname_plh" => __("Enter first name", "kc-lang"),
        "welcome_date" => __("welcome date", "kc-lang"),
        "clinic_logo" => __("Clinic logo", "kc-lang"),
        "save_btn" => __("Save", "kc-lang"),
        "dt_lbl_contect" => __("Contact No", "kc-lang"),
        "dt_lbl_sr" => __("Sr.", "kc-lang"),
        "dt_lbl_name" => __("Name", "kc-lang"),
        "dt_lbl_status" => __("Status", "kc-lang"),
        "dt_plh_name_filter" => __("Filter clinic by name", "kc-lang"),
        "dt_lbl_clinic_name" => __("Clinic", "kc-lang"),
        "dt_lbl_email" => __("Email", "kc-lang"),
        "dt_plh_fltr_name" => __("Filter doctor by name", "kc-lang"),
        "dt_plh_fltr_contact" => __("Filter clinic by contact number", "kc-lang"),
        "dt_plh_fltr_specialitiy" => __("Filter clinic by specialties", "kc-lang"),
        "dt_lbl_registered_on" => __("Registered ON", "kc-lang"),
        "dt_lbl_mobile_number" => __("Mobile", "kc-lang"),
        "dt_lbl_specialties" => __("Specialization", "kc-lang"),
        "dt_lbl_action" => __("Action", "kc-lang"),
        "plh_email" => __("Enter email address", "kc-lang"),
        "plh_contact" => __("Enter contact number", "kc-lang"),
        "plh_clinic_specialization" => __("Clinic specialization", "kc-lang"),
        "plh_address" => __("Clinic address", "kc-lang"),
        "plh_country" => __("Enter country", "kc-lang"),
        "plh_city" => __("Enter city", "kc-lang"),
        "plh_pcode" => __("Enter postal code", "kc-lang"),
        "plh_currency_prefix" => __("Enter currency prefix", "kc-lang"),
        "plh_currency_postfix" => __("Enter currency postfix", "kc-lang"),
        "plh_currency_decimal" => __("currency decimals", "kc-lang"),
        "plh_select_decimal" => __("Select decimal", "kc-lang"),
        "plh_clinic_name" => __("Enter clinic name", "kc-lang"),
        "add_session_detail" => __("Add session details", "kc-lang"),
        "editholiday" => __("Edit holiday", "kc-lang"),
        "no_doctor_found" => __("No Doctor Found", "kc-lang"),
        "edit_session_detail" => __("Edit Session Detail", "kc-lang"),
        "save_session_detail" => __("Save Session Detail", "kc-lang"),
        "edit_clinic_Profile" => __("Edit Clinic Profile", "kc-lang"),
        "plh_record_not_found" => __("Record Not Found", "kc-lang"),
        "plh_clinic_not_found" => __("Clinic Not Found", "kc-lang"),
        "no_speciality_found" => __("No Speciality Found", "kc-lang"),
        "clinic_profile_updated_successfully" => __("Clinic profile updated successfully", "kc-lang"),
        "clinic_profile_not_updated_successfully" => __("Clinic profile not updated successfully", "kc-lang")
    ],
    "appointments" => [
        "paid" => __("Paid", "kc-lang"),
        "unpaid" => __("Unpaid", "kc-lang"),
        "pending" => __("Pending", "kc-lang"),
        "appointment" => __("Appointment", "kc-lang"),
        "appointments" => __("Appointments", "kc-lang"),
        "description" => __("Description", "kc-lang"),
        "booked" => __("Booked", "kc-lang"),
        "cancelled" => __("Cancelled", "kc-lang"),
        "arrived" => __("Arrived", "kc-lang"),
        "check_in" => __("Check in", "kc-lang"),
        "reschedule_appointment" => __("Reschedule Appointment", "kc-lang"),
        "reschedule" => __("Reschedule", "kc-lang"),
        "check_out" => __("Check out", "kc-lang"),
        "start" => __("Start", "kc-lang"),
        "join" => __("Join", "kc-lang"),
        "doc_required" => __("Doctor is required", "kc-lang"),
        "visit_type_required" => __("Visit type is required", "kc-lang"),
        "appointment_date" => __("Appointment Date", "kc-lang"),
        "appointment_date_required" => __("Appointment date is required", "kc-lang"),
        "select_status" => __("Select status", "kc-lang"),
        "status_required" => __("Status is required", "kc-lang"),
        "available_slot" => __("Available Slot", "kc-lang"),
        "session" => __("Session", "kc-lang"),
        "no_time_slots_found" => __("No time slots found", "kc-lang"),
        "time_slot_required" => __("Time Slot required", "kc-lang"),
        "appointment_details" => __("Appointment details", "kc-lang"),
        "appointment_type" => __("Appointment type", "kc-lang"),
        "completed" => __("Completed", "kc-lang"),
        "appointment_time" => __("Appointment Time", "kc-lang"),
        "appointment_time_required" => __(" Appointment time is required.", "kc-lang"),
        "book_appointment" => __("Book Appointment", "kc-lang"),
        "today_appointment" => __("Todays Appointment", "kc-lang"),
        "tomorrow_appointment" => __("Tomorrows Appointment", "kc-lang"),
        "appointment_booking" => __("Appointment Booking", "kc-lang"),
        "available_appointments_on" => __("Available Appointments On", "kc-lang"),
        "appointment_visit_type_required" => __("Appointment visit type is required.", "kc-lang"),
        "appointment_detail" => __("Appointment Detail", "kc-lang"),
        "save_appointment" => __("Save Appointment", "kc-lang"),
        "appointment_list" => __("Appointments", "kc-lang"),
        "add_review" => __("Add Review to doctor", "kc-lang"),
        "patient_review" => __("Patient rating", "kc-lang"),
        "ratings" => __("Ratings", "kc-lang"),
        "save_btn" => __("Save", "kc-lang"),
        "search_plh" => __("Search", "kc-lang"),
        "doctor_plh" => __("Select doctor", "kc-lang"),
        "tag_visit_type_plh" => __("Visit type", "kc-lang"),
        "tag_patient_type_plh" => __("Patient type", "kc-lang"),
        "patient_requires" => __("Patient is required", "kc-lang"),
        "appointment_desc_plh" => __("Enter appointment description", "kc-lang"),
        "tag_patient_plh" => __("Patient", "kc-lang"),
        "all" => __("all", "kc-lang"),
        "upcoming" => __("Upcoming", "kc-lang"),
        "past" => __("past", "kc-lang"),
        "tag_plh_session_doc" => __("Select session doctors", "kc-lang"),
        "tag_plh_appointment_type" => __("Select appointment type", "kc-lang"),
        "plh_enter_something" => __("Enter something", "kc-lang"),
        "plh_doc_name" => __("DOCTOR NAME", "kc-lang"),
        "plh_patient_name" => __("PATIENT NAME", "kc-lang"),
        "plh_date" => __("Date", "kc-lang"),
        "plh_patient" => __("Patient", "kc-lang"),
        "plh_status" => __("Status", "kc-lang"),
        "dt_lbl_sr" => __("Sr.", "kc-lang"),
        "dt_lbl_data" => __("Date", "kc-lang"),
        "dt_lbl_time" => __("Time", "kc-lang"),
        "dt_lbl_paient_name" => __("Patient Name", "kc-lang"),
        "dt_lbl_doc_name" => __("Doctor Name", "kc-lang"),
        "dt_lbl_status" => __("Status", "kc-lang"),
        "dt_lbl_visi_type" => __("Visit Type", "kc-lang"),
        "dt_lbl_description" => __("Description", "kc-lang"),
        "dt_lbl_action" => __("Action", "kc-lang"),
        "add_appointment_btn" => __("Add appointment", "kc-lang"),
        "close_form_btn" => __("Close form", "kc-lang"),
        "appointment_date_plh" => __("Appointment Date", "kc-lang"),
        "select_date" => __("Select Date", "kc-lang"),
        "select_clinic" => __("Select Clinic", "kc-lang"),
        "select_patient" => __("Select Patient", "kc-lang"),
        "restrict_appointment" => __("Appointment Setting", "kc-lang"),
        "booking_restriction" => __("Booking Restriction", "kc-lang"),
        "restrict_appointment_detail" => __("Restrict Advance Appointment Booking", "kc-lang"),
        "appointment_cancellation_buffer" => __("Appointment Cancellation Buffer", "kc-lang"),
        "appointment_cancellation_buffer_note" => __("To prevent appointments from getting canceled too close to the appointment time by patients, you can set a cancellation buffer.", "kc-lang"),
        "enable_cancellation_buffer_lbl" => __("Enable Cancellation Buffer", "kc-lang"),
        "notice_of_cancellation_buffer" => __("Select Hours", "kc-lang"),
        "pre_appointment" => __("Booking Close Before (in Days)", "kc-lang"),
        "post_appointment" => __("Booking Open Before (in Days)", "kc-lang"),
        "pre_book_are_you_sure" => __("Are you sure want save settings?", "kc-lang"),
        "pre_appointment_required" => __("Pre Appointment Restriction Days is Required", "kc-lang"),
        "post_appointment_required" => __("Post Appointment Restriction Days is Required", "kc-lang"),
        "pre_appointment_length" => __("Pre Appointment Restriction Days must be greater than zero and less than 365 days", "kc-lang"),
        "post_appointment_length" => __("Post Appointment Restriction Days must be greater than zero and less than 365 days", "kc-lang"),
        "multi_file_upload" => __("Appointment File Upload Setting", "kc-lang"),
        "appointment_multi_file_upload" => __("Appointment File Upload", "kc-lang"),
        "appointment_daily_reminder" => __("Appointment Reminder", "kc-lang"),
        "appointment_email_reminder" => __("Email Reminder", "kc-lang"),
        "notice_of_appointment_reminder" => __("Select Hours", "kc-lang"),
        "appointment_sms_reminder" => __("Sms Reminder (Twilio)", "kc-lang"),
        "appointment_whatsapp_reminder" => __("Whatsapp Reminder (Twilio)", "kc-lang"),
        "pre_post_note"  => __("For example, Booking Open Before: 60 days, Booking Close Before: 7 days, As consideration for the current date, The appointment booking opens 60 days ago and closed 7 days ago.  ", "kc-lang"),
        "file_uploading" => __("Medical Report Uploading......", "kc-lang"),
        "appointment_reminder_info" => __("cron job will run in every 2 minutes and select the appointment in next select hours (Example if you select/save 06:00  cron will job collect all appointment of current date + 6 hours ) and send email/sms/whatsapps accordings to setting notification to patient only once a day.", "kc-lang"),
        "post_day_must_be_greater_then_pre_day" => __("Appointment Restriction Post Days Must Be Greater Than Pre Day", "kc-lang"),
        "appointment_time_format" => __("Appointment Time Format", "kc-lang"),
        "appointment_time_24_format" => __("Format Appointment Time in 24 Hours Format", "kc-lang"),
        "disableDelete" => __("Disable multiple delete", "kc-lang"),
        "enableDelete" => __("Enable multiple delete", "kc-lang"),
        "deleteSelectedAppointment" => __("Delete selected appointment", "kc-lang"),
        "appointment_description_notes" => __("Appointment Setting"),
        "appointment_description" => __("Appointment Description"),
        "show_patient_information" => __("Show Patient Info While Save Appointment."),
        "start_video_call" =>  __("Start video Call", "kc-lang"),
        "join_video_call" =>  __("Join video Call", "kc-lang"),
        "resend_video_conference_link" =>  __("Resend Video Conference link", "kc-lang"),
        "view_report" =>  __("view report", "kc-lang"),
        "encounter_template" => __("Select Consultation Templates", "kc-lang"),
        "same_day_booking_only_lbl" => __("Allow Same Day Booking Only", "kc-lang")
    ],
    "clinic_schedule" => [
        "schedule" => __("Schedule", "kc-lang"),
        "holiday_of" => __("Absence of", "kc-lang"),
        "module_type_required" => __("Module type is required", "kc-lang"),
        "schedule_date" => __("Schedule date", "kc-lang"),
        "schedule_date_required" => __("Schedule date is required", "kc-lang"),
        "holiday_list" => __("Absences", "kc-lang"),
        "tag_module_type_plh" => __("Select module type", "kc-lang"),
        "select_modulr_plh" => __("Select module", "kc-lang"),
        "select_schedule_date_plh" => __("Select Schedule date", "kc-lang"),
        "tag_doctors_plh" => __("doctors", "kc-lang"),
        "select_doc_plh" => __("Select Doctor", "kc-lang"),
        "dt_lbl_sr" => __("Sr.", "kc-lang"),
        "dt_lbl_schedul_of" => __("Schedule Of", "kc-lang"),
        "dt_plh_fltr_by_schedule" => __("Filter Absence by Schedule", "kc-lang"),
        "dt_lbl_name" => __("Name", "kc-lang"),
        "dt_plh_fltr_by_doc" => __("Filter Absence by doctor", "kc-lang"),
        "dt_lbl_from_date" => __("From Date", "kc-lang"),
        "dt_plh_fltr_by_date" => __("Filter Absence by start date", "kc-lang"),
        "dt_lbl_to_date" => __("To Date", "kc-lang"),
        "dt_plh_fltr_by_end_date" => __("Filter Absence by end date", "kc-lang"),
        "dt_lbl_action" => __("Action", "kc-lang"),
        "dt_srvr_err" => __("Internal server error", "kc-lang"),
        "dt_are_you_sure" => __("Are you sure ?", "kc-lang"),
        "dt_press_dlt" => __("Press yes to delete holiday", "kc-lang"),
        "dt_holiday_list" => __("Absences", "kc-lang"),
        "dt_lbl_dlt" => __("Delete", "kc-lang"),
        "dt_lbl_edit" => __("Edit", "kc-lang"),
        "dt_lbl_print" => __("Print", "kc-lang"),
        "dt_export_excel" => __("Export Excel", "kc-lang"),
        "dt_export_csv" => __("Export CSV", "kc-lang"),
        "clinic" => __("Clinic", "kc-lang"),
        "add_holiday_btn" => __("Add Absence", "kc-lang"),
        "close_form_btn" => __("close form", "kc-lang"),
        "dt_current_appointment_session" => __("Delete Current Appointment Session", "kc-lang"),
        "schedule_not_found" => __("Schedule not found", "kc-lang"),
        "dt_delete_doctor_appointment" => __("This action may delete your doctor's appointments, sessions and holidays.", "kc-lang")
    ],
    "doctor_session" => [
        "doc_sessions" => __("Availability", "kc-lang"),
        "session_doc_required" => __("Session doctor is required", "kc-lang"),
        "doc_already_added" => __("Selected Doctor is already added in another session", "kc-lang"),
        "week_days" => __("Week days", "kc-lang"),
        "days_required" => __("Days is required", "kc-lang"),
        "days_already_exist" => __("Selected days already exist in the other session", "kc-lang"),
        "morning_session" => __("Morning session", "kc-lang"),
        "start_time_required" => __("Start time is required", "kc-lang"),
        "start_time_smaller_then_end" => __("Start time must be smaller than end time", "kc-lang"),
        "end_time_required" => __("End time is required", "kc-lang"),
        "end_time_bigger_then_start" => __("End time must be bigger than start time", "kc-lang"),
        "evening_session" => __("Evening session", "kc-lang"),
        "start_time_smaller_then_first_session_end_time" => __("Start time must be smaller than first sessions end time", "kc-lang"),
        "set_session_for_doc" => __("This tab helps you to set sessions for individual Doctors", "kc-lang"),
        "plh_search" => __("Search", "kc-lang"),
        "plh_tag_session_doc" => __("Select session doctors", "kc-lang"),
        "plh_tag_clinic" => __("Select clinic", "kc-lang"),
        "plh_start_time" => __("Start time", "kc-lang"),
        "plh_end_time" => __("End time", "kc-lang"),
        "dt_plh_fltr_by_doc" => __("Filter doctor session by name", "kc-lang"),
        "dt_lbl_sr" => __("Sr.", "kc-lang"),
        "dt_lbl_doc" => __("Doctor", "kc-lang"),
        "dt_lbl_clinic" => __("Clinic Name", "kc-lang"),
        "dt_lbl_days" => __("Days", "kc-lang"),
        "dt_lbl_morning_session" => __("Morning Session", "kc-lang"),
        "dt_lbl_evening_session" => __("Evening Session", "kc-lang"),
        "dt_lblaction" => __("Action", "kc-lang"),
        "add_session_btn" => __("Add Availability", "kc-lang"),
        "close_form_btn" => __("close form", "kc-lang"),
        "save_btn" => __("Save Session", "kc-lang"),
        "clinic_session_list" => __("Clinic Sessions", "kc-lang"),
        "doctor_session_not_saved_successfully" => __("Doctor session not saved successfully", "kc-lang"),
        "doctor_session_saved_successfully" => __("Doctor session saved successfully", "kc-lang"),
        "edit_session" => __("Edit session", "kc-lang"),
        "export_CSV" => __("Export CSV", "kc-lang"),
        "export_excel" => __("Export Excel", "kc-lang"),
        "no_speciality_found" => __("No speciality Found", "kc-lang"),
        "save_session" => __("Save session", "kc-lang"),
        "clinic_profile_data_not_found" => __("Clinic Profile Data Not Found", "kc-lang")
    ],
    "patient_encounter" => [
        "patient_extra_details" => __("Patient extra details", ""),
        "send_pre_mail" => __("Email To Patient", "kc-lang"),
        "encounters" => __("Consultations", "kc-lang"),
        "encounters_list" => __("Consultations", "kc-lang"),
        "encounter_dashboard" => __("Patient dashboard", "kc-lang"),
        "is_required" => __("is required", "kc-lang"),
        "note_prescription" => __("Note: Type and press enter to create new prescription", "kc-lang"),
        "note_problem" => __("Note: Type and press enter to create new problem", "kc-lang"),
        "note_observation" => __("Note: Type and press enter to create new observation", "kc-lang"),
        "select_problem" => __("Select Problem", "kc-lang"),
        "select_observation" => __("Select Observation", "kc-lang"),
        "frequency" => __("Frequency", "kc-lang"),
        "frequency_required" => __("Frequency is required", "kc-lang"),
        "duration_Days" => __("Duration (In Days)", "kc-lang"),
        "duration_required" => __("Duration is required", "kc-lang"),
        "instruction" => __("Instruction", "kc-lang"),
        "duration" => __("Duration", "kc-lang"),
        "no_prescription_found" => __("No prescription found", "kc-lang"),
        "no_patient_report_found" => __("No patient report found", "kc-lang"),
        "add_prescription" => __("Add prescription", "kc-lang"),
        "encounter_date" => __("Consultation Date", "kc-lang"),
        "encounter_date_required" => __("Consultation date is required", "kc-lang"),
        "encounter_module" => __("Consultation Module", "kc-lang"),
        "prescription" => __("Prescription", "kc-lang"),
        "encounter_details" => __("Consultation details", "kc-lang"),
        "detail_placeholder" => __("placeholder", "kc-lang"),
        "save_btn" => __("Save", "kc-lang"),
        "proceed_btn" => __("Proceed", "kc-lang"),
        "tag_select_clinic" => __("Select clinic", "kc-lang"),
        "search_plh" => __("Search", "kc-lang"),
        "tag_select_doctor" => __("Select doctor", "kc-lang"),
        "tag_patient_type_plh" => __("Patient type", "kc-lang"),
        "tag_name_plh" => __("Name", "kc-lang"),
        "dt_lbl_sr" => __("Sr.", "kc-lang"),
        "dt_lbl_doc_name" => __("Doctor Name", "kc-lang"),
        "dt_plh_fltr_by_doc" => __("Filter Consultation by doctor", "kc-lang"),
        "dt_lbl_clinic" => __("Clinic Name", "kc-lang"),
        "dt_plh_fltr_by_clinic" => __("Filter Consultation by clinic name", "kc-lang"),
        "dt_lbl_patient" => __("Patient Name", "kc-lang"),
        "dt_plh_fltr_patient" => __("Filter Consultation by patient name", "kc-lang"),
        "dt_lbl_name" => __("Date", "kc-lang"),
        "dt_plh_fltr_date" => __("Filter Consultation by date", "kc-lang"),
        "dt_lbl_action" => __("Action", "kc-lang"),
        "edit_prescription" => __("Edit prescription", "kc-lang"),
        "lname_plh" => __("Enter last name", "kc-lang"),
        "encounter_close_note" => __("Note: To close the consultation, invoice payment is mandatory", "kc-lang"),
        "doctor_signature" => __("Doctor Signature:", "kc-lang"),
        "ecounter_template" => __("Select Consultation Template", "kc-lang")
    ],
    "patient_encounter_template" => [
        "dt_lbl_name" => __("Template Name", "kc-lang"),
        "dt_plh_fltr_name" => __("Filter Template Name by name", "kc-lang"),
        "encounter_template" => __("Consultation Template", "kc-lang")
    ],
    "medical_records" => [
        "problem_type" => __("Problem type", "kc-lang"),
        "problem_start_date_required" => __("Problem start date is required", "kc-lang"),
        "problem_start_date" => __("Problem start date", "kc-lang"),
        "problem_end_date" => __("Problem end date", "kc-lang"),
        "problem_outcome" => __("Problem outcome", "kc-lang"),
        "medical_records" => __("Medical records", "kc-lang"),
        "add_medical_problems" => __("Add medical problems", "kc-lang"),
        "plh_problem_type" => __("Select problem type", "kc-lang"),
        "plh_search" => __("Search", "kc-lang"),
        "plh_problem_outcome" => __("Select problem outcome", "kc-lang"),
        "edit_medical_record" => __("Edit medical record", "kc-lang"),
        "lbl_action" => __("START DATE", "kc-lang"),
        "lbl_date" => __("DATE", "kc-lang"),
        "lbl_desc" => __("DESCRIPTION", "kc-lang"),
        "lbl_end_date" => __("END DATE", "kc-lang"),
        "lbl_outcome" => __("OUTCOME", "kc-lang"),
        "lbl_problem_type" => __("PROBLEM TYPE", "kc-lang"),
        "lbl_sr_no" => __("Sr. NO", "kc-lang"),
        "lbl_start_date" => __("START DATE", "kc-lang"),
        "medical_record_not_found" => __("Medical record not found", "kc-lang"),
        "plh_medical_desc" => __("Enter Medical Record description", "kc-lang")
    ],
    "reports" => [
        "reports" => __("Reports", "kc-lang"),
        "filter_by" => __("Filter By", "kc-lang"),
        "clinic_revenue_overall" => __("Clinic Revenue (Overall)", "kc-lang"),
        "clinic_revenue_detail" => __("Clinic Revenue (Detail)", "kc-lang"),
        "clinic_doctor_revenue" => __("Clinic Doctor Revenue", "kc-lang"),
        "prescription_module" => __("Prescription Module", "kc-lang"),
        "report_required" => __("Report is required.", "kc-lang"),
        "tag_plh_select_clinic" => __("Select clinic", "kc-lang"),
        "plh_search" => __("Search", "kc-lang"),
        "plh_flter_by" => __("Select Filter by", "kc-lang"),
        "plh_select" => __("Select", "kc-lang")
    ],
    "patient_front_widget" => [
        "specialization" => __("Specialization", "kc-lang"),
        "username_email" => __("Username or Email", "kc-lang"),
        "fill_form" => __("Please fill in this form to create an account.", "kc-lang")
    ],
    "service" => [
        "service_list" => __("Services", "kc-lang"),
        "service_category" => __("Service category", "kc-lang"),
        "service_category_required" => __("Service category is required", "kc-lang"),
        "note_category" => __("Type and press enter to add new category", "kc-lang"),
        "note_telemed" => __("Doctor is telemed not enabled", "kc-lang"),
        "category" => __(" category", "kc-lang"),
        "name" => __(" name", "kc-lang"),
        "charges" => __(" charges", "kc-lang"),
        "doctor" => __(" doctor", "kc-lang"),
        "service_name" => __("Service Name", "kc-lang"),
        "service_name_required" => __("Service name is required", "kc-lang"),
        "service_validation" => __("Service name length should be between 2 to 100 character", "kc-lang"),
        "charges" => __("Charges", "kc-lang"),
        "service_charge" => __("service charge from service module. ", "kc-lang"),
        "service_charges_required" => __("Service charges is required", "kc-lang"),
        "service_charge_length" => __("Service charges should be between 0 to 10000000000", "kc-lang"),
        "select_all" => __("Select all", "kc-lang"),
        "save_btn" => __("Save", "kc-lang"),
        "tag_select_service_plh" => __("Select service category", "kc-lang"),
        "select_service_plh" => __("Select service category", "kc-lang"),
        "service_name_plh" => __("Enter service name", "kc-lang"),
        "charges_plh" => __("Enter charges", "kc-lang"),
        "tag_select_doc_plh" => __("Select doctor", "kc-lang"),
        "select_doc_plh" => __("Select doctor", "kc-lang"),
        "select_status_plh" => __(" Select status", "kc-lang"),
        "dt_lbl_sr" => __(" Sr.", "kc-lang"),
        "dt_lbl_name" => __(" Name", "kc-lang"),
        "dt_lbl_clinic_name" => __(" Clinic Name", "kc-lang"),
        "dt_lbl_email" => __("Email", "kc-lang"),
        "dt_lbl_mobile" => __("Mobile No", "kc-lang"),
        "dt_lbl_status" => __("Status", "kc-lang"),
        "dt_lbl_action" => __("Action", "kc-lang"),
        "dt_plh_name_fltr" => __("Filter service by name", "kc-lang"),
        "dt_plh_fltr_by_doc" => __("Filter service by doctor", "kc-lang"),
        "dt_plh_fltr_by_price" => __("Filter Service by price", "kc-lang"),
        "dt_all" => __("All", "kc-lang"),
        "dt_active" => __("Active", "kc-lang"),
        "dt_inactive" => __("InActive", "kc-lang"),
        "dt_lbl_charges" => __("Charges", "kc-lang"),
        "dt_lbl_doctor" => __("Doctor", "kc-lang"),
        "dt_lbl_category" => __("Category", "kc-lang"),
        "add_service_btn" => __("Add Service", "kc-lang"),
        "close_form_btn" => __("close form", "kc-lang"),
        "is_telemed_service" => __("Is this a telemed service ?", "kc-lang"),
        "service_duration" => __("Service Duration", "kc-lang"),
        "telemed_service_required" => __("Telemed service is required", "kc-lang"),
    ],
    "patient_bill" => [
        "paid"=> "Paid",
        "unpaid"=> "Unpaid",
        "mark_as_paid"=> "Mark as paid",
        "mark_as_unpaid"=> "Mark as unpaid",
        "payment_status_updated"=> "Payment status updated successfully",
        "payment_status_update_failed"=> "Failed to update payment status",
        "invoice_id" => __("Invoice id", "kc-lang"),
        "created_at" => __("Created at", "kc-lang"),
        "payment_status" => __("Payment Status", "kc-lang"),
        "paid" => __("Paid", "kc-lang"),
        "unpaid" => __("Unpaid", "kc-lang"),
        "patient_details" => __("Patient details", "kc-lang"),
        "amount_due" => __("Amount due", "kc-lang"),
        "print" => __("Print", "kc-lang"),
        "send_to_patient_lbl" => __("Send to patient", "kc-lang"),
        "service_required" => __("Service is required", "kc-lang"),
        "price_required" => __("Price is required", "kc-lang"),
        "prize_greater_then_0" => __("Price must be greater than or equal to zero", "kc-lang"),
        "prize_between_number" => __("Price must be between 0 to 1000000000000000000", "kc-lang"),
        "quantity_required" => __("Quantity is required", "kc-lang"),
        "please_add_bill_items" => __("Please add bill items", "kc-lang"),
        "payment_link" => __("payment link", "kc-lang"),
        "bill_total_required" => __("Bill total is required", "kc-lang"),
        "discount" => __("Discount", "kc-lang"),
        "discount_amount" => __("Discount in amount", "kc-lang"),
        "discount_required" => __("Discount is required", "kc-lang"),
        "discount_greater_then_0" => __("Discount must be greater than zero", "kc-lang"),
        "discount_less_then_total_bill_amount" => __("Discount must be less than total bill amount", "kc-lang"),
        "payable_amount" => __("Payable Amount", "kc-lang"),
        "bill_title" => __("Bill title", "kc-lang"),
        "bill_title_required" => __("Bill title is required", "kc-lang"),
        "bill_items" => __("Bill items", "kc-lang"),
        "grand_total" => __("Grand total", "kc-lang"),
        "grand_total_required" => __("Grand total is required", "kc-lang"),
        "print_bill" => __("Print bill", "kc-lang"),
        "billing_records" => __("Billing records", "kc-lang"),
        "add_bill" => __("Add bill", "kc-lang"),
        "add_new_bill" => __("Add new bill", "kc-lang"),
        "patient_required" => __("Patient is required", "kc-lang"),
        "encounter_close" => __("Close Consultation", "kc-lang"),
        // "encounter_close_checkout" => __("Close", "kc-lang"),
        "encounter_close_checkout" => __("Close & Checkout", "kc-lang"),

        "bill_details" => __("Generate Invoice", "kc-lang"),
        "other_info" => __("Other information", "kc-lang"),
        "patients_encounter_list" => __("Patient Consultations", "kc-lang"),
        "patients_encounter_list_is_Empty" => __("Patient Consultations Is Empty", "kc-lang"),
        "encounter_template_list" => __("Consultation Templates", "kc-lang"),
        "add_encounter_template" => __("Add Consultations Template", "kc-lang"),
        "encounter_template_name_required" => __("Consultations Template Name Required", "kc-lang"),
        "bills" => __("Bills", "kc-lang"),
        "payment_setting" => __("Payment Setting", "kc-lang"),
        "woocommerce_payment_notice" => __("Note: If you enable Woocommerce payment. This action may redirect appointments for payment on the default woocommerce cart page with selected appointment services. The appointment will be canceled automatically in case of an unsuccessful payment. (woocommerce redirection is for the patient role only)", "kc-lang"),
        "woocommerce_payment_gateway" => __("Woocommerce Payment Gateway", "kc-lang"),
        "amount" => __("Amount", "kc-lang"),
        "items" => __("Items", "kc-lang"),
        "notes" => __("Notes", "kc-lang"),
        "invoice_n_payment" => __("Services will be used for invoicing and other future payment related implementations", "kc-lang"),
        "currency" => __("Set currency prefix, postfix, and decimals points.", "kc-lang"),
        "tag_plh_service" => __("Select service", "kc-lang"),
        "plh_service" => __("Services", "kc-lang"),
        "plh_price" => __("Price", "kc-lang"),
        "plh_quality" => __("Quantity", "kc-lang"),
        "plh_select_service" => __("Select service", "kc-lang"),
        "plh_services" => __("Services", "kc-lang"),
        "tag_plh_status" => __("Select status", "kc-lang"),
        "plh_status" => __("Status", "kc-lang"),
        "plh_total_amount" => __("Enter total_amount", "kc-lang"),
        "plh_discount" => __("Enter discount", "kc-lang"),
        "plh_enter_title" => __("Enter title", "kc-lang"),
        "add_item" => __("Add item", "kc-lang"),
        "bill_add_item" => __("Add Bill Item", "kc-lang"),
        "bill_close" => __("Close Form", "kc-lang"),
        "lbl_action" => __("Action", "kc-lang"),
        "lbl_actual_amount" => __("ACTUAL AMOUNT", "kc-lang"),
        "lbl_date" => __("DATE", "kc-lang"),
        "lbl_discount" => __("DISCOUNT", "kc-lang"),
        "lbl_sr_no" => __("Sr. NO", "kc-lang"),
        "lbl_status" => __("Status", "kc-lang"),
        "lbl_title" => __("Title", "kc-lang"),
        "lbl_total_amount" => __("Total AMOUNT", "kc-lang"),
        "plh_search" => __("Search", "kc-lang"),
        "payment_or_bill_item_error" => __("Please Enable Payment Gateway And Add Items in Bills", "kc-lang"),
        "generate_invoice" => __("Generate invoice", "kc-lang"),
        "invoice_detail" => __("Invoice detail", "kc-lang"),
        "local_payment_gateway" => __("Local Payment", "kc-lang"),
        "generate_bill" => __("Generate new bill", "kc-lang"),
        "no_encounter_found_for_billing" => __("No consultations found for billing", "kc-lang"),
    ],
    "settings" => [
        "general" => __("General", "kc-lang"),
        "holidays" => __("Absences", "kc-lang"),
        "configurations" => __("Configurations", "kc-lang"),
        "app_config" => __("App Configurations", "kc-lang"),
        "one_signal_app_notification" => __("One Signal App Configuration (Deprecated)", "kc-lang"),
        "firebase_app_config" => __("Firebase Cloud Messeging Configuration", "kc-lang"),

        "app_id" => __("App ID", "kc-lang"),
        "app_id_placeholder" => __("Enter App ID", "kc-lang"),
        "api_key" => __("API Key", "kc-lang"),
        "api_key_placeholder" => __("Enter API Key", "kc-lang"),

        "email_template" => __("Email Template", "kc-lang"),
        "sms_template" => __("SMS/WhatsApp Template", "kc-lang"),
        "listings" => __("Listings", "kc-lang"),
        "custom_field" => __("Custom Field", "kc-lang"),
        "payment" => __("Payment", "kc-lang"),
        "new_setting" => __("New refined settings with various settings like email, invoice, currency, etc.", "kc-lang"),
        "pro_settings" => __(" Pro Settings ", "kc-lang"),
        "permission_setting" => __(" Permission Setting ", "kc-lang"),
        "language_settings" => __("Language Settings", "kc-lang"),
        "tag_plh_option" => __("Select Option", "kc-lang"),
        "plh_search" => __("Search", "kc-lang"),
        "plh_enter_acc_sid" => __("Enter your ACCOUNT SID", "kc-lang"),
        "plh_auth_token" => __("Enter your AUTH TOKEN", "kc-lang"),
        "plh_enter_number" => __("Enter your to number", "kc-lang"),
        "patient_setting" => __("Patient Setting", "kc-lang"),
        "dynamic_keys_list" => __("Template Dynamic Keys List(click on button to copy)", "kc-lang"),
        "click_to_copy" => __("Click To Copy", "kc-lang"),
        "copied" => __("Key Copied", "kc-lang"),
        "lbl_email_subject" => __("Email Subject", "kc-lang"),
        "app" => [
            'serverKey' => __('Firebase Server Key', 'kc-lang'),
            'client_emaail' => __('Client Email' , 'kc-lang'),
            'privat_key' => __('Private Key' , 'kc-lang'),
            'project_id' => __('Project Id' , 'kc-lang'),
        ],
        "content_sid" => __("Content SID", "kc-lang"),
        "fetch_twilio_template" => __("Fatch Twillio Template", "kc-lang"),
    ],
    "pro_setting" => [
        "theme_setting" => __("Theme Settings", "kc-lang"),
        "set_site_logo" => __("Site Logo", "kc-lang"),
        "set_site_loader" => __("Site Loader", "kc-lang"),
        "set_language" => __("Language", "kc-lang"),
        "set_theme_color" => __("Theme color", "kc-lang"),
        "rtl_mode" => __("RTL Mode", "kc-lang"),
        "on" => __("on", "kc-lang"),
        "twilo_sms_configration" => __("SMS Configuration", "kc-lang"),
        "twilo_whatsapp_configration" => __("WhatsApp Configuration", "kc-lang"),
        "account_sid" => __("ACCOUNT SID", "kc-lang"),
        "auth_token" => __("AUTH TOKEN", "kc-lang"),
        "phone_number" => __("PHONE NUMBER", "kc-lang"),
        "twilio_account_setting" => __("Twilio Account Settings", "kc-lang"),
        "twilo_sms_guide" => __("Twilo SMS guide", "kc-lang"),
        "twilo_whatsapp_guide" => __("Twilo Whatsapp guide", "kc-lang"),
        "twilio_step_1" => __("Step 1:  You can sign up for a free Twilio trial account here", "kc-lang"),
        "twilo_sms_portal" => __("Twilo SMS portal", "kc-lang"),
        "twilio_step_2" => __("Step 2: To get the Twilio CLI connected to your account. Visit", "kc-lang"),
        "get_console" => __("get console", "kc-lang"),
        "unique_sid" => __("and you’ll find your unique Account SID and Auth Token to provide to the CLI.", "kc-lang"),
        "twilio_step_3" => __("Step 3: Copy and Paste ACCOUNT SID  and AUTH TOKEN and click on save button and here you go.", "kc-lang"),
        "twilio_step_4" => __("Step 4 (Optional): To get your first Twilio phone number for sending sms. Visit", "kc-lang"),
        "twillo_imp_note" => __('Important Note: Reciever(doctor/patient) Phone/contact No must be in twillo specific format ([+] [country code] [mobile number] )', 'kc-lang'),
        "twillo_help_note" => __('Please Refer here for more details', 'kc-lang'),
        "head_on_console" => __("head on over to the console", "kc-lang"),
        "phone_msg_sid" => __("and you will get phone number to send SMS if you dont want any particular number to send message use your SID", "kc-lang"),
        "add_new_langauge" => __("Add New Langauge", "kc-lang"),
        "translate" => __("Translate", "kc-lang"),
        "custom_langauge_translation" => __("Custom Langauge Translation", "kc-lang"),
        "translating" => __("translating...", "kc-lang"),
        "select_color" => __("Select Color", "kc-lang"),
        "google_account_setting" => __("Google Account Settings", "kc-lang"),
        "connect_with_google" => __("Connect with google", "kc-lang"),
        "connect_with_zoom" => __("Connect with", "kc-lang"),
        "please_refer_link" => __("Please refer the following link for the setup.", "kc-lang"),
        "plh_select_lang" => __("Select Language", "kc-lang"),
        "clinical_detail" => __("Include Consultations Clinical Details in Prescription print", "kc-lang"),
        "clinical_detail_patient_hide" => __("Hide Consultations Clinical Details To Patient", "kc-lang"),
        "include_encounter_custom_fields_in_print" => __("Include Consultations custom fields in Prescription print", "kc-lang"),
        'copy_right_text' => __("Copyright Text", "kc-lang"),
        "change_copy_right_text" => __("Change Copyright Text", "kc-lang"),

    ],
    "custom_field" => [
        "label_name_required" => __("Label name is required", "kc-lang"),
        "label_name_validation" => __("Label name allows only alphabetic value", "kc-lang"),
        "where_it_look_like" => __("Where it looks like", "kc-lang"),
        "shows_in_doc_creation_form" => __("It shows in doctor creation form", "kc-lang"),
        "shows_in_patient_encounter_dashboard" => __("It shows in patient consultations dashboard", "kc-lang"),
        "shows_in_patient_creation_form" => __("It shows in Appointment dashboard", "kc-lang"),
        "shows_in_appointment_module" => __("It shows in patient creation form", "kc-lang"),
        "filed_name" => __("Field name", "kc-lang"),
        "invalid_label_name" => __("Invalid label name", "kc-lang"),
        "label_required" => __("Label is required", "kc-lang"),
        "field_name_used" => __("Field name is already used.", "kc-lang"),
        "input_type" => __("Input Type", "kc-lang"),
        "input_type_required" => __("Input type is required", "kc-lang"),
        "placeholder" => __("Placeholder", "kc-lang"),
        "options" => __("Options", "kc-lang"),
        "validation" => __("Validation", "kc-lang"),
        "mandatory_field" => __("Mandatory field", "kc-lang"),
        "custom_field_list" => __("Custom Fields", "kc-lang"),
        "add_custom_field" => __("Add custom field", "kc-lang"),
        "tag_module_plh" => __("Module", "kc-lang"),
        "select_module_plh" => __("Select module", "kc-lang"),
        "doctors_plh" => __("Doctors", "kc-lang"),
        "tag_doctors_plh" => __("Select Doctor", "kc-lang"),
        "field_label_plh" => __("Enter field label", "kc-lang"),
        "input_type_plh" => __("Select input type", "kc-lang"),
        "placeholder_plh" => __("Enter placeholder", "kc-lang"),
        "tag_add_new_option_plh" => __("Add this as new option", "kc-lang"),
        "serach_plh" => __("Search or add a option", "kc-lang"),
        "status_plh" => __("Select status", "kc-lang"),
        "dt_lbl_sr" => __("Sr.", "kc-lang"),
        "dt_lbl_field" => __("Field", "kc-lang"),
        "dt_plh_fltr_by_name" => __("Filter custom field by name", "kc-lang"),
        "dt_lbl_type" => __("Type", "kc-lang"),
        "dt_plh_fltr_by_type" => __("Filter custom field by type", "kc-lang"),
        "dt_lbl_action" => __("Action", "kc-lang"),
        "add_field" => __("Add field", "kc-lang"),
        "add_new_field" => __("Add new field", "kc-lang"),
        "are_you_sure" => __("Are you sure ?", "kc-lang"),
        "doctor_profile_data_not_found" => __("Doctor profile data not found", "kc-lang"),
        "edit_custom_field" => __("Edit custom field", "kc-lang"),
        "edit_field" => __("Edit field", "kc-lang"),
        "exports_CSV" => __("Export CSV", "kc-lang"),
        "exports_excel" => __("Export Excel", "kc-lang"),
        "press_yes_to_delete" => __("Press yes to delete", "kc-lang"),
        "prints" => __("Print", "kc-lang"),
        "record_not_found" => __("Record not found", "kc-lang"),
        "save_field" => __("Save field", "kc-lang"),
        "label" => __("Label", "kc-lang")
    ],
    "encounter_dashboard" => [
        "problems" => __("Problems", "kc-lang"),
        "observation" => __("Observations", "kc-lang"),
        "notes" => __("Notes", "kc-lang"),
        "add_btn" => __("Add", "kc-lang"),
        "add_prescription_btn" => __("Add Prescription", "kc-lang"),
        "presciption_save_btn" => __("Save", "kc-lang"),
        "title" => __("Clinical Detail", "kc-lang"),
        "close_form" => __("Close Form", "kc-lang"),
        "add_encounter" => __("Add Consultation", "kc-lang"),
        "template_name" => __("Enter Consultation Template Name", "kc-lang"),
        "template_name_placeholder" => __("Enter Template Name", "kc-lang")
    ],
    "setup_wizard" => [
        "previous" => __("Previous", "kc-lang"),
        "add_session_details" => __("Add session details", "kc-lang"),
        "session_doctors" => __("Session doctors", "kc-lang"),
        "days" => __("Days", "kc-lang"),
        "no_sessions_found" => __("No sessions found", "kc-lang"),
        "time_slot_minute" => __("Buffer Time (in minute)", "kc-lang"),
        "open_time" => __("Open time", "kc-lang"),
        "close_time" => __("Close time", "kc-lang"),
        "session_demo" => __("Session demo", "kc-lang"),
        "invalid_time_slot" => __("Invalid time slot found. invalid slot time is ", "kc-lang"),
        "doctor_list" => __("Doctors", "kc-lang"),
        "kivicare_ehr" => __("Medroid (EHR)", "kc-lang"),
        "prev" => __("Prev", "kc-lang"),
        "plh_enter_current_password" => __("Enter your current password", "kc-lang"),
        "plh_enter_fname" => __("Enter first name", "kc-lang"),
        "plh_enter_lame" => __("Enter last name", "kc-lang"),
        "plh_enter_email" => __("Enter email", "kc-lang"),
        "plh_enter_contct" => __("Enter telephone number", "kc-lang"),
        "plh_welcome_date" => __("welcome date", "kc-lang"),
        "plh_enter_clinic" => __("Enter clinic name", "kc-lang"),
        "plh_clinic_specialization" => __("Clinic specialization", "kc-lang"),
        "plh_specialization" => __("Specialization", "kc-lang"),
        "plh_currency_prefix" => __("Enter currency prefix", "kc-lang"),
        "plh_currency_postfix" => __("Enter currency postfix", "kc-lang"),
        "plh_enter_address" => __("Enter address", "kc-lang"),
        "plh_enter_city" => __("Enter city", "kc-lang"),
        "plh_enter_country" => __("Enter country", "kc-lang"),
        "plh_enter_pcode" => __("Enter postal code", "kc-lang"),
        "plh_enter_pwd" => __("Enter password", "kc-lang"),
        "plh_doc_specialization" => __("Doctor specialization", "kc-lang"),
        "plh_search" => __("Search", "kc-lang"),
        "plh_enter_fees" => __("Enter fees", "kc-lang"),
        "plh_min_price_range" => __("Min price range", "kc-lang"),
        "plh_max_price_range" => __("Max price range", "kc-lang"),
        "plh_enter_degree" => __("Enter degree", "kc-lang"),
        "plh_enter_university" => __("Enter university name", "kc-lang"),
        "plh_select_session_doc" => __("Select session doctors", "kc-lang"),
        "plh_start_time" => __("Select start time", "kc-lang"),
        "plh_end_time" => __("Select end time", "kc-lang")
    ],
    "notification" => [
        "notification" => __("Send test email", "kc-lang"),
        "test_sender_email_required" => __("Test sender email is required", "kc-lang"),
        "test_content" => __("Test content", "kc-lang"),
        "test_content_required" => __("Test content is required", "kc-lang"),
        "email_notification" => __("Enable/Disable email notification.", "kc-lang"),
        "forbidden_403" => __("403 | forbidden", "kc-lang"),
        "plh_enter_email" => __("Enter email Id", "kc-lang")

    ],
    "static_data" => [
        "listing_data" => __("Listing Data", "kc-lang"),
        "terms_n_condition" => __("Terms and Condition", "kc-lang"),
        "new_filters_n_view" => __("New Enhanced Filters and view", "kc-lang"),
        "booking_widget_updated" => __("The booking widget is updated", "kc-lang"),
        "visit_type_replaced" => __("Visiting Type is replaced with services (please check service tab)", "kc-lang"),
        "appointment_flow_update" => __("Appointment check-in and check-out flow updated", "kc-lang"),
        "label" => __("Label", "kc-lang"),
        "add_list_data_btn" => __("Add List Data", "kc-lang"),
        "data_label_plh" => __("Enter data label", "kc-lang"),
        "tag_select_type_plh" => __("Select type", "kc-lang"),
        "select_type_plh" => __("Select type", "kc-lang"),
        "tag_select_status_plh" => __("Select status", "kc-lang"),
        "select_status_plh" => __("Select status", "kc-lang"),
        "dt_lbl_sr" => __("Sr.", "kc-lang"),
        "dt_lbl_name" => _x("Name", "static data label", "kc-lang"),
        "dt_lbl_plh_fltr_name" => __("Filter by name", "kc-lang"),
        "dt_lbl_type" => __("Type", "kc-lang"),
        "dt_lbl_plh_fltr_type" => __("Filter by type", "kc-lang"),
        "dt_lbl_status" => __("Status", "kc-lang"),
        "dt_lbl_plh_sr_fltr_status" => __("Filter by status", "kc-lang"),
        "dt_lbl_action" => __("Action", "kc-lang"),
        "static_data_not_found" => __("Static data not found", "kc-lang"),
    ],
    "widgets" => [
        "doc_not_found" => __("Doctor not found", "kc-lang"),
        "zoom_config" => __("Zoom configuration", "kc-lang"),
        "terms_condition" => __("Terms and Condition", "kc-lang"),
        "date_required" => __("Date is required", "kc-lang"),
        "file_required" => __("File is required", "kc-lang"),
        "current_pwd" => __("Current password", "kc-lang"),
        "current_pwd_required" => __("Current password is required", "kc-lang"),
        "new_pwd" => __("New password", "kc-lang"),
        "appointment_info" => __("Appointment info", "kc-lang"),
        "available_slots" => __("Available slots", "kc-lang"),
        "service_detail" => __("Service Detail", "kc-lang"),
        "no_service_detail_found" => __("No service detail found.", "kc-lang"),
        "book_now" => __("Book Now", "kc-lang"),
        "registration_success" => __("Registration successful please check your email", "kc-lang"),
        "more_detail" => __("more detail ...", "kc-lang"),
        "username_email_required" => __("Username or email is required.", "kc-lang"),
        "new_pwd_required" => __("New password is required", "kc-lang"),
        "confirm_pwd" => __("Confirm password", "kc-lang"),
        "confirm_pwd_required" => __("Confirm password is required", "kc-lang"),
        "pwd_validation" => __("New password and Confirm password doest match", "kc-lang"),
        "home" => __("Home", "kc-lang"),
        "change_pwd" => __("Change Password ", "kc-lang"),
        "logging_out" => __("Logging out ....", "kc-lang"),
        "total_visits" => __("Total Visits", "kc-lang"),
        "upcoming_visits" => __("Upcoming Visits", "kc-lang"),
        "example_component" => __("Example Component", "kc-lang"),
        "email_to_get_help_1" => __("Other than this many more fine-tunings and tweaks are done. Please email at", "kc-lang"),
        "email_to_get_help_2" => __("<EMAIL>", "kc-lang"),
        "email_to_get_help_3" => __("if you face any issues with the update.", "kc-lang"),
        "feedback_note" => __("After great user feedback, We have some major changes released in this update.", "kc-lang"),
        "imp_version_update" => __("Important! Major Version update!! (V2.0.1)", "kc-lang"),
        "replace_appointment" => __("Replace appointment", "kc-lang"),
        "option_as" => __("option as", "kc-lang"),
        "service_type" => __("service type", "kc-lang"),
        "add_charges" => __("you have to add charges for", "kc-lang"),
        "manage_doctor" => __("Can Manage individual doctors", "kc-lang"),
        "test_email" => __("Test email", "kc-lang"),
        "send_test_email" => __("Send test Email", "kc-lang"),
        "send_test_sms" => __("Send test Sms", "kc-lang"),
        "send_test_whatsapp" => __("Send test Whatsapp", "kc-lang"),
        "morning" => __("Morning", "kc-lang"),
        "evening" => __("Evening", "kc-lang"),
        "plh_search" => __("Search", "kc-lang"),
        "plh_enter_desc" => __("Enter description", "kc-lang"),
        "clinics" => __("Clinics", "kc-lang"),
        "roles" => __("Roles", "kc-lang"),
        "plh_select_doc" => __("Select doctor", "kc-lang"),
        "plh_select_service" => __("Select service", "kc-lang"),
        "plh_enter_usrname_email" => __("Enter username or email", "kc-lang"),
        "plh_enter_fnmae" => __("Enter firstname", "kc-lang"),
        "plh_enter_pwd" => __("Enter password", "kc-lang"),
        "plh_enter_lname" => __("Enter lastname", "kc-lang"),
        "plh_enter_email" => __("Enter email", "kc-lang"),
        "plh_enter_contact" => __("Enter contact", "kc-lang"),
        "plh_enter_crrent_pwd" => __("Enter current password", "kc-lang"),
        "plh_enter_new_pwd" => __("Enter new password", "kc-lang"),
        "plh_confirm_pwd" => __("Enter confirm password", "kc-lang"),
        "patient_info" => __("Patient Info", "kc-lang"),
        "select_doctor_msg" => __("Select doctor to get appointments slots.", "kc-lang"),
        "slot_not_available_msg" => __("Sorry, No slots available of this doctor on selected day.", "kc-lang"),
        "session" => __("Session", "kc-lang"),
        "Session" => __("Session", "kc-lang"),
        "login_user_not_found" => __("Login user not found", "kc-lang"),
        "record_not_found" => __("Record not found", "kc-lang"),
        "onlyForPatient" => __("Admin can not view the widget. Only patients can view the widget. Please open this page in incognito mode or use another browser without an admin login.", "kc-lang"),
        "summary" => __("Summary", "kc-lang"),
        "doctors" => __("Doctors", "kc-lang"),
        "dr_prefix" => __("Dr.", "kc-lang"),
        "back_to_home" => __("Back To Home Page", "kc-lang"),
    ],
    "widget_setting" => [
        "widget_setting" => __("Widget Setting", "kc-lang"),
        "clinic_setting" => __("Clinic", "kc-lang"),
        "doctor_setting" => __("Doctor", "kc-lang"),
        "show_clinic" => __("Show clinic", "kc-lang"),
        "show_clinic_image" => __("Show clinic image", "kc-lang"),
        "show_clinic_address" => __("Show clinic address", "kc-lang"),
        "contact_details" => __("Contact Details", "kc-lang"),
        "show_doctor_image" => __("Show doctor image", "kc-lang"),
        "show_doctor_experience" => __("Show doctor experience", "kc-lang"),
        "show_doctor_speciality" => __("Show doctor speciality", "kc-lang"),
        "show_doctor_degree" => __("Show doctor degree", "kc-lang"),
        "contact_details_required" => __("Contact Detail is required", "kc-lang"),
        "service_setting" => __("Service", "kc-lang"),
        "show_service_image" => __("Show service image", "kc-lang"),
        "skip_service_when_single" => __("Skip service when single is present", "kc-lang"),
        "show_service_type" => __("Show service type", "kc-lang"),
        "show_service_price" => __("Show service price", "kc-lang"),
        "show_service_duration" => __("Show service duration", "kc-lang"),
        "choose_your_doctor" => __("Choose Your Doctor", "kc-lang"),
        "choose_your_doctor_text" => __("pick a specific Doctor to perform your service", "kc-lang"),
        "services_from_category" => __("Services from Category", "kc-lang"),
        "services_from_category_text" => __("Please select a service from below options", "kc-lang"),
        "select_date_and_time" => __("Select Date and Time", "kc-lang"),
        "select_date_and_time_text" => __("Select date to see a timeline of available slots", "kc-lang"),
        "user_detail_information" => __("User Detail Information", "kc-lang"),
        "user_detail_information_text" => __("Please provide you contact details", "kc-lang"),
        "confirmation" => __("Confirmation", "kc-lang"),
        "confirmation_detail" => __("Confirmation Detail", "kc-lang"),
        "confirmation_text" => __("Confirm your booking", "kc-lang"),
        "choose_clinic" => __("Choose a Clinic", "kc-lang"),
        "select_clinic" => __("Select Clinic", "kc-lang"),
        "choose_clinic_text" => __("Please select a Clinic you want to visit", "kc-lang"),
        "clinic_contact" => __("Contact", "kc-lang"),
        "clinic_email" => __("Email", "kc-lang"),
        "available_time_slots" => __("Available time slots", "kc-lang"),
        "enter_details" => __("Enter Details", "kc-lang"),
        "signup" => __("Signup", "kc-lang"),
        "appointment_summary" => __("Appointment summary", "kc-lang"),
        "date_time" => __("Date And Time", "kc-lang"),
        "at" => __("at", "kc-lang"),
        "total_price" => __("Total Price", "kc-lang"),
        "number" => __("Number", "kc-lang"),
        "confirm" => __("Confirm", "kc-lang"),
        "payment_method" => __("Payment method", "kc-lang"),
        "payPal" => __("PayPal", "kc-lang"),
        "pay_later" => __("Pay Later", "kc-lang"),
        "visa" => __("VISA", "kc-lang"),
        "pay" => __("Pay", "kc-lang"),
        "select_category" => __("Select Category", "kc-lang"),
        "widget_order" => __("Widget Order", "kc-lang"),
        "widget_color" => __("Widget Color", "kc-lang"),
        "set_widget_primary_color" => __("Primary Color", "kc-lang"),
        "set_widget_primary_hover_color" => __("Primary Hover Color", "kc-lang"),
        "set_widget_secondary_color" => __("Secondary Color", "kc-lang"),
        "set_widget_secondary_hover_color" => __("Secondary Hover Color", "kc-lang"),
        "widget_print_setting" => __("Print Detail setting", "kc-lang"),
        "print" => __("Print Detail", "kc-lang"),
        "phone_email" => __("Show Phone & Email", "kc-lang"),
        "show_phone_number" => __("Show phone number", "kc-lang"),
        "show_email_address" => __("Show email address", "kc-lang"),
        "hide_contact_details" => __("Hide contact details", "kc-lang"),
        "loader_setting" => __("Loader", "kc-lang"),
        "loader_select" => __("Select Loader", "kc-lang"),
        "redirectAfterWoocommerce" => __("Redirect to Print after payment", "kc-lang"),
        "redirectAfterWoocommerceNotice" => __("Redirect to Print appointment after woocommerce payment complete.", "kc-lang"),
        "enable_woocommerce" => __("Enable WooCommerce payment.", "kc-lang"),
        "enable_local_payment" => __("Enable Local payment.", "kc-lang"),
        "show_doctor_rating" => __("Show doctor rating", "kc-lang"),
    ],
    "patient_dashboard_widget" => [
        "blood_group" => __("Blood Group", "kc-lang"),
        "fname_plh" => __("Enter first name", "kc-lang"),
        "lname_plh" => __("Enter last name", "kc-lang"),
        "email_plh" => __("Enter email", "kc-lang"),
        "search_plh" => __("Search", "kc-lang"),
        "select_blood_grp" => __("Search", "kc-lang"),
        "contact_plh" => __("Enter contact", "kc-lang"),
        "address_plh" => __("Enter address", "kc-lang"),
        "city_plh" => __("Enter city", "kc-lang"),
        "state_plh" => __("Enter state", "kc-lang"),
        "country_plh" => __("Enter country", "kc-lang"),
        "pcode_plh" => __("Enter pin code", "kc-lang"),
        "save_profile_btn" => __("save", "kc-lang"),
        "change_pass_btn" => __("Change Password", "kc-lang"),
        "latest_appointments" => __("Latest appointments", "kc-lang"),
        "no_appointment_msg" => __("No appointment found.", "kc-lang"),
        "appointment_list" => __("Appointments", "kc-lang"),
        "select_doctor_msg" => __("Select doctor to get appointments slots.", "kc-lang"),
        "slot_not_available_msg" => __("Sorry, No slots available of this doctor on selected day.", "kc-lang"),
        "book_now_btn" => __("Book Appointment", "kc-lang"),
        "profile_head" => __("Profile", "kc-lang"),
        "change_password_head" => __("Change Password", "kc-lang"),
        "personal_info" => __("Personal Info", "kc-lang")
    ],
    "change_password" => [
        "plh_confirm_password" => __("Confirm password", "kc-lang"),
        "plh_old_pwd" => __("Enter your old password", "kc-lang"),
        "plh_new_pwd" => __("Enter your new password", "kc-lang"),
        "plh_confirm_pwd" => __("Enter your confirm password", "kc-lang"),
        "plh_enter_confirm_password" => __("Enter confirm password", "kc-lang"),
        "plh_new_password" => __("New password", "kc-lang"),
    ],
    "zoom_config" => [
        "plh_api_key" => __("Enter API key", "kc-lang"),
        "plh_api_secret" => __("Enter API secret", "kc-lang"),
    ],
    "question" => [
        "question_list" => __("Questions", "kc-lang"),
        "close_form_btn" => __("Close form", "kc-lang"),
        "add_question_btn" => __("Add Question", "kc-lang"),
        "question" => __("Question", "kc-lang"),
        "quest_flt_plh" => __("Filter Health Question by Question", "kc-lang"),
        "clinic" => __("Clinic ID", "kc-lang"),
        "clinic_flt_plh" => __("Filter Health Question by Clinic ID", "kc-lang"),
        "question_flt_plh" => __("Enter Question ", "kc-lang"),
        "question_required" => __("Question is Required", "kc-lang"),
    ],
    "datatable" => [
        "next_text" => __("Next", "kc-lang"),
        "prev_text" => __("Prev", "kc-lang"),
        "rows_per_page" => __("Rows per page", "kc-lang"),
        "of_text" => __("of", "kc-lang"),
        "all_text" => __("ALL", "kc-lang"),
        "search_placeholder" => __("Search Table", "kc-lang"),
        "page_text" => __("Page", "kc-lang"),
    ],
    "google_event" => [
        "google_event_title" => __("Google Event title", "kc-lang"),
        "google_event_desc" => __("Google Event Description", "kc-lang"),
        "template" => __("Google Event Template", "kc-lang"),
    ],
    "auth" => [
        "login" => __("Login", "kc-lang"),
        "register" => __("Register", "kc-lang"),
        "forget_password" => __("Forget Password ?", "kc-lang"),
    ],
    "googlemeet" => [
        "googlemeet" => __("Google Meet", "kc-lang"),
        "google_meet_configuration" => __("Google Meet Configuration", "kc-lang"),
        "google_meet_client_id" => __("Google Meet Client ID", "kc-lang"),
        "google_meet_client_id_required" => __("Google Meet Client ID is required", "kc-lang"),
        "google_meet_client_secret" => __("Google Meet Client Secret", "kc-lang"),
        "google_meet_client_secret_required" => __("Google Meet Client Secret is required", "kc-lang"),
        "guide_to_setup_google_meet" => __("Guide to setup google GoogleMeet.", "kc-lang"),
        "google_event_template" => __("Google Meet Event Template", "kc-lang"),
        "google_meet_intergration" => __("Google Meet Integration", "kc-lang"),
        "please_connect_google_meet_service" => __("Please connect with your google account to use google meet service.", "kc-lang"),

    ],
    "zoom_telemed" => [
        "zoom_telemed" => __("Zoom Telemed", 'kc-lang'),
        "zoom_telemed_oauth" => __("Zoom Telemed Oauth", 'kc-lang'),
        "zoom_configuration" => __("Zoom Telemed Configuration", 'kc-lang'),
        "zoom_client_id" => __("Zoom Telemed Client ID", "kc-lang"),
        "zoom_client_id_required" => __("Zoom Telemed Client ID is required", "kc-lang"),
        "zoom_client_secret" => __("Zoom Telemed Client Secret", "kc-lang"),
        "zoom_client_secret_required" => __("Zoom Telemed Client Secret is required", "kc-lang"),
        "zoom_telemed_intergration" => __("Zoom Telemed Integration", "kc-lang"),
        "guide_to_setup_zoom" => __("Guide to setup Zoom.", "kc-lang"),
        "please_connect_zoom_telemed_service" => __("Please Connect With Your Zoom Account To Use Zoom Telemed Service.", "kc-lang"),
        "connected_zoom_telemed_service" => __("You are connected with the Zoom.", "kc-lang"),
        "redirect_url" => __("Redirect URL.", "kc-lang"),
        "redirect_url_required" => __("Redirect URL is Required", "kc-lang"),
        "deprecated_notice" => __("The Zoom JWT app type is being deprecated. Please switch to OAuth for your Zoom app to continue your Telemed Service.", "kc-lang"),
        "deprecated_notice_btn" => __("Click Here To Goto Settings", "kc-lang"),
    ],
    "fullcalendar" => [
        "today" => __("Today", "kc-lang"),
        "day" => __("Day", "kc-lang"),
        "month" => __("Month", "kc-lang"),
        "week" => __("Week", "kc-lang"),
    ],
    "days" => [
        "mon" => __("Mon", "kc-lang"),
        "tue" => __("Tue", "kc-lang"),
        "wed" => __("Wed", "kc-lang"),
        "thu" => __("Thu", "kc-lang"),
        "fri" => __("Fri", "kc-lang"),
        "sat" => __("Sat", "kc-lang"),
        "sun" => __("Sun", "kc-lang")
    ],
    "months" => [
        "January" => __("January", "kc-lang"),
        "February" => __("February", "kc-lang"),
        "March" => __("March", "kc-lang"),
        "April" => __("April", "kc-lang"),
        "May" => __("May", "kc-lang"),
        "June" => __("June", "kc-lang"),
        "July" => __("July", "kc-lang"),
        "August" => __("August", "kc-lang"),
        "September" => __("September", "kc-lang"),
        "October" => __("October", "kc-lang"),
        "November" => __("November", "kc-lang"),
        "December" => __("December", "kc-lang")
    ],
    "months_short" => [
        "Jan" => __("Jan", "kc-lang"),
        "Feb" => __("Feb", "kc-lang"),
        "Mar" => __("Mar", "kc-lang"),
        "Apr" => __("Apr", "kc-lang"),
        "May" => __("May", "kc-lang"),
        "Jun" => __("Jun", "kc-lang"),
        "Jul" => __("Jul", "kc-lang"),
        "Aug" => __("Aug", "kc-lang"),
        "Sep" => __("Sep", "kc-lang"),
        "Oct" => __("Oct", "kc-lang"),
        "Nov" => __("Nov", "kc-lang"),
        "Dec" => __("Dec", "kc-lang")
    ],
    "webhooks" => [
        'webhooks' => _x('Webhooks', 'administrator-sidebar', 'kc-lang'),
    ],
   
];

$sidebar =   [
    "administrator" => [
        'dashboard' => _x('Cockpit', 'administrator-sidebar', 'kc-lang'),
        'appointment_list' => _x('Appointments', 'administrator-sidebar', 'kc-lang'),
        'parent' => _x('Consultations', 'administrator-sidebar', 'kc-lang'),
        'patient_encounter_list' => _x('Consultations', 'administrator-sidebar', 'kc-lang'),
        'encounter_template' => _x('Consultations Templates', 'administrator-sidebar', 'kc-lang'),
        'clinic' => _x('Clinic', 'administrator-sidebar', 'kc-lang'),
        'patient' => _x('Patients', 'administrator-sidebar', 'kc-lang'),
        'doctor' => _x('Doctors', 'administrator-sidebar', 'kc-lang'),
        'receptionist' => _x('Receptionist', 'administrator-sidebar', 'kc-lang'),
        'service' => _x('Services', 'administrator-sidebar', 'kc-lang'),
        'doctor_session' => _x('Availability', 'administrator-sidebar', 'kc-lang'),
        "tax" => _x('Taxes', 'administrator-sidebar', 'kc-lang'),
        'billings' => _x('Billing records', 'administrator-sidebar', 'kc-lang'),
        'clinic-revenue-reports' => _x('Reports', 'administrator-sidebar', 'kc-lang'),
        'settings' => _x('Settings', 'administrator-sidebar', 'kc-lang'),
        'get_help' => _x('Get help', 'administrator-sidebar', 'kc-lang'),
        'get_pro' => _x('Get Pro', 'administrator-sidebar', 'kc-lang'),
        'request_feature' => _x('Request Features', 'administrator-sidebar', 'kc-lang'),
    ],
    $kcBase->getClinicAdminRole() => [
        'home' => _x('Home', 'clinic-admin-sidebar', 'kc-lang'),
        'dashboard' => _x('Cockpit', 'clinic-admin-sidebar', 'kc-lang'),
        'appointment_list' => _x('Appointments', 'clinic-admin-sidebar', 'kc-lang'),
        'parent' => _x('Consultations', 'clinic-admin-sidebar', 'kc-lang'),
        'patient_encounter_list' => _x('Consultations', 'clinic-admin-sidebar', 'kc-lang'),
        'encounter_template' => _x('Consultations Templates', 'clinic-admin-sidebar', 'kc-lang'),
        'patient' => _x('Patients', 'clinic-admin-sidebar', 'kc-lang'),
        'doctor' => _x('Doctors', 'clinic-admin-sidebar', 'kc-lang'),
        'receptionist' => _x('Receptionist', 'clinic-admin-sidebar', 'kc-lang'),
        'service' => _x('Services', 'clinic-admin-sidebar', 'kc-lang'),
        'doctor_session' => _x('Availability', 'clinic-admin-sidebar', 'kc-lang'),
        'tax' => _x('Taxes', 'clinic-admin-sidebar', 'kc-lang'),
        'billings' => _x('Billing records', 'clinic-admin-sidebar', 'kc-lang'),
        'clinic-revenue-reports' => _x('Reports', 'clinic-admin-sidebar', 'kc-lang'),
        'clinic_settings' => _x('Settings', 'clinic-admin-sidebar', 'kc-lang'),
    ],
    $kcBase->getReceptionistRole() => [
        'home' => _x('Home', 'receptionist-sidebar', 'kc-lang'),
        'dashboard' => _x('Cockpit', 'receptionist-sidebar', 'kc-lang'),
        'appointment_list' => _x('Appointments', 'receptionist-sidebar', 'kc-lang'),
        'parent' => _x('Consultations', 'receptionist-sidebar', 'kc-lang'),
        'patient_encounter_list' => _x('Consultations', 'receptionist-sidebar', 'kc-lang'),
        'encounter_template' => _x('Consultations Templates', 'receptionist-sidebar', 'kc-lang'),
        'patient' => _x('Patients', 'receptionist-sidebar', 'kc-lang'),
        'doctor' => _x('Doctors', 'receptionist-sidebar', 'kc-lang'),
        'service' => _x('Services', 'receptionist-sidebar', 'kc-lang'),
        'billings' => _x('Billing records', 'receptionist-sidebar', 'kc-lang'),
        'clinic_settings' => _x('Settings', 'receptionist-sidebar', 'kc-lang'),
    ],
    $kcBase->getDoctorRole() => [
        'home' => _x('Home', 'doctor-sidebar', 'kc-lang'),
        'dashboard' => _x('Cockpit', 'doctor-sidebar', 'kc-lang'),
        'appointment_list' => _x('Appointments', 'doctor-sidebar', 'kc-lang'),
        'parent' => _x('Consultations', 'doctor-sidebar', 'kc-lang'),
        'patient_encounter_list' => _x('Consultations', 'doctor-sidebar', 'kc-lang'),
        'encounters_template_list' => _x('Consultations Templates', 'doctor-sidebar', 'kc-lang'),
        'patient' => _x('Patients', 'doctor-sidebar', 'kc-lang'),
        'service' => _x('Services', 'doctor-sidebar', 'kc-lang'),
        'billings' => _x('Billing records', 'doctor-sidebar', 'kc-lang'),
        'clinic_settings' => _x('Settings', 'doctor-sidebar', 'kc-lang'),
    ],
    $kcBase->getPatientRole() => [
        'home' => _x('Home', 'patient-sidebar', 'kc-lang'),
        'dashboard' => _x('Cockpit', 'patient-sidebar', 'kc-lang'),
        'appointment_list' => _x('Appointments', 'patient-sidebar', 'kc-lang'),
        'patient_encounter_list' => _x('Consultations', 'patient-sidebar', 'kc-lang'),
        'doctors' => _x('Doctors', 'patient-sidebar', 'kc-lang'),
        'lab_tests' => _x('Lab Tests', 'patient-sidebar', 'kc-lang'),
        'medications' => _x('Medications', 'patient-sidebar', 'kc-lang'),
        'invoices' => _x('Invoices', 'patient-sidebar', 'kc-lang'),
        'records' => _x('Records', 'patient-sidebar', 'kc-lang'),
        'profile' => _x('Profile', 'patient-sidebar', 'kc-lang'),
        'billings' => _x('Billing records', 'patient-sidebar', 'kc-lang'),
        'patient_medical' => _x('Reports', 'patient-sidebar', 'kc-lang'),
        'patient_clinic' => _x('Clinic', 'patient-sidebar', 'kc-lang'),
    ]
];

if(!empty($kcBase->getLoginUserRole()))
    $data['sidebar'] = $sidebar[$kcBase->getLoginUserRole()]; 


return apply_filters('kivicare_language_data', $data);
