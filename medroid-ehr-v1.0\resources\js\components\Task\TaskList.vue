<template>
  <div class="task-list-container">
    <!-- Loading state -->
    <div v-if="loading" class="flex justify-center py-6">
      <svg class="animate-spin h-8 w-8 text-black" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
        </path>
      </svg>
    </div>

    <!-- Empty state -->
    <div v-else-if="!tasks.length" class="bg-white border rounded-lg p-8 text-center">
      <div class="max-w-md mx-auto">
        <div class="text-black text-6xl mb-4">
          <svg class="w-20 h-20 inline-block" fill="none" stroke="currentColor" viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01">
            </path>
          </svg>
        </div>
        <h5 class="text-xl font-medium text-gray-800 mb-2">{{ formTranslation.task.no_tasks_found || 'No tasks found' }}
        </h5>
        <p class="text-gray-500 mb-6">{{ formTranslation.task.get_started || 'Get started by creating your first task'
          }}</p>
        <button
          class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-black hover:bg-black-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black-500"
          @click="$emit('add-task')">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          {{ formTranslation.task.add_task || 'Add Task' }}
        </button>
      </div>
    </div>

    <!-- Task list -->
    <div v-else>
      <div class="bg-white shadow rounded-lg overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <div class="flex items-center">
                  <input type="checkbox" class="h-4 w-4 text-black focus:ring-black border-gray-300 rounded"
                    :checked="selectAll" @change="toggleSelectAll">
                </div>
              </th>
              <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ formTranslation.task.title || 'Title' }}
              </th>
              <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ formTranslation.task.assignees || 'Assignees' }}
              </th>
              <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ formTranslation.task.priority || 'Priority' }}
              </th>
              <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ formTranslation.task.status || 'Status' }}
              </th>
              <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ formTranslation.task.due_date || 'Due Date' }}
              </th>
              <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ formTranslation.common.actions || 'Actions' }}
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="task in tasks" :key="task.id" :class="{ 'bg-red-50': isTaskOverdue(task) }"
              class="hover:bg-gray-50">
              <td class="px-3 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <input type="checkbox" class="h-4 w-4 text-black focus:ring-black border-gray-300 rounded"
                    v-model="selectedTasks" :value="task.id">
                </div>
              </td>
              <td class="px-3 py-4">
                <div @click="$emit('view-task', task.id)" class="cursor-pointer">
                  <div class="flex items-start">
                    <div class="flex-shrink-0 mt-1">
                      <svg v-if="task.status === 'completed'" class="h-5 w-5 text-green-500" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <svg v-else-if="task.status === 'pending'" class="h-5 w-5 text-yellow-500" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <svg v-else-if="task.status === 'in-progress'" class="h-5 w-5 text-blue-500" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                      </svg>
                      <svg v-else-if="task.status === 'cancelled'" class="h-5 w-5 text-red-500" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div class="ml-3">
                      <h3 class="text-sm font-medium text-gray-900"
                        :class="{ 'line-through': task.status === 'completed' }">
                        {{ task.title }}
                      </h3>
                      <div v-if="task.patient_name" class="text-xs text-gray-500 mt-1">
                        <svg class="inline-block h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        {{ task.patient_name }}
                      </div>
                      <div v-if="task.description" class="text-xs text-gray-500 mt-1 max-w-xs truncate">
                        {{ truncateDescription(task.description) }}
                      </div>
                    </div>
                  </div>
                </div>
              </td>
              <td class="px-3 py-4 whitespace-nowrap">
                <div class="flex -space-x-2">
                  <div v-if="task.assignees && task.assignees.length">
                    {{ task.assignees_names }}
                  </div>
                  <div v-else class="text-sm text-gray-500">{{ formTranslation.task.unassigned || 'Unassigned' }}</div>
                </div>
              </td>
              <td class="px-3 py-4 whitespace-nowrap text-center">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" :class="{
                  'bg-blue-100 text-blue-800': task.priority === 'low',
                  'bg-yellow-100 text-yellow-800': task.priority === 'medium',
                  'bg-red-100 text-red-800': task.priority === 'high'
                }">
                  {{ getPriorityLabel(task.priority) }}
                </span>
              </td>
              <td class="px-3 py-4 whitespace-nowrap text-center">
                <div class="relative inline-block text-left">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full cursor-pointer" :class="{
                    'bg-yellow-100 text-yellow-800': task.status === 'pending',
                    'bg-blue-100 text-blue-800': task.status === 'in-progress',
                    'bg-green-100 text-green-800': task.status === 'completed',
                    'bg-red-100 text-red-800': task.status === 'cancelled'
                  }" @click="toggleStatusDropdown(task.id)">
                    {{ getStatusLabel(task.status) }}
                  </span>

                  <div v-if="statusDropdownOpen === task.id"
                    class="origin-top-right absolute right-0 mt-2 w-40 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-10"
                    role="menu">
                    <div class="py-1">
                      <a class="flex items-center px-4 py-2 text-xs text-yellow-800 hover:bg-gray-100 cursor-pointer"
                        @click.prevent="changeStatus(task.id, 'pending')">
                        <span class="inline-block w-3 h-3 rounded-full bg-yellow-400 mr-2"></span>
                        {{ formTranslation.task.status_pending || 'Pending' }}
                      </a>
                      <a class="flex items-center px-4 py-2 text-xs text-blue-800 hover:bg-gray-100 cursor-pointer"
                        @click.prevent="changeStatus(task.id, 'in-progress')">
                        <span class="inline-block w-3 h-3 rounded-full bg-blue-400 mr-2"></span>
                        {{ formTranslation.task.status_in_progress || 'In Progress' }}
                      </a>
                      <a class="flex items-center px-4 py-2 text-xs text-green-800 hover:bg-gray-100 cursor-pointer"
                        @click.prevent="changeStatus(task.id, 'completed')">
                        <span class="inline-block w-3 h-3 rounded-full bg-green-400 mr-2"></span>
                        {{ formTranslation.task.status_completed || 'Completed' }}
                      </a>
                      <a class="flex items-center px-4 py-2 text-xs text-red-800 hover:bg-gray-100 cursor-pointer"
                        @click.prevent="changeStatus(task.id, 'cancelled')">
                        <span class="inline-block w-3 h-3 rounded-full bg-red-400 mr-2"></span>
                        {{ formTranslation.task.status_cancelled || 'Cancelled' }}
                      </a>
                    </div>
                  </div>
                </div>
              </td>
              <td class="px-3 py-4 whitespace-nowrap text-center">
                <div :class="{ 'text-red-600': isTaskOverdue(task) }" class="text-sm">
                  {{ formatDate(task.due_date) }}
                  <span v-if="isTaskOverdue(task)" class="flex items-center justify-center text-xs mt-1 text-red-600">
                    <svg class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                    {{ formTranslation.task.overdue || 'Overdue' }}
                  </span>
                </div>
              </td>
              <td class="px-3 py-4 whitespace-nowrap text-center">
                <div class="flex items-center justify-center space-x-2">
                  <!-- Edit button -->
                  <button
                    class="text-blue-600 hover:text-blue-800 p-1 rounded-full hover:bg-blue-100 focus:outline-none"
                    @click="$emit('edit-task', task.id)" :title="formTranslation.common.edit || 'Edit'">
                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </button>

                  <!-- Complete button - only shows for non-completed/cancelled tasks -->
                  <button v-if="task.status !== 'completed' && task.status !== 'cancelled'"
                    class="text-green-600 hover:text-green-800 p-1 rounded-full hover:bg-green-100 focus:outline-none"
                    @click="completeTask(task.id)" :title="formTranslation.task.mark_complete || 'Mark Complete'">
                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                  </button>

                  <!-- Delete button -->
                  <button class="text-red-600 hover:text-red-800 p-1 rounded-full hover:bg-red-100 focus:outline-none"
                    @click="confirmDeleteTask(task.id)" :title="formTranslation.common.delete || 'Delete'">
                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Bulk actions -->
      <div v-if="selectedTasks.length"
        class="fixed bottom-6 left-1/2 transform -translate-x-1/2 bg-white rounded-lg shadow-lg border border-gray-200 p-4 z-50">
        <div class="flex items-center">
          <span class="mr-4 text-sm font-medium">
            <span class="font-bold">{{ selectedTasks.length }}</span> {{ formTranslation.task.selected_tasks || 'tasks selected' }}
          </span>
          <div class="flex space-x-2">
            <button
              class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-black hover:bg-black-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black-500"
              @click="bulkComplete">
              <svg class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              {{ formTranslation.task.mark_complete || 'Mark Complete' }}
            </button>
            <button
              class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-yellow-500 hover:bg-yellow-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
              @click="bulkChangeStatus('pending')">
              <svg class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              {{ formTranslation.task.mark_pending || 'Mark Pending' }}
            </button>
            <button
              class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-red-500 hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              @click="bulkDelete">
              <svg class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              {{ formTranslation.common.delete || 'Delete' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { post, get } from "../../config/request";

export default {
  name: 'TaskList',
  props: {
    tasks: {
      type: Array,
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectedTasks: [],
      selectAll: false,
      statusDropdownOpen: null,
      actionsDropdownOpen: null
    }
  },
  mounted() {
    // Add click outside event listener for the dropdowns
    document.addEventListener('click', this.closeDropdowns);
  },
  beforeDestroy() {
    // Remove click outside event listener
    document.removeEventListener('click', this.closeDropdowns);
  },
  methods: {
    toggleStatusDropdown(taskId) {
      // Close any open dropdowns first
      this.actionsDropdownOpen = null;

      // Toggle the status dropdown
      if (this.statusDropdownOpen === taskId) {
        this.statusDropdownOpen = null;
      } else {
        this.statusDropdownOpen = taskId;
      }
    },

    toggleActionsDropdown(taskId) {
      // Close any open dropdowns first
      this.statusDropdownOpen = null;

      // Toggle the actions dropdown
      if (this.actionsDropdownOpen === taskId) {
        this.actionsDropdownOpen = null;
      } else {
        this.actionsDropdownOpen = taskId;
      }
    },

    closeDropdowns(event) {
      // Only close if click is outside of dropdown elements
      if (!event.target.closest('.dropdown-toggle') && !event.target.closest('.dropdown-menu')) {
        this.statusDropdownOpen = null;
        this.actionsDropdownOpen = null;
      }
    },

    async completeTask(taskId) {
      try {
        // Show confirmation dialog first
        const result = await this.$swal.fire({
          title: formTranslation.task.confirm_complete || 'Mark Task as Complete',
          text: formTranslation.task.complete_confirmation || 'Are you sure you want to mark this task as complete?',
          icon: 'question',
          showCancelButton: true,
          confirmButtonColor: '#3085d6',
          cancelButtonColor: '#d33',
          confirmButtonText: formTranslation.task.mark_complete || 'Mark Complete',
          cancelButtonText: formTranslation.common.cancel || 'Cancel'
        });

        // If confirmed, proceed with API call
        if (result.isConfirmed) {
          const response = await post('complete_task', { id: taskId });

          if (response.data && response.data.status) {
            // Show success message
            this.$swal.fire({
              icon: 'success',
              title: formTranslation.common.success || 'Success',
              text: response.data.message || formTranslation.task.task_completed_successfully || 'Task marked as complete successfully',
              showConfirmButton: false,
              timer: 1500
            });

            // Emit event to parent to refresh tasks
            this.$emit('task-completed', taskId);
            // Emit task-refresh event to parent to reload the task list
            this.$emit('task-refresh');
          } else {
            // Show error message if API returns unsuccessful status
            this.$swal.fire({
              icon: 'error',
              title: formTranslation.common.error || 'Error',
              text: response.data.message || formTranslation.task.complete_task_error || 'Failed to mark task as complete'
            });
          }
        }
      } catch (error) {
        console.error('Error completing task:', error);
        this.$swal.fire({
          icon: 'error',
          title: formTranslation.common.error || 'Error',
          text: formTranslation.task.complete_task_error || 'Failed to mark task as complete'
        });
      }
    },

    formatDate(dateString) {
      if (!dateString) return '-';

      const date = new Date(dateString);
      return new Intl.DateTimeFormat(this.$i18n.locale, {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      }).format(date);
    },

    isTaskOverdue(task) {
      if (task.status === 'completed' || task.status === 'cancelled' || !task.due_date) {
        return false;
      }

      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const dueDate = new Date(task.due_date);
      dueDate.setHours(0, 0, 0, 0);

      return dueDate < today;
    },

    getPriorityLabel(priority) {
      const labels = {
        'low': formTranslation.task ? formTranslation.task.priority_low || 'Low' : 'Low',
        'medium': formTranslation.task ? formTranslation.task.priority_medium || 'Medium' : 'Medium',
        'high': formTranslation.task ? formTranslation.task.priority_high || 'High' : 'High'
      };

      return labels[priority] || priority;
    },

    getStatusLabel(status) {
      const labels = {
        'pending': formTranslation.task ? formTranslation.task.status_pending || 'Pending' : 'Pending',
        'in-progress': formTranslation.task ? formTranslation.task.status_in_progress || 'In Progress' : 'In Progress',
        'completed': formTranslation.task ? formTranslation.task.status_completed || 'Completed' : 'Completed',
        'cancelled': formTranslation.task ? formTranslation.task.status_cancelled || 'Cancelled' : 'Cancelled'
      };

      return labels[status] || status;
    },

    confirmDeleteTask(taskId) {
      this.$swal.fire({
        title: formTranslation.task.confirm_delete || 'Delete Task?',
        text: formTranslation.task.delete_confirmation || 'Are you sure you want to delete this task? This action cannot be undone.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: formTranslation.common.delete || 'Delete',
        confirmButtonColor: '#dc3545',
        cancelButtonText: formTranslation.common.cancel || 'Cancel'
      }).then((result) => {
        if (result.isConfirmed) {
          this.deleteTask(taskId);
        }
      });
    },

    async deleteTask(taskId) {
      try {
        const response = await post(`delete_task`, { id: taskId });

        if (response.data && response.data.status) {
          this.$swal.fire({
            icon: 'success',
            title: formTranslation.common.success || 'Success',
            text: response.data.message || formTranslation.task.delete_success || 'Task deleted successfully',
            showConfirmButton: false,
            timer: 1500
          });

          // Emit event to parent to refresh tasks
          this.$emit('task-deleted', taskId);
          // Also emit task-refresh to ensure the list is reloaded
          this.$emit('task-refresh');
        } else {
          this.$swal.fire({
            icon: 'error',
            title: formTranslation.common.error || 'Error',
            text: response.data.message || formTranslation.task.delete_error || 'Failed to delete task'
          });
        }
      } catch (error) {
        console.error('Error deleting task:', error);
        this.$swal.fire({
          icon: 'error',
          title: formTranslation.common.error || 'Error',
          text: formTranslation.task.delete_error || 'Failed to delete task'
        });
      }
    },

    limitedAssignees(assignees) {
      return assignees.slice(0, 3);
    },

    getInitials(name) {
      if (!name) return '?';

      return name.split(' ')
        .map(word => word.charAt(0).toUpperCase())
        .slice(0, 2)
        .join('');
    },

    getAvatarColor(name) {
      if (!name) return '#6c757d';

      // Generate consistent color based on name
      let hash = 0;
      for (let i = 0; i < name.length; i++) {
        hash = name.charCodeAt(i) + ((hash << 5) - hash);
      }

      const colors = [
        '#3498db', '#2ecc71', '#e74c3c', '#f39c12', '#9b59b6',
        '#1abc9c', '#d35400', '#c0392b', '#16a085', '#8e44ad',
        '#27ae60', '#2980b9', '#f1c40f', '#e67e22', '#ecf0f1'
      ];

      return colors[Math.abs(hash) % colors.length];
    },

    truncateDescription(description, length = 100) {
      if (!description) return '';

      if (description.length <= length) return description;

      return description.substring(0, length) + '...';
    },

    toggleSelectAll() {
      if (this.selectAll) {
        this.selectedTasks = this.tasks.map(task => task.id);
      } else {
        this.selectedTasks = [];
      }
    },

    changeStatus(taskId, status) {
      this.statusDropdownOpen = null;
      this.$emit('status-change', { taskId, status });
    },

    bulkComplete() {
      if (!this.selectedTasks.length) return;

      this.$swal.fire({
        title: formTranslation.task.confirm_bulk_complete || 'Complete Multiple Tasks',
        text: formTranslation.task.bulk_complete_confirmation || `Are you sure you want to mark ${this.selectedTasks.length} tasks as complete?`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: formTranslation.common.yes || 'Yes',
        cancelButtonText: formTranslation.common.cancel || 'Cancel'
      }).then((result) => {
        if (result.isConfirmed) {
          // Emit event for each selected task
          this.selectedTasks.forEach(taskId => {
            this.$emit('complete-task', taskId);
          });
          this.selectedTasks = [];
          this.selectAll = false;
          // Refresh the task list
          this.$emit('task-refresh');
        }
      });
    },

    bulkChangeStatus(status) {
      if (!this.selectedTasks.length) return;

      this.$swal.fire({
        title: formTranslation.task.confirm_bulk_status_change || 'Change Status for Multiple Tasks',
        text: formTranslation.task.bulk_status_change_confirmation || `Are you sure you want to change status for ${this.selectedTasks.length} tasks?`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: formTranslation.common.yes || 'Yes',
        cancelButtonText: formTranslation.common.cancel || 'Cancel'
      }).then((result) => {
        if (result.isConfirmed) {
          // Emit event for each selected task
          this.selectedTasks.forEach(taskId => {
            this.$emit('status-change', { taskId, status });
          });
          this.selectedTasks = [];
          this.selectAll = false;
          // Refresh the task list
          this.$emit('task-refresh');
        }
      });
    },

    bulkDelete() {
      if (!this.selectedTasks.length) return;

      this.$swal.fire({
        title: formTranslation.task.confirm_bulk_delete || 'Delete Multiple Tasks',
        text: formTranslation.task.bulk_delete_confirmation || `Are you sure you want to delete ${this.selectedTasks.length} tasks? This action cannot be undone.`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: formTranslation.common.delete || 'Delete',
        confirmButtonColor: '#dc3545',
        cancelButtonText: formTranslation.common.cancel || 'Cancel'
      }).then((result) => {
        if (result.isConfirmed) {
          // Emit event for each selected task
          this.selectedTasks.forEach(taskId => {
            this.deleteTask(taskId);
          });
          this.selectedTasks = [];
          this.selectAll = false;
          // Refresh the task list
          this.$emit('task-refresh');
        }
      });
    }
  },
  watch: {
    // Update selectAll state based on selection
    selectedTasks(newVal) {
      this.selectAll = newVal.length && newVal.length === this.tasks.length;
    },

    // Reset selection when task list changes
    tasks() {
      this.selectedTasks = [];
      this.selectAll = false;
    }
  }
}
</script>