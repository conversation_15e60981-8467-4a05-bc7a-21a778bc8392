<template>
  <div v-if="openAddReceptionistModal" class="fixed inset-0 z-50 overflow-y-auto">
    <!-- Backdrop -->
    <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity" @click="handleClose"></div>

    <!-- Modal -->
    <div class="flex min-h-screen items-center justify-center p-4">
      <div class="relative w-full max-w-7xl bg-white rounded-lg shadow-xl">
        <form @submit.prevent="handleSubmit" :novalidate="true" class="h-full">
          <!-- Header -->
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex justify-between items-center">
              <h3 class="text-xl font-semibold text-gray-900">
                {{ formTranslation.clinic.add_receptionist }}
              </h3>
              <button type="button" class="text-gray-400 hover:text-gray-500 focus:outline-none" @click="handleClose">
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          <div class="px-6 py-4 max-h-[calc(100vh-200px)] overflow-y-auto">
            <!-- Basic Details Section -->
            <div class="mb-6">
              <h4 class="text-lg font-medium text-gray-900 mb-4">
                {{ formTranslation.common.basic_details }}
              </h4>

              <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- First Name -->
                <div>
                  <label for="first_name" class="block text-sm font-medium text-gray-700">
                    {{ formTranslation.common.fname }}
                    <span class="text-red-500">*</span>
                  </label>
                  <input id="first_name" v-model="receptionistData.first_name"
                    :placeholder="formTranslation.receptionist.fname_plh" type="text"
                    class="w-full rounded-lg px-4 py-2.5 border border-gray-300 shadow-sm focus:ring-black focus:border-black"
                    :class="{
                      'border-red-300 focus:border-red-500 focus:ring-red-500':
                        submitted && $v.receptionistData.first_name.$error
                    }" />
                  <p v-if="submitted && !$v.receptionistData.first_name.required" class="mt-1 text-sm text-red-600">
                    {{ formTranslation.common.fname_required }}
                  </p>
                </div>

                <!-- Last Name -->
                <div>
                  <label for="last_name" class="block text-sm font-medium text-gray-700">
                    {{ formTranslation.common.lname }}
                    <span class="text-red-500">*</span>
                  </label>
                  <input id="last_name" v-model="receptionistData.last_name"
                    :placeholder="formTranslation.receptionist.lname_plh" type="text"
                    class="w-full rounded-lg px-4 py-2.5 border border-gray-300 shadow-sm focus:ring-black focus:border-black"
                    :class="{
                      'border-red-300 focus:border-red-500 focus:ring-red-500':
                        submitted && $v.receptionistData.last_name.$error
                    }" />
                  <p v-if="submitted && !$v.receptionistData.last_name.required" class="mt-1 text-sm text-red-600">
                    {{ formTranslation.common.lname_required }}
                  </p>
                </div>

                <!-- Email -->
                <div>
                  <label for="email" class="block text-sm font-medium text-gray-700">
                    {{ formTranslation.common.email }}
                    <span class="text-red-500">*</span>
                  </label>
                  <input id="email" v-model="receptionistData.user_email"
                    :placeholder="formTranslation.receptionist.email_plh" type="email"
                    class="w-full rounded-lg px-4 py-2.5 border border-gray-300 shadow-sm focus:ring-black focus:border-black"
                    :class="{
                      'border-red-300 focus:border-red-500 focus:ring-red-500':
                        submitted && $v.receptionistData.user_email.$error
                    }" />
                  <p v-if="submitted && !$v.receptionistData.user_email.required" class="mt-1 text-sm text-red-600">
                    {{ formTranslation.common.email_required }}
                  </p>
                </div>

                <!-- Clinic Selection -->
                <div v-if="userData.addOns.kiviPro && getUserRole() == 'administrator'">
                  <label for="clinic_id" class="block text-sm font-medium text-gray-700">
                    {{ formTranslation.receptionist.select_clinic }}
                    <span class="text-red-500">*</span>
                  </label>
                  <select id="clinic_id" v-model="receptionistData.clinic_id"
                    class="w-full rounded-lg px-4 py-2.5 border border-gray-300 shadow-sm focus:ring-black focus:border-black"
                    :class="{
                      'border-red-300 focus:border-red-500 focus:ring-red-500':
                        submitted && $v.receptionistData.clinic_id.$error
                    }">
                    <option value="">{{ formTranslation.receptionist.select_clinic }}</option>
                    <option v-for="clinic in clinics" :key="clinic.id" :value="clinic">
                      {{ clinic.label }}
                    </option>
                  </select>
                  <p v-if="submitted && !$v.receptionistData.clinic_id.required" class="mt-1 text-sm text-red-600">
                    {{ formTranslation.common.clinic_is_required }}
                  </p>
                </div>

                <!-- Phone Number -->
                <div>
                  <label for="mobile_number" class="block text-sm font-medium text-gray-700">
                    {{ formTranslation.common.contact_no }}
                    <span class="text-red-500">*</span>
                  </label>
                  <VuePhoneNumberInput v-model="receptionistData.mobile_number"
                    :default-country-code="defaultCountryCode" @update="contactUpdateHandaler" class="mt-1"
                    :error="submitted && $v.receptionistData.mobile_number.$error" />
                  <p v-if="submitted && !$v.receptionistData.mobile_number.required" class="mt-1 text-sm text-red-600">
                    {{ formTranslation.common.contact_num_required }}
                  </p>
                </div>

                <!-- Date of Birth -->
                <div>
                  <label for="dob" class="block text-sm font-medium text-gray-700">
                    {{ formTranslation.common.dob }}
                  </label>
                  <input type="date" id="dob" v-model="receptionistData.dob"
                    :max="new Date().toISOString().slice(0, 10)"
                    class="w-full rounded-lg px-4 py-2.5 border border-gray-300 shadow-sm focus:ring-black focus:border-black" />
                </div>

                <!-- Status -->
                <div>
                  <label for="status" class="block text-sm font-medium text-gray-700">
                    {{ formTranslation.common.status }}
                    <span class="text-red-500">*</span>
                  </label>
                  <select id="status" v-model="receptionistData.user_status"
                    class="w-full rounded-lg px-4 py-2.5 border border-gray-300 shadow-sm focus:ring-black focus:border-black"
                    :class="{
                      'border-red-300 focus:border-red-500 focus:ring-red-500':
                        submitted && $v.receptionistData.user_status.$error
                    }">
                    <option value="">{{ formTranslation.appointments.select_status }}</option>
                    <option value="0">{{ formTranslation.common.active }}</option>
                    <option value="1">{{ formTranslation.common.inactive }}</option>
                  </select>
                  <p v-if="submitted && !$v.receptionistData.user_status.required" class="mt-1 text-sm text-red-600">
                    {{ formTranslation.appointments.status_required }}
                  </p>
                </div>

                <!-- Gender -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    {{ formTranslation.common.gender }}
                    <span class="text-red-500">*</span>
                  </label>
                  <div class="space-x-4">
                    <label class="inline-flex items-center">
                      <input type="radio" v-model="receptionistData.gender" value="male"
                        class="form-radio text-purple-600" />
                      <span class="ml-2">{{ formTranslation.common.male }}</span>
                    </label>
                    <label class="inline-flex items-center">
                      <input type="radio" v-model="receptionistData.gender" value="female"
                        class="form-radio text-purple-600" />
                      <span class="ml-2">{{ formTranslation.common.female }}</span>
                    </label>
                    <label v-if="defaultUserRegistrationFormSettingData === 'on'" class="inline-flex items-center">
                      <input type="radio" v-model="receptionistData.gender" value="other"
                        class="form-radio text-purple-600" />
                      <span class="ml-2">{{ formTranslation.common.other }}</span>
                    </label>
                  </div>
                  <p v-if="submitted && !$v.receptionistData.gender.required" class="mt-1 text-sm text-red-600">
                    {{ formTranslation.common.gender_required }}
                  </p>
                </div>
              </div>

              <!-- Profile Image -->
              <div class="mt-6">
                <div class="flex items-center space-x-6">
                  <div class="relative h-24 w-24 rounded-full overflow-hidden bg-gray-100">
                    <img v-if="imagePreview" :src="imagePreview" class="h-full w-full object-cover"
                      alt="Profile preview" />
                  </div>
                  <button type="button" @click="uploadProfile"
                    class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                    <i class="fas fa-pencil-alt mr-2"></i>
                    {{ formTranslation.clinic.edit_profile_img }}
                  </button>
                </div>
              </div>
            </div>

            <!-- Other Details Section -->
            <div class="mb-6">
              <h4 class="text-lg font-medium text-gray-900 mb-4">
                {{ formTranslation.doctor.other_details }}
              </h4>

              <!-- Address -->
              <div class="mb-6">
                <label for="address" class="block text-sm font-medium text-gray-700">
                  {{ formTranslation.common.address }}
                </label>
                <textarea id="address" v-model="receptionistData.address"
                  :placeholder="formTranslation.receptionist.address_plh" rows="3"
                  class="w-full rounded-lg px-4 py-2.5 border border-gray-300 shadow-sm focus:ring-black focus:border-black"></textarea>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Country -->
                <div>
                  <label for="country" class="block text-sm font-medium text-gray-700">
                    {{ formTranslation.common.country }}
                  </label>
                  <input id="country" v-model="receptionistData.country"
                    :placeholder="formTranslation.receptionist.country_plh" type="text"
                    class="w-full rounded-lg px-4 py-2.5 border border-gray-300 shadow-sm focus:ring-black focus:border-black" />
                </div>

                <!-- City -->
                <div>
                  <label for="city" class="block text-sm font-medium text-gray-700">
                    {{ formTranslation.common.city }}
                  </label>
                  <input id="city" v-model="receptionistData.city" :placeholder="formTranslation.receptionist.city_plh"
                    type="text"
                    class="w-full rounded-lg px-4 py-2.5 border border-gray-300 shadow-sm focus:ring-black focus:border-black" />
                </div>

                <!-- Postal Code -->
                <div>
                  <label for="postal_code" class="block text-sm font-medium text-gray-700">
                    {{ formTranslation.common.postal_code }}
                  </label>
                  <input id="postal_code" v-model="receptionistData.postal_code"
                    :placeholder="formTranslation.receptionist.pcode_plh" type="text"
                    class="w-full rounded-lg px-4 py-2.5 border border-gray-300 shadow-sm focus:ring-black focus:border-black" />
                </div>
              </div>
            </div>
          </div>

          <!-- Footer -->
          <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
            <button type="button" @click="handleClose"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
              {{ formTranslation.common.cancel }}
            </button>
            <button type="submit" :disabled="loading"
              class="px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-md shadow-sm hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed">
              <template v-if="!loading">
                <i class="fa fa-save mr-2"></i>
                {{ formTranslation.receptionist.save_btn }}
              </template>
              <template v-else>
                <i class="fa fa-sync fa-spin mr-2"></i>
                {{ formTranslation.common.loading }}
              </template>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import VuePhoneNumberInput from "vue-phone-number-input";
import "vue-phone-number-input/dist/vue-phone-number-input.css";
import {
  required,
  numeric,
  requiredIf,
  alpha,
  minLength,
  maxLength,
  minValue,
  maxValue,
} from "vuelidate/lib/validators";
import { post, get } from "../../config/request";
import {
  validateForm,
  randomString,
  phoneNumber,
  alphaSpace,
  emailValidate,
} from "../../config/helper";

export default {
  components: {
    VuePhoneNumberInput,
  },
  props: {
    openAddReceptionistModal: {
      // Updated prop name to match parent
      type: Boolean,
      required: true,
    },
    editMode: {
      type: Boolean,
      default: false,
    },
    initialData: {
      type: Object,
      default: () => ({}),
    },
  },
  data: () => {
    return {
      receptionistData: {},
      loading: false,
      submitted: false,
      qualificationSubmitted: false,
      cardTitle: "Add receptionist",
      buttonText: '<i class="fa fa-save"></i> Save',
      price_type: ["range", "fixed"],
      randomString: randomString(8),
      imagePreview: pluginBASEURL + "assets/images/kc-demo-img.png",
      formLoader: false,
      clinicMultiselectLoader: true,
      defaultCountryCode: null,
      defaultUserRegistrationFormSettingData: "on",
    };
  },
  mounted() {
    this.getCountryCodeData();
    this.getUserRegistrationFormData();
    // this.receptionistData = this.defaultReceptionistData();

    // Populate form with initial data if in edit mode
    if (this.editMode && this.initialData) {
      this.receptionistData = {
        ...this.defaultReceptionistData(),
        ...this.initialData,
      };
    }
    this.init();
  },
  watch: {
    initialData: {
      handler(newData) {
        if (newData) {
          this.receptionistData = {
            ...this.defaultReceptionistData(),
            ...newData,
          };

          // Match the clinic_id with the correct clinic object from the clinics array
          if (this.receptionistData.clinic_id && this.clinics) {
            const clinicId = typeof this.receptionistData.clinic_id === 'object'
              ? this.receptionistData.clinic_id.id
              : this.receptionistData.clinic_id;

            const matchedClinic = this.clinics.find(clinic => clinic.id == clinicId);
            if (matchedClinic) {
              this.receptionistData.clinic_id = matchedClinic;
            }
          }
        }
      },
      immediate: true,
    },
  },
  validations: {
    receptionistData: {
      first_name: {
        required,
        minLength: minLength(2),
        maxLength: maxLength(50),
      },
      last_name: {
        required,
        minLength: minLength(2),
        maxLength: maxLength(50),
      },
      user_email: {
        required,
        emailValidate,
      },
      mobile_number: {
        required,
        // phoneNumber,
        minLength: minLength(4),
        maxLength: maxLength(15),
      },
      // dob: {required},
      user_status: { required },
      gender: {
        required,
      },
      clinic_id: {
        required: requiredIf(function () {
          return (
            this.userData.addOns.kiviPro == true &&
            this.getUserRole() == "administrator"
          );
        }),
      },
      // city: {

      //     maxLength: maxLength(30)
      // },
      // country: {

      //     maxLength: maxLength(30)
      // },
      // postal_code: {
      //     maxLength: maxLength(12)
      // }
    },
  },
  methods: {
    contactUpdateHandaler: function (val) {
      this.receptionistData.country_code = val.countryCode;
      this.receptionistData.country_calling_code = val.countryCallingCode;
    },
    init: function () {
      /// Code for the Edit functionality...
      if (this.$route.params.id !== undefined) {
        this.cardTitle = this.formTranslation.common.edit_receptionist;
        this.buttonText =
          '<i class="fa fa-save"></i> ' + this.formTranslation.common.save;
        this.formLoader = true;
        get("receptionist_edit", {
          id: this.$route.params.id,
        })
          .then((response) => {
            if (
              response.data.status !== undefined &&
              response.data.status === true
            ) {
              setTimeout(() => {
                this.receptionistData = response.data.data;

                // Match the clinic_id with the correct clinic object from the clinics array
                if (this.receptionistData.clinic_id && this.clinics) {
                  const clinicId = typeof this.receptionistData.clinic_id === 'object'
                    ? this.receptionistData.clinic_id.id
                    : this.receptionistData.clinic_id;

                  const matchedClinic = this.clinics.find(clinic => clinic.id == clinicId);
                  if (matchedClinic) {
                    this.receptionistData.clinic_id = matchedClinic;
                  }
                }

                if (
                  response.data.data.country_calling_code !== "" &&
                  response.data.data.country_calling_code !== undefined
                ) {
                  this.defaultCountryCode = response.data.data.country_code;
                }
                this.formLoader = false;
                // this.receptionistData.dob = new Date(this.receptionistData.dob + ' 00:00');
                if (this.receptionistData.user_profile) {
                  this.imagePreview = this.receptionistData.user_profile;
                }
              }, 200);
            }
          })
          .catch((error) => {
            this.formLoader = false;
            console.log(error);
            displayErrorMessage(this.formTranslation.widgets.record_not_found);
          });
      }
    },
    handleClose() {
      this.receptionistData = this.defaultReceptionistData();
      this.submitted = false;
      this.$emit('close', false);
      this.$emit('refresh-list', true);
    },
    uploadProfile() {
      let _this = this;

      var custom_uploader = kivicareCustomImageUploader(this.formTranslation);

      custom_uploader.on("select", function () {
        var attachment = custom_uploader
          .state()
          .get("selection")
          .first()
          .toJSON();
        _this.imagePreview = attachment.url;
        _this.receptionistData.profile_image = attachment.id;
      });

      //Open the uploader dialog
      custom_uploader.open();
    },
    defaultReceptionistData: function () {
      var oneYearFromNow = new Date();
      oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() - 18);
      return {
        first_name: "",
        last_name: "",
        username: "",
        user_email: "",
        user_pass: "",
        mobile_number: "",
        country_code: "",
        country_calling_code: "",
        gender: "",
        dob: "",
        about_me: "",
        address: "",
        city: "",
        country: "",
        postal_code: "",
        specialties: [],
        price_type: this.formTranslation.doctor.range,
        price: 0,
        minPrice: 0,
        maxPrice: 0,
        qualifications: [],
        time_slot: 5,
        custom_fields: {},
        user_status: 0,
        clinic_id: "",
        profile_image: "",
      };
    },
    handleSubmit: function () {
      this.loading = true;

      this.submitted = true;

      // stop here if form is invalid
      this.$v.$touch();
      this.$nextTick(() => {
        if (
          document.querySelector(".is-invalid") !== null &&
          document.querySelector(".is-invalid") !== undefined
        ) {
          document
            .querySelector(".is-invalid")
            .scrollIntoView({ block: "center", behavior: "smooth" });
        } else if (
          document.querySelector(".invalid-feedback") !== null &&
          document.querySelector(".invalid-feedback") !== undefined
        ) {
          document
            .querySelector(".invalid-feedback")
            .scrollIntoView({ block: "center", behavior: "smooth" });
        }
      });
      if (this.$v.receptionistData.$invalid) {
        this.loading = false;
        return;
      }

      // this.receptionistData.dob = moment(this.receptionistData.dob).format("YYYY-MM-DD");

      if (validateForm("receptionistDataForm")) {
        post("receptionist_save", this.receptionistData)
          .then((response) => {
            this.loading = false;
            if (
              response.data.status !== undefined &&
              response.data.status === true
            ) {
              displayMessage(response.data.message);
              this.handleClose();
            } else {
              // this.receptionistData.dob = new Date(this.receptionistData.dob + ' 00:00');
              displayErrorMessage(response.data.message);
            }
          })
          .catch((error) => {
            console.log(error);
            this.loading = false;
            displayErrorMessage(
              this.formTranslation.common.internal_server_error
            );
          });
      }
    },
    getCountryCodeData: function () {
      get("get_country_code_settings_data", {})
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.defaultCountryCode = response.data.data.country_code;
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    getUserRegistrationFormData: function () {
      get("get_user_registration_form_settings_data", {})
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.defaultUserRegistrationFormSettingData =
              response.data.data.userRegistrationFormSettingData;
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
  },
  computed: {
    userData() {
      return this.$store.state.userDataModule.user;
    },
    clinics() {
      this.clinicMultiselectLoader = false;
      return this.$store.state.clinic;
    },
    formTranslation: function () {
      return this.$store.state.staticDataModule.langTranslateData;
    },
  },
};
</script>
<style scoped>
[type="date"] {
  background: #fff url(https://cdn1.iconfinder.com/data/icons/cc_mono_icon_set/blacks/16x16/calendar_2.png) 97% 50% no-repeat;
}

[type="date"]::-webkit-inner-spin-button {
  display: none;
}

[type="date"]::-webkit-calendar-picker-indicator {
  opacity: 0;
}

label {
  display: block;
}

#doc_birthdate {
  border: 1px solid #c4c4c4;
  border-radius: 5px;
  background-color: #fff;
  padding: 3px 5px;
  box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.1);
  /* width: 190px; */
  width: 100%;
  height: 45px;
  color: #8c9cad;
}

#doc_birthdate ::placeholder {
  color: #8c9cad;
}
</style>
