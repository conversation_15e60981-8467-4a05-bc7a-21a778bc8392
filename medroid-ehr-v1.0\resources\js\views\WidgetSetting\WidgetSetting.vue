<template>
  <div :style="cssProps">
    <!-- Loader -->
    <div class="fixed inset-0 flex items-center justify-center bg-white bg-opacity-75 z-50" v-if="formLoader">
      <loader-component-2></loader-component-2>
    </div>

    <!-- Main Content -->
    <div v-else class="bg-white rounded-lg shadow">
      <!-- Header -->
      <div class="p-4">
        <h2 class="text-xl font-bold flex items-center gap-2">
          {{ formTranslation.widget_setting.widget_setting }}
          <a v-if="request_status == 'off'"
            href="https://apps.medroid.ai/docs/product/kivicare/clinic-admin/settings/#widget-setting"
            target="_blank"
            class="text-gray-500 hover:text-gray-700">
            <i class="fa fa-question-circle"></i>
          </a>
        </h2>
      </div>

      <hr class="border-t border-gray-200" />

      <!-- Form -->
      <form id="widgetSettingForm" @submit.prevent="handleSubmit" :novalidate="true" enctype="multipart/form-data">
        
        <!-- Clinic Settings Section -->
        <div v-if="userData.addOns.kiviPro" class="p-4 space-y-4">
          <h2 class="text-lg font-semibold">
            {{ formTranslation.widget_setting.clinic_setting }}
          </h2>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <!-- Show Clinic Image -->
            <div class="space-y-2">
              <label class="inline-flex items-center">
                <input type="checkbox"
                       v-model="widgetConfigData.showClinicImage"
                       class="form-checkbox h-5 w-5 text-blue-600 rounded"
                       id="show-clinic-image">
                <span class="ml-2 font-semibold">
                  {{ formTranslation.widget_setting.show_clinic_image }}
                </span>
              </label>
            </div>

            <!-- Show Clinic Address -->
            <div class="space-y-2">
              <label class="inline-flex items-center">
                <input type="checkbox"
                       v-model="widgetConfigData.showClinicAddress"
                       class="form-checkbox h-5 w-5 text-blue-600 rounded"
                       id="show-clinic-address">
                <span class="ml-2 font-semibold">
                  {{ formTranslation.widget_setting.show_clinic_address }}
                </span>
              </label>
            </div>

            <!-- Contact Details -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                {{ formTranslation.widget_setting.contact_details }}
              </label>
              <multi-select
                id="mode"
                v-model="widgetConfigData.clinicContactDetails"
                label="label"
                track-by="id"
                :options="dropDownOption"
                :placeholder="formTranslation.settings.tag_plh_option"
                class="w-full">
              </multi-select>
            </div>
          </div>
        </div>

        <hr class="border-t border-gray-200" v-if="userData.addOns.kiviPro" />

        <!-- Doctor Settings Section -->
        <div class="p-4 space-y-4">
          <h2 class="text-lg font-semibold">
            {{ formTranslation.widget_setting.doctor_setting }}
          </h2>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <!-- Doctor Settings Checkboxes -->
            <div class="space-y-2">
              <label class="inline-flex items-center">
                <input type="checkbox"
                       v-model="widgetConfigData.showDoctorImage"
                       class="form-checkbox h-5 w-5 text-blue-600 rounded"
                       id="show-doctor-image">
                <span class="ml-2 font-semibold">
                  {{ formTranslation.widget_setting.show_doctor_image }}
                </span>
              </label>
            </div>

            <div class="space-y-2">
              <label class="inline-flex items-center">
                <input type="checkbox"
                       v-model="widgetConfigData.showDoctorExperience"
                       class="form-checkbox h-5 w-5 text-blue-600 rounded"
                       id="show-doctor-experience">
                <span class="ml-2 font-semibold">
                  {{ formTranslation.widget_setting.show_doctor_experience }}
                </span>
              </label>
            </div>

            <!-- Doctor Contact Details -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                {{ formTranslation.widget_setting.contact_details }}
              </label>
              <multi-select
                id="mode"
                v-model="widgetConfigData.doctorContactDetails"
                label="label"
                track-by="id"
                :options="dropDownOption"
                :placeholder="formTranslation.settings.tag_plh_option"
                class="w-full">
              </multi-select>
            </div>

            <!-- Additional Doctor Settings -->
            <div class="space-y-2">
              <label class="inline-flex items-center">
                <input type="checkbox"
                       v-model="widgetConfigData.showDoctorSpeciality"
                       class="form-checkbox h-5 w-5 text-blue-600 rounded"
                       id="show-doctor-speciality">
                <span class="ml-2 font-semibold">
                  {{ formTranslation.widget_setting.show_doctor_speciality }}
                </span>
              </label>
            </div>

            <div class="space-y-2">
              <label class="inline-flex items-center">
                <input type="checkbox"
                       v-model="widgetConfigData.showDoctorDegree"
                       class="form-checkbox h-5 w-5 text-blue-600 rounded"
                       id="show-doctor-degree">
                <span class="ml-2 font-semibold">
                  {{ formTranslation.widget_setting.show_doctor_degree }}
                </span>
              </label>
            </div>

            <div class="space-y-2">
              <label class="inline-flex items-center">
                <input type="checkbox"
                       v-model="widgetConfigData.showDoctorRating"
                       class="form-checkbox h-5 w-5 text-blue-600 rounded"
                       id="show-doctor-rating">
                <span class="ml-2 font-semibold">
                  {{ formTranslation.widget_setting.show_doctor_rating }}
                </span>
              </label>
            </div>
          </div>
        </div>

        <hr class="border-t border-gray-200" />

        <!-- Service Settings Section -->
        <div class="p-4 space-y-4">
          <h2 class="text-lg font-semibold">
            {{ formTranslation.widget_setting.service_setting }}
          </h2>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <!-- Service Settings Checkboxes -->
            <div class="space-y-2">
              <label class="inline-flex items-center">
                <input type="checkbox"
                       v-model="widgetConfigData.showServiceImage"
                       class="form-checkbox h-5 w-5 text-blue-600 rounded"
                       id="show-service-image">
                <span class="ml-2 font-semibold">
                  {{ formTranslation.widget_setting.show_service_image }}
                </span>
              </label>
            </div>

            <div class="space-y-2">
              <label class="inline-flex items-center">
                <input type="checkbox"
                       v-model="widgetConfigData.showServicetype"
                       class="form-checkbox h-5 w-5 text-blue-600 rounded"
                       id="show-service-type">
                <span class="ml-2 font-semibold">
                  {{ formTranslation.widget_setting.show_service_type }}
                </span>
              </label>
            </div>

            <div class="space-y-2">
              <label class="inline-flex items-center">
                <input type="checkbox"
                       v-model="widgetConfigData.showServicePrice"
                       class="form-checkbox h-5 w-5 text-blue-600 rounded"
                       id="show-service-price">
                <span class="ml-2 font-semibold">
                  {{ formTranslation.widget_setting.show_service_price }}
                </span>
              </label>
            </div>

            <!-- Pro Features -->
            <div v-if="userData.addOns.kiviPro" class="space-y-2">
              <label class="inline-flex items-center">
                <input type="checkbox"
                       v-model="widgetConfigData.showServiceDuration"
                       class="form-checkbox h-5 w-5 text-blue-600 rounded"
                       id="show-service-duration">
                <span class="ml-2 font-semibold">
                  {{ formTranslation.widget_setting.show_service_duration }}
                </span>
              </label>
            </div>

            <div class="space-y-2">
              <label class="inline-flex items-center">
                <input type="checkbox"
                       v-model="widgetConfigData.skip_service_when_single"
                       class="form-checkbox h-5 w-5 text-blue-600 rounded"
                       id="skip-service-when-single">
                <span class="ml-2 font-semibold">
                  {{ formTranslation.widget_setting.skip_service_when_single }}
                </span>
              </label>
            </div>
          </div>
        </div>

        <hr class="border-t border-gray-200" />

        <!-- Loader Settings Section -->
        <div class="p-4 space-y-4">
          <h2 class="text-lg font-semibold">
            {{ formTranslation.widget_setting.loader_setting }}
          </h2>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                {{ formTranslation.widget_setting.loader_select }}
              </label>
              <div class="flex items-center space-x-2">
                <button 
                  class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  type="button"
                  @click.prevent="uploadWidgetLoader()">
                  {{ formTranslation.common.choose_file }}
                </button>
                <span class="text-gray-600">{{ widgetLoader }}</span>
              </div>
            </div>

            <div class="flex justify-center items-center">
              <img 
                v-if="widgetLoaderPreview"
                :src="widgetLoaderPreview"
                class="max-h-24 object-contain"
                alt="Loader preview">
              <div v-else class="double-lines-spinner"></div>
            </div>
          </div>
        </div>

        <hr class="border-t border-gray-200" />

        <!-- Widget Color Section -->
        <div class="p-4 space-y-4">
          <h2 class="text-lg font-semibold">
            {{ formTranslation.widget_setting.widget_color }}
          </h2>

          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <!-- Primary Color -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                {{ formTranslation.widget_setting.set_widget_primary_color }}
              </label>
              <input 
                type="color"
                v-model="widgetConfigData.primaryColor"
                class="h-10 w-full rounded border border-gray-300"
                id="example-color-input">
            </div>

            <!-- Primary Hover Color -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                {{ formTranslation.widget_setting.set_widget_primary_hover_color }}
              </label>
              <input 
                type="color"
                v-model="widgetConfigData.primaryHoverColor"
                class="h-10 w-full rounded border border-gray-300"
                id="example-color-input1">
            </div>

            <!-- Secondary Color -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                {{ formTranslation.widget_setting.set_widget_secondary_color }}
              </label>
              <input 
                type="color"
                v-model="widgetConfigData.secondaryColor"
                class="h-10 w-full rounded border border-gray-300"
                id="example-color-input2">
            </div>

            <!-- Secondary Hover Color -->
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">
                {{ formTranslation.widget_setting.set_widget_secondary_hover_color }}
              </label>
              <input 
                type="color"
                v-model="widgetConfigData.secondaryHoverColor"
                class="h-10 w-full rounded border border-gray-300"
                id="example-color-input4">
            </div>
          </div>
        </div>

        <hr class="border-t border-gray-200" />

        <!-- Widget Order Section -->
        <div class="relative">
          <overlay-message v-if="userData.addOns.kiviPro != true" addon_type="pro"></overlay-message>
          <div class="p-4 space-y-4">
            <h2 class="text-lg font-semibold">
              {{ formTranslation.widget_setting.widget_order }}
            </h2>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div v-if="listLoader" class="flex justify-center items-center">
                <i class="fa fa-spinner fa-spin text-4xl text-gray-600"></i>
              </div>
              <div v-else>
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th scope="col" class="w-1/12 px-6 py-3"></th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {{ formTranslation.static_data.dt_lbl_name }}
                      </th>
                    </tr>
                  </thead>
                  <draggable v-model="widgetListOrder" tag="tbody" class="bg-white divide-y divide-gray-200"
                    :options="{ animation: 1000, handle: '.my_handle:not(.exclude-this-item)' }" 
                    :move="checkMove">
                    <tr v-for="item in widgetListOrder" 
                        v-if="!(!userData.addOns.kiviPro && item.att_name === 'clinic')"
                        :key="item.name" 
                        :class="!item.fixed ? 'draggable my_handle hover:bg-gray-50' : ''"
                        class="cursor-move">
                      <td class="px-6 py-4 whitespace-nowrap w-1/12">
                        <i :class="'my_handle ' + (item.fixed ? ' exclude-this-item' : ' fa fa-align-justify text-gray-400')"></i>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        {{ item.name }}
                      </td>
                    </tr>
                  </draggable>
                </table>
              </div>
            </div>
          </div>
        </div>

        <hr class="border-t border-gray-200" />

        <!-- Widget Print Settings -->
        <div class="p-4 space-y-4">
          <h2 class="text-lg font-semibold">
            {{ formTranslation.widget_setting.widget_print_setting }}
          </h2>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Print Toggle -->
            <div class="space-y-2">
              <label class="inline-flex items-center">
                <input type="checkbox"
                       v-model="widgetConfigData.widget_print"
                       @change="printStatusChange"
                       class="form-checkbox h-5 w-5 text-blue-600 rounded"
                       id="widget-print">
                <span class="ml-2 font-semibold">
                  {{ formTranslation.widget_setting.print }}
                </span>
              </label>
            </div>

            <!-- Redirect After WooCommerce -->
            <div v-if="widgetConfigData.widget_print && (userData.addOns.kiviPro || userData.addOns.telemed || userData.addOns.googlemeet)"
                 class="space-y-2">
              <label class="inline-flex items-center">
                <input type="checkbox"
                       v-model="widgetConfigData.afterWoocommerceRedirect"
                       class="form-checkbox h-5 w-5 text-blue-600 rounded"
                       id="widget-print-redirect">
                <div class="ml-2">
                  <span class="font-semibold block">
                    {{ formTranslation.widget_setting.redirectAfterWoocommerce }}
                  </span>
                  <span class="text-sm text-gray-500">
                    {{ formTranslation.widget_setting.redirectAfterWoocommerceNotice }}
                  </span>
                </div>
              </label>
            </div>
          </div>
        </div>

        <!-- Form Submit Button -->
        <div class="p-4 border-t border-gray-200">
          <div class="flex justify-end">
            <button type="submit" 
                    :disabled="loader"
                    class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2">
              <i :class="loader ? 'fa fa-sync fa-spin' : 'fa fa-save'"></i>
              {{ loader ? formTranslation.common.loading : formTranslation.common.save }}
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>
<script>
import { required } from "vuelidate/lib/validators";
import { post, get } from "../../config/request";
import { alphaSpace, validateForm } from "../../config/helper";
import draggable from 'vuedraggable';
import { Chrome } from "vue-color";

export default {
  name: "WidgetSetting",
  display: "Table",
  order: 8,
  components: {
    draggable,
    "chrome-color-picker": Chrome,
  },
  data: () => {
    return {
      widgetConfigData: {},
      submitted: false,
      listLoader: true,
      dragging: false,
      request_status: 'off',
      dropDownOption: [
        { id: 1, label: 'phone_email' },
        { id: 2, label: 'show_phone_number' },
        { id: 3, label: 'show_email_address' },
        { id: 4, label: 'hide_contact_details' }
      ],
      loader: false,
      widgetLoaderPreview: '',
      formLoader: true,
      widgetListOrder: [],
      widgetLoader: ''
    };
  },
  mounted() {
    if (!['administrator'].includes(this.getUserRole())) {
      this.$router.push({ name: "403" })
    }
    this.widgetLoader = this.formTranslation.common.no_file_chosen
    this.widgetConfigData = this.defaultWidgetData();
    this.widgetEdit();
    this.init();
    this.getWidgetLoader();
    this.getModule();
  },
  validations: {
    widgetConfigData: {
      showClinicImage: {},
      showClinicAddress: {},
      clinicContactDetails: {},
      showDoctorImage: {},
      showDoctorExperience: {},
      doctorContactDetails: {},
      showDoctorSpeciality: {},
      showDoctorDegree: {},
      showDoctorRating: {},
      showServiceImage: {},
      skip_service_when_single: {},
      showServicetype: {},
      showServicePrice: {},
      showServiceDuration: {},
      primaryColor: {},
      primaryHoverColor: {},
      secondaryColor: {},
      secondaryHoverColor: {},
      widget_print: {},
      afterWoocommerceRedirect: {},
      widgetLoader: {}
    }
  },
  methods: {
    init() {
      this.dropDownOption = this.dropDownOption.map((item) => {
        item.label = this.formTranslation.widget_setting[item.label];
        return item;
      })
    },
    checkMove(e) {
      return this.isDraggable(e.draggedContext);
    },
    isDraggable(context) {
      const { index, futureIndex } = context
      return !(this.widgetConfigData.list[index].fixed || this.widgetConfigData.list[futureIndex].fixed);
    },
    defaultWidgetData() {
      return {
        showClinicImage: '1',
        showClinicAddress: '1',
        clinicContactDetails: '',
        showDoctorImage: '1',
        showDoctorExperience: '1',
        doctorContactDetails: '',
        showDoctorSpeciality: '1',
        showDoctorDegree: '1',
        showDoctorRating: '1',
        showServiceImage: '1',
        skip_service_when_single: '0',
        showServicetype: '1',
        showServicePrice: '1',
        showServiceDuration: '1',
        primaryColor: '#7093e5',
        primaryHoverColor: '#948f8f',
        secondaryColor: '#f68685',
        secondaryHoverColor: '#df504e',
        widget_print: '1',
        afterWoocommerceRedirect: '1',
        list: [],
      };
    },
    getWidgetLoader: function () {
      this.formLoader = true;
      get("get_widget_loader", {})
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.widgetLoaderPreview = response.data.url
          }
          this.formLoader = false;
        })
        .catch((error) => {
          this.formLoader = false;
          console.log(error);
        });
    },
    uploadWidgetLoader: function () {
      let _this = this;

      var custom_uploader = kivicareCustomImageUploader(this.formTranslation)

      custom_uploader.on('select', function () {
        var attachment = custom_uploader.state().get('selection').first().toJSON();
        _this.widgetLoaderPreview = attachment.url;
        _this.widgetLoader = attachment.filename
        post("upload_widget_loader", { widget_loader: attachment.id })
          .then((response) => {
            if (
              response.data.status !== undefined &&
              response.data.status === true
            ) {
              _this.widgetLoaderPreview = response.data.data;
              displayMessage(response.data.message);
            } else {
              displayErrorMessage(response.data.message);
            }
          })
          .catch((error) => {
            console.log(error);
          });
      });

      //Open the uploader dialog
      custom_uploader.open();
    },
    handleSubmit() {

      this.submitted = true;
      this.loader = true;
      // stop here if form is invalid
      this.$v.$touch();
      if (this.$v.$invalid) {
        this.loader = false;
        return;
      }

      if (validateForm("widgetSettingForm")) {
        post("widget_setting_save", { data: this.widgetConfigData, list: this.widgetListOrder })
          .then((response) => {
            this.loader = false;
            if (
              response.data.status !== undefined &&
              response.data.status === true
            ) {
              displayMessage(response.data.message);
            } else {
              displayErrorMessage(response.data.message);
            }
          })
          .catch((error) => {
            console.log(error);
            this.loader = false;
            this.submitted = false;
            displayErrorMessage(this.formTranslation.common.internal_server_error);
          });
      }
    },
    widgetEdit() {
      this.formLoader = true;
      get("get_widget_setting", {})
        .then((response) => {
          this.formLoader = false;
          this.listLoader = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.widgetConfigData = response.data.data;
            this.widgetConfigData.list = response.data.widgetOrder;
          }
          if (response.data.widgetOrder !== undefined && response.data.widgetOrder.length > 0) {
            this.widgetListOrder = response.data.widgetOrder
          }
        })
        .catch((error) => {
          this.listLoader = false;
          this.formLoader = false;
          console.log(error);
          displayErrorMessage(this.formTranslation.widgets.record_not_found);
        });
    },
    printStatusChange() {
      if (this.widgetConfigData.widget_print != true) {
        this.widgetConfigData.afterWoocommerceRedirect = false
      }
    },
    getModule: function () {
      if (window.request_data.link_show_hide !== undefined && window.request_data.link_show_hide !== '') {
        this.request_status = window.request_data.link_show_hide;
      }
    }
  },
  computed: {
    userData() {
      return this.$store.state.userDataModule.user;
    },
    cssProps() {
      return {
        '--color-spinner': this.widgetConfigData.primaryColor,
      }
    }
  },
  watch: {

  },
  formTranslation: function () {
    return this.$store.state.staticDataModule.langTranslateData;
  },
};
</script>
<style scoped>
.buttons {
  margin-top: 35px;
}

.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}

.my_handle {
  float: right;
}


*,
*:before {
  -webkit-box-sizing: inherit;
  -moz-box-sizing: inherit;
  box-sizing: inherit;
}

.double-lines-spinner {
  width: 64px !important;
  height: 64px !important;
  border-radius: 50% !important;
  position: relative !important;
}

.double-lines-spinner::before {
  content: "" !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  display: block !important;
  border-radius: 50% !important;
  border: 2px solid rgba(0, 0, 0, 0.05) !important;
}

.double-lines-spinner::before {
  border-right: 2px solid var(--color-spinner) !important;
  -webkit-animation: kivi-spin 0.5s 0s linear infinite;
  -moz-animation: kivi-spin 0.5s 0s linear infinite;
  animation: kivi-spin 0.5s 0s linear infinite !important;
}


@keyframes kivi-spin {
  100% {
    transform: rotate(360deg) !important;
    -webkit-transform: rotate(360deg);
  }
}

@-moz-keyframes kivi-spin {
  100% {
    -moz-transform: rotate(360deg);
  }
}

@-webkit-keyframes kivi-spin {
  100% {
    -webkit-transform: rotate(360deg);
  }
}
</style>