<template>
  <div class="space-y-3">
    <!-- Additional Tabs Toggle Buttons - KiviCare Style -->
    <div class="flex flex-wrap gap-2 mb-2">
      <div v-for="(field, key) in template" :key="key">
        <button @click="toggleTab(key)"
          class="flex items-center gap-1.5 px-3 py-1.5 rounded text-sm relative group" :class="[
            isTabActive(key)
              ? 'bg-black text-white border border-black'
              : 'bg-white border hover:bg-gray-50',
          ]">
          <component :is="getIcon(field.icon)" class="w-4 h-4" />
          <span>{{ field.label }}</span>
          <span v-if="!isTabActive(key)"
            class="absolute -top-2 -right-2 w-5 h-5 bg-black text-white rounded-full flex items-center justify-center text-xs group-hover:bg-black">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
              class="w-3 h-3">
              <path d="M5 12h14"></path>
              <path d="M12 5v14"></path>
            </svg>
          </span>
        </button>
      </div>
    </div>

    <!-- Active Additional Tabs - KiviCare Style -->
    <div v-if="Object.keys(additionalTabs).length" class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-3">
      <div v-for="(entries, tabKey) in additionalTabs" :key="tabKey" class="relative">
        <div class="bg-white rounded border p-3">
          <div class="flex justify-between items-center mb-2">
            <div class="flex items-center gap-2">
              <component :is="getIcon(template[tabKey]?.icon)" class="w-4 h-4" />
              <h2 class="font-medium">{{ template[tabKey]?.label || tabKey }}</h2>
            </div>
            <div class="flex gap-1">
              <button @click="addEntry(tabKey)" class="text-blue-500 hover:text-blue-600">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                  class="w-4 h-4">
                  <path d="M5 12h14"></path>
                  <path d="M12 5v14"></path>
                </svg>
              </button>
              <button @click="removeTab(tabKey)" class="text-red-500 hover:text-red-600">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                  class="w-4 h-4">
                  <path d="M18 6 6 18"></path>
                  <path d="m6 6 12 12"></path>
                </svg>
              </button>
            </div>
          </div>

          <div class="flex flex-col gap-2">
            <!-- Existing Entries -->
            <div v-if="entries?.length" class="space-y-2">
              <div v-for="(entry, index) in entries" :key="entry.id" class="relative">
                <textarea :value="entry.content" @input="updateEntry(tabKey, entry.id, $event.target.value)"
                  class="w-full h-20 text-sm resize-none focus:outline-none border-b"
                  :placeholder="'Enter ' + (template[tabKey]?.label || tabKey).toLowerCase() + '...'"></textarea>
                <div class="flex justify-between items-center mt-1">
                  <div class="text-xs text-gray-500">{{ formatDate(entry.created_at) }}</div>
                  <button @click="removeEntry(tabKey, entry.id)" class="text-red-500 hover:text-red-600 text-xs">
                    Remove
                  </button>
                </div>
              </div>
            </div>

            <!-- Empty State -->
            <div v-else>
              <textarea @input="addEntry(tabKey, $event.target.value)"
                class="w-full h-20 text-sm resize-none focus:outline-none border-b"
                :placeholder="'Enter ' + (template[tabKey]?.label || tabKey).toLowerCase() + '...'"></textarea>
            </div>

            <div class="flex justify-between items-center w-full">
              <button class="text-sm text-gray-500 hover:text-gray-600">
                Prefill
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State for Additional Tabs -->
    <div v-else class="text-center py-8 text-gray-500">
      <div class="text-sm mb-4">No additional sections added yet</div>
      <div class="relative inline-block">
        <Button variant="outline" @click="showDropdown = !showDropdown">
          <Plus class="w-4 h-4 mr-1" />
          Add Your First Section
        </Button>

        <!-- Custom Dropdown -->
        <div v-if="showDropdown" class="absolute left-1/2 transform -translate-x-1/2 top-full mt-1 w-56 bg-white border rounded-md shadow-lg z-50">
          <div
            v-for="(field, key) in availableAdditionalTabs"
            :key="key"
            @click="addTab(key)"
            class="flex items-center px-3 py-2 hover:bg-gray-50 cursor-pointer"
          >
            <component :is="getIcon(field.icon)" class="w-4 h-4 mr-2" />
            {{ field.label }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import {
  Plus, X, AlertTriangle, Users, User, FileText, Pill,
  List, Shield, LifeBuoy, Heart, Brain, Activity, Edit
} from 'lucide-vue-next'

interface TabEntry {
  id: number
  content: string
  created_at: string
}

interface Props {
  additionalTabs: Record<string, TabEntry[]>
  template: Record<string, any>
}

interface Emits {
  (e: 'update', additionalTabs: Record<string, TabEntry[]>): void
  (e: 'add-tab', tabKey: string): void
  (e: 'remove-tab', tabKey: string): void
  (e: 'add-entry', tabKey: string, content: string): void
  (e: 'remove-entry', tabKey: string, entryId: number): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Component state
const showDropdown = ref(false)

// Methods
const isTabActive = (tabKey: string) => {
  return !!props.additionalTabs[tabKey]
}

const toggleTab = (tabKey: string) => {
  if (isTabActive(tabKey)) {
    removeTab(tabKey)
  } else {
    addTab(tabKey)
  }
  showDropdown.value = false
}

// Methods
const addTab = (tabKey: string) => {
  emit('add-tab', tabKey)
  showDropdown.value = false
}

const removeTab = (tabKey: string) => {
  emit('remove-tab', tabKey)
}

const addEntry = (tabKey: string) => {
  emit('add-entry', tabKey, '')
}

const removeEntry = (tabKey: string, entryId: number) => {
  emit('remove-entry', tabKey, entryId)
}

const updateEntry = (tabKey: string, entryId: number, content: string) => {
  const updatedAdditionalTabs = { ...props.additionalTabs }
  if (updatedAdditionalTabs[tabKey]) {
    const entry = updatedAdditionalTabs[tabKey].find(e => e.id === entryId)
    if (entry) {
      entry.content = content
      emit('update', updatedAdditionalTabs)
    }
  }
}

const getIcon = (iconName: string) => {
  const icons = {
    'alert-triangle': AlertTriangle,
    'users': Users,
    'user': User,
    'file-text': FileText,
    'pill': Pill,
    'list': List,
    'shield': Shield,
    'life-buoy': LifeBuoy,
    'heart': Heart,
    'brain': Brain,
    'activity': Activity,
    'edit': Edit
  }
  
  return icons[iconName] || FileText
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString()
}
</script>
