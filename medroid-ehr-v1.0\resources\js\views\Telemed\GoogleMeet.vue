<template>
  <div>
    <!-- Not Connected State -->
    <div v-if="userData.is_enable_doctor_gmeet == 'off'" class="bg-white rounded-lg shadow-sm mb-6">
      <!-- Header -->
      <div class="flex items-center p-6 border-b border-gray-200">
        <div class="bg-black p-2 rounded-lg">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
          </svg>
        </div>
        <div class="ml-4">
          <h2 class="text-lg font-semibold text-gray-900">{{formTranslation.googlemeet.googlemeet}}</h2>
          <p class="text-sm text-gray-500">Integrate video consultations with Google Meet</p>
        </div>
      </div>

      <!-- Content -->
      <div class="p-6">
        <div class="bg-gray-50 rounded-lg p-6">
          <div class="flex flex-col md:flex-row justify-between items-center gap-6">
            <div class="flex-1 space-y-4">
              <p class="font-medium text-gray-700">{{ formTranslation.googlemeet.please_connect_google_meet_service }}</p>
              <div class="flex items-center space-x-3 text-sm text-gray-600">
                <svg class="h-5 w-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>Host unlimited video consultations</span>
              </div>
              <div class="flex items-center space-x-3 text-sm text-gray-600">
                <svg class="h-5 w-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>Automatic meeting link generation</span>
              </div>
              <div class="flex items-center space-x-3 text-sm text-gray-600">
                <svg class="h-5 w-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>Secure and reliable connection</span>
              </div>
            </div>
            <button 
              @click="connect"
              class="inline-flex items-center px-6 py-3 text-sm font-medium text-white bg-black rounded-lg hover:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-900 transition-colors duration-200"
            >
              <img 
                :src="googleSignInImage" 
                alt="Google Sign In" 
                class="mr-3 h-5 w-5"
              >
              {{ formTranslation.pro_setting.connect_with_google }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Connected State -->
    <div v-else class="bg-white rounded-lg shadow-sm mb-6">
      <!-- Header -->
      <div class="flex items-center p-6 border-b border-gray-200">
        <div class="bg-black p-2 rounded-lg">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
          </svg>
        </div>
        <div class="ml-4">
          <h2 class="text-lg font-semibold text-gray-900">{{formTranslation.googlemeet.googlemeet}}</h2>
          <p class="text-sm text-gray-500">Your Google Meet integration is active</p>
        </div>
      </div>

      <!-- Content -->
      <div class="p-6">
        <div class="bg-gray-50 rounded-lg p-6">
          <div class="flex flex-col md:flex-row justify-between items-center gap-6">
            <div class="flex items-center space-x-4">
              <div class="flex-shrink-0">
                <div class="h-10 w-10 bg-green-100 rounded-full flex items-center justify-center">
                  <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                </div>
              </div>
              <div>
                <p class="font-medium text-gray-900">{{formTranslation.common.connected_with_google_meet}}</p>
                <p class="text-sm text-gray-500">Your appointments will automatically include Meet links</p>
              </div>
            </div>
            <button 
              @click="closeConnection"
              class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black transition-colors duration-200"
            >
              <svg class="mr-2 h-4 w-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
              {{formTranslation.common.disconnect}}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { post } from "../../config/request";
import {required} from "vuelidate/lib/validators";
export default {
  name: "GoogleMeet",
  components: {
  },
  validations: {
    googlemeet:{
      video_price:{required}
    }
  },
  data: () => {
    return {
      data: {},
      disconnect: false,
      googleSignInImage: '',
      googlemeetloading:false,
      googlemeetSubmitted:false,
      googlemeet:{
        video_price:0,
        telemed_service_id:0,
        doctor_id:0
      }
    };
  },
  mounted() {
    if(!['doctor'].includes(this.getUserRole())) {
      this.$router.push({ name: "403"})
    }
    setTimeout(()=>{
      this.start();
    },2000)
    this.googleSignInImage= window.pluginBASEURL + 'assets/images/google.svg';
    this.googlemeet = {
      video_price: this.userData.doctor_telemed_price,
      telemed_service_id:this.userData.telemed_service_id,
      doctor_id:this.userData.ID
    };
  },
  methods: {
    start() {
      const id = this.userData;
      let calendarConfig ={
        client_id: id.googlemeet_client_id,
        scope: "https://www.googleapis.com/auth/calendar",
      };
      if(id.googlemeet_app_name){
        calendarConfig.plugin_name = id.googlemeet_app_name;
      }
      gapi.load('auth2', function() {
        var auth2 = gapi.auth2.init(calendarConfig);
      });
    },
    connect(){
      let calendarConfig_new ={
        client_id: this.userData.googlemeet_client_id,
        scope: "https://www.googleapis.com/auth/calendar",
      };
      if(this.userData.googlemeet_app_name){
        calendarConfig_new.plugin_name = this.userData.googlemeet_app_name;
      }

      var auth2 = gapi.auth2.init(calendarConfig_new);

      auth2.grantOfflineAccess().then(this.signInCallback).catch(e=>console.log(e))
    },
    closeConnection(){
      let doctor_id = this.userData.ID
      post("diconnect_meet_doctor", {doctor_id: doctor_id})
          .then((response) => {
            if (
                response.data.status !== undefined &&
                response.data.status === true
            ) {
              this.$store.dispatch("userDataModule/fetchUserData", {});
              this.disconnect =false;
              displayMessage(response.data.message);
            }
          })
          .catch((error) => {
            console.log(error);
            displayErrorMessage(this.formTranslation.widgets.record_not_found);
          });
    },
    signInCallback(authResult) {
      if (authResult['code']) {
        let doctor_id = this.userData.ID
        let _this = this
        $.ajax({
          url: window.request_data.ajaxurl,
          type: 'POST',
          data: {
            route_name: 'connect_meet_doctor',
            doctor_id: doctor_id,
            _ajax_nonce: window.request_data.nonce,
            code: authResult['code'],
            action:'ajax_post'
          }, 
          success: function(data) {
            // data = JSON.parse(data);
            if (
                  data.status !== undefined &&
                  data.status === true
              ) {
                _this.$store.dispatch("userDataModule/fetchUserData", {});
                _this.disconnect =true;
                displayMessage(data.message);
              }
          }
        });
        // post("connect_meet_doctor", {doctor_id: doctor_id,code:authResult['code']})
        //     .then((response) => {
        //       if (
        //           response.data.status !== undefined &&
        //           response.data.status === true
        //       ) {
        //         this.$store.dispatch("userDataModule/fetchUserData", {});
        //         this.disconnect =true;
        //         displayMessage(response.data.message);

        //       }
        //     })
        //     .catch((error) => {
        //       console.log(error);
        //       displayErrorMessage(this.formTranslation.widgets.record_not_found);
        //     });
      } else {
        console.log('error')
      }
    }
  },
  computed:{
    userData() {
      return this.$store.state.userDataModule.user;
    },
  }
};
</script>