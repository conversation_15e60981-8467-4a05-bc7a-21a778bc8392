<?php

namespace App\controllers;

use App\baseClasses\KCBase;
use App\baseClasses\KCRequest;
use App\models\KCChatConversation;
use App\models\KCChatMessage;
use App\models\KCChatSetting;
use Exception;

class KCChatController extends KCBase {

    public $db;
    private $request;
    private $chat_conversation;
    private $chat_message;
    private $chat_setting;

    public function __construct() {
        global $wpdb;
        $this->db = $wpdb;
        $this->request = new KCRequest();
        $this->chat_conversation = new KCChatConversation();
        $this->chat_message = new KCChatMessage();
        $this->chat_setting = new KCChatSetting();
        
        parent::__construct();
        
        // Add filters to include chat menu in all sidebar menus
        add_filter('kivicare_administrator_dashboard_sidebar_data', array($this, 'addChatToSidebar'));
        add_filter('kivicare_clinic_admin_dashboard_sidebar_data', array($this, 'addChatToSidebar'));
        add_filter('kivicare_doctor_dashboard_sidebar_data', array($this, 'addChatToSidebar'));
        add_filter('kivicare_receptionist_dashboard_sidebar_data', array($this, 'addChatToSidebar'));
        add_filter('kivicare_patient_dashboard_sidebar_data', array($this, 'addChatToSidebar'));
    }

    /**
     * Add chat menu item to sidebar
     * 
     * @param array $sidebar_data Sidebar menu items array
     * @return array Modified sidebar menu items array
     */
    public function addChatToSidebar($sidebar_data) {
        $translate_lang = require KIVI_CARE_DIR . 'resources/assets/lang/temp.php';
        
        // Chat menu item
        $chat_menu = [
            'label' => isset($translate_lang['chat']['chat']) ? $translate_lang['chat']['chat'] : esc_html__('Chat', 'kc-lang'),
            'type' => 'route',
            'link' => 'chat',
            'iconClass' => 'fas fa-comments',
            'routeClass' => 'chat',
        ];
        
        // Find dashboard to insert after it
        $dashboard_index = -1;
        foreach ($sidebar_data as $index => $item) {
            if (isset($item['routeClass']) && $item['routeClass'] === 'dashboard') {
                $dashboard_index = $index;
                break;
            }
        }
        
        // Insert after dashboard or at the beginning
        if ($dashboard_index !== -1) {
            array_splice($sidebar_data, $dashboard_index + 1, 0, [$chat_menu]);
        } else {
            array_unshift($sidebar_data, $chat_menu);
        }
        
        return $sidebar_data;
    }

    /**
     * Get all conversations for the current user
     * 
     * @return void
     */
    public function getConversations() {
        $user_id = get_current_user_id();
        
        if (empty($user_id)) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('User not logged in', 'kc-lang')
            ]);
        }

        try {
            $conversations = $this->chat_conversation->getConversationsForUser($user_id);
            
            foreach ($conversations as &$conversation) {
                // Get the last message for this conversation
                $last_message = $this->chat_message->getLastMessageForConversation($conversation->id);
                $conversation->last_message = $last_message;
                
                // Get unread count for this user
                $conversation->unread_count = $this->chat_message->getUnreadCount($conversation->id, $user_id);
                
                // Get members names
                $members = $this->chat_conversation->getConversationMembers($conversation->id);
                $conversation->members = $members;
            }
            
            wp_send_json([
                'status' => true,
                'data' => $conversations
            ]);
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get all messages for a specific conversation
     * 
     * @return void
     */
    public function getMessages() {
        $request_data = $this->request->getInputs();
        $user_id = get_current_user_id();
        
        // Validate request
        $rules = [
            'conversation_id' => 'required'
        ];
        
        $errors = kcValidateRequest($rules, $request_data);
        
        if (!empty($errors)) {
            wp_send_json([
                'status' => false,
                'message' => $errors[0]
            ]);
            return;
        }
        
        // Ensure conversation_id is a valid integer
        $conversation_id = intval($request_data['conversation_id']);
        if ($conversation_id <= 0) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Invalid conversation ID', 'kc-lang')
            ]);
            return;
        }
        
        try {
            // Add request logging for debugging
            error_log('getMessages request for conversation_id: ' . $conversation_id . ' by user: ' . $user_id);
            
            // Check if database tables exist before proceeding
            global $wpdb;
            $tables_exist = true;
            $required_tables = ['md_chat_conversations', 'md_chat_members', 'md_chat_messages', 'md_chat_message_read'];
            
            foreach ($required_tables as $table) {
                $table_exists = $wpdb->get_var(
                    $wpdb->prepare(
                        "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = %s AND table_name = %s",
                        DB_NAME,
                        $wpdb->prefix . $table
                    )
                );
                
                if (!$table_exists) {
                    $tables_exist = false;
                    error_log('Missing required table: ' . $wpdb->prefix . $table);
                }
            }
            
            // Check if conversation exists
            $conversation = $this->chat_conversation->get_by(['id' => $conversation_id]);
            if (empty($conversation)) {
                error_log('Conversation not found with ID: ' . $conversation_id);
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Conversation not found', 'kc-lang')
                ]);
                return;
            }
            
            // Check if user is a member of this conversation
            $is_member = $this->chat_conversation->isUserMember($conversation_id, $user_id);
            error_log('User membership check for conversation ' . $conversation_id . ': ' . ($is_member ? 'Yes' : 'No'));
            
            if (!$is_member) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('You are not a member of this conversation', 'kc-lang')
                ]);
                return;
            }
            
            // Get messages for this conversation
            $messages = $this->chat_message->getMessagesForConversation($conversation_id);
            error_log('Retrieved ' . count($messages) . ' messages for conversation ' . $conversation_id);
            
            // Mark messages as read
            $read_result = $this->chat_message->markMessagesAsRead($conversation_id, $user_id);
            error_log('Mark messages as read result: ' . ($read_result ? 'Success' : 'Failed'));
            
            // Get conversation details to include with the response
            $conversation_details = $this->chat_conversation->get_by(['id' => $conversation_id]);
            $members = $this->chat_conversation->getConversationMembers($conversation_id);
            
            // Return comprehensive response
            wp_send_json([
                'status' => true,
                'data' => [
                    'messages' => $messages,
                    'conversation' => $conversation_details,
                    'members' => $members
                ]
            ]);
        } catch (Exception $e) {
            error_log('Error in getMessages: ' . $e->getMessage());
            if (method_exists($e, 'getTraceAsString')) {
                error_log('Stack trace: ' . $e->getTraceAsString());
            }
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Error retrieving messages', 'kc-lang') . ': ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Send a new message in a conversation
     * 
     * @return void
     */
    public function sendMessage() {
        $request_data = $this->request->getInputs();
        $user_id = get_current_user_id();
        
        // Detailed logging of request data
        error_log('SendMessage request_data: ' . json_encode($request_data));
        error_log('Current user ID: ' . $user_id);
        
        // Check if a file is being uploaded
        $has_file = !empty($_FILES['file']) && !empty($_FILES['file']['tmp_name']);
        error_log('File upload present: ' . ($has_file ? 'Yes' : 'No'));
        
        if ($has_file) {
            error_log('File details: ' . json_encode($_FILES['file']));
        }
        
        // Validate request - message is only required if no file is being uploaded
        $rules = [
            'conversation_id' => 'required'
        ];
        
        // Only require message if there's no file
        if (!$has_file) {
            $rules['message'] = 'required';
        }
        
        $errors = kcValidateRequest($rules, $request_data);
        
        if (!empty($errors)) {
            error_log('Validation errors: ' . json_encode($errors));
            wp_send_json([
                'status' => false,
                'message' => $errors[0]
            ]);
            return;
        }
        
        // If there's no message but a file, set a default empty message
        if (empty($request_data['message']) && $has_file) {
            $request_data['message'] = '';
            error_log('Setting empty message for file upload');
        }
        
        try {
            // Check if user is a member of this conversation
            $is_member = $this->chat_conversation->isUserMember($request_data['conversation_id'], $user_id);
            error_log('User is member of conversation: ' . ($is_member ? 'Yes' : 'No'));
            
            if (!$is_member) {
                error_log('User ' . $user_id . ' is not a member of conversation ' . $request_data['conversation_id']);
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('You are not a member of this conversation', 'kc-lang')
                ]);
                return; // Add return statement here to prevent further execution
            }
            
            // Process file upload if any
            $file_url = '';
            $file_type = '';
            $file_name = '';
            
            if (!empty($_FILES['file']) && !empty($_FILES['file']['tmp_name'])) {
                error_log('Processing file upload: ' . $_FILES['file']['name']);
                
                // Get file details for better error messages
                $original_filename = $_FILES['file']['name'] ?? 'unknown';
                $file_size = $_FILES['file']['size'] ?? 0;
                $tmp_name = $_FILES['file']['tmp_name'] ?? '';
                
                if (empty($tmp_name) || !file_exists($tmp_name)) {
                    error_log('File upload failed: Temporary file does not exist');
                    wp_send_json([
                        'status' => false,
                        'message' => esc_html__('File upload failed: The temporary file does not exist', 'kc-lang')
                    ]);
                    return;
                }
                
                if ($file_size > 10 * 1024 * 1024) { // 10MB limit
                    error_log('File upload failed: File size exceeds 10MB limit');
                    wp_send_json([
                        'status' => false,
                        'message' => esc_html__('File size exceeds 10MB limit', 'kc-lang')
                    ]);
                    return;
                }
                
                // Define upload parameters
                $upload_overrides = [
                    'test_form' => false,
                    'test_size' => true,
                    'test_upload' => true,
                    'unique_filename_callback' => function($dir, $name, $ext) {
                        return 'chat_' . time() . '_' . wp_hash(basename($name)) . $ext;
                    }
                ];
                
                // Handle the file upload
                $upload = wp_handle_upload($_FILES['file'], $upload_overrides);
                
                if (!empty($upload['error'])) {
                    error_log('File upload failed: ' . $upload['error']);
                    wp_send_json([
                        'status' => false,
                        'message' => $upload['error']
                    ]);
                    return;
                }
                
                if (empty($upload['url']) || empty($upload['file'])) {
                    error_log('File upload failed: WordPress returned an incomplete response');
                    wp_send_json([
                        'status' => false,
                        'message' => esc_html__('File upload failed', 'kc-lang')
                    ]);
                    return;
                }
                
                $file_url = $upload['url'];
                $file_type = wp_check_filetype($upload['file'])['type'];
                $file_name = basename($original_filename);
                
                error_log('File upload successful: ' . $file_url . ' (' . $file_type . ')');
            }
            
            // Create message
            $message_text = isset($request_data['message']) ? $request_data['message'] : '';
            
            // If we have a file but no message, use the filename as the message
            if (empty($message_text) && !empty($file_name)) {
                $message_text = $file_name;
                error_log('Using filename as message text: ' . $message_text);
            }
            
            $message_data = [
                'conversation_id' => $request_data['conversation_id'],
                'user_id' => $user_id,
                'message' => sanitize_textarea_field($message_text),
                'file_url' => $file_url,
                'file_type' => $file_type,
                'file_name' => sanitize_text_field($file_name),
                'created_at' => current_time('mysql')
            ];
            
            error_log('About to insert message with data: ' . json_encode($message_data));
            
            // Force verification of the file_name column before insertion
            global $wpdb;
            $table_name = $wpdb->prefix . 'md_chat_messages';
            $file_name_exists = $wpdb->get_var(
                $wpdb->prepare(
                    "SELECT COUNT(*)
                    FROM information_schema.columns
                    WHERE table_schema = %s
                    AND table_name = %s
                    AND column_name = %s",
                    DB_NAME,
                    $table_name,
                    'file_name'
                )
            );
            
            error_log('file_name column exists in controller check: ' . ($file_name_exists ? 'Yes' : 'No'));
            
            // If the column doesn't exist, add it now
            if (!$file_name_exists) {
                error_log('Adding file_name column directly from controller');
                $alter_result = $wpdb->query(
                    "ALTER TABLE {$table_name} 
                     ADD COLUMN file_name VARCHAR(255) DEFAULT NULL AFTER file_type"
                );
                
                if ($alter_result === false) {
                    error_log('Failed to add file_name column: ' . $wpdb->last_error);
                }
            }
            
            // Now try direct database insertion for maximum control
            if (isset($message_data['file_name']) && empty($file_name_exists)) {
                error_log('Removing file_name from data since column does not exist');
                unset($message_data['file_name']);
            }
            
            // Try to insert with direct wpdb call for better error handling
            $result = $wpdb->insert($table_name, $message_data);
            
            if ($result === false) {
                error_log('Direct wpdb insert failed: ' . $wpdb->last_error);
                
                // Fall back to model insert as last resort
                $message_id = $this->chat_message->insert($message_data);
            } else {
                $message_id = $wpdb->insert_id;
                error_log('Direct wpdb insert succeeded with ID: ' . $message_id);
            }
            
            if (!$message_id) {
                error_log('Failed to insert message after all attempts');
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Failed to send message', 'kc-lang')
                ]);
                return;
            }
            
            // Update conversation last_activity
            $update_result = $this->chat_conversation->update(
                ['last_activity' => current_time('mysql')], 
                ['id' => $request_data['conversation_id']]
            );
            
            error_log('Updated conversation last_activity: ' . ($update_result ? 'Success' : 'Failed'));
            
            // Get the new message
            $message = $this->chat_message->get_by(['id' => $message_id]);
            
            if (!$message) {
                error_log('Message was inserted but could not be retrieved');
                // Try direct query as fallback
                $message = $wpdb->get_row($wpdb->prepare(
                    "SELECT * FROM {$table_name} WHERE id = %d",
                    $message_id
                ));
            }
            
            error_log('Retrieved message: ' . json_encode($message));
            
            // Get conversation members to send notifications
            $conversation_members = $this->chat_conversation->getConversationMembers($request_data['conversation_id']);
            if (!empty($conversation_members)) {
                // Get sender details for notification
                $sender_name = '';
                $sender_data = get_userdata($user_id);
                if ($sender_data) {
                    $sender_name = $sender_data->display_name;
                }
                
                // Prepare notification data
                $notification_title = sprintf(__('New message from %s', 'kc-lang'), $sender_name);
                $notification_message = $message_text;
                if (empty($notification_message) && !empty($file_name)) {
                    $notification_message = sprintf(__('%s sent a file: %s', 'kc-lang'), $sender_name, $file_name);
                }
                
                // Get recipient IDs (excluding sender)
                $recipient_ids = [];
                foreach ($conversation_members as $member) {
                    if ($member->user_id != $user_id) {
                        $recipient_ids[] = $member->user_id;
                    }
                }
                
                // Send system notifications to all recipients
                if (!empty($recipient_ids) && function_exists('kc_send_notification_to_multiple')) {
                    kc_send_notification_to_multiple(
                        $recipient_ids,
                        $notification_title,
                        $notification_message,
                        'info',
                        $message_id,
                        'chat'
                    );
                }
            }
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Message sent successfully', 'kc-lang'),
                'data' => $message
            ]);
        } catch (Exception $e) {
            error_log('Exception in sendMessage: ' . $e->getMessage());
            if (method_exists($e, 'getTraceAsString')) {
                error_log('Stack trace: ' . $e->getTraceAsString());
            }
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Create a new conversation
     * 
     * @return void
     */
    public function createConversation() {
        $request_data = $this->request->getInputs();
        $user_id = get_current_user_id();
        
        // Validate request
        $rules = [
            'members' => 'required',
            'type' => 'required'
        ];
        
        $errors = kcValidateRequest($rules, $request_data);
        
        if (!empty($errors)) {
            wp_send_json([
                'status' => false,
                'message' => $errors[0]
            ]);
            return;
        }
        
        // Add extensive debugging for request data
        error_log('Create conversation request data: ' . json_encode($request_data));
        error_log('Current user ID: ' . $user_id);
        
        // Make sure current user is included in members
        $members = array_map('intval', $request_data['members']);
        if (!in_array($user_id, $members)) {
            $members[] = $user_id;
        }
        
        error_log('Conversation members: ' . json_encode($members));
        
        // Validate conversation type
        $type = sanitize_text_field($request_data['type']);
        if (!in_array($type, ['direct', 'group'])) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Invalid conversation type', 'kc-lang')
            ]);
            return;
        }
        
        // For direct conversation, make sure there are only 2 members
        if ($type === 'direct' && count($members) !== 2) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Direct conversation must have exactly 2 members', 'kc-lang')
            ]);
            return;
        }
        
        // For direct conversation, check if a conversation already exists between these users
        if ($type === 'direct') {
            try {
                $existing_conversation = $this->chat_conversation->findDirectConversation($members[0], $members[1]);
                
                if (!empty($existing_conversation)) {
                    wp_send_json([
                        'status' => true,
                        'message' => esc_html__('Conversation already exists', 'kc-lang'),
                        'data' => $existing_conversation
                    ]);
                    return;
                }
            } catch (\Exception $e) {
                error_log('Error checking for existing conversation: ' . $e->getMessage());
                // Continue with creating a new conversation
            }
        }
        
        try {
            // Create the conversation
            $conversation_data = [
                'name' => sanitize_text_field($request_data['name'] ?? ''),
                'type' => $type,
                'created_by' => $user_id,
                'created_at' => current_time('mysql'),
                'last_activity' => current_time('mysql')
            ];
            
            // Adding some debug logging
            error_log('Creating conversation with data: ' . json_encode($conversation_data));
            
            // Check if tables exist before attempting insert
            global $wpdb;
            $chat_conversations_exists = $wpdb->get_var(
                $wpdb->prepare(
                    "SELECT COUNT(*)
                    FROM information_schema.tables
                    WHERE table_schema = %s
                    AND table_name = %s",
                    DB_NAME,
                    $wpdb->prefix . 'md_chat_conversations'
                )
            );
            
            if (!$chat_conversations_exists) {
                error_log('Table ' . $wpdb->prefix . 'chat_conversations does not exist. Attempting to create it again.');
                
                // Check again after attempted creation
                $chat_conversations_exists = $wpdb->get_var(
                    $wpdb->prepare(
                        "SELECT COUNT(*)
                        FROM information_schema.tables
                        WHERE table_schema = %s
                        AND table_name = %s",
                        DB_NAME,
                        $wpdb->prefix . 'md_chat_conversations'
                    )
                );
                
                if (!$chat_conversations_exists) {
                    error_log('Failed to create ' . $wpdb->prefix . 'chat_conversations table');
                    wp_send_json([
                        'status' => false,
                        'message' => esc_html__('Could not create chat tables. Please contact support.', 'kc-lang')
                    ]);
                    return;
                }
            }
            
            // Insert the conversation with direct SQL to avoid any model issues
            $result = $wpdb->insert(
                $wpdb->prefix . 'md_chat_conversations',
                $conversation_data,
                ['%s', '%s', '%d', '%s', '%s']
            );
            
            if ($result === false) {
                $db_error = $wpdb->last_error;
                error_log('Failed to create conversation. Database error: ' . $db_error);
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Failed to create conversation: ', 'kc-lang') . $db_error
                ]);
                return;
            }
            
            $conversation_id = $wpdb->insert_id;
            error_log('Conversation created with ID: ' . $conversation_id);
            
            // Add members to the conversation
            $members_added = true;
            foreach ($members as $member_id) {
                $member_result = $wpdb->insert(
                    $wpdb->prefix . 'md_chat_members',
                    [
                        'conversation_id' => $conversation_id,
                        'user_id' => $member_id,
                        'joined_at' => current_time('mysql')
                    ],
                    ['%d', '%d', '%s']
                );
                
                if ($member_result === false) {
                    error_log('Failed to add member ' . $member_id . ' to conversation ' . $conversation_id . '. Error: ' . $wpdb->last_error);
                    $members_added = false;
                }
            }
            
            if (!$members_added) {
                error_log('Warning: Not all members were added to conversation ' . $conversation_id);
            }
            
            // Get the new conversation
            $conversation = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}md_chat_conversations WHERE id = %d",
                $conversation_id
            ));
            
            if (!$conversation) {
                error_log('Failed to retrieve created conversation with ID: ' . $conversation_id);
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Conversation was created but could not be retrieved', 'kc-lang')
                ]);
                return;
            }
            
            // Get conversation members
            $members_list = $wpdb->get_results($wpdb->prepare(
                "SELECT m.*, u.display_name 
                FROM {$wpdb->prefix}md_chat_members m 
                JOIN {$wpdb->users} u ON m.user_id = u.ID 
                WHERE m.conversation_id = %d",
                $conversation_id
            ));
            
            $conversation->members = $members_list ?: [];
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Conversation created successfully', 'kc-lang'),
                'data' => $conversation
            ]);
        } catch (Exception $e) {
            error_log('Exception in createConversation: ' . $e->getMessage());
            if ($e->getTraceAsString) {
                error_log('Stack trace: ' . $e->getTraceAsString());
            }
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Update a conversation (rename or add/remove members)
     * 
     * @return void
     */
    public function updateConversation() {
        $request_data = $this->request->getInputs();
        $user_id = get_current_user_id();
        
        // Validate request
        $rules = [
            'conversation_id' => 'required'
        ];
        
        $errors = kcValidateRequest($rules, $request_data);
        
        if (!empty($errors)) {
            wp_send_json([
                'status' => false,
                'message' => $errors[0]
            ]);
        }
        
        try {
            // Get the conversation
            $conversation = $this->chat_conversation->get_by(['id' => $request_data['conversation_id']]);
            
            if (empty($conversation)) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Conversation not found', 'kc-lang')
                ]);
            }
            
            // Check if user is the creator of this conversation
            if ($conversation->created_by != $user_id) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('You do not have permission to update this conversation', 'kc-lang')
                ]);
            }
            
            // Update conversation name if provided
            if (isset($request_data['name'])) {
                $this->chat_conversation->update(
                    ['name' => sanitize_text_field($request_data['name'])],
                    ['id' => $request_data['conversation_id']]
                );
            }
            
            // Update members if provided
            if (isset($request_data['members']) && $conversation->type === 'group') {
                $members = array_map('intval', $request_data['members']);
                
                // Make sure creator is included
                if (!in_array($user_id, $members)) {
                    $members[] = $user_id;
                }
                
                // Remove all existing members
                $this->chat_conversation->removeAllMembers($request_data['conversation_id']);
                
                // Add new members
                foreach ($members as $member_id) {
                    $this->chat_conversation->addMember($request_data['conversation_id'], $member_id);
                }
            }
            
            // Get the updated conversation with members
            $updated_conversation = $this->chat_conversation->get_by(['id' => $request_data['conversation_id']]);
            $updated_conversation->members = $this->chat_conversation->getConversationMembers($request_data['conversation_id']);
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Conversation updated successfully', 'kc-lang'),
                'data' => $updated_conversation
            ]);
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Mark messages in a conversation as read
     * 
     * @return void
     */
    public function markAsRead() {
        $request_data = $this->request->getInputs();
        $user_id = get_current_user_id();
        
        // Validate request
        $rules = [
            'conversation_id' => 'required'
        ];
        
        $errors = kcValidateRequest($rules, $request_data);
        
        if (!empty($errors)) {
            wp_send_json([
                'status' => false,
                'message' => $errors[0]
            ]);
        }
        
        try {
            // Check if user is a member of this conversation
            $is_member = $this->chat_conversation->isUserMember($request_data['conversation_id'], $user_id);
            
            if (!$is_member) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('You are not a member of this conversation', 'kc-lang')
                ]);
            }
            
            // Mark messages as read
            $this->chat_message->markMessagesAsRead($request_data['conversation_id'], $user_id);
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Messages marked as read', 'kc-lang')
            ]);
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Delete a message
     * 
     * @return void
     */
    public function deleteMessage() {
        $request_data = $this->request->getInputs();
        $user_id = get_current_user_id();
        
        // Validate request
        $rules = [
            'message_id' => 'required'
        ];
        
        $errors = kcValidateRequest($rules, $request_data);
        
        if (!empty($errors)) {
            wp_send_json([
                'status' => false,
                'message' => $errors[0]
            ]);
        }
        
        try {
            // Get the message
            $message = $this->chat_message->get_by(['id' => $request_data['message_id']]);
            
            if (empty($message)) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Message not found', 'kc-lang')
                ]);
            }
            
            // Check if user is the sender of this message or conversation creator
            $conversation = $this->chat_conversation->get_by(['id' => $message->conversation_id]);
            
            if ($message->user_id != $user_id && $conversation->created_by != $user_id) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('You do not have permission to delete this message', 'kc-lang')
                ]);
            }
            
            // Delete the message
            $deleted = $this->chat_message->delete(['id' => $request_data['message_id']]);
            
            if (!$deleted) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Failed to delete message', 'kc-lang')
                ]);
            }
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Message deleted successfully', 'kc-lang')
            ]);
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Leave a conversation (group only)
     * 
     * @return void
     */
    public function leaveConversation() {
        $request_data = $this->request->getInputs();
        $user_id = get_current_user_id();
        
        // Validate request
        $rules = [
            'conversation_id' => 'required'
        ];
        
        $errors = kcValidateRequest($rules, $request_data);
        
        if (!empty($errors)) {
            wp_send_json([
                'status' => false,
                'message' => $errors[0]
            ]);
        }
        
        try {
            // Get the conversation
            $conversation = $this->chat_conversation->get_by(['id' => $request_data['conversation_id']]);
            
            if (empty($conversation)) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Conversation not found', 'kc-lang')
                ]);
            }
            
            // Only group conversations can be left
            if ($conversation->type !== 'group') {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('You cannot leave a direct conversation', 'kc-lang')
                ]);
            }
            
            // Check if user is a member of this conversation
            $is_member = $this->chat_conversation->isUserMember($request_data['conversation_id'], $user_id);
            
            if (!$is_member) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('You are not a member of this conversation', 'kc-lang')
                ]);
            }
            
            // If user is the creator, transfer ownership or delete the conversation
            if ($conversation->created_by == $user_id) {
                // Get other members
                $members = $this->chat_conversation->getConversationMembers($request_data['conversation_id']);
                $other_members = array_filter($members, function($member) use ($user_id) {
                    return $member->id != $user_id;
                });
                
                // If there are no other members, delete the conversation
                if (empty($other_members)) {
                    $this->chat_conversation->delete(['id' => $request_data['conversation_id']]);
                    
                    wp_send_json([
                        'status' => true,
                        'message' => esc_html__('Conversation deleted successfully', 'kc-lang')
                    ]);
                }
                
                // Transfer ownership to another member
                $new_owner = reset($other_members);
                $this->chat_conversation->update(
                    ['created_by' => $new_owner->id],
                    ['id' => $request_data['conversation_id']]
                );
            }
            
            // Remove user from conversation
            $this->chat_conversation->removeMember($request_data['conversation_id'], $user_id);
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('You have left the conversation', 'kc-lang')
            ]);
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get chat settings for a clinic
     * 
     * @return void
     */
    public function getChatSettings() {
        $request_data = $this->request->getInputs();
        $user_id = get_current_user_id();
        
        // Get user's role
        $user_role = $this->getUserRole($user_id);
        
        // Only administrators and clinic_admin can view chat settings
        if (!in_array($user_role, ['administrator', 'clinic_admin'])) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('You do not have permission to view chat settings', 'kc-lang')
            ]);
        }
        
        try {
            // Get the clinic ID - either from request or user's clinic
            $clinic_id = 0;
            
            if (isset($request_data['clinic_id'])) {
                $clinic_id = intval($request_data['clinic_id']);
            } else {
                if ($user_role === 'clinic_admin') {
                    $clinic_id = $this->getUserClinicId($user_id);
                }
            }
            
            // Get settings for this clinic
            $settings = $this->chat_setting->getClinicSettings($clinic_id);
            
            wp_send_json([
                'status' => true,
                'data' => $settings
            ]);
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get user's clinics
     * 
     * @return void
     */
    public function getUserClinics() {
        $user_id = get_current_user_id();
        
        if (empty($user_id)) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('User not logged in', 'kc-lang')
            ]);
        }
        
        try {
            $clinics = [];
            $user_clinics = $this->getUserClinicsPrivate($user_id);
            
            // Get clinic details for each clinic ID
            foreach ($user_clinics as $clinic_id) {
                $clinic = $this->db->get_row(
                    $this->db->prepare(
                        "SELECT id, name FROM {$this->db->prefix}kc_clinics WHERE id = %d",
                        $clinic_id
                    )
                );
                
                if ($clinic) {
                    $clinics[] = [
                        'id' => $clinic->id,
                        'name' => $clinic->name
                    ];
                }
            }
            
            wp_send_json([
                'status' => true,
                'data' => $clinics
            ]);
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Save chat settings for a clinic
     * 
     * @return void
     */
    public function saveChatSettings() {
        $request_data = $this->request->getInputs();
        $user_id = get_current_user_id();
        
        // Validate request
        $rules = [
            'clinic_id' => 'required',
            'allow_patient_doctor_chat' => 'required'
        ];
        
        $errors = kcValidateRequest($rules, $request_data);
        
        if (!empty($errors)) {
            wp_send_json([
                'status' => false,
                'message' => $errors[0]
            ]);
        }
        
        // Get user's role
        $user_role = $this->getUserRole($user_id);
        
        // Only administrators and clinic_admin can update chat settings
        if (!in_array($user_role, ['administrator', 'clinic_admin'])) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('You do not have permission to update chat settings', 'kc-lang')
            ]);
        }
        
        // If clinic_admin, make sure they're updating their own clinic
        if ($user_role === 'clinic_admin') {
            $user_clinic_id = $this->getUserClinicId($user_id);
            
            if ($user_clinic_id != $request_data['clinic_id']) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('You do not have permission to update settings for this clinic', 'kc-lang')
                ]);
            }
        }
        
        try {
            // Update or create settings
            $settings_data = [
                'clinic_id' => intval($request_data['clinic_id']),
                'allow_patient_doctor_chat' => sanitize_text_field($request_data['allow_patient_doctor_chat']),
                'updated_at' => current_time('mysql')
            ];
            
            $existing_settings = $this->chat_setting->get_by(['clinic_id' => $request_data['clinic_id']]);
            
            if (!empty($existing_settings)) {
                $this->chat_setting->update($settings_data, ['clinic_id' => $request_data['clinic_id']]);
            } else {
                $settings_data['created_at'] = current_time('mysql');
                $this->chat_setting->insert($settings_data);
            }
            
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Chat settings saved successfully', 'kc-lang')
            ]);
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get users available for chat
     * 
     * @return void
     */
    public function getUsersForChat() {
        $user_id = get_current_user_id();
        
        if (empty($user_id)) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('User not logged in', 'kc-lang')
            ]);
        }
        
        try {
            $user_role = $this->getUserRole($user_id);
            $users = [];
            
            // Get clinic(s) of the current user
            $user_clinics = $this->getUserClinicsPrivate($user_id);
            
            // For each clinic, get users based on role
            foreach ($user_clinics as $clinic_id) {
                // Check if patient-doctor chat is allowed in this clinic
                $allow_patient_doctor_chat = $this->isDoctorPatientChatAllowed($clinic_id);

                switch ($user_role) {
                    case 'administrator':
                        // Admin can chat with anyone
                        $users = array_merge($users, $this->getAllClinicUsers($clinic_id));
                        break;
                        
                    case 'clinic_admin':
                        // Clinic admin can chat with all users in their clinic
                        $users = array_merge($users, $this->getAllClinicUsers($clinic_id, ['clinic_admin', 'receptionist', 'doctor']));
                        break;
                        
                    case 'doctor':
                        // Doctors can chat with admin, other doctors, receptionists and patients (if allowed)
                        $clinic_users = $this->getAllClinicUsers($clinic_id, ['clinic_admin', 'receptionist', 'doctor']);
                        
                        if ($allow_patient_doctor_chat) {
                            $clinic_users = array_merge($clinic_users, $this->getClinicPatients($clinic_id));
                        }
                        
                        $users = array_merge($users, $clinic_users);
                        break;
                        
                    case 'receptionist':
                        // Receptionists can chat with admin, doctors, other receptionists
                        $users = array_merge($users, $this->getAllClinicUsers($clinic_id, ['clinic_admin', 'receptionist', 'doctor']));
                        break;
                        
                    case 'patient':
                        // Patients can chat with admin, doctors (if allowed)
                        $clinic_users = $this->getAllClinicUsers($clinic_id, ['clinic_admin']);
                        
                        if ($allow_patient_doctor_chat) {
                            $clinic_users = array_merge($clinic_users, $this->getClinicDoctors($clinic_id));
                        }
                        
                        $users = array_merge($users, $clinic_users);
                        break;
                }
            }
            
            // Remove current user from list
            $users = array_filter($users, function($user) use ($user_id) {
                return $user->ID != $user_id;
            });
            
            // Remove duplicates
            $unique_users = [];
            foreach ($users as $user) {
                $unique_users[$user->ID] = $user;
            }
            
            wp_send_json([
                'status' => true,
                'data' => array_values($unique_users)
            ]);
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get total unread message count for user across all conversations
     * 
     * @return void
     */
    public function getUnreadCount() {
        // Ensure request method is GET since the route specifies 'get'
        $request_method = $_SERVER['REQUEST_METHOD'];
        if ($request_method !== 'GET') {
            // If someone tries to use POST, we'll return an error
            wp_send_json([
                'status' => false,
                'message' => esc_html__('Method is not allowed', 'kc-lang')
            ], 405);
            exit;
        }
        
        $user_id = get_current_user_id();
        
        if (empty($user_id)) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('User not logged in', 'kc-lang')
            ]);
        }
        
        try {
            $count = $this->chat_message->getTotalUnreadCount($user_id);
            
            wp_send_json([
                'status' => true,
                'data' => $count
            ]);
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /* Helper methods */

    /**
     * Get user's role
     * 
     * @param int $user_id User ID
     * @return string User role
     */
    private function getUserRole($user_id) {
        $user = get_userdata($user_id);
        
        if (in_array('administrator', $user->roles)) {
            return 'administrator';
        } elseif (in_array(KIVI_CARE_PREFIX . 'clinic_admin', $user->roles)) {
            return 'clinic_admin';
        } elseif (in_array(KIVI_CARE_PREFIX . 'doctor', $user->roles)) {
            return 'doctor';
        } elseif (in_array(KIVI_CARE_PREFIX . 'receptionist', $user->roles)) {
            return 'receptionist';
        } elseif (in_array(KIVI_CARE_PREFIX . 'patient', $user->roles)) {
            return 'patient';
        }
        
        return '';
    }

    /**
     * Get clinic ID for a clinic admin
     * 
     * @param int $user_id User ID
     * @return int Clinic ID
     */
    private function getUserClinicId($user_id) {
        $user_role = $this->getUserRole($user_id);
        
        switch ($user_role) {
            case 'clinic_admin':
                $clinic = $this->db->get_row("SELECT id FROM {$this->db->prefix}kc_clinics WHERE clinic_admin_id = {$user_id}");
                return $clinic ? $clinic->id : 0;
                
            case 'doctor':
                $doctor_mapping = $this->db->get_row("SELECT clinic_id FROM {$this->db->prefix}kc_doctor_clinic_mappings WHERE doctor_id = {$user_id} LIMIT 1");
                return $doctor_mapping ? $doctor_mapping->clinic_id : 0;
                
            case 'receptionist':
                $receptionist_mapping = $this->db->get_row("SELECT clinic_id FROM {$this->db->prefix}kc_receptionist_clinic_mappings WHERE receptionist_id = {$user_id} LIMIT 1");
                return $receptionist_mapping ? $receptionist_mapping->clinic_id : 0;
                
            case 'patient':
                $patient_mapping = $this->db->get_row("SELECT clinic_id FROM {$this->db->prefix}kc_patient_clinic_mappings WHERE patient_id = {$user_id} LIMIT 1");
                return $patient_mapping ? $patient_mapping->clinic_id : 0;
                
            default:
                return 0;
        }
    }

    /**
     * Get all clinics associated with a user
     * 
     * @param int $user_id User ID
     * @return array Clinic IDs
     */
    private function getUserClinicsPrivate($user_id) {
        $user_role = $this->getUserRole($user_id);
        $clinic_ids = [];
        
        try {
            switch ($user_role) {
                case 'administrator':
                    // Admin has access to all clinics
                    $clinics = $this->db->get_results("SELECT id FROM {$this->db->prefix}kc_clinics");
                    if ($clinics) {
                        foreach ($clinics as $clinic) {
                            $clinic_ids[] = $clinic->id;
                        }
                    }
                    break;
                    
                case 'clinic_admin':
                    $clinic = $this->db->get_row($this->db->prepare(
                        "SELECT id FROM {$this->db->prefix}kc_clinics WHERE clinic_admin_id = %d",
                        $user_id
                    ));
                    if ($clinic) {
                        $clinic_ids[] = $clinic->id;
                    }
                    break;
                    
                case 'doctor':
                    $doctor_mappings = $this->db->get_results($this->db->prepare(
                        "SELECT clinic_id FROM {$this->db->prefix}kc_doctor_clinic_mappings WHERE doctor_id = %d",
                        $user_id
                    ));
                    if ($doctor_mappings) {
                        foreach ($doctor_mappings as $mapping) {
                            $clinic_ids[] = $mapping->clinic_id;
                        }
                    }
                    break;
                    
                case 'receptionist':
                    $receptionist_mappings = $this->db->get_results($this->db->prepare(
                        "SELECT clinic_id FROM {$this->db->prefix}kc_receptionist_clinic_mappings WHERE receptionist_id = %d",
                        $user_id
                    ));
                    if ($receptionist_mappings) {
                        foreach ($receptionist_mappings as $mapping) {
                            $clinic_ids[] = $mapping->clinic_id;
                        }
                    }
                    break;
                    
                case 'patient':
                    $patient_mappings = $this->db->get_results($this->db->prepare(
                        "SELECT clinic_id FROM {$this->db->prefix}kc_patient_clinic_mappings WHERE patient_id = %d",
                        $user_id
                    ));
                    if ($patient_mappings) {
                        foreach ($patient_mappings as $mapping) {
                            $clinic_ids[] = $mapping->clinic_id;
                        }
                    }
                    break;
            }
            
            // If no clinics found, return array with default clinic
            if (empty($clinic_ids)) {
                $default_clinic = $this->db->get_var("SELECT id FROM {$this->db->prefix}kc_clinics LIMIT 1");
                if ($default_clinic) {
                    $clinic_ids[] = $default_clinic;
                }
            }
            
            return $clinic_ids;
        } catch (\Exception $e) {
            error_log('Error getting user clinics: ' . $e->getMessage());
            // Return array with at least one clinic if possible
            $default_clinic = $this->db->get_var("SELECT id FROM {$this->db->prefix}kc_clinics LIMIT 1");
            if ($default_clinic) {
                return [$default_clinic];
            }
            return [];
        }
    }

    /**
     * Get all users in a clinic with specific roles
     * 
     * @param int $clinic_id Clinic ID
     * @param array $roles User roles to include
     * @return array Users
     */
    private function getAllClinicUsers($clinic_id, $roles = ['administrator', 'clinic_admin', 'doctor', 'receptionist', 'patient']) {
        $users = [];
        
        // Add administrators
        if (in_array('administrator', $roles)) {
            $admin_users = get_users(['role' => 'administrator']);
            $users = array_merge($users, $admin_users);
        }
        
        // Add clinic admins
        if (in_array('clinic_admin', $roles)) {
            $clinic = $this->db->get_row("SELECT clinic_admin_id FROM {$this->db->prefix}kc_clinics WHERE id = {$clinic_id}");
            if ($clinic && $clinic->owner_id) {
                $clinic_admin = get_user_by('id', $clinic->owner_id);
                if ($clinic_admin) {
                    $users[] = $clinic_admin;
                }
            }
        }
        
        // Add doctors
        if (in_array('doctor', $roles)) {
            $users = array_merge($users, $this->getClinicDoctors($clinic_id));
        }
        
        // Add receptionists
        if (in_array('receptionist', $roles)) {
            $receptionist_mappings = $this->db->get_results("
                SELECT receptionist_id FROM {$this->db->prefix}kc_receptionist_clinic_mappings 
                WHERE clinic_id = {$clinic_id}
            ");
            
            foreach ($receptionist_mappings as $mapping) {
                $receptionist = get_user_by('id', $mapping->receptionist_id);
                if ($receptionist) {
                    $users[] = $receptionist;
                }
            }
        }
        
        // Add patients
        if (in_array('patient', $roles)) {
            $users = array_merge($users, $this->getClinicPatients($clinic_id));
        }
        
        return $users;
    }

    /**
     * Get all doctors in a clinic
     * 
     * @param int $clinic_id Clinic ID
     * @return array Doctors
     */
    private function getClinicDoctors($clinic_id) {
        $doctors = [];
        $doctor_mappings = $this->db->get_results("
            SELECT doctor_id FROM {$this->db->prefix}kc_doctor_clinic_mappings 
            WHERE clinic_id = {$clinic_id}
        ");
        
        foreach ($doctor_mappings as $mapping) {
            $doctor = get_user_by('id', $mapping->doctor_id);
            if ($doctor) {
                $doctors[] = $doctor;
            }
        }
        
        return $doctors;
    }

    /**
     * Get all patients in a clinic
     * 
     * @param int $clinic_id Clinic ID
     * @return array Patients
     */
    private function getClinicPatients($clinic_id) {
        $patients = [];
        $patient_mappings = $this->db->get_results("
            SELECT patient_id FROM {$this->db->prefix}kc_patient_clinic_mappings 
            WHERE clinic_id = {$clinic_id}
        ");
        
        foreach ($patient_mappings as $mapping) {
            $patient = get_user_by('id', $mapping->patient_id);
            if ($patient) {
                $patients[] = $patient;
            }
        }
        
        return $patients;
    }

    /**
     * Check if doctor-patient chat is allowed in a clinic
     * 
     * @param int $clinic_id Clinic ID
     * @return bool True if allowed, false otherwise
     */
    private function isDoctorPatientChatAllowed($clinic_id) {
        $settings = $this->chat_setting->get_by(['clinic_id' => $clinic_id]);
        
        $response = 0;
        if (!empty($settings)) {
            $response = (int)$settings[0]->allow_patient_doctor_chat;
        }

        return $response;
    }
}