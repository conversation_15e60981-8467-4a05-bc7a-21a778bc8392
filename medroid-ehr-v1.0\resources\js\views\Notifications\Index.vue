<template>
  <div class="card shadow-sm border-0 rounded-lg overflow-hidden">
    <div class="card-header py-3 bg-white border-b border-gray-100">
      <div class="flex justify-between items-center">
        <h5 class="card-title font-bold text-lg text-gray-700">{{ $t('notifications.title') }}</h5>
        <div class="flex gap-2">
          <button 
            @click="markAllAsRead" 
            class="px-3 py-1.5 text-sm bg-purple-50 text-purple-600 hover:bg-purple-100 rounded-md transition"
            :disabled="loading || notifications.length === 0">
            <i class="fas fa-check-double mr-1"></i> Mark all as read
          </button>
          <button
            @click="refreshNotifications"
            class="px-3 py-1.5 text-sm bg-gray-50 text-gray-600 hover:bg-gray-100 rounded-md transition"
            :disabled="loading">
            <i class="fas fa-sync-alt mr-1" :class="{ 'animate-spin': loading }"></i> Refresh
          </button>
        </div>
      </div>
    </div>
    
    <div class="card-body p-0">
      <!-- Filter Bar -->
      <div class="bg-gray-50 border-b border-gray-100 p-4">
        <div class="flex flex-wrap gap-3">
          <!-- Filter: Status -->
          <div class="w-40">
            <select 
              v-model="filters.status" 
              class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-purple-500 focus:border-purple-500 transition-all">
              <option value="all">All Status</option>
              <option value="read">Read</option>
              <option value="unread">Unread</option>
            </select>
          </div>
          
          <!-- Filter: Type -->
          <div class="w-40">
            <select 
              v-model="filters.type" 
              class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-purple-500 focus:border-purple-500 transition-all">
              <option value="all">All Types</option>
              <option value="info">Info</option>
              <option value="success">Success</option>
              <option value="warning">Warning</option>
              <option value="error">Error</option>
              <option value="appointment">Appointment</option>
              <option value="encounter">Encounter</option>
              <option value="prescription">Prescription</option>
              <option value="billing">Billing</option>
              <option value="task">Task</option>
            </select>
          </div>
          
          <!-- Search -->
          <div class="flex-grow">
            <div class="relative">
              <input 
                v-model="filters.search" 
                type="text" 
                placeholder="Search in notifications..." 
                class="w-full px-10 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-purple-500 focus:border-purple-500 transition-all">
              <svg 
                class="absolute left-3 top-2.5 h-4 w-4 text-gray-400" 
                xmlns="http://www.w3.org/2000/svg" 
                viewBox="0 0 24 24" 
                fill="none" 
                stroke="currentColor" 
                stroke-width="2">
                <circle cx="11" cy="11" r="8" />
                <path d="m21 21-4.3-4.3" />
              </svg>
              <button 
                v-if="filters.search" 
                @click="filters.search = ''" 
                class="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Loading Indicator -->
      <div v-if="loading" class="p-8 text-center">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-purple-700"></div>
        <p class="mt-2 text-gray-600">Loading notifications...</p>
      </div>
      
      <!-- Empty State -->
      <div v-else-if="filteredNotifications.length === 0" class="p-8 text-center">
        <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-100 mb-4">
          <i class="fas fa-bell-slash text-2xl text-gray-400"></i>
        </div>
        <h3 class="text-lg font-medium text-gray-800 mb-1">No notifications found</h3>
        <p class="text-gray-500">
          {{ filters.status !== 'all' || filters.type !== 'all' || filters.search 
            ? 'Try changing your filters or search term'
            : 'You have no notifications at this time' }}
        </p>
      </div>
      
      <!-- Notifications List -->
      <div v-else class="divide-y divide-gray-100">
        <div 
          v-for="notification in filteredNotifications" 
          :key="notification.id"
          class="p-4 hover:bg-gray-50 transition-colors duration-200"
          :class="{ 'bg-purple-50/30': !notification.is_read }">
          <div class="flex gap-4">
            <!-- Notification Icon -->
            <div 
              class="w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0"
              :class="getNotificationTypeClass(notification.type)">
              <i :class="getNotificationTypeIcon(notification.type)" class="text-lg"></i>
            </div>
            
            <!-- Notification Content -->
            <div class="flex-grow">
              <div class="flex justify-between">
                <h3 class="font-medium text-gray-900 mb-1" :class="{ 'font-semibold': !notification.is_read }">
                  {{ notification.title }}
                </h3>
                <span class="text-xs text-gray-500">{{ formatDate(notification.created_at) }}</span>
              </div>
              <p class="text-gray-600 mb-3">{{ notification.message }}</p>
              
              <!-- Action Buttons -->
              <div class="flex gap-2">
                <button 
                  v-if="!notification.is_read"
                  @click="markAsRead(notification.id)"
                  class="text-xs px-2 py-1 bg-blue-50 text-blue-600 hover:bg-blue-100 rounded transition-colors">
                  <i class="fas fa-check mr-1"></i> Mark as read
                </button>
                <button 
                  v-if="notification.reference_id && notification.reference_type"
                  @click="navigateToReference(notification)"
                  class="text-xs px-2 py-1 bg-purple-50 text-purple-600 hover:bg-purple-100 rounded transition-colors">
                  <i class="fas fa-external-link-alt mr-1"></i> View details
                </button>
                <button 
                  @click="deleteNotification(notification.id)"
                  class="text-xs px-2 py-1 bg-red-50 text-red-600 hover:bg-red-100 rounded transition-colors">
                  <i class="fas fa-trash-alt mr-1"></i> Delete
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Pagination -->
    <div v-if="pagination.total_pages > 1" class="card-footer bg-white border-t border-gray-100 p-4">
      <div class="flex justify-between items-center">
        <div class="text-sm text-gray-600">
          Showing {{ (pagination.current_page - 1) * pagination.per_page + 1 }} to 
          {{ Math.min(pagination.current_page * pagination.per_page, pagination.total_items) }} of 
          {{ pagination.total_items }} notifications
        </div>
        <div class="flex gap-1">
          <button 
            @click="goToPage(pagination.current_page - 1)" 
            :disabled="pagination.current_page === 1"
            class="px-3 py-1 rounded border border-gray-300 text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50">
            <i class="fas fa-chevron-left"></i>
          </button>
          <button 
            v-for="page in displayedPages" 
            :key="page"
            @click="goToPage(page)"
            class="px-3 py-1 rounded border text-sm"
            :class="page === pagination.current_page 
              ? 'border-purple-500 bg-purple-50 text-purple-700' 
              : 'border-gray-300 hover:bg-gray-50'">
            {{ page }}
          </button>
          <button 
            @click="goToPage(pagination.current_page + 1)" 
            :disabled="pagination.current_page === pagination.total_pages"
            class="px-3 py-1 rounded border border-gray-300 text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50">
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { get, post } from '../../config/request';
import { displayMessage, displayErrorMessage } from '../../utils/message';

export default {
  name: 'NotificationsIndex',
  
  data() {
    return {
      notifications: [],
      loading: true,
      pagination: {
        current_page: 1,
        per_page: 20,
        total_items: 0,
        total_pages: 0
      },
      filters: {
        status: 'all',
        type: 'all',
        search: ''
      }
    };
  },
  
  computed: {
    // Apply filters to notifications
    filteredNotifications() {
      let result = [...this.notifications];
      
      // Filter by status
      if (this.filters.status === 'read') {
        result = result.filter(n => n.is_read);
      } else if (this.filters.status === 'unread') {
        result = result.filter(n => !n.is_read);
      }
      
      // Filter by type
      if (this.filters.type !== 'all') {
        result = result.filter(n => n.type === this.filters.type);
      }
      
      // Filter by search
      if (this.filters.search) {
        const searchTerm = this.filters.search.toLowerCase();
        result = result.filter(n => 
          (n.title && n.title.toLowerCase().includes(searchTerm)) || 
          (n.message && n.message.toLowerCase().includes(searchTerm))
        );
      }
      
      return result;
    },
    
    // Calculate pages to display in pagination
    displayedPages() {
      const totalPages = this.pagination.total_pages;
      const currentPage = this.pagination.current_page;
      const pages = [];
      
      // Logic to show current page, 2 pages before and after (when available)
      // Always include first and last page
      for (let i = 1; i <= totalPages; i++) {
        // Always include first and last page
        if (i === 1 || i === totalPages) {
          pages.push(i);
          continue;
        }
        
        // Include pages around current page
        if (i >= currentPage - 2 && i <= currentPage + 2) {
          pages.push(i);
          continue;
        }
        
        // Add ellipsis (using page number 0 to represent it)
        if ((i === 2 && currentPage > 4) || 
            (i === totalPages - 1 && currentPage < totalPages - 3)) {
          if (!pages.includes(0)) {
            pages.push(0);
          }
        }
      }
      
      return pages.sort((a, b) => a - b);
    }
  },
  
  mounted() {
    this.loadNotifications();
  },
  
  methods: {
    // Load notifications from API
    loadNotifications() {
      this.loading = true;
      
      const params = {
        per_page: this.pagination.per_page,
        page: this.pagination.current_page,
        only_unread: this.filters.status === 'unread' ? true : false
      };
      
      if (this.filters.type !== 'all') {
        params.reference_type = this.filters.type;
      }
      
      get('notifications_list', params)
        .then(response => {
          if (response.data && response.data.status) {
            this.notifications = response.data.data.notifications || [];
            this.pagination = response.data.data.pagination || this.pagination;
          } else {
            this.notifications = [];
            displayErrorMessage(response.data.message || 'Failed to load notifications');
          }
        })
        .catch(error => {
          console.error('Error loading notifications:', error);
          displayErrorMessage('Error loading notifications');
        })
        .finally(() => {
          this.loading = false;
        });
    },
    
    // Refresh notifications manually
    refreshNotifications() {
      this.loadNotifications();
    },
    
    // Go to specific page
    goToPage(page) {
      if (page < 1 || page > this.pagination.total_pages || page === this.pagination.current_page) {
        return;
      }
      
      this.pagination.current_page = page;
      this.loadNotifications();
    },
    
    // Mark a notification as read
    markAsRead(notificationId) {
      post('notifications_mark_read', {
        notification_ids: [notificationId]
      })
        .then(response => {
          if (response.data && response.data.status) {
            // Update the notification in the local array
            const index = this.notifications.findIndex(n => n.id === notificationId);
            if (index !== -1) {
              this.notifications[index].is_read = true;
            }
            
            displayMessage('Notification marked as read');
          } else {
            displayErrorMessage(response.data.message || 'Failed to mark notification as read');
          }
        })
        .catch(error => {
          console.error('Error marking notification as read:', error);
          displayErrorMessage('Error marking notification as read');
        });
    },
    
    // Mark all notifications as read
    markAllAsRead() {
      if (this.loading || this.notifications.length === 0) {
        return;
      }
      
      this.loading = true;
      
      post('notifications_mark_all_read', {})
        .then(response => {
          if (response.data && response.data.status) {
            // Update all notifications in the local array
            this.notifications.forEach(notification => {
              notification.is_read = true;
            });
            
            displayMessage('All notifications marked as read');
          } else {
            displayErrorMessage(response.data.message || 'Failed to mark all notifications as read');
          }
        })
        .catch(error => {
          console.error('Error marking all notifications as read:', error);
          displayErrorMessage('Error marking all notifications as read');
        })
        .finally(() => {
          this.loading = false;
        });
    },
    
    // Delete a notification
    deleteNotification(notificationId) {
      if (confirm('Are you sure you want to delete this notification?')) {
        this.loading = true;
        
        post('notifications_delete', {
          id: notificationId
        })
          .then(response => {
            if (response.data && response.data.status) {
              // Remove the notification from the local array
              this.notifications = this.notifications.filter(n => n.id !== notificationId);
              displayMessage('Notification deleted successfully');
            } else {
              displayErrorMessage(response.data.message || 'Failed to delete notification');
            }
          })
          .catch(error => {
            console.error('Error deleting notification:', error);
            displayErrorMessage('Error deleting notification');
          })
          .finally(() => {
            this.loading = false;
          });
      }
    },
    
    // Navigate to the reference of a notification
    navigateToReference(notification) {
      if (!notification.reference_type || !notification.reference_id) {
        return;
      }
      
      // Mark as read before navigating
      if (!notification.is_read) {
        this.markAsRead(notification.id);
      }
      
      // Handle different reference types
      switch (notification.reference_type) {
        case 'appointment':
          this.$router.push({
            name: 'appointment.list',
            query: { appointment_id: notification.reference_id }
          });
          break;
          
        case 'encounter':
          this.$router.push({
            name: 'patient.encounter',
            params: { id: notification.reference_id }
          });
          break;
          
        case 'patient':
          this.$router.push({
            name: 'patient.view',
            params: { id: notification.reference_id }
          });
          break;
          
        case 'prescription':
          this.$router.push({
            name: 'prescription.list',
            query: { id: notification.reference_id }
          });
          break;
          
        case 'billing':
          this.$router.push({
            name: 'billing.view',
            params: { id: notification.reference_id }
          });
          break;
          
        case 'task':
          this.$router.push({
            name: 'task.view',
            params: { id: notification.reference_id }
          });
          break;
      }
    },
    
    // Format date for display
    formatDate(dateString) {
      if (!dateString) return '';
      
      const date = new Date(dateString);
      const now = new Date();
      const diffInHours = (now - date) / (1000 * 60 * 60);
      
      // Today: "Today at HH:MM"
      if (diffInHours < 24 && date.getDate() === now.getDate()) {
        return `Today at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
      }
      
      // Yesterday: "Yesterday at HH:MM"
      if (diffInHours < 48 && date.getDate() === now.getDate() - 1) {
        return `Yesterday at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
      }
      
      // This week: "DayName at HH:MM"
      if (diffInHours < 168) { // 7 days
        const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        return `${days[date.getDay()]} at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
      }
      
      // Older: "MMM D, YYYY at HH:MM"
      return date.toLocaleDateString([], { 
        month: 'short', 
        day: 'numeric', 
        year: 'numeric' 
      }) + ` at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    },
    
    // Get CSS class for notification type
    getNotificationTypeClass(type) {
      const classes = {
        info: 'bg-blue-100 text-blue-500',
        success: 'bg-green-100 text-green-500',
        warning: 'bg-yellow-100 text-yellow-600',
        error: 'bg-red-100 text-red-500',
        appointment: 'bg-purple-100 text-purple-500',
        encounter: 'bg-pink-100 text-pink-500',
        prescription: 'bg-indigo-100 text-indigo-500',
        billing: 'bg-amber-100 text-amber-500',
        task: 'bg-emerald-100 text-emerald-500'
      };
      
      return classes[type] || 'bg-gray-100 text-gray-500';
    },
    
    // Get icon for notification type
    getNotificationTypeIcon(type) {
      const icons = {
        info: 'fas fa-info-circle',
        success: 'fas fa-check-circle',
        warning: 'fas fa-exclamation-triangle',
        error: 'fas fa-times-circle',
        appointment: 'fas fa-calendar-check',
        encounter: 'fas fa-stethoscope',
        prescription: 'fas fa-prescription',
        billing: 'fas fa-file-invoice-dollar',
        task: 'fas fa-tasks'
      };
      
      return icons[type] || 'fas fa-bell';
    }
  },
  
  watch: {
    // Watch for filter changes to reload data
    'filters.status'() {
      this.pagination.current_page = 1;
      this.loadNotifications();
    },
    'filters.type'() {
      this.pagination.current_page = 1;
      this.loadNotifications();
    }
  }
};
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>