<?php

namespace App\Http\Controllers;

use App\Models\ConsultationDocument;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ConsultationDocumentController extends Controller
{
    /**
     * Upload documents for a consultation.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'consultation_id' => 'required|exists:consultations,id',
            'document_type' => 'required|string|max:50',
            'description' => 'nullable|string',
            'files' => 'required|array|min:1',
            'files.*' => 'file|max:10240|mimes:pdf,doc,docx,jpg,jpeg,png,gif', // 10MB max
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $uploadedDocuments = [];

        foreach ($request->file('files') as $file) {
            $originalName = $file->getClientOriginalName();
            $fileName = Str::uuid() . '.' . $file->getClientOriginalExtension();
            $filePath = $file->storeAs('consultation-documents', $fileName, 'public');

            $document = ConsultationDocument::create([
                'consultation_id' => $request->consultation_id,
                'document_type' => $request->document_type,
                'file_path' => $filePath,
                'file_name' => $fileName,
                'original_name' => $originalName,
                'file_size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'description' => $request->description,
                'uploaded_by' => auth()->id(),
            ]);

            $uploadedDocuments[] = $document->load('uploader');
        }

        return response()->json([
            'message' => 'Documents uploaded successfully',
            'data' => $uploadedDocuments
        ], 201);
    }

    /**
     * Get documents for a consultation.
     */
    public function index(Request $request)
    {
        $consultationId = $request->get('consultation_id');

        if (!$consultationId) {
            return response()->json([
                'message' => 'Consultation ID is required'
            ], 400);
        }

        $documents = ConsultationDocument::where('consultation_id', $consultationId)
            ->with('uploader')
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'data' => $documents
        ]);
    }

    /**
     * Download a document.
     */
    public function download($id)
    {
        $document = ConsultationDocument::findOrFail($id);

        if (!Storage::disk('public')->exists($document->file_path)) {
            return response()->json([
                'message' => 'File not found'
            ], 404);
        }

        return Storage::disk('public')->download($document->file_path, $document->original_name);
    }

    /**
     * Delete a document.
     */
    public function destroy($id)
    {
        $document = ConsultationDocument::findOrFail($id);

        // Delete file from storage
        if (Storage::disk('public')->exists($document->file_path)) {
            Storage::disk('public')->delete($document->file_path);
        }

        $document->delete();

        return response()->json([
            'message' => 'Document deleted successfully'
        ]);
    }
}
