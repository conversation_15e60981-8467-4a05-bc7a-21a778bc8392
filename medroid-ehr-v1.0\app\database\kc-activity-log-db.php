<?php

use App\baseClasses\KCActivate;

/**
 * @package  KiviCarePlugin
 */

/**
 * Create activity logs table
 * 
 * This function is now deprecated and only kept for backward compatibility.
 * The table creation is now handled by the database migration system.
 * @see app/database/kc-database-migration.php
 */
function kivicareCreateActivityLogTable() {
    // This function is now deprecated and only calls the central migration system
    kivicare_run_database_migrations();
}

// Keep this action for backward compatibility
add_action('kcActivatePlugin', 'kivicareCreateActivityLogTable', 10);