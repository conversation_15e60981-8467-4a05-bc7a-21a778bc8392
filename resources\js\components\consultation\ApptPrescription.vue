<template>
  <!-- Exact KiviCare Prescription Component -->
  <div>
    <!-- Main Prescription List -->
    <div class="bg-white border rounded mb-3">
      <div class="flex justify-between items-center p-3 border-b">
        <h2 class="font-medium">Prescription</h2>
        <div class="flex gap-2">
          <button @click="printPrescription" class="px-3 py-1.5 bg-black text-white rounded text-sm hover:bg-black">
            View
          </button>
          <button @click="handleAddPrescriptionForm"
            class="px-3 py-1.5 bg-black text-white rounded text-sm hover:bg-black">
            Add Medication
          </button>
        </div>
      </div>
      <table class="w-full text-sm">
        <thead class="bg-gray-50 text-gray-600">
          <tr>
            <th class="text-left p-2 font-medium">Name</th>
            <th class="text-left p-2 font-medium">Frequency</th>
            <th class="text-left p-2 font-medium">Duration</th>
            <th class="text-left p-2 font-medium w-24">Action</th>
          </tr>
        </thead>
        <tbody>
          <tr v-if="prescriptions.length === 0">
            <td colspan="4" class="p-2 text-gray-500">
              No prescriptions found
            </td>
          </tr>
          <tr v-for="prescription in prescriptions" :key="prescription.id">
            <td class="p-2">{{ prescription.medicationName }}</td>
            <td class="p-2">{{ prescription.frequency }}</td>
            <td class="p-2">{{ formatDuration(prescription.duration) }}</td>
            <td class="p-2">
              <button @click="deletePrescription(prescription.id)" class="text-red-500 hover:text-red-600">
                <i class="fa fa-trash"></i>
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Modal -->
    <div v-if="isAddPrescriptionModalOpen" class="fixed inset-0 bg-black opacity-50"
      @click="isAddPrescriptionModalOpen = false"></div>

    <div v-if="isAddPrescriptionModalOpen" class="fixed inset-0 z-50 overflow-y-auto">
      <div class="min-h-screen px-4 py-6 flex items-start justify-center">
        <div class="w-full max-w-6xl bg-white rounded-xl shadow-xl">
          <div class="p-6">
            <div class="bg-white rounded-lg w-full max-w-6xl h-[700px]">
              <!-- Modal Header -->
              <div class="flex justify-between items-center p-4 border-b">
                <h3 class="font-medium">Add New Prescription</h3>
                <button @click="isAddPrescriptionModalOpen = false">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-x w-4 h-4">
                    <path d="M18 6 6 18"></path>
                    <path d="m6 6 12 12"></path>
                  </svg>
                </button>
              </div>

              <!-- Modal Content -->
              <div class="grid grid-cols-12 h-[calc(100%-130px)]">
                <!-- Left Column - Prescription List -->
                <div class="col-span-3 border-r p-4 space-y-2">
                  <div class="flex justify-between items-center mb-4">
                    <h4 class="font-medium">Medications</h4>
                    <button @click="addNewPrescriptionForm" class="p-1 text-blue-500 hover:text-blue-600">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-plus w-4 h-4">
                        <path d="M5 12h14"></path>
                        <path d="M12 5v14"></path>
                      </svg>
                    </button>
                  </div>

                  <div v-for="(form, index) in prescriptionForms" :key="form.id"
                    class="p-2 rounded flex justify-between items-center cursor-pointer border"
                    :class="{ 'bg-blue-50 border-blue-200': form.isSelected }" @click="selectPrescriptionForm(index)">
                    <span class="text-sm truncate">{{
                      form.medicationName || "New Medication"
                    }}</span>
                    <button v-if="prescriptionForms.length > 1" @click.stop="deletePrescriptionForm(index)"
                      class="text-gray-400 hover:text-red-500">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-trash2 w-4 h-4">
                        <path d="M3 6h18"></path>
                        <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                        <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                        <line x1="10" x2="10" y1="11" y2="17"></line>
                        <line x1="14" x2="14" y1="11" y2="17"></line>
                      </svg>
                    </button>
                  </div>
                </div>

                <!-- Middle Column - Form -->
                <div class="col-span-5 p-4 space-y-4 overflow-y-auto">
                  <div>
                    <label class="block text-sm font-medium mb-1">Medication Name</label>
                    <div class="relative">
                      <input v-model="searchQuery" @input="debounceSearch" class="w-full p-2 pl-8 border rounded"
                        placeholder="Search medication..." type="text" />
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-search w-4 h-4 absolute left-2 top-3 text-gray-400">
                        <circle cx="11" cy="11" r="8"></circle>
                        <path d="m21 21-4.3-4.3"></path>
                      </svg>

                      <!-- Search Results Dropdown -->
                      <div v-if="searchQuery && searchResults.length > 0"
                        class="absolute z-50 w-full mt-1 bg-white border rounded-md shadow-lg max-h-60 overflow-y-auto">
                        <div @click="selectDrug({ name: searchQuery })"
                          class="p-2 hover:bg-gray-50 cursor-pointer border-b last:border-b-0">
                          <div class="text-sm font-medium">
                            {{ searchQuery }}
                          </div>
                        </div>
                        <div v-for="result in searchResults" :key="result.ui" @click="selectDrug(result)"
                          class="p-2 hover:bg-gray-50 cursor-pointer border-b last:border-b-0">
                          <div class="text-sm font-medium">
                            {{ result.name }} - {{ result.rxcui }}
                          </div>
                        </div>
                      </div>

                      <!-- No Results Message -->
                      <div v-if="
                        searchQuery &&
                        searchResults.length === 0 &&
                        !isLoading
                      " class="absolute z-50 w-full mt-1 bg-white border rounded-md shadow-lg p-2">
                        <div class="text-sm text-gray-500" @click="selectDrug({ name: searchQuery })">
                          {{ searchQuery }}
                        </div>
                      </div>

                      <!-- Loading State -->
                      <div v-if="isLoading" class="absolute z-50 w-full mt-1 bg-white border rounded-md shadow-lg p-2">
                        <div class="text-sm text-gray-500">Searching...</div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <label class="block text-sm font-medium mb-1">Dose</label>
                    <input v-model="currentForm.dose" class="w-full p-2 border rounded mb-2" placeholder="Enter dose"
                      type="text" />
                    <div class="flex flex-wrap gap-2">
                      <button v-for="dose in commonDoses" :key="dose" @click="updateFormField('dose', dose)"
                        class="px-2 py-1 text-xs bg-gray-100 rounded hover:bg-gray-200">
                        {{ dose }}
                      </button>
                    </div>
                  </div>

                  <div>
                    <label class="block text-sm font-medium mb-1">Route</label>
                    <input v-model="currentForm.route" class="w-full p-2 border rounded mb-2" placeholder="Enter route"
                      type="text" />
                    <div class="flex flex-wrap gap-2">
                      <button v-for="route in routes" :key="route" @click="updateFormField('route', route)"
                        class="px-2 py-1 text-xs bg-gray-100 rounded hover:bg-gray-200">
                        {{ route }}
                      </button>
                    </div>
                  </div>

                  <div>
                    <label class="block text-sm font-medium mb-1">Frequency</label>
                    <input v-model="currentForm.frequency" class="w-full p-2 border rounded mb-2"
                      placeholder="Enter frequency" type="text" />
                    <div class="flex flex-wrap gap-2">
                      <button v-for="freq in frequencies" :key="freq" @click="updateFormField('frequency', freq)"
                        class="px-2 py-1 text-xs bg-gray-100 rounded hover:bg-gray-200">
                        {{ freq }}
                      </button>
                    </div>
                  </div>

                  <div>
                    <label class="block text-sm font-medium mb-1">Duration</label>
                    <input v-model="currentForm.duration" class="w-full p-2 border rounded mb-2"
                      placeholder="Enter duration" type="text" />
                    <div class="flex flex-wrap gap-2">
                      <button v-for="duration in durations" :key="duration.value"
                        @click="updateFormField('duration', duration)"
                        class="px-2 py-1 text-xs bg-gray-100 rounded hover:bg-gray-200">
                        {{ duration.label }}
                      </button>
                    </div>
                  </div>

                  <div>
                    <label class="block text-sm font-medium mb-1">Instructions</label>
                    <textarea v-model="currentForm.instructions" class="w-full p-2 border rounded h-20"
                      placeholder="Enter instructions"></textarea>
                  </div>
                </div>

                <!-- Right Column - Preview -->
                <div class="col-span-4 p-4 bg-gray-50">
                  <h4 class="font-medium mb-4">Preview</h4>
                  <div class="space-y-2 text-sm">
                    <div><strong>Medication:</strong> {{ currentForm.medicationName || 'Not specified' }}</div>
                    <div><strong>Dose:</strong> {{ currentForm.dose || 'Not specified' }}</div>
                    <div><strong>Route:</strong> {{ currentForm.route || 'Not specified' }}</div>
                    <div><strong>Frequency:</strong> {{ currentForm.frequency || 'Not specified' }}</div>
                    <div><strong>Duration:</strong> {{ currentForm.duration || 'Not specified' }}</div>
                    <div><strong>Instructions:</strong> {{ currentForm.instructions || 'Not specified' }}</div>
                  </div>
                </div>
              </div>

              <!-- Modal Footer -->
              <div class="flex justify-end gap-2 p-4 border-t">
                <button @click="isAddPrescriptionModalOpen = false"
                  class="px-4 py-2 text-gray-600 border rounded hover:bg-gray-50">
                  Cancel
                </button>
                <button @click="savePrescriptions" class="px-4 py-2 bg-black text-white rounded hover:bg-gray-800">
                  Save All
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ApptPrescription",

  props: {
    encounterId: {
      type: [String, Number],
      required: true,
    },
    isEncounterTemp: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      prescriptions: [],
      isAddPrescriptionModalOpen: false,
      prescriptionForms: [
        {
          id: 1,
          medicationName: "",
          dose: "",
          route: "",
          frequency: "",
          duration: "",
          instructions: "",
          isSelected: true,
        },
      ],
      currentFormIndex: 0,
      searchQuery: "",
      searchResults: [],
      isLoading: false,
      searchTimeout: null,
      
      // Quick options
      commonDoses: ["5mg", "10mg", "25mg", "50mg", "100mg", "250mg", "500mg"],
      routes: ["Oral", "IV", "IM", "Topical", "Sublingual", "Rectal"],
      frequencies: ["Once daily", "Twice daily", "Three times daily", "Four times daily", "As needed"],
      durations: [
        { label: "3 days", value: "3 days" },
        { label: "5 days", value: "5 days" },
        { label: "7 days", value: "7 days" },
        { label: "14 days", value: "14 days" },
        { label: "30 days", value: "30 days" },
      ],
    };
  },

  computed: {
    currentForm() {
      return this.prescriptionForms[this.currentFormIndex] || {};
    },
  },

  mounted() {
    this.loadPrescriptions();
  },

  methods: {
    loadPrescriptions() {
      // Load existing prescriptions for this encounter
      // TODO: Implement API call
    },

    handleAddPrescriptionForm() {
      this.isAddPrescriptionModalOpen = true;
    },

    addNewPrescriptionForm() {
      const newForm = {
        id: Date.now(),
        medicationName: "",
        dose: "",
        route: "",
        frequency: "",
        duration: "",
        instructions: "",
        isSelected: false,
      };
      
      // Deselect all forms
      this.prescriptionForms.forEach(form => form.isSelected = false);
      
      // Add and select new form
      newForm.isSelected = true;
      this.prescriptionForms.push(newForm);
      this.currentFormIndex = this.prescriptionForms.length - 1;
    },

    selectPrescriptionForm(index) {
      this.prescriptionForms.forEach(form => form.isSelected = false);
      this.prescriptionForms[index].isSelected = true;
      this.currentFormIndex = index;
    },

    deletePrescriptionForm(index) {
      if (this.prescriptionForms.length > 1) {
        this.prescriptionForms.splice(index, 1);
        if (this.currentFormIndex >= this.prescriptionForms.length) {
          this.currentFormIndex = this.prescriptionForms.length - 1;
        }
        this.prescriptionForms[this.currentFormIndex].isSelected = true;
      }
    },

    debounceSearch() {
      if (this.searchTimeout) {
        clearTimeout(this.searchTimeout);
      }
      
      this.searchTimeout = setTimeout(() => {
        this.searchMedications();
      }, 300);
    },

    searchMedications() {
      if (!this.searchQuery.trim()) {
        this.searchResults = [];
        return;
      }

      this.isLoading = true;
      // TODO: Implement actual medication search API
      setTimeout(() => {
        this.searchResults = [
          { name: this.searchQuery + " 10mg", rxcui: "123456", ui: "1" },
          { name: this.searchQuery + " 25mg", rxcui: "123457", ui: "2" },
        ];
        this.isLoading = false;
      }, 500);
    },

    selectDrug(drug) {
      this.currentForm.medicationName = drug.name;
      this.searchQuery = "";
      this.searchResults = [];
    },

    updateFormField(field, value) {
      this.currentForm[field] = typeof value === 'object' ? value.value : value;
    },

    savePrescriptions() {
      // Save all prescription forms
      const validForms = this.prescriptionForms.filter(form => 
        form.medicationName && form.dose && form.frequency
      );

      if (validForms.length === 0) {
        alert("Please fill in at least medication name, dose, and frequency for one prescription.");
        return;
      }

      // TODO: Implement save to backend
      this.prescriptions = [...this.prescriptions, ...validForms.map(form => ({
        id: Date.now() + Math.random(),
        ...form
      }))];

      this.isAddPrescriptionModalOpen = false;
      this.resetForms();
    },

    resetForms() {
      this.prescriptionForms = [
        {
          id: 1,
          medicationName: "",
          dose: "",
          route: "",
          frequency: "",
          duration: "",
          instructions: "",
          isSelected: true,
        },
      ];
      this.currentFormIndex = 0;
    },

    deletePrescription(id) {
      this.prescriptions = this.prescriptions.filter(p => p.id !== id);
    },

    formatDuration(duration) {
      return duration || 'Not specified';
    },

    printPrescription() {
      // TODO: Implement print functionality
      console.log('Print prescription');
    },
  },
};
</script>
