<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Http\Request;

/**
 * External Cron Routes
 * These routes can be called by external services like UptimeRobot
 * to ensure automated posting continues even without server cron jobs
 */

// Laravel scheduler endpoint
Route::get('/cron/schedule-run', function (Request $request) {
    // Security: Check for secret token
    if ($request->get('token') !== env('CRON_TOKEN', 'default-secret')) {
        abort(403, 'Unauthorized');
    }

    try {
        Artisan::call('schedule:run');
        $output = Artisan::output();
        
        return response()->json([
            'status' => 'success',
            'message' => 'Schedule executed',
            'output' => trim($output),
            'timestamp' => now()->toISOString()
        ]);
    } catch (Exception $e) {
        return response()->json([
            'status' => 'error',
            'message' => $e->getMessage(),
            'timestamp' => now()->toISOString()
        ], 500);
    }
});

// Force publish ready posts endpoint
Route::get('/cron/force-publish', function (Request $request) {
    // Security: Check for secret token
    if ($request->get('token') !== env('CRON_TOKEN', 'default-secret')) {
        abort(403, 'Unauthorized');
    }

    try {
        Artisan::call('bot:force-publish');
        $output = Artisan::output();
        
        return response()->json([
            'status' => 'success',
            'message' => 'Force publish executed',
            'output' => trim($output),
            'timestamp' => now()->toISOString()
        ]);
    } catch (Exception $e) {
        return response()->json([
            'status' => 'error',
            'message' => $e->getMessage(),
            'timestamp' => now()->toISOString()
        ], 500);
    }
});

// Generate new posts endpoint
Route::get('/cron/generate-posts', function (Request $request) {
    // Security: Check for secret token
    if ($request->get('token') !== env('CRON_TOKEN', 'default-secret')) {
        abort(403, 'Unauthorized');
    }

    try {
        Artisan::call('bot:manage', ['action' => 'generate-posts', '--force' => true]);
        $output = Artisan::output();
        
        return response()->json([
            'status' => 'success',
            'message' => 'Posts generated',
            'output' => trim($output),
            'timestamp' => now()->toISOString()
        ]);
    } catch (Exception $e) {
        return response()->json([
            'status' => 'error',
            'message' => $e->getMessage(),
            'timestamp' => now()->toISOString()
        ], 500);
    }
});

// System health check endpoint
Route::get('/cron/health-check', function (Request $request) {
    // Security: Check for secret token
    if ($request->get('token') !== env('CRON_TOKEN', 'default-secret')) {
        abort(403, 'Unauthorized');
    }

    try {
        // Check queue jobs count
        $queueJobs = \DB::table('jobs')->count();
        
        // Check recent posts
        $recentPosts = \DB::table('automated_posts')
            ->where('created_at', '>=', now()->subHours(24))
            ->count();
            
        // Check queue worker process
        $queueWorkerRunning = shell_exec('pgrep -f "queue:work"') ? true : false;
        
        if (!$queueWorkerRunning) {
            // Restart queue worker if not running
            shell_exec('nohup php artisan queue:work --daemon --tries=3 > /dev/null 2>&1 &');
        }
        
        return response()->json([
            'status' => 'success',
            'queue_jobs' => $queueJobs,
            'recent_posts_24h' => $recentPosts,
            'queue_worker_running' => $queueWorkerRunning,
            'queue_worker_restarted' => !$queueWorkerRunning,
            'timestamp' => now()->toISOString()
        ]);
    } catch (Exception $e) {
        return response()->json([
            'status' => 'error',
            'message' => $e->getMessage(),
            'timestamp' => now()->toISOString()
        ], 500);
    }
});