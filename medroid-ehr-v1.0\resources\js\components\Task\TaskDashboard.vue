<template>
  <div>
    <div class="bg-white shadow rounded-lg">
      <div class="flex justify-between items-center p-5 border-b">
        <div>
          <h4 class="text-xl font-medium text-gray-800">{{ formTranslation.task.task_manager || 'Task Manager' }}</h4>
        </div>
        <div class="flex items-center">
          <div class="relative dropdown">
            <div class="relative inline-block text-left">
              <button type="button" class="flex items-center text-black hover:text-black-600 focus:outline-none"
                @click.stop="toggleViewDropdown">
                <span>{{ viewLabels[viewType] }}</span>
                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </button>
              <div v-if="showViewDropdown"
                class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-10"
                role="menu">
                <div class="py-1">
                  <a href="#"
                    class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 active:bg-gray-200"
                    role="menuitem" @click.prevent="changeView('list')">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2">
                      </path>
                    </svg>
                    {{ formTranslation.task.list_view || 'List View' }}
                  </a>
                  <a href="#"
                    class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 active:bg-gray-200"
                    role="menuitem" @click.prevent="changeView('calendar')">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                      </path>
                    </svg>
                    {{ formTranslation.task.calendar_view || 'Calendar View' }}
                  </a>
                  <a href="#"
                    class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 active:bg-gray-200"
                    role="menuitem" @click.prevent="changeView('kanban')">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z">
                      </path>
                    </svg>
                    {{ formTranslation.task.kanban_view || 'Kanban View' }}
                  </a>
                </div>
              </div>
            </div>
          </div>
          <button type="button"
            class="ml-4 px-4 py-2 bg-black text-white rounded-md hover:bg-black focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black flex items-center"
            @click="openTaskModal(null)">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6">
              </path>
            </svg>
            {{ formTranslation.task.add_task || 'Add Task' }}
          </button>
        </div>
      </div>

      <div class="p-5">
        <!-- Task Statistics Summary -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div class="bg-blue-50 rounded-lg p-4">
            <div class="flex items-center">
              <div class="flex items-center justify-center w-12 h-12 rounded-full bg-blue-500 mr-4">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2">
                  </path>
                </svg>
              </div>
              <div>
                <h4 class="text-xl font-semibold mb-1">{{ taskStats.total || 0 }}</h4>
                <p class="text-gray-600 text-sm">{{ formTranslation.task.total_tasks || 'Total Tasks' }}</p>
              </div>
            </div>
          </div>
          <div class="bg-yellow-50 rounded-lg p-4">
            <div class="flex items-center">
              <div class="flex items-center justify-center w-12 h-12 rounded-full bg-yellow-500 mr-4">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <div>
                <h4 class="text-xl font-semibold mb-1">{{ taskStats.pending || 0 }}</h4>
                <p class="text-gray-600 text-sm">{{ formTranslation.task.pending_tasks || 'Pending Tasks' }}</p>
              </div>
            </div>
          </div>
          <div class="bg-green-50 rounded-lg p-4">
            <div class="flex items-center">
              <div class="flex items-center justify-center w-12 h-12 rounded-full bg-green-500 mr-4">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
              </div>
              <div>
                <h4 class="text-xl font-semibold mb-1">{{ taskStats.completed || 0 }}</h4>
                <p class="text-gray-600 text-sm">{{ formTranslation.task.completed_tasks || 'Completed Tasks' }}</p>
              </div>
            </div>
          </div>
          <div class="bg-red-50 rounded-lg p-4">
            <div class="flex items-center">
              <div class="flex items-center justify-center w-12 h-12 rounded-full bg-red-500 mr-4">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <div>
                <h4 class="text-xl font-semibold mb-1">{{ taskStats.overdue || 0 }}</h4>
                <p class="text-gray-600 text-sm">{{ formTranslation.task.overdue_tasks || 'Overdue Tasks' }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Filters & Search Section (not shown in Calendar and Kanban views) -->
<div class="mb-6" v-if="viewType === 'list'">
  <div class="bg-white border border-gray-200 rounded-lg shadow-sm">
    <div class="p-4">
      <div class="flex flex-wrap items-center gap-4">
        <!-- Search Box -->
        <div class="w-full md:w-auto flex-grow">
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
                          <input type="text"
              class="block w-full pl-10 pr-3 py-2 h-10 border border-gray-300 rounded-md text-sm bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
              v-model="searchQuery" placeholder="Search by title, patient, or assignee..." @input="debounceSearch" />
          </div>
          <div v-if="searchQuery.trim()" class="mt-1 text-xs text-gray-500">
            <span class="italic">Searching across all tasks...</span>
          </div>
        </div>

        <!-- Filters Row -->
        <div class="flex flex-wrap items-center gap-4">
          <!-- Status Filter -->
          <div class="flex items-center">
            <label class="mr-2 text-sm font-medium text-gray-700">Status:</label>
            <div class="w-48">
              <multiselect v-model="filters.status" :options="statusOptions" :searchable="false"
                :close-on-select="true" :show-labels="false" track-by="id" label="label" placeholder="All"
                class="h-10 text-sm" @input="applyFilters" 
                :style="{ height: '40px', display: 'flex', alignItems: 'center' }"></multiselect>
            </div>
          </div>

          <!-- Priority Filter -->
          <div class="flex items-center">
            <label class="mr-2 text-sm font-medium text-gray-700">Priority:</label>
            <div class="w-48">
              <multiselect v-model="filters.priority" :options="priorityOptions" :searchable="false"
                :close-on-select="true" :show-labels="false" track-by="id" label="label" placeholder="All"
                class="h-10 text-sm" @input="applyFilters"
                :style="{ height: '40px', display: 'flex', alignItems: 'center' }"></multiselect>
            </div>
          </div>

          <!-- Due Date Filter -->
          <div class="flex items-center">
            <label class="mr-2 text-sm font-medium text-gray-700">Due:</label>
            <div class="flex">
              <input type="date"
                class="h-10 w-44 py-2 px-3 border border-gray-300 text-sm focus:border-blue-500 focus:ring-blue-500 transition-all duration-200"
                v-model="filters.start_date" @input="applyFilters" placeholder="From" />
              <div class="px-2 flex items-center">-</div>
              <input type="date"
                class="h-10 w-44 py-2 px-3 border border-gray-300 text-sm focus:border-blue-500 focus:ring-blue-500 transition-all duration-200"
                v-model="filters.end_date" @input="applyFilters" placeholder="To" />
              <button v-if="filters.start_date || filters.end_date" 
                class="ml-2 px-2 h-10 inline-flex items-center text-xs font-medium rounded text-gray-600 hover:text-gray-800 hover:bg-gray-100 border border-gray-300 transition-colors duration-200"
                @click="clearDateFilter">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                {{ formTranslation.common.clear || 'Clear' }}
              </button>
            </div>
          </div>

          <!-- Clear All Filters Button -->
          <button
            class="inline-flex items-center px-3 py-2 h-10 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
            @click="clearFilters">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M6 18L18 6M6 6l12 12"></path>
            </svg>
            {{ formTranslation.common.clear_all || 'Clear All' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

        <!-- Calendar Filter Section -->
        <div class="mb-6" v-if="viewType === 'calendar'">
          <div class="bg-white border border-gray-200 rounded-lg shadow-sm p-4">
            <div class="flex flex-wrap items-center justify-between">
              <div class="flex items-center space-x-4">
                <button 
                  class="px-4 py-2 bg-black text-white rounded-md hover:bg-black-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black-500"
                  @click="previousMonth">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                  </svg>
                </button>
                <h3 class="text-lg font-medium">{{ currentMonthName }} {{ currentYear }}</h3>
                <button 
                  class="px-4 py-2 bg-black text-white rounded-md hover:bg-black-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black-500"
                  @click="nextMonth">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                </button>
              </div>
              <div class="mt-4 md:mt-0">
                <button 
                  class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                  @click="setToday">
                  {{ formTranslation.calendar.today || 'Today' }}
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Task View Container (conditional rendering based on viewType) -->
        <div>
          <!-- List View -->
          <task-list 
            v-if="viewType === 'list'" 
            :tasks="filteredTasks" 
            :loading="loading" 
            @view-task="openTaskModal" 
            @edit-task="openTaskModal"
            @complete-task="completeTask" 
            @status-change="changeTaskStatus"
            @task-refresh="fetchTasks"
            @task-deleted="fetchTasks"
            @add-task="openTaskModal(null)" />
            
          <!-- Pagination Controls for List View -->
          <div v-if="viewType === 'list' && totalPages > 1" class="mt-4 flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
            <div class="flex flex-1 justify-between sm:hidden">
              <button
                @click="goToPage(currentPage - 1)"
                :disabled="currentPage === 1"
                :class="[
                  'relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700',
                  currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'
                ]"
              >
                {{ formTranslation.pagination.previous || 'Previous' }}
              </button>
              <button
                @click="goToPage(currentPage + 1)"
                :disabled="currentPage === totalPages"
                :class="[
                  'relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700',
                  currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'
                ]"
              >
                {{ formTranslation.pagination.next || 'Next' }}
              </button>
            </div>
            <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
              <div>
                <p class="text-sm text-gray-700">
                  {{ formTranslation.pagination.showing || 'Showing' }} <span class="font-medium">{{ ((currentPage - 1) * perPage) + 1 }}</span> {{ formTranslation.pagination.to || 'to' }} <span class="font-medium">{{ Math.min(currentPage * perPage, totalTasks) }}</span> {{ formTranslation.pagination.of || 'of' }} <span class="font-medium">{{ totalTasks }}</span> {{ formTranslation.pagination.results || 'results' }}
                </p>
              </div>
              <div>
                <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                  <button
                    @click="goToPage(currentPage - 1)"
                    :disabled="currentPage === 1"
                    :class="[
                      'relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400',
                      currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'
                    ]"
                  >
                    <span class="sr-only">{{ formTranslation.pagination.previous || 'Previous' }}</span>
                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd" />
                    </svg>
                  </button>
                  
                  <!-- Page numbers -->
                  <template v-for="page in paginationRange">
                    <button
                      v-if="page !== '...'"
                      :key="page"
                      @click="goToPage(page)"
                      :class="[
                        'relative inline-flex items-center px-4 py-2 text-sm font-semibold',
                        currentPage === page
                          ? 'z-10 bg-black text-white focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600'
                          : 'text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:outline-offset-0'
                      ]"
                    >
                      {{ page }}
                    </button>
                    <span
                      v-else
                      :key="'ellipsis-' + page"
                      class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-700 ring-1 ring-inset ring-gray-300"
                    >
                      ...
                    </span>
                  </template>
                  
                  <button
                    @click="goToPage(currentPage + 1)"
                    :disabled="currentPage === totalPages"
                    :class="[
                      'relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400',
                      currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'
                    ]"
                  >
                    <span class="sr-only">{{ formTranslation.pagination.next || 'Next' }}</span>
                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
                    </svg>
                  </button>
                </nav>
              </div>
            </div>
          </div>

          <!-- Calendar View -->
          <task-calendar 
            v-if="viewType === 'calendar'" 
            :tasks="tasks" 
            :current-month="currentMonth" 
            :current-year="currentYear"
            @day-click="createTaskOnDate" 
            @task-click="openTaskModal" />

          <!-- Kanban View -->
          <task-kanban 
            v-if="viewType === 'kanban'" 
            :tasks="tasks" 
            :loading="loading"
            @view-task="openTaskModal" 
            @edit-task="openTaskModal"
            @complete-task="completeTask" 
            @status-change="changeTaskStatus" />
        </div>
      </div>
    </div>

    <!-- Task Modal -->
    <task-modal :show="showTaskModal" :task-id="selectedTaskId" :is-new="isNewTask" :task-data="selectedTaskData"
      :initial-due-date="selectedDate" @close="closeTaskModal" @task-saved="onTaskSaved" @task-deleted="onTaskDeleted" />
  </div>
</template>

<script>
import TaskList from './TaskList.vue';
import TaskCalendar from './TaskCalendar.vue';
import TaskKanban from './TaskKanban.vue';
import TaskModal from './TaskModal.vue';
import Multiselect from 'vue-multiselect';
import debounce from 'lodash/debounce';

// Import task translations from the separate file
import taskTranslationsModule from '../../config/task-translations';
import { post, get } from "../../config/request";

// Add extensive fallback translations for the Task Manager
const initTaskManagerTranslations = () => {
  if (!window.formTranslation) {
    window.formTranslation = {};
  }

  // Basic namespaces
  ['task', 'common', 'sidebar', 'pagination', 'calendar'].forEach(namespace => {
    if (!window.formTranslation[namespace]) {
      window.formTranslation[namespace] = {};
    }
  });

  // Common translations
  const commonTranslations = {
    all: 'All',
    loading: 'Loading...',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    cancel: 'Cancel',
    yes: 'Yes',
    save: 'Save',
    clear: 'Clear',
    clear_all: 'Clear All',
    actions: 'Actions',
    success: 'Success',
    error: 'Error',
    info: 'Information',
    add: 'Add',
    create: 'Create',
    update: 'Update',
    search: 'Search',
    no_results: 'No results found',
    select: 'Select'
  };

  // Sidebar translations
  const sidebarTranslations = {
    dashboard: 'Dashboard',
    task_manager: 'Task Manager',
    appointment_list: 'Appointments',
    clinic: 'Clinic',
    patient: 'Patients',
    doctor: 'Doctors',
    service: 'Services',
    settings: 'Settings'
  };

  // Pagination translations
  const paginationTranslations = {
    previous: 'Previous',
    next: 'Next',
    showing: 'Showing',
    to: 'to',
    of: 'of',
    results: 'results'
  };
  
  // Calendar translations
  const calendarTranslations = {
    today: 'Today'
  };

  // Merge translations with existing ones
  Object.assign(window.formTranslation.common, commonTranslations);
  Object.assign(window.formTranslation.sidebar, sidebarTranslations);
  Object.assign(window.formTranslation.pagination, paginationTranslations);
  Object.assign(window.formTranslation.calendar, calendarTranslations);

  // Use the translations from the imported module directly
  if (!window.formTranslation.task) {
    window.formTranslation.task = {};
  }
  Object.assign(window.formTranslation.task, taskTranslationsModule);

  // Also make sure they're in window.__kivicarelang if needed
  if (!window.__kivicarelang) {
    window.__kivicarelang = {};
  }
  if (!window.__kivicarelang.task) {
    window.__kivicarelang.task = {};
  }
  Object.assign(window.__kivicarelang.task, taskTranslationsModule);
};

// Initialize translations
initTaskManagerTranslations();

export default {
  name: 'TaskDashboard',
  components: {
    TaskList,
    TaskCalendar,
    TaskKanban,
    TaskModal,
    Multiselect
  },
  data() {
    return {
      viewType: 'list',
      loading: false,
      tasks: [],
      selectedTaskId: null,
      selectedTaskData: null,
      isNewTask: false,
      showTaskModal: false,
      searchQuery: '',
      showViewDropdown: false,
      selectedDate: null,
      
      // Pagination related data
      currentPage: 1,
      perPage: 10,
      totalTasks: 0,
      totalPages: 1,
      
      // Calendar related data
      currentMonth: new Date().getMonth(),
      currentYear: new Date().getFullYear(),

      filters: {
        status: { id: 'all', label: 'All' },
        priority: { id: 'all', label: 'All' },
        assignee: null,
        start_date: null,
        end_date: null
      },

      statusOptions: [
        { id: 'all', label: 'All' },
        { id: 'pending', label: 'Pending' },
        { id: 'in-progress', label: 'In Progress' },
        { id: 'completed', label: 'Completed' },
        { id: 'cancelled', label: 'Cancelled' }
      ],

      priorityOptions: [
        { id: 'all', label: 'All' },
        { id: 'low', label: 'Low' },
        { id: 'medium', label: 'Medium' },
        { id: 'high', label: 'High' }
      ],

      userOptions: [],
      
      // Labels for view types
      viewLabels: {
        list: 'List View',
        calendar: 'Calendar View',
        kanban: 'Kanban View'
      }
    }
  },
  computed: {
    // Using direct Vuex state instead of getters which might not be defined
    isAdmin() {
      return this.$store.state.userDataModule.user.roles &&
        (this.$store.state.userDataModule.user.roles.includes('administrator'));
    },
    isClinicAdmin() {
      return this.$store.state.userDataModule.user.roles &&
        this.$store.state.userDataModule.user.roles.includes('clinic_admin');
    },
    currentUser() {
      return this.$store.state.userDataModule.user;
    },

    filteredTasks() {
      return this.tasks;
    },

    taskStats() {
      // Calculate task statistics
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      return {
        total: this.tasks.length,
        pending: this.tasks.filter(task => task.status === 'pending').length,
        completed: this.tasks.filter(task => task.status === 'completed').length,
        overdue: this.tasks.filter(task => {
          if (task.status === 'completed' || task.status === 'cancelled') return false;
          if (!task.due_date) return false;
          const dueDate = new Date(task.due_date);
          return dueDate < today;
        }).length
      };
    },
    
    // Calendar computed properties
    currentMonthName() {
      const monthNames = [
        'January', 'February', 'March', 'April', 'May', 'June', 
        'July', 'August', 'September', 'October', 'November', 'December'
      ];
      return monthNames[this.currentMonth];
    },
    
    // Computed property to generate pagination range with ellipsis
    paginationRange() {
      const range = [];
      const totalVisiblePages = 5; // How many page buttons to show
      
      if (this.totalPages <= totalVisiblePages) {
        // If we have fewer pages than our limit, show all pages
        for (let i = 1; i <= this.totalPages; i++) {
          range.push(i);
        }
      } else {
        // Always show first page
        range.push(1);
        
        // Calculate start and end of visible pages
        let start = Math.max(2, this.currentPage - 1);
        let end = Math.min(this.totalPages - 1, this.currentPage + 1);
        
        // Adjust if we're at the beginning or end
        if (this.currentPage <= 2) {
          end = 4;
        } else if (this.currentPage >= this.totalPages - 1) {
          start = this.totalPages - 3;
        }
        
        // Add ellipsis before middle pages if needed
        if (start > 2) {
          range.push('...');
        }
        
        // Add middle pages
        for (let i = start; i <= end; i++) {
          range.push(i);
        }
        
        // Add ellipsis after middle pages if needed
        if (end < this.totalPages - 1) {
          range.push('...');
        }
        
        // Always show last page
        range.push(this.totalPages);
      }
      
      return range;
    }
  },
  mounted() {
    // Add click outside event listener for the dropdown
    document.addEventListener('click', this.hideViewDropdown);
  },
  beforeDestroy() {
    // Remove click outside event listener
    document.removeEventListener('click', this.hideViewDropdown);
  },
  created() {
    this.debounceSearch = debounce(() => {
      // Reset to first page and fetch tasks with new search query
      this.currentPage = 1;
      this.fetchTasks();
    }, 300);

    this.fetchTasks();
    this.fetchUsers();
  },
  methods: {
    toggleViewDropdown(event) {
      event.stopPropagation();
      this.showViewDropdown = !this.showViewDropdown;
    },

    hideViewDropdown(event) {
      if (!event.target.closest('.dropdown')) {
        this.showViewDropdown = false;
      }
    },

    getUserRole() {
      // Get user role from store
      if (this.currentUser && this.currentUser.roles && this.currentUser.roles.length > 0) {
        return this.currentUser.roles[0];
      }
      return '';
    },

    async fetchTasks() {
      this.loading = true;
      try {
        // Determine parameters based on user role
        const userRole = this.getUserRole();
        const userId = this.currentUser.ID;

        // Build request parameters based on role
        let params = { 
          ...this.serverParams,
          page: this.currentPage,
          per_page: this.perPage
        };

        // Add search query if it exists
        if (this.searchQuery) {
          params.search = this.searchQuery;
        }
        
        // Add status filter if not 'all'
        if (this.filters.status && this.filters.status.id !== 'all') {
          params.status = this.filters.status.id;
        }
        
        // Add priority filter if not 'all'
        if (this.filters.priority && this.filters.priority.id !== 'all') {
          params.priority = this.filters.priority.id;
        }
        
        // Add assignee filter
        if (this.filters.assignee && this.filters.assignee.id !== 'all') {
          params.assignee_id = this.filters.assignee.id;
        }
        
        // Add date filters
        if (this.filters.start_date) {
          params.due_date_start = this.filters.start_date;
        }
        
        if (this.filters.end_date) {
          params.due_date_end = this.filters.end_date;
        }

        // Role-specific filtering
        if (userRole === 'doctor') {
          params.doctor_id = userId;
        } else if (userRole === 'receptionist') {
          params.receptionist_id = userId;
        } else if (userRole === 'clinic_admin') {
          params.clinic_admin_id = userId;
        } else if (userRole === 'patient') {
          params.patient_id = userId;
        }

        const response = await get("tasks_list", params);

        if (response.data.status !== undefined && response.data.status === true) {
          this.tasks = response.data.data || [];
          this.totalTasks = response.data.total || 0;
          this.totalPages = response.data.total_pages || 1;
          this.currentPage = response.data.page || 1;
        } else {
          this.tasks = [];
          this.totalTasks = 0;
          this.totalPages = 1;
        }
      } catch (error) {
        this.tasks = [];
        this.totalTasks = 0;
        this.totalPages = 1;
        
        if (typeof displayErrorMessage === 'function') {
          displayErrorMessage(formTranslation.common.error || 'Internal server error');
        } else {
          this.$swal.fire({
            icon: 'error',
            title: formTranslation.common.error || 'Error',
            text: formTranslation.common.internal_error || 'Internal server error'
          });
        }
      } finally {
        this.loading = false;
      }
    },

    async fetchUsers() {
      try {
        if (this.isAdmin || this.isClinicAdmin) {
          const endpoint = '/wp-admin/admin-ajax.php?action=ajax_get&route_name=get_clinic_staff';
          const response = await axios.get(endpoint);

          if (response.data && response.data.status) {
            this.userOptions = [
              { id: 'all', label: formTranslation.common.all || 'All' },
              ...response.data.data.map(user => ({
                id: user.id,
                label: user.display_name || user.username
              }))
            ];
          }
        }
      } catch (error) {
        // Silent failure, userOptions will remain empty
      }
    },

    // Calendar navigation methods
    previousMonth() {
      if (this.currentMonth === 0) {
        this.currentMonth = 11;
        this.currentYear--;
      } else {
        this.currentMonth--;
      }
    },

    nextMonth() {
      if (this.currentMonth === 11) {
        this.currentMonth = 0;
        this.currentYear++;
      } else {
        this.currentMonth++;
      }
    },

    setToday() {
      const today = new Date();
      this.currentMonth = today.getMonth();
      this.currentYear = today.getFullYear();
    },

    changeView(type) {
      this.viewType = type;
      this.showViewDropdown = false;
      
      // If switching to calendar view, make sure we're showing the current month
      if (type === 'calendar') {
        this.setToday();
      }
    },

    openTaskModal(taskId) {
      this.selectedTaskId = taskId;
      this.isNewTask = !taskId;

      // If editing an existing task, find the task data in the current list
      if (taskId) {
        this.selectedTaskData = this.tasks.find(task => task.id == taskId);
      } else {
        this.selectedTaskData = null;
      }

      this.showTaskModal = true;
    },

    closeTaskModal() {
      this.showTaskModal = false;
      this.selectedTaskId = null;
      this.selectedTaskData = null;
      this.selectedDate = null;
    },

    onTaskSaved() {
      // Refresh task list after a task is created or updated
      this.fetchTasks();
      this.closeTaskModal();
    },

    onTaskDeleted() {
      // Refresh task list after a task is deleted
      this.fetchTasks();
      this.closeTaskModal();
    },

    async changeTaskStatus({ taskId, status }) {
      try {
        const response = await post(`update_task_status`, { id: taskId, status: status });

        if (response.data && response.data.status) {
          // After successful status update, refresh the tasks to update the UI
          await this.fetchTasks();
          
          // Show a subtle success notification
          if (this.$toaster) {
            this.$toaster.success(formTranslation.task.status_updated_successfully || 'Status updated successfully', {
              position: 'top-right',
              duration: 3000
            });
          }
        } else {
          this.$swal.fire({
            icon: 'error',
            title: formTranslation.common.error || 'Error',
            text: response.data.message || (formTranslation.task.status_update_error || 'Failed to update task status')
          });
        }
      } catch (error) {
        this.$swal.fire({
          icon: 'error',
          title: formTranslation.common.error || 'Error',
          text: formTranslation.task.status_update_error || 'Failed to update task status'
        });
      }
    },

    async completeTask(taskId) {
      try {
        const response = await post(`complete_task`, { id: taskId });

        if (response.data && response.data.status) {
          this.$swal.fire({
            icon: 'success',
            title: formTranslation.common.success || 'Success',
            text: formTranslation.task.mark_complete_success || 'Task marked as complete',
            showConfirmButton: false,
            timer: 1500
          });

          // Refresh all tasks after successful completion
          await this.fetchTasks();
        } else {
          this.$swal.fire({
            icon: 'error',
            title: formTranslation.common.error || 'Error',
            text: response.data.message || (formTranslation.task.mark_complete_error || 'Failed to mark task as complete')
          });
        }
      } catch (error) {
        this.$swal.fire({
          icon: 'error',
          title: formTranslation.common.error || 'Error',
          text: formTranslation.task.mark_complete_error || 'Failed to mark task as complete'
        });
      }
    },

    createTaskOnDate(date) {
      // Open the task modal with the selected date prefilled
      this.selectedTaskId = null;
      this.isNewTask = true;
      this.selectedDate = date;
      this.showTaskModal = true;
    },

    applyFilters() {
      // Reset to first page and fetch tasks with new filters
      this.currentPage = 1;
      this.fetchTasks();
    },

    clearFilters() {
      this.filters = {
        status: { id: 'all', label: 'All' },
        priority: { id: 'all', label: 'All' },
        assignee: null,
        start_date: null,
        end_date: null
      };
      this.searchQuery = '';
      // Reset to first page and fetch tasks with cleared filters
      this.currentPage = 1;
      this.fetchTasks();
    },

    clearDateFilter() {
      this.filters.start_date = null;
      this.filters.end_date = null;
      this.applyFilters();
    },
    
    // Method to handle page navigation
    goToPage(page) {
      if (page < 1 || page > this.totalPages || page === this.currentPage) {
        return;
      }
      
      this.currentPage = page;
      this.fetchTasks();
      
      // Scroll to top of the task list for better UX
      window.scrollTo({
        top: this.$el.offsetTop - 20,
        behavior: 'smooth'
      });
    }
  }
}
</script>