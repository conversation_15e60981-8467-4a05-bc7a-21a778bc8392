<template>
    <div class="w-full">
        <div class="w-full bg-white rounded-lg shadow-md">
            <!-- Warning Note -->
            <div class="p-4 m-4 border border-gray-200 bg-gray-50 text-gray-600 rounded">
                <span class="font-bold">Note:- </span>
                If Woocommerce payment settings is toggle on, then appointment message sent through Email/SMS/Whatsapp after payment successfully done by patient And this feature is only available on Kivicare-Pro.
            </div>

            <!-- Advance Appointment Section -->
            <div class="p-4">
                <div class="mb-6">
                    <h2 class="text-2xl font-semibold text-blue-600">
                        Restrict Advance Appointment Booking
                    </h2>
                </div>
                
                <div class="space-y-4 text-gray-700">
                    <ul class="list-disc pl-6 space-y-2">
                        <li>
                            <span class="font-semibold">Open booking days:- </span>
                            As consideration of current date booking already opened as mentioned days in settings.
                        </li>
                        <li>
                            <span class="font-semibold">Close booking days:- </span>
                            As consideration of current date booking already closed as mentioned days in settings.
                        </li>
                    </ul>
                    
                    <div class="mt-4">
                        <span class="font-semibold">Example:</span>
                        <div class="mt-2 space-y-1">
                            <p>current Date => 25 November</p>
                            <p>Pre-booking => 10 days, so patient/doctor/receptionist can't select the date from 25 November to 5 December.</p>
                            <p>Post-booking => 10 days, so patient/doctor/receptionist can't select the date after 5 December.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- File Upload Section -->
            <div class="p-4 border-t border-gray-100">
                <div class="mb-6">
                    <h2 class="text-2xl font-semibold text-blue-600">
                        File Upload Setting
                    </h2>
                </div>
                
                <div class="space-y-4 text-gray-700">
                    <p>
                        If <span class="font-semibold">Appointment File Upload</span> toggle is on, then patient can add their reports at the time of booking an appointment.
                    </p>
                    
                    <div class="p-4 border border-gray-200 bg-gray-50 rounded">
                        <span class="font-semibold">Note:- </span>
                        If kivicare pro version is activate then patients can add more then one report, else patients can only add one report at a time while booking an appointment.
                    </div>
                </div>
            </div>

            <!-- Appointment Reminder Section -->
            <div class="p-4 border-t border-gray-100">
                <div class="mb-6">
                    <h2 class="text-2xl font-semibold text-blue-600">
                        Appointment Reminder
                    </h2>
                </div>
                
                <div class="space-y-4 text-gray-700">
                    <ol class="list-decimal pl-6 space-y-2">
                        <li>
                            <span class="font-semibold">Appointment Email Reminder:- </span>
                            If the toggle is on, Then an <span class="font-semibold">Appointment Email Reminder</span> will be send to <span class="font-semibold">patient</span> through <span class="font-semibold">Email</span>.
                        </li>
                        <li>
                            <span class="font-semibold">Appointment Sms Reminder:- </span>
                            If the toggle is on, Then an <span class="font-semibold">Appointment Sms Reminder</span> will be send to <span class="font-semibold">patient</span> through <span class="font-semibold">SMS</span>.
                        </li>
                        <li>
                            <span class="font-semibold">Appointment Whatsapp Reminder:- </span>
                            If the toggle is on, Then an <span class="font-semibold">Appointment Whatsapp Reminder</span> will be send to <span class="font-semibold">patient</span> through <span class="font-semibold">WhatsApp</span>.
                        </li>
                    </ol>
                    
                    <div class="p-4 border border-gray-200 bg-gray-50 rounded">
                        <span class="font-semibold">Note:- </span>
                        Appointment Sms Reminder and Appointment Whatsapp Reminder, Required Kivicare-Pro to be Activated and also from pro settings:- toggle on WhatsApp and SMS, for access Appointment Sms Reminder and Appointment Whatsapp Reminder, this two setting is required to toggle on.
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'SettingsInfo',
    data() {
        return {}
    },
    mounted() {
    },
    methods: {
        init() {
        }
    }
}
</script>