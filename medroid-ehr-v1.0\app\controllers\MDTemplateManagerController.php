<?php

namespace App\controllers;

use App\baseClasses\KCBase;
use App\baseClasses\KCRequest;
use App\models\MDTemplateManager;
use App\models\KCClinic;
use App\models\KCDoctorClinicMapping;
use Exception;
use WP_User;

class MDTemplateManagerController extends KCBase {

    public $db;
    
    /**
     * @var KCRequest
     */
    private $request;
    
    /**
     * @var MDTemplateManager
     */
    private $template_manager;

    /**
     * Constructor
     */
    public function __construct() {
        global $wpdb;
        
        $this->db = $wpdb;
        $this->request = new KCRequest();
        $this->template_manager = new MDTemplateManager();
        
        parent::__construct();
    }

    /**
     * Get templates based on user role and permissions
     */
    public function index() {
        // Check permission for template list
        // if (!kcCheckPermission('template_list') && is_user_logged_in()) {
        //     wp_send_json(kcUnauthorizeAccessResponse(403));
        // }

        $request_data = $this->request->getInputs();

        try {
            // Get request inputs
            $request_data = $this->request->getInputs();
            
            // Get current user information
            $user_id = get_current_user_id();
            $current_login_user_role = $this->getLoginUserRole();
            
            // Default query conditions
            $search_condition = $doctor_condition = $clinic_condition = $paginationCondition = "";
            $orderByCondition = " ORDER BY created_at DESC ";
            $template_table = $this->db->prefix . 'md_template_manager';
            
            // Handle pagination
            if (isset($request_data['type']) && $request_data['type'] === 'list') {
                if ((int) $request_data['perPage'] > 0) {
                    $perPage = (int) $request_data['perPage'];
                    $offset = ((int) $request_data['page'] - 1) * $perPage;
                    $paginationCondition = " LIMIT {$perPage} OFFSET {$offset} ";
                }
                
                // Handle sorting
                if (!empty($request_data['sort'])) {
                    $request_data['sort'] = kcRecursiveSanitizeTextField(json_decode(stripslashes($request_data['sort'][0]), true));
                    if (!empty($request_data['sort']['field']) && !empty($request_data['sort']['type']) && $request_data['sort']['type'] !== 'none') {
                        $sortField = esc_sql($request_data['sort']['field']);
                        $sortByValue = esc_sql(strtoupper($request_data['sort']['type']));
                        
                        // Apply sorting based on allowed fields
                        switch ($sortField) {
                            case 'id':
                            case 'name':
                            case 'category':
                            case 'share_status':
                            case 'created_at':
                            case 'updated_at':
                                $orderByCondition = " ORDER BY {$sortField} {$sortByValue} ";
                                break;
                        }
                    }
                }
                
                // Handle search
                if (isset($request_data['searchTerm']) && trim($request_data['searchTerm']) !== '') {
                    $searchTerm = esc_sql(strtolower(trim($request_data['searchTerm'])));
                    $search_condition .= " AND (
                        {$template_table}.id LIKE '%{$searchTerm}%' 
                        OR {$template_table}.name LIKE '%{$searchTerm}%' 
                        OR {$template_table}.category LIKE '%{$searchTerm}%' 
                        OR {$template_table}.share_status LIKE '%{$searchTerm}%' 
                    ) ";
                } else if (!empty($request_data['columnFilters'])) {
                    $request_data['columnFilters'] = json_decode(stripslashes($request_data['columnFilters']), true);
                    foreach ($request_data['columnFilters'] as $column => $searchValue) {
                        $searchValue = !empty($searchValue) ? $searchValue : '';
                        $searchValue = esc_sql(strtolower(trim($searchValue)));
                        $column = esc_sql($column);
                        if ($searchValue === '') {
                            continue;
                        }
                        
                        // Apply filtering based on column
                        switch ($column) {
                            case 'id':
                            case 'name':
                            case 'category':
                            case 'share_status':
                                $search_condition .= " AND {$template_table}.{$column} LIKE '%{$searchValue}%' ";
                                break;
                            case 'doctor_name':
                                $search_condition .= " AND doctor_name LIKE '%{$searchValue}%' ";
                                break;
                            case 'clinic_name':
                                $search_condition .= " AND clinic_name LIKE '%{$searchValue}%' ";
                                break;
                        }
                    }
                }
            }
            
            // Role-specific conditions
            $templates_query = "";
            
            switch ($current_login_user_role) {
                case 'administrator':
                    // Admin can see all templates
                    $templates_query = "SELECT t.*, 
                            CASE WHEN t.doctor_id > 0 THEN 
                                (SELECT display_name FROM {$this->db->users} WHERE ID = t.doctor_id) 
                                ELSE '" . esc_sql(__('System', 'kc-lang')) . "' 
                            END as doctor_name,
                            CASE WHEN t.clinic_id > 0 THEN 
                                (SELECT name FROM {$this->db->prefix}kc_clinics WHERE id = t.clinic_id) 
                                ELSE '' 
                            END as clinic_name
                        FROM {$template_table} t
                        WHERE t.status = 1 {$search_condition}
                        {$orderByCondition}";
                    break;
                    
                case $this->getClinicAdminRole():
                    // Clinic admin sees clinic templates and personal templates
                    $clinic_id = kcGetClinicIdOfClinicAdmin();

                    if (empty($clinic_id)) {
                        $templates_query = "SELECT * FROM {$template_table} WHERE 1=0"; // Empty result
                    } else {
                        $templates_query = "SELECT t.*, 
                                CASE WHEN t.doctor_id > 0 THEN 
                                    (SELECT display_name FROM {$this->db->users} WHERE ID = t.doctor_id) 
                                    ELSE '" . esc_sql(__('System', 'kc-lang')) . "' 
                                END as doctor_name,
                                CASE WHEN t.clinic_id > 0 THEN 
                                    (SELECT name FROM {$this->db->prefix}kc_clinics WHERE id = t.clinic_id) 
                                    ELSE '' 
                                END as clinic_name
                            FROM {$template_table} t
                            WHERE t.status = 1 
                            AND (t.clinic_id = {$clinic_id} OR t.doctor_id = {$user_id} OR t.share_status = 'public')
                            {$search_condition}
                            {$orderByCondition}";
                    }
                    break;
                    
                case $this->getDoctorRole():
                    // Doctor sees personal, clinic and public templates
                    $doctor_clinics = $this->db->get_col("
                        SELECT clinic_id FROM {$this->db->prefix}kc_doctor_clinic_mappings 
                        WHERE doctor_id = {$user_id} AND status = 1
                    ");
                    
                    $clinic_condition = "";
                    if (!empty($doctor_clinics)) {
                        $clinic_ids = implode(',', array_map('intval', $doctor_clinics));
                        $clinic_condition = " OR t.clinic_id IN ({$clinic_ids}) ";
                    }
                    
                    $templates_query = "SELECT t.*, 
                            CASE WHEN t.doctor_id > 0 THEN 
                                (SELECT display_name FROM {$this->db->users} WHERE ID = t.doctor_id) 
                                ELSE '" . esc_sql(__('System', 'kc-lang')) . "' 
                            END as doctor_name,
                            CASE WHEN t.clinic_id > 0 THEN 
                                (SELECT name FROM {$this->db->prefix}kc_clinics WHERE id = t.clinic_id) 
                                ELSE '' 
                            END as clinic_name
                        FROM {$template_table} t
                        WHERE t.status = 1 
                        AND (t.doctor_id = {$user_id} {$clinic_condition} OR t.share_status = 'public')
                        {$search_condition}
                        {$orderByCondition}";
                    break;
                    
                default:
                    // Other roles see no templates or only public ones
                    $templates_query = "SELECT t.*, 
                            CASE WHEN t.doctor_id > 0 THEN 
                                (SELECT display_name FROM {$this->db->users} WHERE ID = t.doctor_id) 
                                ELSE '" . esc_sql(__('System', 'kc-lang')) . "' 
                            END as doctor_name,
                            CASE WHEN t.clinic_id > 0 THEN 
                                (SELECT name FROM {$this->db->prefix}kc_clinics WHERE id = t.clinic_id) 
                                ELSE '' 
                            END as clinic_name
                        FROM {$template_table} t
                        WHERE t.status = 1 
                        AND t.share_status = 'public'
                        {$search_condition}
                        {$orderByCondition}";
                    break;
            }
            
            // Count total records for pagination
            $total = 0;
            if (isset($request_data['type']) && $request_data['type'] === 'list') {
                $count_query = preg_replace('/SELECT.*?FROM/is', 'SELECT COUNT(*) FROM', $templates_query);
                $count_query = preg_replace('/ORDER BY.*$/is', '', $count_query);
                $total = $this->db->get_var($count_query);
                
                // Add pagination to the main query
                $templates_query .= $paginationCondition;
            }
            
            // Execute the query
            $templates = $this->db->get_results($templates_query);
            
            // Process templates to add additional information
            if (!empty($templates)) {
                $share_status_labels = [
                    'private' => __('Private', 'kc-lang'),
                    'clinic' => __('Clinic', 'kc-lang'),
                    'public' => __('Public', 'kc-lang')
                ];
                
                $category_labels = [
                    'general' => __('General', 'kc-lang'),
                    'referral' => __('Referral', 'kc-lang'),
                    'sick_note' => __('Sick Note', 'kc-lang'),
                    'consultation' => __('Consultation', 'kc-lang'),
                    'procedure' => __('Procedure', 'kc-lang')
                ];
                
                foreach ($templates as $key => $template) {
                    // Set user-friendly labels
                    $templates[$key]->share_status_label = $share_status_labels[$template->share_status] ?? $template->share_status;
                    $templates[$key]->category_label = $category_labels[$template->category] ?? $template->category;
                    
                    // Ownership and editing permissions
                    $templates[$key]->is_owner = ($template->doctor_id == $user_id);
                    $templates[$key]->is_editable = ($template->doctor_id == $user_id) || ($current_login_user_role === 'administrator') || ($current_login_user_role === $this->getClinicAdminRole() && $template->clinic_id == kcGetClinicIdOfClinicAdmin());
                }
            }
            
            // Send the response
            if (empty($templates)) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('No templates found', 'kc-lang'),
                    'data' => []
                ]);
            } else {
                wp_send_json([
                    'status' => true,
                    'message' => esc_html__('Templates retrieved successfully', 'kc-lang'),
                    'data' => $templates,
                    'total_rows' => isset($request_data['type']) && $request_data['type'] === 'list' ? $total : count($templates)
                ]);
            }
            
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Check if the templates table exists
     *
     * @return bool
     */
    private function tableExists() {
        return $this->db->get_var("SHOW TABLES LIKE '{$this->db->prefix}md_template_manager'") === $this->db->prefix . 'md_template_manager';
    }

    /**
     * Save a new template
     */
    public function save_template() {
        // Check permission for template save
        // if (!kcCheckPermission('template_add') && is_user_logged_in()) {
        //     wp_send_json(kcUnauthorizeAccessResponse(403));
        // }
        
        try {
            // Get request data
            $request_data = $this->request->getInputs();
            $user_id = get_current_user_id();
            $current_login_user_role = $this->getLoginUserRole();
            
            // Validate required fields
            if (empty($request_data['name']) || empty($request_data['category']) || empty($request_data['content'])) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Name, category and content are required fields', 'kc-lang')
                ]);
            }
            
            // Prepare template data
            $template_data = [
                'name' => sanitize_text_field($request_data['name']),
                'category' => sanitize_text_field($request_data['category']),
                'content' => wp_kses_post($request_data['content']), // Content may contain HTML but sanitize it
                'share_status' => sanitize_text_field($request_data['share_status'] ?? 'private'),
                'status' => 1
            ];

            // Check if we're updating an existing template
            if (!empty($request_data['id'])) {
                $template_id = (int) $request_data['id'];
                $existing_template = $this->template_manager->get_template($template_id);
                
                if (!$existing_template) {
                    wp_send_json([
                        'status' => false,
                        'message' => esc_html__('Template not found', 'kc-lang')
                    ]);
                }
                
                // System templates cannot be edited
                if ($existing_template->is_system) {
                    wp_send_json([
                        'status' => false,
                        'message' => esc_html__('System templates cannot be edited. Please clone it to make your own version.', 'kc-lang')
                    ]);
                }

                // Check if user has permission to edit this template
                $can_edit = false;
                
                if ($current_login_user_role === 'administrator') {
                    $can_edit = true;
                } else if ($current_login_user_role === $this->getClinicAdminRole() && 
                           $existing_template->clinic_id == kcGetClinicIdOfClinicAdmin()) {
                    $can_edit = true;
                } else if ($existing_template->doctor_id == $user_id) {
                    $can_edit = true;
                }
                
                if (!$can_edit) {
                    wp_send_json([
                        'status' => false,
                        'message' => esc_html__('You do not have permission to edit this template', 'kc-lang')
                    ]);
                }

                $template_data['updated_at'] = current_time('Y-m-d H:i:s');
                $template_data['clinic_id'] = !empty($request_data['clinic_id']) ? $request_data['clinic_id'] : null;

                $result = $this->db->update($this->db->prefix . "md_template_manager", $template_data, ['id' => $template_id]);

                if ($result) {
                    wp_send_json([
                        'status' => true,
                        'message' => esc_html__('Template updated successfully', 'kc-lang'),
                        'data' => [
                            'id' => $template_id
                        ]
                    ]);
                } else {
                    wp_send_json([
                        'status' => false,
                        'message' => esc_html__('Failed to update template', 'kc-lang')
                    ]);
                }
            } else {
                // Set doctor_id and clinic_id based on user role for new templates
                switch ($current_login_user_role) {
                    case $this->getDoctorRole():
                        $template_data['doctor_id'] = $user_id;
                        
                        // If clinic_id is provided and doctor belongs to this clinic, use it
                        if (!empty($request_data['clinic_id'])) {
                            $clinic_id = (int) $request_data['clinic_id'];
                            
                            // Verify doctor belongs to this clinic
                            $is_doctor_in_clinic = $this->db->get_var($this->db->prepare(
                                "SELECT COUNT(*) FROM {$this->db->prefix}kc_doctor_clinic_mappings 
                                WHERE doctor_id = %d AND clinic_id = %d AND status = 1",
                                $user_id, $clinic_id
                            ));
                            
                            if ($is_doctor_in_clinic) {
                                $template_data['clinic_id'] = $clinic_id;
                            } else {
                                // If doctor does not belong to the clinic, use their default clinic
                                $template_data['clinic_id'] = kcGetDefaultClinicId();
                            }
                        } else {
                            // Use default clinic if none specified
                            $template_data['clinic_id'] = kcGetDefaultClinicId();
                        }
                        break;
                        
                    case $this->getClinicAdminRole():
                        $template_data['clinic_id'] = kcGetClinicIdOfClinicAdmin();
                        
                        // Check if the clinic admin is also a doctor
                        $is_also_doctor = in_array($this->getDoctorRole(), get_userdata($user_id)->roles);
                        if ($is_also_doctor) {
                            $template_data['doctor_id'] = $user_id;
                        } else {
                            $template_data['doctor_id'] = null;
                        }
                        break;
                        
                    case 'administrator':
                        // Admin can create system templates or assign to specific doctor/clinic
                        $template_data['is_system'] = !empty($request_data['is_system']) ? 1 : 0;
                        
                        if (!empty($request_data['doctor_id'])) {
                            $template_data['doctor_id'] = (int) $request_data['doctor_id'];
                        } else {
                            $template_data['doctor_id'] = null;
                        }
                        
                        if (!empty($request_data['clinic_id'])) {
                            $template_data['clinic_id'] = (int) $request_data['clinic_id'];
                        } else {
                            $template_data['clinic_id'] = null;
                        }
                        break;
                        
                    default:
                        // Other roles cannot create templates
                        wp_send_json([
                            'status' => false,
                            'message' => esc_html__('You do not have permission to create templates', 'kc-lang')
                        ]);
                        break;
                }
                
                // Insert new template
                $template_data['created_at'] = current_time('Y-m-d H:i:s');
                $template_data['is_system'] = $template_data['is_system'] ?? 0;
                
                $id = $this->template_manager->insert($template_data);
                
                if ($id) {
                    wp_send_json([
                        'status' => true,
                        'message' => esc_html__('Template saved successfully', 'kc-lang'),
                        'data' => [
                            'id' => $id
                        ]
                    ]);
                } else {
                    wp_send_json([
                        'status' => false,
                        'message' => esc_html__('Failed to save template', 'kc-lang')
                    ]);
                }
            }
            
        } catch (Exception $e) {
            error_log('Template update error: ' . $e->getMessage());

            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Update an existing template
     */
    public function update_template_delete($request) {
        try {
            $params = $request->get_params();
            
            // Check for nonce if provided
            if (isset($params['_ajax_nonce']) && !wp_verify_nonce($params['_ajax_nonce'], 'ajax_nonce')) {
                return new WP_REST_Response([
                    'status' => false,
                    'message' => esc_html__('Unauthorized access', 'kc-lang')
                ], 403);
            }
            
            // Use params directly or fall back to the old way
            $request_data = isset($params) && !empty($params) ? $params : $this->request->getInputs();
            $user_id = get_current_user_id();
            $user = new WP_User($user_id);
            
            // Validate required fields
            if (empty($request_data['id']) || empty($request_data['name']) || empty($request_data['category']) || empty($request_data['content'])) {
                throw new Exception(__('ID, name, category and content are required fields', 'kc-lang'));
            }
            
            // Get the existing template
            $template = $this->template_manager->get_template($request_data['id']);
            
            if (!$template) {
                throw new Exception(__('Template not found', 'kc-lang'));
            }
            
            // Check if user has permission to edit this template
            $has_permission = false;
            
            // Admin can edit any template
            if (in_array('administrator', $user->roles)) {
                $has_permission = true;
            }
            // Clinic admin can edit clinic templates
            else if (in_array('kiviCare_clinic_admin', $user->roles) && $template->clinic_id == kcGetClinicIdOfClinicAdmin()) {
                $has_permission = true;
            }
            // Doctor can edit their own templates
            else if ($template->doctor_id == $user_id) {
                $has_permission = true;
            }
            
            if (!$has_permission) {
                throw new Exception(__('You do not have permission to edit this template', 'kc-lang'));
            }
            
            // System templates cannot be edited
            if ($template->is_system) {
                throw new Exception(__('System templates cannot be edited. Please clone it to make your own version.', 'kc-lang'));
            }
            
            // Prepare updated data
            $template_data = [
                'name' => sanitize_text_field($request_data['name']),
                'category' => sanitize_text_field($request_data['category']),
                'content' => $request_data['content'], // Content may contain HTML
                'share_status' => sanitize_text_field($request_data['share_status'] ?? $template->share_status),
                'updated_at' => current_time('Y-m-d H:i:s')
            ];
            
            // Update the template
            $result = $this->template_manager->update($template_data, ['id' => $request_data['id']]);
            
            if ($result) {
                return new WP_REST_Response([
                    'status' => true,
                    'message' => __('Template updated successfully', 'kc-lang')
                ], 200);
            } else {
                throw new Exception(__('Failed to update template', 'kc-lang'));
            }
            
        } catch (Exception $e) {
            error_log('Template update error: ' . $e->getMessage());
            return new WP_REST_Response([
                'status' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a template (soft delete by setting status = 0)
     */
    public function delete_template() {
        // Check permission for template delete
        // if (!kcCheckPermission('template_delete') && is_user_logged_in()) {
        //     wp_send_json(kcUnauthorizeAccessResponse(403));
        // }
        
        try {
            // Get request data
            $request_data = $this->request->getInputs();
            $user_id = get_current_user_id();
            $current_login_user_role = $this->getLoginUserRole();
            
            // Validate required fields
            if (empty($request_data['id'])) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Template ID is required', 'kc-lang')
                ]);
            }
            
            $template_id = (int) $request_data['id'];
            
            // Get template from database
            $template = $this->template_manager->get_template($template_id);
            
            if (!$template) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Template not found', 'kc-lang')
                ]);
            }
            
            // System templates cannot be deleted
            if ($template->is_system) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('System templates cannot be deleted', 'kc-lang')
                ]);
            }
            
            // Check if user has permission to delete this template
            $can_delete = false;
            
            if ($current_login_user_role === 'administrator') {
                $can_delete = true;
            } else if ($current_login_user_role === $this->getClinicAdminRole() && 
                      $template->clinic_id == kcGetClinicIdOfClinicAdmin()) {
                $can_delete = true;
            } else if ($template->doctor_id == $user_id) {
                $can_delete = true;
            }
            
            if (!$can_delete) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('You do not have permission to delete this template', 'kc-lang')
                ]);
            }
            
            // Soft delete the template by setting status = 0
            $result = $this->db->update($this->db->prefix . "md_template_manager", ['status' => 0], ['id' => $template_id]);

            if ($result) {
                wp_send_json([
                    'status' => true,
                    'message' => esc_html__('Template deleted successfully', 'kc-lang')
                ]);
            } else {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Failed to delete template', 'kc-lang')
                ]);
            }
            
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get details of a single template
     */
    public function getTemplateDetails() {
        // Check permission for template details
        // if (!kcCheckPermission('template_view') && is_user_logged_in()) {
        //     wp_send_json(kcUnauthorizeAccessResponse(403));
        // }
        
        try {
            // Get request data
            $request_data = $this->request->getInputs();
            
            // Validate required fields
            if (empty($request_data['id'])) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Template ID is required', 'kc-lang')
                ]);
            }
            
            // Sanitize input
            $template_id = (int) $request_data['id'];
            
            // Get template from database
            $template = $this->template_manager->get_template($template_id);
            
            if (!$template) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Template not found', 'kc-lang')
                ]);
            }
            
            // Check if user has access to this template
            $user_id = get_current_user_id();
            $current_login_user_role = $this->getLoginUserRole();
            $has_access = false;
            
            // Access control based on user role
            switch ($current_login_user_role) {
                case 'administrator':
                    // Admin can access any template
                    $has_access = true;
                    break;
                    
                case $this->getClinicAdminRole():
                    // Clinic admin can access clinic templates
                    $clinic_id = kcGetClinicIdOfClinicAdmin();
                    if ($template->clinic_id == $clinic_id || $template->doctor_id == $user_id || $template->share_status == 'public') {
                        $has_access = true;
                    }
                    break;
                    
                case $this->getDoctorRole():
                    // Doctor can access their templates, their clinic's templates and public templates
                    if ($template->doctor_id == $user_id || $template->share_status == 'public' || $template->is_system) {
                        $has_access = true;
                    } else if ($template->share_status == 'clinic') {
                        // Check if doctor belongs to the clinic
                        $doctor_clinic_count = $this->db->get_var($this->db->prepare(
                            "SELECT COUNT(*) FROM {$this->db->prefix}kc_doctor_clinic_mappings 
                            WHERE doctor_id = %d AND clinic_id = %d AND status = 1",
                            $user_id, $template->clinic_id
                        ));
                        
                        if ($doctor_clinic_count > 0) {
                            $has_access = true;
                        }
                    }
                    break;
                    
                default:
                    // Other roles can access only public templates
                    if ($template->share_status == 'public') {
                        $has_access = true;
                    }
                    break;
            }
            
            if (!$has_access) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('You do not have access to this template', 'kc-lang')
                ]);
            }
            
            // Add additional information to the template
            
            // Get doctor name
            if (!empty($template->doctor_id)) {
                $doctor = get_userdata($template->doctor_id);
                $template->doctor_name = $doctor ? $doctor->display_name : esc_html__('Unknown', 'kc-lang');
            } else {
                $template->doctor_name = $template->is_system ? esc_html__('System', 'kc-lang') : '';
            }
            
            // Get clinic name
            if (!empty($template->clinic_id)) {
                $clinic = $this->db->get_row($this->db->prepare(
                    "SELECT name FROM {$this->db->prefix}kc_clinics WHERE id = %d",
                    $template->clinic_id
                ));
                $template->clinic_name = $clinic ? decodeSpecificSymbols($clinic->name) : esc_html__('Unknown', 'kc-lang');
            } else {
                $template->clinic_name = '';
            }
            
            // Get category and share status labels
            $category_labels = [
                'general' => esc_html__('General', 'kc-lang'),
                'referral' => esc_html__('Referral', 'kc-lang'),
                'sick_note' => esc_html__('Sick Note', 'kc-lang'),
                'consultation' => esc_html__('Consultation', 'kc-lang'),
                'procedure' => esc_html__('Procedure', 'kc-lang')
            ];
            
            $share_status_labels = [
                'private' => esc_html__('Private', 'kc-lang'),
                'clinic' => esc_html__('Clinic', 'kc-lang'),
                'public' => esc_html__('Public', 'kc-lang')
            ];
            
            $template->category_label = $category_labels[$template->category] ?? $template->category;
            $template->share_status_label = $share_status_labels[$template->share_status] ?? $template->share_status;
            
            // Check if user can edit this template
            $template->is_owner = ($template->doctor_id == $user_id);
            $template->is_editable = ($template->doctor_id == $user_id) || 
                                    ($current_login_user_role === 'administrator') || 
                                    ($current_login_user_role === $this->getClinicAdminRole() && 
                                    $template->clinic_id == kcGetClinicIdOfClinicAdmin());
            
            // Send response
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Template details retrieved successfully', 'kc-lang'),
                'data' => $template
            ]);
            
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Clone a template (create a copy for the current user)
     */
    public function clone_template() {
        // Check permission for template cloning
        // if (!kcCheckPermission('template_add') && is_user_logged_in()) {
        //     wp_send_json(kcUnauthorizeAccessResponse(403));
        // }
        
        try {
            // Get request data
            $request_data = $this->request->getInputs();
            $user_id = get_current_user_id();
            $current_login_user_role = $this->getLoginUserRole();
            
            // Validate required fields
            if (empty($request_data['id'])) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Template ID is required', 'kc-lang')
                ]);
            }
            
            $template_id = (int) $request_data['id'];
            
            // Get the source template
            $source = $this->template_manager->get_template($template_id);
            
            if (!$source) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Template not found', 'kc-lang')
                ]);
            }
            
            // Check if user can access this template
            $has_access = false;
            
            switch ($current_login_user_role) {
                case 'administrator':
                    // Admin can clone any template
                    $has_access = true;
                    break;
                    
                case $this->getClinicAdminRole():
                    // Clinic admin can clone clinic templates
                    $clinic_id = kcGetClinicIdOfClinicAdmin();
                    if ($source->clinic_id == $clinic_id || $source->doctor_id == $user_id || 
                        $source->share_status == 'public' || $source->is_system) {
                        $has_access = true;
                    }
                    break;
                    
                case $this->getDoctorRole():
                    // Doctor can clone their templates, their clinic's templates and public templates
                    if ($source->doctor_id == $user_id || $source->share_status == 'public' || $source->is_system) {
                        $has_access = true;
                    } else if ($source->share_status == 'clinic') {
                        // Check if doctor belongs to the clinic
                        $doctor_clinic_count = $this->db->get_var($this->db->prepare(
                            "SELECT COUNT(*) FROM {$this->db->prefix}kc_doctor_clinic_mappings 
                            WHERE doctor_id = %d AND clinic_id = %d AND status = 1",
                            $user_id, $source->clinic_id
                        ));
                        
                        if ($doctor_clinic_count > 0) {
                            $has_access = true;
                        }
                    }
                    break;
                    
                default:
                    // Other roles cannot clone templates
                    $has_access = false;
                    break;
            }
            
            if (!$has_access) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('You do not have access to clone this template', 'kc-lang')
                ]);
            }
            
            // Prepare new template data
            $clone_data = [
                'name' => sprintf(esc_html__('Copy of %s', 'kc-lang'), $source->name),
                'category' => $source->category,
                'content' => $source->content,
                'is_system' => 0,
                'share_status' => 'private', // Default to private for cloned templates
                'status' => 1,
                'created_at' => current_time('Y-m-d H:i:s')
            ];
            
            // Set doctor_id and clinic_id based on user role
            switch ($current_login_user_role) {
                case $this->getDoctorRole():
                    $clone_data['doctor_id'] = $user_id;
                    
                    // Use doctor's default clinic or source clinic if doctor belongs to it
                    if ($source->clinic_id) {
                        $doctor_in_source_clinic = $this->db->get_var($this->db->prepare(
                            "SELECT COUNT(*) FROM {$this->db->prefix}kc_doctor_clinic_mappings 
                            WHERE doctor_id = %d AND clinic_id = %d AND status = 1",
                            $user_id, $source->clinic_id
                        ));
                        
                        if ($doctor_in_source_clinic) {
                            $clone_data['clinic_id'] = $source->clinic_id;
                        } else {
                            $clone_data['clinic_id'] = kcGetDefaultClinicId();
                        }
                    } else {
                        $clone_data['clinic_id'] = kcGetDefaultClinicId();
                    }
                    break;
                    
                case $this->getClinicAdminRole():
                    $clone_data['clinic_id'] = kcGetClinicIdOfClinicAdmin();
                    
                    // If clinic admin is also a doctor, assign doctor_id
                    $is_also_doctor = in_array($this->getDoctorRole(), get_userdata($user_id)->roles);
                    if ($is_also_doctor) {
                        $clone_data['doctor_id'] = $user_id;
                    } else {
                        $clone_data['doctor_id'] = null;
                    }
                    break;
                    
                case 'administrator':
                    // Admin can assign to specific doctor/clinic or keep it as system-wide
                    $clone_data['doctor_id'] = !empty($request_data['doctor_id']) ? (int) $request_data['doctor_id'] : null;
                    $clone_data['clinic_id'] = !empty($request_data['clinic_id']) ? (int) $request_data['clinic_id'] : null;
                    break;
                    
                default:
                    $clone_data['doctor_id'] = null;
                    $clone_data['clinic_id'] = null;
                    break;
            }
            
            // Create the cloned template
            $id = $this->template_manager->insert($clone_data);
            
            if ($id) {
                wp_send_json([
                    'status' => true,
                    'message' => esc_html__('Template cloned successfully', 'kc-lang'),
                    'data' => [
                        'id' => $id
                    ]
                ]);
            } else {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Failed to clone template', 'kc-lang')
                ]);
            }
            
        } catch (Exception $e) {
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Import default templates from the templates.json file
     */
    public function import_default_templates($request) {
        try {
            $params = $request->get_params();
            
            // Check for nonce if provided
            if (isset($params['_ajax_nonce']) && !wp_verify_nonce($params['_ajax_nonce'], 'ajax_nonce')) {
                return new WP_REST_Response([
                    'status' => false,
                    'message' => esc_html__('Unauthorized access', 'kc-lang')
                ], 403);
            }
            // Check if templates already exist
            $templates_count = $this->db->get_var("SELECT COUNT(*) FROM {$this->db->prefix}md_template_manager WHERE is_system = 1");
            
            if ($templates_count > 0) {
                throw new Exception(__('Default templates have already been imported', 'kc-lang'));
            }
            
            // Get templates from JSON file
            $templates_file = KIVI_CARE_DIR . 'resources/js/lib/templates.json';
            
            if (!file_exists($templates_file)) {
                throw new Exception(__('Templates file not found', 'kc-lang'));
            }
            
            $templates_content = file_get_contents($templates_file);
            $templates = json_decode($templates_content, true);
            
            if (empty($templates)) {
                throw new Exception(__('No templates found in the templates file', 'kc-lang'));
            }
            
            // Import templates
            $this->template_manager->insert_system_templates($templates);
            
            // Return success
            return new WP_REST_Response([
                'status' => true,
                'message' => __('Default templates imported successfully', 'kc-lang')
            ], 200);
            
        } catch (Exception $e) {
            error_log('Import templates error: ' . $e->getMessage());
            return new WP_REST_Response([
                'status' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Register the templates page in the admin menu
     */
    public function register_menu() {
        // This will be handled in Vue router
    }

    /**
     * Initialize the template manager
     */
    public function init() {
        // Add hook to register the database table in KCActivate
        add_action('kcpro_init_db_tables', [$this, 'register_table']);
        
        // Add filter for encounter summarize
        add_filter('kivicare_encounter_summarize', [$this, 'process_user_template'], 10, 2);
        
        // Load the controller
        new self();
    }

    /**
     * Register the database table
     */
    public function register_table() {
        require_once KIVI_CARE_DIR . 'app/database/md-template-manager-db.php';
    }
    
    /**
     * Process user templates for encounter summarize
     * 
     * @param array $data The data to be processed
     * @param array $request_data The original request data
     * @return array The processed data
     */
    public function process_user_template($data, $request_data) {
        error_log('Process user template called with request data: ' . print_r($request_data, true));
        
        // Check if this is a user template
        if (!empty($request_data['isUserTemplate']) && !empty($request_data['templateContent'])) {
            error_log('Processing user template with content length: ' . strlen($request_data['templateContent']));
            
            // Get the template content
            $template_content = $request_data['templateContent'];
            
            // Replace variables in the template content with actual data
            $template_content = $this->replace_template_variables($template_content, $request_data);
            
            error_log('Template content after variable replacement: ' . substr($template_content, 0, 100) . '...');
            
            // Replace the template content in the data
            $data['template_content'] = $template_content;
            
            error_log('Template processing completed successfully');
        } else {
            error_log('Invalid template data: isUserTemplate=' . 
                     (isset($request_data['isUserTemplate']) ? var_export($request_data['isUserTemplate'], true) : 'undefined') . 
                     ', templateContent=' . 
                     (isset($request_data['templateContent']) ? (strlen($request_data['templateContent']) > 0 ? 'present' : 'empty') : 'undefined'));
        }
        
        return $data;
    }
    
    /**
     * Replace template variables with actual data
     * 
     * @param string $content The template content
     * @param array $request_data The request data with patient, doctor, clinic and encounter information
     * @return string The processed content with variables replaced
     */
    private function replace_template_variables($content, $request_data) {
        // Regular expression to find all ${variable.name} patterns
        $variable_regex = '/\${([^}]+)}/';
        
        // Find all variables in the content
        preg_match_all($variable_regex, $content, $matches);
        
        // If no variables found, return the content as is
        if (empty($matches[1])) {
            return $content;
        }
        
        // Get variables to replace
        $variables = $matches[1];
        
        // Create a mapping of variables to their values
        $replacements = [];
        
        // Loop through each variable and prepare replacement
        foreach ($variables as $variable) {
            $value = $this->get_variable_value($variable, $request_data);
            $replacements['${' . $variable . '}'] = $value;
        }
        
        // Replace all variables in the content
        return str_replace(array_keys($replacements), array_values($replacements), $content);
    }
    
    /**
     * Get the value of a variable based on its name and request data
     * 
     * @param string $variable The variable name in format "section.field" (e.g. "patient.name")
     * @param array $request_data The request data
     * @return string The variable value or empty string if not found
     */
    private function get_variable_value($variable, $request_data) {
        // Parse variable name (format: section.field)
        $parts = explode('.', $variable);
        
        if (count($parts) !== 2) {
            return '';
        }
        
        $section = $parts[0];
        $field = $parts[1];
        
        // Handle dynamic date/time variables
        if ($section === 'date' && $field === '') {
            return date('d/m/Y');
        } elseif ($section === 'time' && $field === '') {
            return date('H:i');
        }
        
        // Handle patient variables
        if ($section === 'patient') {
            $patient_data = !empty($request_data['patient_details']) ? $request_data['patient_details'] : [];
            
            switch ($field) {
                case 'name':
                    return $patient_data['patient_name'] ?? '';
                case 'unique_id':
                    return $patient_data['patient_unique_id'] ?? '';
                case 'dob':
                    return $patient_data['dob'] ?? '';
                case 'age':
                    // Calculate age if DOB is available
                    if (!empty($patient_data['dob'])) {
                        $dob = new \DateTime($patient_data['dob']);
                        $now = new \DateTime();
                        $interval = $now->diff($dob);
                        return $interval->y;
                    }
                    return '';
                case 'gender':
                    return $patient_data['gender'] ?? '';
                case 'email':
                    return $patient_data['patient_email'] ?? '';
                case 'mobile_number':
                    return $patient_data['mobile_number'] ?? '';
                case 'address':
                    return $patient_data['address'] ?? '';
                case 'city':
                    return $patient_data['city'] ?? '';
                case 'country':
                    return $patient_data['country'] ?? '';
                case 'postal_code':
                    return $patient_data['postal_code'] ?? '';
                default:
                    return '';
            }
        }
        
        // Handle doctor variables
        if ($section === 'doctor') {
            $doctor_id = !empty($request_data['doctor_id']) ? $request_data['doctor_id'] : 0;
            
            if ($doctor_id) {
                $doctor = get_userdata($doctor_id);
                
                switch ($field) {
                    case 'name':
                        return $doctor ? $doctor->display_name : '';
                    case 'speciality':
                        return get_user_meta($doctor_id, 'specialties', true) ?? '';
                    case 'qualification':
                        return get_user_meta($doctor_id, 'qualifications', true) ?? '';
                    default:
                        return '';
                }
            }
        }
        
        // Handle clinic variables
        if ($section === 'clinic') {
            $clinic_id = !empty($request_data['clinic_id']) ? $request_data['clinic_id'] : 0;
            
            if ($clinic_id) {
                global $wpdb;
                $clinic = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}kc_clinics WHERE id = {$clinic_id}");
                
                switch ($field) {
                    case 'name':
                        return $clinic ? $clinic->name : '';
                    case 'address':
                        return $clinic ? $clinic->address : '';
                    case 'contact':
                        return $clinic ? $clinic->telephone_no : '';
                    case 'email':
                        return $clinic ? $clinic->email : '';
                    default:
                        return '';
                }
            }
        }
        
        // Handle encounter variables
        if ($section === 'encounter') {
            switch ($field) {
                case 'id':
                    return $request_data['encounter_id'] ?? '';
                case 'concerns':
                    return isset($request_data['concerns']) ? $request_data['concerns'] : '';
                case 'history':
                    return isset($request_data['history']) ? $request_data['history'] : '';
                case 'examination':
                    return isset($request_data['examination']) ? $request_data['examination'] : '';
                case 'diagnosis':
                    return isset($request_data['diagnosis']) ? $request_data['diagnosis'] : '';
                case 'plan':
                    return isset($request_data['plan']) ? $request_data['plan'] : '';
                case 'medical_history':
                    return isset($request_data['medical_history']) ? $request_data['medical_history'] : '';
                case 'allergies':
                    return isset($request_data['allergies']) ? $request_data['allergies'] : '';
                case 'medications':
                    return isset($request_data['medications']) ? $request_data['medications'] : '';
                case 'date':
                    $encounter_id = $request_data['encounter_id'] ?? 0;
                    if ($encounter_id) {
                        global $wpdb;
                        $date = $wpdb->get_var("SELECT created_at FROM {$wpdb->prefix}kc_patient_encounters WHERE id = {$encounter_id}");
                        return $date ? date('d/m/Y', strtotime($date)) : '';
                    }
                    return '';
                default:
                    return '';
            }
        }
        
        // Handle appointment variables
        if ($section === 'appointment') {
            $appointment_id = !empty($request_data['appointment_id']) ? $request_data['appointment_id'] : 0;
            
            if ($appointment_id) {
                global $wpdb;
                $appointment = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}kc_appointments WHERE id = {$appointment_id}");
                
                switch ($field) {
                    case 'date':
                        return $appointment ? date('d/m/Y', strtotime($appointment->appointment_start_date)) : '';
                    case 'time':
                        return $appointment ? date('H:i', strtotime($appointment->appointment_start_time)) : '';
                    default:
                        return '';
                }
            }
        }
        
        return '';
    }
}