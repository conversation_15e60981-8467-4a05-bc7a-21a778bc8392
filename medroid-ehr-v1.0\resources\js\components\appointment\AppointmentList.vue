<template>
  <div>
    <div class="page-loader-section" v-if="isAppointmentLoading">
      <loader-component-2></loader-component-2>
    </div>
    <div v-if="isAppointmentLoading == false">
      <div v-if="appointmentList.length > 0" class="space-y-4">
        <div
          v-for="(appointment, index) in appointmentList"
          :key="index"
          class="w-full"
        >
          <div
            class="bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200"
          >
            <div class="p-4">
              <div
                class="flex flex-col md:flex-row md:items-center md:justify-between gap-4"
              >
                <!-- Patient Information -->
                <div class="flex-1 space-y-2">
                  <h3 class="text-lg font-semibold text-blue-600">
                    {{ appointment.patient_name }}
                    <span class="ml-2 text-sm font-normal text-gray-500">
                      ({{ appointment.appointment_start_time }} -
                      {{ appointment.appointment_end_time }})
                    </span>
                  </h3>

                  <div class="space-y-1">
                    <h4 class="text-base">
                      {{ formTranslation.common.doctor }}:
                      <span class="text-blue-600">{{
                        appointment.doctor_name
                      }}</span>
                    </h4>

                    <h4
                      v-if="getUserRole() === 'administrator'"
                      class="text-base"
                    >
                      {{ formTranslation.clinic.clinic }}:
                      <span class="text-blue-600">{{
                        appointment.clinic_name
                      }}</span>
                    </h4>

                    <p
                      v-if="enableDisableAppointmentDescriptionStatus === 'on'"
                      class="text-sm text-gray-600"
                    >
                      <span class="font-medium"
                        >{{ formTranslation.appointments.description }}:</span
                      >
                      {{ appointment.description || "not found" }}
                    </p>
                  </div>
                </div>

                <!-- Appointment Type -->
                <div class="md:w-1/4">
                  <h5 class="text-gray-600 font-medium">
                    {{ appointment.type_label }}
                  </h5>
                </div>

                <!-- Status Badge -->
                <div class="md:w-1/6 flex items-center">
                  <span
                    v-if="appointment.status === '1'"
                    class="px-3 py-1 text-sm font-medium rounded-full bg-blue-100 text-blue-800"
                  >
                    {{ formTranslation.appointments.booked }}
                  </span>
                  <span
                    v-if="appointment.status === '0'"
                    class="px-3 py-1 text-sm font-medium rounded-full bg-red-100 text-red-800"
                  >
                    {{ formTranslation.appointments.cancelled }}
                  </span>
                  <span
                    v-if="appointment.status === '2'"
                    class="px-3 py-1 text-sm font-medium rounded-full bg-green-100 text-green-800"
                  >
                    {{ formTranslation.appointments.pending }}
                  </span>
                  <span
                    v-if="appointment.status === '4'"
                    class="px-3 py-1 text-sm font-medium rounded-full bg-green-100 text-green-800"
                  >
                    {{ formTranslation.appointments.check_in }}
                  </span>
                  <span
                    v-if="appointment.status === '3'"
                    class="px-3 py-1 text-sm font-medium rounded-full bg-red-100 text-red-800"
                  >
                    {{ formTranslation.appointments.check_out }}
                  </span>
                </div>

                <!-- Action Buttons -->
                <div class="flex items-center justify-end">
                  <div class="inline-flex rounded-md shadow-sm" role="group">
                    <!-- Consultation Details Button -->
                    <router-link
                      v-if="appointment.encounter_id !== null"
                      :to="{
                        name: 'patient-encounter.dashboard',
                        params: { encounter_id: appointment.encounter_id },
                      }"
                      class="inline-flex items-center px-3 py-2 text-sm font-medium border border-gray-200 rounded-l-lg hover:bg-blue-50 hover:text-blue-700 focus:ring-2 focus:ring-blue-700 bg-white text-gray-700"
                    >
                      <i class="fa fa-tachometer-alt"></i>
                    </router-link>

                    <!-- Start Video Call Button (for doctor/admin) -->
                    <a
                      v-if="
                        (getUserRole() === 'doctor' ||
                          getUserRole() === 'clinic_admin' ||
                          getUserRole() === 'administrator') &&
                        appointment.video_consultation &&
                        appointment.status === '4' &&
                        currentDate === appointment.appointment_end_date
                      "
                      :href="
                        appointment.zoom_data !== null
                          ? appointment.zoom_data.start_url
                          : '#'
                      "
                      target="_blank"
                      class="inline-flex items-center px-3 py-2 text-sm font-medium border border-gray-200 hover:bg-blue-50 hover:text-blue-700 focus:ring-2 focus:ring-blue-700 bg-white text-gray-700"
                    >
                      <i class="fa fa-video"></i>
                    </a>

                    <!-- Join Video Call Button (for patient) -->
                    <a
                      v-if="
                        getUserRole() === 'patient' &&
                        appointment.video_consultation &&
                        appointment.status === '4' &&
                        currentDate === appointment.appointment_end_date
                      "
                      :href="
                        appointment.zoom_data !== null
                          ? appointment.zoom_data.join_url
                          : '#'
                      "
                      target="_blank"
                      class="inline-flex items-center px-3 py-2 text-sm font-medium border border-gray-200 hover:bg-blue-50 hover:text-blue-700 focus:ring-2 focus:ring-blue-700 bg-white text-gray-700"
                    >
                      <i class="fa fa-video"></i>
                    </a>

                    <!-- Check In Button -->
                    <button
                      v-if="
                        kcCheckPermission(
                          'patient_appointment_status_change'
                        ) &&
                        !['3', '4', '0', '2'].includes(appointment.status) &&
                        currentDate === appointment.appointment_end_date
                      "
                      :id="'status_update_' + appointment.id"
                      @click="handleAppointmentStatus(appointment, '4')"
                      class="inline-flex items-center px-3 py-2 text-sm font-medium border border-gray-200 hover:bg-blue-50 hover:text-blue-700 focus:ring-2 focus:ring-blue-700 bg-white text-gray-700"
                    >
                      <i class="fa fa-sign-in-alt mr-2"></i>
                      {{ formTranslation.appointments.check_in }}
                    </button>

                    <!-- Check Out Button -->
                    <button
                      v-if="
                        kcCheckPermission(
                          'patient_appointment_status_change'
                        ) &&
                        appointment.status === '4' &&
                        currentDate === appointment.appointment_end_date
                      "
                      :id="'status_update_' + appointment.id"
                      @click="handleAppointmentStatus(appointment, '3')"
                      class="inline-flex items-center px-3 py-2 text-sm font-medium border border-gray-200 hover:bg-red-50 hover:text-red-700 focus:ring-2 focus:ring-red-700 bg-white text-gray-700"
                    >
                      <i class="fa fa-sign-out-alt mr-2"></i>
                      {{ formTranslation.appointments.check_out }}
                    </button>

                    <!-- Booked Button -->
                    <button
                      v-if="
                        kcCheckPermission(
                          'patient_appointment_status_change'
                        ) &&
                        appointment.status === '2' &&
                        currentDate === appointment.appointment_end_date
                      "
                      :id="'status_update_' + appointment.id"
                      @click="handleAppointmentStatus(appointment, '1')"
                      class="inline-flex items-center px-3 py-2 text-sm font-medium border border-gray-200 hover:bg-blue-50 hover:text-blue-700 focus:ring-2 focus:ring-blue-700 bg-white text-gray-700"
                    >
                      <i class="fa fa-sign-in-alt mr-2"></i>
                      {{ formTranslation.appointments.booked }}
                    </button>

                    <!-- Delete Button -->
                    <button
                      v-if="kcCheckPermission('appointment_delete')"
                      :id="'appointment_delete_' + appointment.id"
                      @click="handleAppointmentDelete(appointment)"
                      class="inline-flex items-center px-3 py-2 text-sm font-medium border border-gray-200 rounded-r-lg hover:bg-red-50 hover:text-red-700 focus:ring-2 focus:ring-red-700 bg-white text-gray-700"
                    >
                      <i class="fa fa-trash"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-else>
        <div class="row">
          <div class="col-md-12">
            <div class="row mb-2">
              <div
                class="col-md-12 text-center d-flex justify-content-center align-items-center"
                style="height: 350px"
              >
                <h2>{{ formTranslation.common.no_appointments }}.</h2>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { post, get } from "../../config/request";
export default {
  props: {
    isLoading: {
      type: [Boolean],
    },
  },
  name: "AppointmentList",
  data: () => {
    return {
      appointmentRequest: {},
      appointmentData: [],
      isAppointmentLoading: true,
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init: function () {
      this.todayAppointmentList();
      this.appointmentRequest = this.defaultAppointmentRequest();
    },
    defaultAppointmentRequest: function () {
      return {
        date: new Date(),
      };
    },
    todayAppointmentList: function () {
      this.$store.dispatch("userDataModule/fetchUserForDropdown", {
        userRoleName: this.patientRoleName,
      });
      let filterData = Object.assign({}, this.appointmentRequest);
      filterData.date = moment(this.appointmentRequest.date).format(
        "YYYY-MM-DD"
      );
      this.$store
        .dispatch("appointmentModule/fetchAppointmentData", {
          filterData: filterData,
        })
        .then(() => {
          this.appointmentData =
            this.$store.state.appointmentModule.appointmentList;
        });
      setTimeout(() => {
        this.isAppointmentLoading = false;
      }, 500);
    },
    handleAppointmentEdit: function (appointment, collapseID) {
      this.editAppointment = true;
      this.$root.$emit("bv::toggle::collapse", collapseID);
      this.$store.commit("TOGGLE_APPOINTMENT_FORM", false);
      this.editId = appointment.id;
      setTimeout(() => {
        this.appointmentFormObj = Object.assign({}, appointment);
      }, 200);
    },
    handleAppointmentDelete: async function (appointment) {
      if (appointment.id !== undefined) {
        let but = $("#appointment_delete_" + appointment.id);
        but.prop("disabled", true);
        but.html(`<i class='fa fa-sync fa-spin'> </i>`);

        try {
          const result = await this.$swal.fire({
            title: this.formTranslation.clinic_schedule.dt_are_you_sure,
            text: this.formTranslation.common.press_yes_delete_billitems,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#dc3545",
            cancelButtonColor: "#6c757d",
            confirmButtonText: this.formTranslation.common.yes,
            cancelButtonText: this.formTranslation.common.cancel,
          });

          if (result.isConfirmed) {
            try {
              const data = await get("appointment_delete", {
                id: appointment.id,
              });

              but.prop("disabled", false);
              but.html(`<i class='fa fa-trash'> </i>`);

              if (data.data.status !== undefined && data.data.status === true) {
                displayMessage(data.data.message);
                this.reloadAppointment();
                this.$emit("refreshDashboard");
              }
            } catch (error) {
              but.prop("disabled", false);
              but.html(`<i class='fa fa-trash'> </i>`);
              console.log(error);
              displayErrorMessage(
                this.formTranslation.common.internal_server_error
              );
            }
          } else {
            // If canceled
            but.prop("disabled", false);
            but.html(`<i class='fa fa-trash'> </i>`);
          }
        } catch (error) {
          but.prop("disabled", false);
          but.html(`<i class='fa fa-trash'> </i>`);
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        }
      }
    },
    handleAppointmentStatus: async function (appointment, status) {
      // Check for encounter status
      if (status === "3") {
        if (
          appointment.encounter_id !== null &&
          appointment.encounter_detail !== undefined &&
          [1, "1"].includes(appointment.encounter_detail.status)
        ) {
          displayErrorMessage(this.formTranslation.common.encounter_not_close);
          return;
        }
      }

      // Handle button state and icon
      var element = $("#status_update_" + appointment.id).find("i");
      $("#status_update_" + appointment.id).prop("disabled", true);
      if (status === "4") {
        element.removeClass("fa fa-sign-in-alt");
      } else {
        element.removeClass("fa fa-sign-out-alt");
      }
      element.addClass("fa fa-spinner fa-spin");

      try {
        // Show SweetAlert confirmation dialog
        const result = await this.$swal.fire({
          title: this.formTranslation.clinic_schedule.dt_are_you_sure,
          text: this.formTranslation.common.update_appointment_status,
          icon: "warning",
          showCancelButton: true,
          confirmButtonColor: "#3085d6",
          cancelButtonColor: "#d33",
          confirmButtonText: this.formTranslation.common.yes,
          cancelButtonText: this.formTranslation.common.cancel,
        });

        // If confirmed
        if (result.isConfirmed) {
          const response = await get("appointment_update_status", {
            appointment_id: appointment.id,
            appointment_status: status,
          });

          $("#status_update_" + appointment.id).prop("disabled", false);
          element.removeClass("fa fa-spinner fa-spin");

          if (status === "4") {
            element.addClass("fa fa-sign-in-alt");
          } else {
            element.addClass("fa fa-sign-out-alt");
          }

          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.reloadAppointment();
            displayMessage(response.data.message);
          } else {
            displayErrorMessage(response.data.message);
          }
        } else {
          // If canceled
          $("#status_update_" + appointment.id).prop("disabled", false);
          element.removeClass("fa fa-spinner fa-spin");
          if (status === "4") {
            element.addClass("fa fa-sign-in-alt");
          } else {
            element.addClass("fa fa-sign-out-alt");
          }
        }
      } catch (error) {
        // Handle errors
        $("#status_update_" + appointment.id).prop("disabled", false);
        element.removeClass("fa fa-spinner fa-spin");
        if (status === "4") {
          element.addClass("fa fa-sign-in-alt");
        } else {
          element.addClass("fa fa-sign-out-alt");
        }
        console.log(error);
        displayErrorMessage(this.formTranslation.common.internal_server_error);
      }
    },
    reloadAppointment: function () {
      // let filterData = Object.assign({}, this.appointmentRequest);
      // filterData.date = moment(this.appointmentRequest.date).format('YYYY-MM-DD');
      setTimeout(() => {
        this.$store.dispatch("appointmentModule/fetchAppointmentData", {
          filterData: { date: moment(new Date()).format("YYYY-MM-DD") },
        });
      }, 300);
    },
    refreshAppointment: function () {
      this.isAppointmentLoading = true;
      // let filterData = Object.assign({},  { date: moment(new Date()).format('YYYY-MM-DD') });
      // filterData.date = moment(new Date()).format('YYYY-MM-DD');
      // console.log(filterData);
      get("get_appointment_queue", {
        filterData: { date: moment(new Date()).format("YYYY-MM-DD") },
      })
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.$store.commit(
              "appointmentModule/FETCH_APPOINTMENT_DATA",
              response.data.data
            );
            this.$emit("isReloadTrue", false);
          }
        })
        .catch((error) => {
          this.isAppointmentLoading = false;
          console.log(error);
        });
    },
  },
  computed: {
    appointmentList: function () {
      return this.$store.state.appointmentModule.appointmentList;
    },
    currentDate() {
      return moment().format("YYYY-MM-DD");
    },
    enableDisableAppointmentDescriptionStatus() {
      return this.$store.state.appointmentModule.description_status;
    },
  },
  watch: {
    isLoading: function (value) {
      if (value) {
        this.isAppointmentLoading = true;
        this.refreshAppointment();
      } else {
        this.isAppointmentLoading = false;
      }
    },
    appointmentList: function () {
      this.$emit("csvData", this.appointmentList);
    },
  },
};
</script>
