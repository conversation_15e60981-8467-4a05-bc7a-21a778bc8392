<!-- Dashboard.vue -->
<template>
  <div>
    <b-row id="encounterPage">
      <!-- Left Column: Patient and Clinic Details -->
      <b-col md="3" v-if="!isEncounterTemp">
        <b-row>
          <b-col sm="12">
            <b-card class="p-0 shadow" header-tag="header" footer-tag="footer">
              <!-- Card Header -->
              <template v-slot:header>
                <b-row>
                  <b-col sm="12" md="6" lg="12">
                    <h3 class="encounter-title">{{ formTranslation.patient_encounter.encounter_details }}</h3>
                  </b-col>
                </b-row>
              </template>

              <!-- Patient Details -->
              <div class="row patient-details">
                <div class="col-md-12">
                  <div class="mb-0">
                    <strong>{{ formTranslation.common.name }}: </strong> {{ encounterData.patient_name }}
                  </div>
                  <div v-if="encounterData.is_patient_unique_id_enable" class="mb-0">
                    <strong>{{ formTranslation.patient.unique_id }}: </strong> {{ encounterData.patient_unique_id }}
                  </div>
                  <div class="mb-0">
                    <strong class="font-weight-bold">{{ formTranslation.common.email }}: </strong>{{
                      encounterData.patient_email }}
                  </div>
                  <div class="mb-0">
                    <strong class="font-weight-bold">{{ formTranslation.patient_encounter.encounter_date }}: </strong>{{
                      encounterData.encounter_date }}
                  </div>
                  <div class="mb-0">
                    <strong class="font-weight-bold">{{ formTranslation.common.address }}: </strong>{{
                      encounterData.patient_address
                      || formTranslation.common.no_records_found }}
                  </div>
                </div>
              </div>

              <hr class="m-2 ml-0">

              <!-- Clinic Details -->
              <div class="row clinic-details">
                <div class="col-md-12">
                  <div class="mb-0">
                    <strong class="font-weight-bold">{{ formTranslation.clinic.clinic_name }}: </strong>{{
                      encounterData.clinic_name
                    }}
                  </div>
                  <div class="mb-0">
                    <strong class="font-weight-bold">{{ formTranslation.doctor.doctor_name }}: </strong>{{
                      encounterData.doctor_name
                    }}
                  </div>
                  <div class="mb-0">
                    <strong class="font-weight-bold">{{ formTranslation.appointments.description }}: </strong>{{
                      encounterData.description || formTranslation.common.no_records_found }}
                  </div>
                  <div class="mb-0 mt-1">
                    <p class="mb-0 float-left">
                      <span class="badge badge-success p-2" v-if="encounterData.status == 1">{{
                        formTranslation.common.active
                        }}</span>
                      <span class="badge badge-danger" v-if="encounterData.status == 0">{{ formTranslation.common.closed
                        }}</span>
                    </p>
                  </div>
                </div>
              </div>

              <!-- Custom Form Modal -->
              <CustomForm :data="encounterCustomFormData" :viewMode="encounterCustomFormViewMode"
                :customFormModal="encounterCustomFormModal" v-if="encounterCustomFormModal"
                @closeModal="encounterCustomFormModal = false" />

              <!-- Bill Generation Modal -->
              <ModalPopup v-if="billModel" modalId="bill-modal" modalSize="lg" :openModal="billModel"
                :modalTitle="formTranslation.patient_bill.generate_invoice" @closeModal="billModel = false">
                <BillForm :encounterId="encounterId" :checkOutVal="checkOutVal" @onBillSaved="handleBillSave"
                  @onBillCancel="handleBillCancel" :appointmentData="encounterData"
                  :clinic_extra="encounterData.clinic_extra !== undefined ? encounterData.clinic_extra : {}"
                  :doctorId="encounterData.doctor_id" />
              </ModalPopup>

              <!-- Bill Details Modal -->
              <ModalPopup v-if="billDetailsModel" modalId="bill-details-modal" modalSize="lg"
                :openModal="billDetailsModel" :modalTitle="formTranslation.patient_bill.invoice_detail"
                @closeModal="billDetailsModel = false">
                <BillDetails :encounterId="encounterId"
                  :clinic_extra="encounterData.clinic_extra !== undefined ? encounterData.clinic_extra : {}"
                  @onBillCancel="billDetailsModel = false" />
              </ModalPopup>
            </b-card>
          </b-col>
        </b-row>

        <!-- Encounter Template Selection -->
        <b-row>
          <b-col sm="12"
            v-if="getUserRole() !== 'patient'"
            id="patient-details-encounter-template">
            <b-card class="p-0 shadow" header-tag="header" footer-tag="footer">
              <b-overlay :show="Boolean(userData.addOns.kiviPro != true && !isEncounterTemp)" variant="white"
                :opacity="0.4">
                <template #overlay>
                  <overlay-message addon_type="pro" />
                </template>

                <template v-slot:header>
                  <b-row>
                    <b-col sm="12" md="6" lg="12">
                      <h3 class="encounter-title">{{ formTranslation.patient_encounter.encounter_template }}</h3>
                    </b-col>
                  </b-row>
                </template>

                <div class="row patient-details">
                  <div class="col-md-12">
                    <TranscriptSummarizer ref="summaryCard" @generateSummary="generateSummary" />

                  </div>
                </div>
              </b-overlay>
            </b-card>
          </b-col>
        </b-row>

        <!-- summry -->

        <!-- Documents Section -->
        <b-row v-if="getUserRole() !== 'patient'">
          <b-col sm="12" v-if="!isEncounterTemp ">
            <b-card class="p-0 shadow" header-tag="header" footer-tag="footer">
              <!-- Card Header -->
              <template v-slot:header>
                <b-row>
                  <b-col sm="12" md="6" lg="12">
                    <h3 class="encounter-title">Documents</h3>
                  </b-col>
                </b-row>
              </template>

              <!-- Documents List -->
              <div class="row patient-documents">
                <div class="col-md-12">
                  <Documents v-if="encounterData?.patient_email" :encounter-id="encounterId" :can-upload="getUserRole() !== 'patient'" 
                  ref="refDocuments"
                  :defaultPatientEmail='encounterData?.patient_email'
                    :can-delete="getUserRole() !== 'patient'" />
                </div>
              </div>
            </b-card>
          </b-col>
        </b-row>


      </b-col>

      <!-- Main Content Column -->
      <b-col :md="!isEncounterTemp ? 9 : 12">
        <b-card class="p-0 shadow" header-tag="header" footer-tag="footer" header-class="sticky-header">
          <!-- Card Header -->
          <template v-slot:header>
            <b-row>
              <b-col sm="12" md="3" lg="3">
                <h3 class="mb-0">
                  {{ kcCheckPermission('medical_records_list') && !hideClinicalDetailsToPatient ?
                    formTranslation.encounter_dashboard.title : '' }}
                </h3>
              </b-col>
              <b-col sm="12" md="9" lg="9">
                <div class="d-md-flex d-lg-flex d-xl-flex justify-content-end">
                  <button type="button" class="btn btn-sm btn-primary mb-1" @click="$router.go(-1);">
                    <i class="fa fa-angle-double-left"></i> {{ formTranslation.common.back }}
                  </button>
                  <button type="button" class="btn btn-sm btn-primary mb-1"
                    v-for="(custom_form_data, key) in encounterData.custom_forms" :key="key"
                    v-if="userData.addOns.kiviPro == true && customFormCondition(encounterData, custom_form_data)"
                    @click="customFormOpen(encounterData, custom_form_data)">
                    <i
                      :class="custom_form_data.name && custom_form_data.name.icon ? custom_form_data.name.icon : 'fas fa-book-medical'"></i>
                    {{ custom_form_data.name && custom_form_data.name.text ? custom_form_data.name.text : '' }}
                  </button>
                  <button v-if="userData.addOns.kiviPro && !isEncounterTemp" type="button" id="kc-encounter-print"
                    class="btn btn-sm btn-primary mb-1" :disabled="isButtonDisabled" @click="printEncounter">
                    <i :class="iconClass"></i>
                    {{ formTranslation.patient_bill.print + ' ' + formTranslation.patient_encounter.encounters }}
                  </button>
                  <router-link :to="{ name: 'patient-encounter.body-chart', params: { encounter_id: encounterId } }"
                    class="btn btn-sm btn-primary mb-1"
                    v-if="userData.addOns.bodyChart == true && kcCheckPermission('body_chart_list')">
                    <i class="fas fa-x-ray"></i>
                    {{ formTranslation.common.body_chart }}
                  </router-link>
                  <router-link :to="{ name: 'patient-medical-report', params: { encounter_id: encounterId } }"
                    class="btn btn-sm btn-primary mb-1"
                    v-if="userData.addOns.kiviPro == true && kcCheckPermission('patient_report') && !isEncounterTemp">
                    <i :class="encounterData.status != 0 ? 'fa fa-upload' : 'fa fa-eye'"></i>
                    {{ encounterData.status != 0 ? formTranslation.patient.upload_report : formTranslation.common.view +
                      ' ' + formTranslation.reports.reports }}
                  </router-link>
                
                  <button class="btn btn-sm btn-outline-danger mb-1 mr-0"
                    v-if="kcCheckPermission('patient_bill_add') && encounterData.status != 0 && !isEncounterTemp"
                    @click="handleEncounterStatus('0')">
                    <i class="fa fa-check"></i> {{ formTranslation.patient_bill.encounter_close }}
                  </button>

                  <button class="btn btn-sm btn-outline-danger ml-md-2 ml-ld-2 ml-xl-2 mb-1"
                    v-if="kcCheckPermission('patient_bill_add') && kcCheckPermission('patient_appointment_status_change') && encounterData.status != 0 && encounterData.appointment_id !== undefined && encounterData.appointment_id !== null"
                    @click="handleEncounterStatus('1')">
                    <i class="fa fa-check"></i> {{ formTranslation.patient_bill.encounter_close_checkout }}
                  </button>
                  <button class="btn btn-sm btn-primary mb-1"
                    v-if="encounterData.status == 0 && checkEnableModule('billing')" @click="handleBillDetails">
                    <i class="fa fa-file-invoice"></i> {{ formTranslation.patient_bill.bill_details }}
                  </button>
                </div>
              </b-col>
            </b-row>
          </template>

          <!-- Clinical Details -->
          <div id="print_clinical_details"
            v-if="kcCheckPermission('medical_records_list') && !hideClinicalDetailsToPatient">
            <b-row>
              <b-col :md="getEnableCount"
                v-if="userData.addOns.kiviPro == true ? getEnableEncounter[0]['status'] == 1 : true">
                <MedicalHistory :updateCounter="counter" ref="medical_history_problems" :recordData="problems"
                  :ecounterStatus="encounterData.encounter_edit_after_close_status === true ? true : isEncounterTemp ? kcCheckPermission('encounters_template_edit') : encounterData.status"
                  :isEcounterTemplateModule="isEncounterTemp" />
              </b-col>
              <b-col :md="getEnableCount"
                v-if="userData.addOns.kiviPro == true ? getEnableEncounter[1]['status'] == 1 : true">
                <MedicalHistory :updateCounter="counter" ref="medical_history_observation" :recordData="observation"
                  :ecounterStatus="encounterData.encounter_edit_after_close_status === true ? true : isEncounterTemp ? kcCheckPermission('encounters_template_edit') : encounterData.status"
                  :isEcounterTemplateModule="isEncounterTemp" />
              </b-col>
              <b-col :md="getEnableCount"
                v-if="userData.addOns.kiviPro == true ? getEnableEncounter[2]['status'] == 1 : true">
                <MedicalHistory :updateCounter="counter" ref="medical_history_note" :recordData="note"
                  :ecounterStatus="encounterData.encounter_edit_after_close_status === true ? true : isEncounterTemp ? kcCheckPermission('encounters_template_edit') : encounterData.status"
                  :isEcounterTemplateModule="isEncounterTemp" />
              </b-col>
            </b-row>
            <b-col sm="12" lg="12" md="12" v-if="getUserRole() !== 'patient'">
              <AdditionalTabs :encounter-id="encounterId" @section-added="handleTabAdded"
                @tab-removed="handleTabRemoved" />
            </b-col>
            <div v-if="tabs.length > 0">
              <b-row>
                <b-col sm="12" md="4" lg="4" v-for="tab in tabs" :key="tab">
                  <b-card class="mb-3" no-body>
                    <b-card-header class="d-flex justify-content-between align-items-center">
                      <h5 class="mb-0">{{ tab.title }}</h5>
                      <b-button v-if="getUserRole() !== 'patient'" variant="danger" size="sm" @click="removeTab(tab.id)">
                        <i class="fas fa-times"></i>
                      </b-button>
                    </b-card-header>

                    <b-card-body>
                      <b-form @submit.prevent="saveTab(tab)">
                        <b-form-group :state="!tab.showError" :invalid-feedback="tab.errorMessage">
                          <b-form-textarea :readonly="getUserRole() == 'patient'" v-model="tab.content" :placeholder="`Enter ${tab?.title?.toLowerCase()}...`"
                            rows="3" :state="!tab.showError" @input="handleInput(tab)"></b-form-textarea>
                        </b-form-group>

                        <template v-if="tab.type === 'allergies'">
                          <b-row>
                            <b-col md="6">
                              <b-form-group label="Allergy Type">
                                <b-form-select  :disabled="getUserRole() == 'patient'"  v-model="tab.allergyType" :options="allergyTypes"
                                  @input="handleInput(tab)"></b-form-select>
                              </b-form-group>
                            </b-col>
                            <b-col md="6">
                              <b-form-group label="Severity">
                                <b-form-select  :disabled="getUserRole() == 'patient'"  v-model="tab.severity" :options="severityLevels"
                                  @input="handleInput(tab)"></b-form-select>
                              </b-form-group>
                            </b-col>
                          </b-row>
                        </template>

                        <template v-if="tab.type === 'examination'">
                          <b-form-group label="Examination Type">
                            <b-form-input  :readonly="getUserRole() == 'patient'"  v-model="tab.examType" placeholder="Enter examination type"
                              @input="handleInput(tab)"></b-form-input>
                          </b-form-group>
                        </template>
                      </b-form>
                    </b-card-body>
                  </b-card>
                </b-col>
              </b-row>
            </div>
            <b-row v-if="extraClinicalData.length > 0">
              <b-col :md="4" v-for="(data, index) in extraClinicalData" :key="index">
                <MedicalHistory :updateCounter="counter" :ref="data.ref" :recordData="data"
                  :ecounterStatus="encounterData.encounter_edit_after_close_status === true ? true : isEncounterTemp ? kcCheckPermission('encounters_template_edit') : encounterData.status" />
              </b-col>
            </b-row>
          </div>

          <!-- Main Content: Prescription and Custom Fields -->
          <b-row>
            <b-col md="12">
              <Prescription v-if="showCustomField" ref="prescription_ref" :updateCounter="counter" :encounterData="{
                encounter_id: encounterId,
                status: encounterData.encounter_edit_after_close_status === true ? '1' : String(Number(isEncounterTemp ? kcCheckPermission('encounters_template_edit') : encounterData.status)),
              }" :encounterId="encounterId" :isEcounterTemplateModule="isEncounterTemp" />
            </b-col>
            <b-col md="12" v-if="customFieldsLength > 0">
              <form id="encounterDataForm" @submit.prevent="handleSubmit" :novalidate="true">
                <div class="card shadow p-0">
                  <div class="card-header">
                    <h4 class="mb-0">{{ formTranslation.patient_bill.other_info }}</h4>
                  </div>
                  <div class="card-body">
                    <edit-custom-fields v-if="showCustomField" :key="componentKey"
                      module_type="patient_encounter_module" @bindCustomField="getCustomFieldsValues"
                      :module_id="customFieldEncounterId" :customFieldsObj="customFieldsObj"
                      :fieldsValue="customFieldsData" @requiredCustomField="getRequireFields"
                      :doctor_id="encounterData.doctor_id"
                      :disabledForPatient="getUserRole() === 'patient' ? true : false"
                      :ecounterStatus="encounterData.status"
                      :encounter_edit_after_close_status="encounterData.encounter_edit_after_close_status" />
                  </div>
                  <div class="card-footer" v-if="encounterData.status === '1' && getUserRole() !== 'patient'">
                    <div class="row">
                      <div class="col-md-12">
                        <div class="float-right">
                          <button v-if="!loading" class="btn btn-primary" type="submit">
                            <i class="fa fa-save"></i> {{ formTranslation.encounter_dashboard.presciption_save_btn }}
                          </button>
                          <button v-else class="btn btn-primary" type="submit" disabled>
                            <i class="fa fa-sync fa-spin"></i> &nbsp; {{ formTranslation.common.loading }}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </form>
            </b-col>
          </b-row>

          <!-- Print Signature (Hidden) -->
          <div class="print-class mr-3 d-none">
            <hr>
            <div class="row">
              <div class="col-12 d-flex justify-content-end align-items-center">
                <p class="nameheading">
                  <strong>{{ formTranslation.patient_encounter.doctor_signature }}</strong>
                </p>
                <div class="border-bottom col-4" v-if="encounterData.doctor_signature !== ''">
                  <img :src="encounterData.doctor_signature" alt="Doctor Signature">
                </div>
                <div v-else class="border-bottom col-4"></div>
              </div>
            </div>
          </div>
        </b-card>
      </b-col>
    </b-row>
    <SummaryModal
      :show="showSummaryModal"
      :summary="summaryContent"
      :template="summaryTemplate"
      @close="showSummaryModal = false"
      @next="handleNext"
      @download="handleDownloadPDF"
      ref="refSummaryModal"
    />
  </div>
</template>


<script>
import { post, get } from "../../config/request";
import { displayMessage, displayErrorMessage } from "../../utils/message"; // Adjust the path
import MedicalHistory from "../../components/PatientEncounter/MedicalHistory";
import Prescription from "../../components/PatientEncounter/Prescription";
import { validateForm } from "../../config/helper";
import BillForm from "../../components/PatientBill/BillForm";
import BillDetails from "../../components/PatientBill/BillDetails";
import ModalPopup from "../../components/Modal/Index";
import CustomForm from "../CustomForm/Form.vue";
import AdditionalTabs from "../../components/PatientEncounter/AdditionalTabs.vue";
import Documents from "../../components/PatientEncounter/Documents.vue";
import TranscriptSummarizer from "../../components/PatientEncounter/TranscriptSummarizer.vue"
import SummaryModal from "../../components/PatientEncounter/SummaryModal.vue"
import {generatePDF} from  "../../utils/pdfGenerator"

export default {
  name: 'Dashboard',
  components: {
    MedicalHistory,
    Prescription,
    BillForm,
    BillDetails,
    ModalPopup,
    CustomForm,
    AdditionalTabs,
    Documents,
    TranscriptSummarizer,
    SummaryModal,
  },
  data() {
    return {
      encounterId: 0,
      patient_id: 0,
      encounterData: {},
      patientBillData: {},
      createBillButton: true,
      billModel: false,
      billDetailsModel: false,
      loading: false,
      submitted: false,
      buttonText: '<i class="fa fa-save"></i> Save',
      customFieldsLength: 0,
      customFieldsObj: {},
      cardTitle: 'Clinical Detail',
      componentKey: 0,
      showCustomField: false,
      problems: {},
      observation: {},
      note: {},
      hideClinicalDetailsToPatient: false,
      checkOutVal: 0,
      extraClinicalData: [],
      isEncounterTemp: false,
      encounterTemplate: '',
      encounterTemplates: [],
      encounterTemplateLoader: false,
      counter: 0,
      encounterCustomFormData: {},
      encounterCustomFormModal: false,
      encounterCustomFormViewMode: false,
      isButtonDisabled: false,
      isLoading: false,
      iconClass: 'fa fa-print',
      extraClinicalSections: [],
      requiredFields: [],
      tabs: [],
      saveTimers: {},
      allergyTypes: [
        { value: 'food', text: 'Food' },
        { value: 'medicine', text: 'Medicine' },
        { value: 'environmental', text: 'Environmental' }
      ],
      severityLevels: [
        { value: 'mild', text: 'Mild' },
        { value: 'moderate', text: 'Moderate' },
        { value: 'severe', text: 'Severe' }
      ],
      showSummaryModal: false,
    summaryContent: '',
    summaryTemplate: ''
    };
  },
  mounted() {
    this.encounterData = this.defaultEncounterDetails();
    this.init();
    this.hideClinicalDetailsToPatient = this.getUserRole === 'patient' ? true : false;
    this.loadTabs()
    // const tabsResponse = await get('get_encounter_tab')
  },
  methods: {
    /**
 * Loads all tabs for the current encounter
 * Should be called when component is mounted
 */
    async loadTabs() {
      try {
        const response = await get('get_encounter_tabs', {
          encounter_id: this.encounterId
        });

        if (response?.data?.status) {
          this.tabs = response.data.data.map(tab => ({
            id: tab.id,
            type: tab.type,
            content: tab.content,
            title: this.getTabTitle(tab.type),
            showError: false,
            errorMessage: '',
            ...tab.metadata
          }));
        }
      } catch (error) {
        console.error('Error loading tabs:', error);
        displayErrorMessage('Failed to load tabs');
      }
    },

    /**
     * Initializes the component by fetching encounter details and related data.
     */
    init() {
      if (this.$route?.query?.isEncounterTemp !== undefined) {
        this.isEncounterTemp = this.$route?.query.isEncounterTemp == 1;
      }
      this.encounterId = this.$route.params.encounter_id;
      this.problems = {
        encounter_id: this.encounterId,
        type: "problem",
        title: this.formTranslation.encounter_dashboard.problems,
        status: this.encounterData.status,
        data: [],
      };

      this.observation = {
        encounter_id: this.encounterId,
        type: "observation",
        title: this.formTranslation.encounter_dashboard.observation,
        status: this.encounterData.status,
        data: [],
      };

      this.note = {
        encounter_id: this.encounterId,
        type: "note",
        title: this.formTranslation.encounter_dashboard.notes,
        status: this.encounterData.status,
        data: [],
      };

      if (this.$route.params.encounter_id !== undefined) {
        this.encounterId = this.$route.params.encounter_id;
        get(this.isEncounterTemp ? "patient_encounter_template_details" : "patient_encounter_details", {
          id: this.encounterId,
        })
          .then((data) => {
            if (data.data.status !== undefined && data.data.status === true) {
              this.showCustomField = true;
              this.encounterData = data.data.data;
              if (this.encounterData.clinic_extra !== undefined) {
                this.encounterData.clinic_extra = JSON.parse(this.encounterData.clinic_extra);
              }
              this.patient_id = this.encounterData.patient_id;
              this.getEncounterCustomField();
              this.getEncounterBill();
              this.hideClinicalDetailsToPatient =
                (data.data.hideInPatient == "true" || data.data.hideInPatient == true) &&
                  this.getUserRole === "patient"
                  ? true
                  : false;
              if (!this.hideClinicalDetailsToPatient) {
                this.getMedicalRecords(this.encounterId);
              }
            } else if (data?.data?.status === false) {
              this.$router.push({ name: "/" });
            }
            // console.log(data.data.patientDetails.patient_email);
            // this.$refs.refDocuments.$props.defaultPatientEmail ={ user_email: data.data.patientDetails.patient_email, user_email: data.data.patientDetails.patient_email}
          })
          .catch((error) => {
            console.log(error);
            displayErrorMessage(this.formTranslation.common.internal_server_error);
          });
      }

      if (this.userData.addOns.kiviPro) {
        this.getEncounterTemplates();
      }
    },

    /**
     * Fetches extra clinical detail fields from the backend.
     * @param {Number|String} encounterId - The ID of the encounter.
     * @param {Number|String} encounter_status - The status of the encounter.
     */
    getExtraClinicalDetail(encounterId, encounter_status) {
      get("encounter_extra_clinical_detail_fields", {
        id: encounterId,
        status: encounter_status,
      })
        .then((data) => {
          if (data.data.status !== undefined && data.data.status === true) {
            this.extraClinicalData = data.data.data;
          }
          if (!this.hideClinicalDetailsToPatient) {
            this.getMedicalRecords(this.encounterId);
          }
        })
        .catch((error) => {
          if (!this.hideClinicalDetailsToPatient) {
            this.getMedicalRecords(this.encounterId);
          }
          console.log(error);
          displayErrorMessage(this.formTranslation.common.internal_server_error);
        });
    },

    /**
     * Forces the component to re-render by updating the component key.
     */
    forceRerender() {
      this.componentKey += 1;
    },

    /**
     * Returns the default structure for encounter details.
     * @returns {Object} - The default encounter details.
     */
    defaultEncounterDetails() {
      return {
        patient_id: 0,
        patient_name: "",
        clinic_name: "",
        doctor_name: "",
        custom_fields: {},
      };
    },

    /**
     * Fetches custom fields related to the encounter from the backend.
     */
    getEncounterCustomField() {
      get("get_custom_fields", {
        module_type: "patient_encounter_module",
        module_id: this.encounterId,
        doctor_id: this.encounterData.doctor_id,
      })
        .then((response) => {
          if (response.data.status !== undefined && response.data.status === true) {
            this.customFieldsObj = response.data.data;
            this.customFieldsLength = response.data.data.length;
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(this.formTranslation.common.internal_server_error);
        });
    },

    /**
     * Handles the binding of custom fields from child components.
     * @param {Object} fieldsObj - The custom fields object.
     */
    getCustomFieldsValues(fieldsObj) {
      if (!fieldsObj || fieldsObj === undefined) {
        return false;
      }
      this.encounterData.custom_fields = fieldsObj;
    },

    /**
     * Handles the requirement of custom fields.
     * @param {Array} validateRequired - The array of required fields.
     */
    getRequireFields(validateRequired) {
      this.requiredFields = validateRequired;
    },

    /**
     * Fetches billing details associated with the encounter.
     */
    getEncounterBill() {
      if (parseInt(this.encounterId) !== 0 && !this.isEncounterTemp) {
        get("patient_bill_detail", {
          encounter_id: this.encounterId,
        })
          .then((response) => {
            if (response.data.status !== undefined && response.data.status === true) {
              if (response.data.data.length === 0) {
                this.createBillButton = true;
              } else {
                this.createBillButton = false;
                this.patientBillData = response.data.data;
              }
            }
          })
          .catch((error) => {
            console.log(error);
            displayErrorMessage(this.formTranslation.widgets.record_not_found);
          });
      }
    },

    /**
     * Handles the submission of custom fields form.
     */
    handleSubmit() {
      if (this.getUserRole === "patient") {
        return;
      }
      this.loading = true;
      this.submitted = true;
      if (this.requiredFields.length > 0) {
        this.loading = false;
        displayErrorMessage(this.formTranslation.common.all_required_field_validation);
        return;
      }
      if (validateForm("encounterDataForm")) {
        post("save_custom_patient_encounter_field", this.encounterData)
          .then((response) => {
            this.loading = false;
            if (response.data.status !== undefined && response.data.status === true) {
              displayMessage(response.data.message);
            } else {
              displayErrorMessage(response.data.message);
            }
          })
          .catch((error) => {
            console.log(error);
            this.loading = false;
            displayErrorMessage(this.formTranslation.common.internal_server_error);
          });
      }
    },

    /**
     * Handles the status update of the encounter.
     * @param {Number|String} status - The new status to set.
     */
    handleEncounterStatus(status) {
      this.checkOutVal = status;
      if (this.isBillModuleActive) {
        this.handleGenerateBill();
        return;
      } else {
        this.handleEncounterstatusUpdate({ id: this.encounterId, status: status });
      }
    },

    /**
     * Updates the status of the encounter in the backend.
     * @param {Object} requestData - The data containing encounter ID and new status.
     */
    handleEncounterstatusUpdate(requestData) {
      post("patient_encounter_update_status", {
        id: requestData.id,
        status: requestData.status,
        checkOutVal: this.checkOutVal,
      })
        .then((response) => {
          if (response.data.status !== undefined && response.data.status === true) {
            this.encounterData.status = requestData.status;
            displayMessage(response.data.message);
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(this.formTranslation.common.internal_server_error);
        });
    },

    /**
     * Opens the bill generation modal.
     */
    handleGenerateBill() {
      this.billModel = true;
    },

    /**
     * Opens the bill details modal.
     */
    handleBillDetails() {
      this.billDetailsModel = true;
    },

    /**
     * Handles the successful saving of a bill.
     * @param {Object} data - The data returned after saving the bill.
     */
    handleBillSave(data) {
      this.billModel = false;
      this.createBillButton = false;
      // Update encounter status based on payment
      this.encounterData.status = data.payment_status && data.payment_status === "paid" ? 0 : 1;
      this.getEncounterBill();
    },

    /**
     * Handles the cancellation of bill-related modals.
     * @param {String} type - The type of cancellation.
     */
    handleBillCancel(type) {
      this.billModel = false;
    },

    /**
     * Returns the default structure for a bill record.
     * @returns {Object} - The default bill record.
     */
    defaultBillRecordData() {
      return {
        title: "",
        encounter_id: 0,
        total_amount: 0,
        discount: 0,
        actual_amount: "",
        status: 0,
        billItems: [],
      };
    },

    /**
     * Returns the default structure for a billing item.
     * @returns {Object} - The default billing item.
     */
    defaultBillingItemData() {
      return {
        item_id: {},
        qty: 1,
        price: 0,
        total: this.billItem.price * this.billItem.qty,
      };
    },

    /**
     * Fetches medical records related to the encounter.
     * @param {Number|String} encounter_id - The ID of the encounter.
     */
    getMedicalRecords(encounter_id) {
      get(this.isEncounterTemp ? "medical_history_list_from_template" : "medical_history_list", {
        encounter_id: encounter_id,
      })
        .then((response) => {
          try {
            if (this.$refs.medical_history_note !== undefined) {
              if (this.userData.addOns.kiviPro !== true) {
                this.$refs.medical_history_note.medicalHistoryListLoader = false;
              } else if (this.getEnableEncounter[2]["status"] == 1) {
                this.$refs.medical_history_note.medicalHistoryListLoader = false;
              }
            }
            if (this.$refs.medical_history_observation !== undefined) {
              if (this.userData.addOns.kiviPro !== true) {
                this.$refs.medical_history_observation.medicalHistoryListLoader = false;
              } else if (this.getEnableEncounter[1]["status"] == 1) {
                this.$refs.medical_history_observation.medicalHistoryListLoader = false;
              }
            }
            if (this.$refs.medical_history_problems !== undefined) {
              if (this.userData.addOns.kiviPro !== true) {
                this.$refs.medical_history_problems.medicalHistoryListLoader = false;
              } else if (this.getEnableEncounter[0]["status"] == 1) {
                this.$refs.medical_history_problems.medicalHistoryListLoader = false;
              }
            }

            this.extraClinicalData.forEach((section) => {
              const ref = this.$refs[section.ref];
              if (ref && ref.length > 0) {
                ref[0].medicalHistoryListLoader = false;
              }
            });
          } catch (error) {
            console.log(error);
          }
          if (response.data.status !== undefined && response.data.status === true) {
            if (response.data.data.problem !== undefined && response.data.data.problem.length > 0) {
              if (this.userData.addOns.kiviPro !== true) {
                this.$refs.medical_history_problems.medicalHistoryList = response.data.data.problem;
              } else if (this.getEnableEncounter[0]["status"] == 1) {
                this.$refs.medical_history_problems.medicalHistoryList = response.data.data.problem;
              }
            }
            if (response.data.data.observation !== undefined && response.data.data.observation.length > 0) {
              if (this.userData.addOns.kiviPro !== true) {
                this.$refs.medical_history_observation.medicalHistoryList = response.data.data.observation;
              } else if (this.getEnableEncounter[1]["status"] == 1) {
                this.$refs.medical_history_observation.medicalHistoryList = response.data.data.observation;
              }
            }
            if (response.data.data.note !== undefined && response.data.data.note.length > 0) {
              if (this.userData.addOns.kiviPro !== true) {
                this.$refs.medical_history_note.medicalHistoryList = response.data.data.note;
              } else if (this.getEnableEncounter[2]["status"] == 1) {
                this.$refs.medical_history_note.medicalHistoryList = response.data.data.note;
              }
            }
            this.extraClinicalData.forEach((section) => {
              if (response.data.data[section.type] !== undefined && response.data.data[section.type].length > 0) {
                this.$refs[section.ref][0].medicalHistoryList = response.data.data[section.type];
              }
            });
          }
        })
        .catch((error) => {
          try {
            if (this.$refs.medical_history_note) {
              this.$refs.medical_history_note.medicalHistoryListLoader = false;
            }
            if (this.$refs.medical_history_observation) {
              this.$refs.medical_history_observation.medicalHistoryListLoader = false;
            }
            if (this.$refs.medical_history_problems) {
              this.$refs.medical_history_problems.medicalHistoryListLoader = false;
            }
          } catch (error) {
            console.log(error);
          }
          console.log(error);
          displayErrorMessage(this.formTranslation.common.internal_server_error);
        });
    },

    /**
     * Fetches encounter templates from the backend.
     */
    getEncounterTemplates() {
      get("get_encounter_templates", { encounter_id: this.encounterId }).then((res) => {
        if (res?.data?.success) {
          this.encounterTemplates = res?.data?.data.list;
          this.encounterTemplate = this.encounterTemplates?.find((obj) => obj.id === res?.data?.data?.default);
        }
        this.encounterTemplateLoader = false;
      });
    },

    /**
     * Handles the selection of an encounter template.
     * @param {Object} val - The selected template object.
     */
    ChangeEncounterHandler(val) {
      post("insert_template_to_encounter", { encounterTemplateID: val.id, encounter_id: this.encounterId, patientID: this.patient_id }).then((res) => {
        this.counter++;
        this.init();
      });
    },

    /**
     * Generates HTML content for custom fields.
     * @param {Array} custom_fields - The array of custom fields.
     * @returns {String} - The generated HTML string.
     */
    customFieldContent(custom_fields) {
      let customContent = "";
      custom_fields.forEach(function (key, index) {
        customContent += `<div class="mt-2"> <p class="mb-0"><span class="font-weight-bold">`;
        customContent += `: </span>`;
        customContent += key.field_data !== null ? (Array.isArray(key.field_data) ? key.field_data.join(", ") : key.field_data) : " ";
        customContent += `</p></div>`;
      });
      return customContent;
    },

    /**
     * Initiates the print process for the encounter details.
     */
    async printEncounter() {
      if (this.isButtonDisabled) return;

      this.isButtonDisabled = true;
      this.iconClass = "fa fa-spinner fa-spin";

      try {
        // Collect all the data
        const printData = {
          encounter_id: this.encounterId,
          problems: this.$refs.medical_history_problems ?
            this.$refs.medical_history_problems.medicalHistoryList : [],
          observations: this.$refs.medical_history_observation ?
            this.$refs.medical_history_observation.medicalHistoryList : [],
          notes: this.$refs.medical_history_note ?
            this.$refs.medical_history_note.medicalHistoryList : [],
          prescription: this.$refs.prescription_ref ? {
            medicines: this.$refs.prescription_ref.prescriptionList || [],
            notes: this.$refs.prescription_ref.notes || ''
          } : null
        };

        const response = await get('get_encounter_print', {
          ...printData
        });


        if (response?.data?.status && response?.data?.data) {
          // Create a temporary iframe for printing
          const printFrame = document.createElement('iframe');
          printFrame.style.display = 'none';
          document.body.appendChild(printFrame);

          const frameDoc = printFrame.contentWindow.document;
          frameDoc.open();
          frameDoc.write(`
                <!DOCTYPE html>
                <html>
                    <head>
                        <title>Print</title>
                        <style>
                            @media print {
                                @page { margin: 2cm; }
                                body { font-family: Arial, sans-serif; }
                                .print-container { width: 100%; }
                                .header { text-align: center; margin-bottom: 20px; }
                                .patient-info { margin-bottom: 20px; }
                                .section { margin-bottom: 15px; }
                                table { width: 100%; border-collapse: collapse; }
                                th, td { padding: 8px; border: 1px solid #ddd; }
                                .signature { margin-top: 50px; text-align: right; }
                            }
                        </style>
                    </head>
                    <body>${response.data.data}</body>
                </html>
            `);
          frameDoc.close();

          // Wait for content and images to load
          setTimeout(() => {
            printFrame.contentWindow.print();
            // Remove the frame after printing
            setTimeout(() => {
              document.body.removeChild(printFrame);
            }, 500);
          }, 500);

        } else {
          throw new Error(response?.data?.message || 'Failed to generate print data');
        }
      } catch (error) {
        console.error('Print error:', error);
        this.$bvToast.toast('Failed to prepare print data. Please try again.', {
          title: 'Error',
          variant: 'danger',
          solid: true
        });
      } finally {
        this.isButtonDisabled = false;
        this.iconClass = 'fa fa-print';
      }
    },

    async generateSummary(selectedTemplate) {
      this.$refs.summaryCard.isLoadingSummary = true
      const printData = {
        encounter_id: this.encounterId,
        problems: this.$refs.medical_history_problems ?
          this.$refs.medical_history_problems.medicalHistoryList : [],
        observations: this.$refs.medical_history_observation ?
          this.$refs.medical_history_observation.medicalHistoryList : [],
        notes: this.$refs.medical_history_note ?
          this.$refs.medical_history_note.medicalHistoryList : [],
        prescription: this.$refs.prescription_ref ? {
          medicines: this.$refs.prescription_ref.prescriptionList || [],
          notes: this.$refs.prescription_ref.notes || '',
          
        } : null
      };
      try {
        const response = await post('encounter_summarize', {consultationData: printData, selectedTemplate: selectedTemplate})
        if (!response?.data?.summary) {
          throw new Error('Failed to generate summary')
        }

        // const data = await response
        this.summaryContent = response?.data?.summary;
        this.summaryTemplate = selectedTemplate;
        this.showSummaryModal = true;
        this.summary = response.data.summary
        // this.editMode = true
      } catch (error) {
        this.$refs.summaryCard.isLoadingSummary = false
        console.log(error)
        console.error('Error generating summary:', error)
      } finally {
        this.$refs.summaryCard.isLoadingSummary = false
      }
      this.$refs.summaryCard.isLoadingSummary = false
    },

    handleNext() {
    // Handle next step
      this.$refs.refSummaryModal.isLoading=true
      let templateName= this.$refs.summaryCard.selectedTemplate.replace(/_/g, ' ')
                .replace('template', '')
                .split(' ')
                .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                .join(' ')
                .trim()
      
      post('save_generate_summery',{plainText	: this.$refs.refSummaryModal.editedSummary,templateName,encounter_id:this.encounterId}).then(res=>{
        displayMessage(res.data.message)	
        this.showSummaryModal= false
        this.$refs.refSummaryModal.isLoading=false
        this.$refs.refDocuments.loadDocuments()
      })
  },

    /**
     * Opens the custom form modal.
     * @param {Object} props - The encounter or appointment properties.
     * @param {Object} custom_form_data - The custom form data.
     */
    customFormOpen(props, custom_form_data) {
      this.encounterCustomFormData = { ...custom_form_data };
      this.encounterCustomFormData.module_id = custom_form_data.module_type === "appointment_module" ? props.appointment_id : props.id;
      this.encounterCustomFormViewMode = props.status === "0";
      this.encounterCustomFormModal = true;
    },

    /**
     * Determines if a custom form should be displayed based on conditions.
     * @param {Object} props - The encounter or appointment properties.
     * @param {Object} custom_form_data - The custom form data.
     * @returns {Boolean} - Whether the custom form should be displayed.
     */
    customFormCondition(props, custom_form_data) {
      return (
        props.custom_forms &&
        props.custom_forms.length &&
        (custom_form_data.clinic_ids.length === 0 || custom_form_data.clinic_ids.includes(props.clinic_id)) &&
        ((custom_form_data.module_type === "appointment_module" && props.appointment_id) ||
          custom_form_data.module_type === "patient_encounter_module")
      );
    },

    /**
     * Generates initials from a given name.
     * @param {String} name - The full name.
     * @returns {String} - The initials.
     */
    getInitials(name) {
      if (name !== undefined && name !== "" && name !== null) {
        const patient_name = name.split(" ");
        const initials = patient_name.map((patient_name) => patient_name.charAt(0).toUpperCase());
        return initials.join("");
      } else {
        return " - ";
      }
    },

    /**
     * Handles the addition of a new section from AdditionalTabs.
     * @param {Object} section - The section data.
     */
    handleSectionAdded(section) {
      // Handle new section if needed
      this.extraClinicalData.push(section);
    },

    /**
     * Handles the saving of an entry from AdditionalTabs.
     * @param {Object} entry - The saved entry data.
     */
    handleEntrySaved(entry) {
      // Handle saved entry if needed
      this.getMedicalRecords(this.encounterId);
    },
    handleTabAdded(newTab) {

      this.tabs.push({
        ...newTab,
        showError: false,
        errorMessage: ''
      });
    },

    async handleTabRemoved(tabId) {
      if (this.saveTimers[tabId]) {
        clearTimeout(this.saveTimers[tabId]);
        delete this.saveTimers[tabId];
      }

      try {
        await post('delete_encounter_tab', {
          encounter_id: this.encounterId,
          tab_id: tabId
        });

        const index = this.tabs.findIndex(t => t.id === tabId);
        if (index !== -1) {
          this.tabs.splice(index, 1);
        }
      } catch (error) {
        console.error('Error deleting tab:', error);
        displayErrorMessage('Failed to delete tab');
      }
    },

    async removeTab(tabId) {
      if (this.saveTimers[tabId]) {
        clearTimeout(this.saveTimers[tabId]);
        delete this.saveTimers[tabId];
      }

      try {
        await post('delete_encounter_tab', {
          encounter_id: this.encounterId,
          tab_id: tabId
        });

        const index = this.tabs.findIndex(t => t.id === tabId);
        if (index !== -1) {
          this.tabs.splice(index, 1);
        }
      } catch (error) {
        console.error('Error deleting tab:', error);
        displayErrorMessage('Failed to delete tab');
      }
    },

    /**
 * Returns appropriate title based on tab type
 */
    getTabTitle(type) {
      const titles = {
        'allergies': 'Allergies',
        'examination': 'Examination',
        'family_history': 'family_history',
        'safeguarding_concerns': 'Safeguarding concerns',
        'comments': 'Comments',
      };
      return titles[type] || 'Tab';
    },

    /**
     * Handles input changes in tab content
     * Implements debounce to prevent too frequent saves
     */
    handleInput(tab) {
      console.log(tab);
      tab.showError = false;
      tab.errorMessage = '';

      // Clear existing timer for this tab
      if (this.saveTimers[tab.id]) {
        clearTimeout(this.saveTimers[tab.id]);
      }

      // Set new timer for autosave
      this.saveTimers[tab.id] = setTimeout(() => {
        this.saveTab(tab);
      }, 1000);
    },


    /**
    * Saves tab content to the server
    */
    async saveTab(tab) {
      if (!tab.content.trim()) {
        tab.showError = true;
        tab.errorMessage = 'Content is required';
        return;
      }

      try {
        const payload = {
          encounter_id: this.encounterId,
          tab_id: tab.id,
          type: tab.type,
          content: tab.content,
          metadata: {}
        };

        // Add type-specific metadata
        if (tab.type === 'allergies') {
          payload.metadata.allergy_type = tab.allergyType;
          payload.metadata.severity = tab.severity;
        } else if (tab.type === 'examination') {
          payload.metadata.exam_type = tab.examType;
        }
        const response = await post('save_encounter_tab', payload);

        if (response?.data?.status) {
          // Update tab ID if it was a new tab
          tab.id = response.data.data.tab_id;
          // if (!tab.id) {
          // }
          displayMessage('Saved successfully');
          tab.showError = false;
          this.$emit('tab-saved', tab);
        } else {
          throw new Error(response?.data?.message || 'Save failed');
        }
      } catch (error) {
        console.error('Error saving tab:', error);
        tab.showError = true;
        tab.errorMessage = 'Failed to save';
      }
    },
    async mailEncounter() {
      try {
        const mailData = {
          encounter_id: this.encounterId,
          problems: this.$refs.medical_history_problems?.medicalHistoryList || [],
          observations: this.$refs.medical_history_observation?.medicalHistoryList || [],
          notes: this.$refs.medical_history_note?.medicalHistoryList || [],
          prescription: this.$refs.prescription_ref ? {
            medicines: this.$refs.prescription_ref.prescriptionList || [],
            notes: this.$refs.prescription_ref.notes || ''
          } : null
        };

        const element = $('#send_encounter_mail').find("i");
        element.removeClass('fa fa-paper-plane');
        element.addClass("fa fa-spinner fa-spin");

        const response = await post('encounter_mail', mailData);

        if (response.data.status) {
          displayMessage(response.data.message);
        } else {
          displayErrorMessage(response.data.message);
        }
      } catch (error) {
        console.error(error);
        displayErrorMessage(this.formTranslation.common.internal_server_error);
      } finally {
        const element = $('#send_encounter_mail').find("i");
        element.removeClass("fa fa-spinner fa-spin");
        element.addClass("fa fa-paper-plane");
      }
    },
    async handleDownloadPDF(plainText) {
      try {
        const clinicData = {
          clinic_name: this.$store.state.userDataModule?.selectedClinic?.name || 'Medical Clinic',
          clinic_address: this.$store.state.userDataModule?.selectedClinic?.address || '',
          clinic_phone: this.$store.state.userDataModule?.selectedClinic?.telephone_no || '',
          clinic_email: this.$store.state.userDataModule?.selectedClinic?.email || ''
        };

        const patientData = {
          patient_name: this.encounterData?.patient_name || '',
          patient_id: this.encounterData?.patient_id || ''
        };
        console.log(this.summaryTemplate);
        post('generate_medical_report',{plainText, clinicData, patientData,encounter_id:this.encounterId,templateName	:this.summaryTemplate .replace(/_/g, ' ')
                .replace('template', '')
                .split(' ')
                .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                .join(' ')
                .trim()},false,{
          headers: { 'Content-Type': 'multipart/form-data' },
          responseType: 'arraybuffer', // Important to set this to receive binary data
        })
        .then(response => {
          var arrBuffer = this.base64ToArrayBuffer(response.data);
          var blob=new Blob([arrBuffer], {type:"application/pdf"});
          var link=document.createElement('a');
          link.href=window.URL.createObjectURL(blob);
          link.download=`medical_report_${new Date().toISOString().split('T')[0]}.pdf`;
          link.click();
        })

      } catch (error) {
        console.error('PDF download failed:', error);
        // Add your error notification method here
      }
  },
  base64ToArrayBuffer(data) {
    var binaryString = window.atob(data);
    var binaryLen = binaryString.length;
    var bytes = new Uint8Array(binaryLen);
    for (var i = 0; i < binaryLen; i++) {
        var ascii = binaryString.charCodeAt(i);
        bytes[i] = ascii;
    }
    return bytes;
}
  },
  computed: {
    /**
     * Retrieves user data from the Vuex store.
     * @returns {Object} - The user data.
     */
    userData() {
      return this.$store.state.userDataModule.user;
    },

    /**
     * Retrieves the encounter ID from the route parameters.
     * @returns {Number|String} - The encounter ID.
     */
    customFieldEncounterId() {
      return this.$route.params.encounter_id;
    },

    /**
     * Retrieves the doctor ID from the encounter data.
     * @returns {Number|String} - The doctor ID.
     */
    encounterDoctorId() {
      return this.encounterData.doctor_id;
    },

    /**
     * Retrieves custom fields data from the encounter data.
     * @returns {Object} - The custom fields data.
     */
    customFieldsData() {
      return this.encounterData.custom_fields;
    },

    /**
     * Checks if custom fields exist.
     * @returns {Boolean} - Whether custom fields exist.
     */
    isCustomeFieldExist() {
      return this.encounterData.custom_fields !== undefined && this.encounterData.custom_fields.length > 0;
    },

    /**
     * Checks if the billing module is active.
     * @returns {Boolean} - Whether the billing module is active.
     */
    isBillModuleActive() {
      const module = this.$store.state.userDataModule.user.module.module_config.filter(
        (thing) => thing.name === "billing" && thing.status === "1"
      );
      return module.length > 0;
    },

    /**
     * Retrieves enabled encounter modules from user data.
     * @returns {Array} - The enabled encounter modules.
     */
    getEnableEncounter() {
      if (this.userData.encounter_enable_module !== undefined) {
        return this.userData.encounter_enable_module;
      }
      return [];
    },

    /**
     * Determines the number of columns to display based on user add-ons.
     * @returns {Number|String} - The number of columns.
     */
    getEnableCount() {
      if (this.userData.addOns.kiviPro == true) {
        return this.userData.encounter_enable_count;
      } else {
        return "4";
      }
    },

  },
  watch: {
    /**
     * Watches for changes in the route and updates the encounter temporary status.
     */
    "$route": function () {
      if (this.$route?.query?.isEncounterTemp !== undefined) {
        this.isEncounterTemp = this.$route?.query.isEncounterTemp;
      }
    },
  },
};
</script>

<style scoped>
.nameheading {
  margin: 0;
  font-weight: 500;
  font-size: 20px;
}

.documents-section {
  min-height: 200px;
}

.document-item {
  transition: background-color 0.2s;
}

.document-item:hover {
  background-color: #f8f9fa;
}

.actions {
  opacity: 0.2;
  transition: opacity 0.2s;
}

.document-item:hover .actions {
  opacity: 1;
}

.additional-tabs {
  position: relative;
  min-height: 200px;
}

.tab-buttons .btn {
  transition: all 0.2s;
}

.tab-buttons .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>
