
.col-xl, .col-xl-auto, .col-xl-12, .col-xl-11, .col-xl-10, .col-xl-9, .col-xl-8, .col-xl-7, .col-xl-6, .col-xl-5, .col-xl-4, .col-xl-3, .col-xl-2, .col-xl-1, .col-lg, .col-lg-auto, .col-lg-12, .col-lg-11, .col-lg-10, .col-lg-9, .col-lg-8, .col-lg-7, .col-lg-6, .col-lg-5, .col-lg-4, .col-lg-3, .col-lg-2, .col-lg-1, .col-md, .col-md-auto, .col-md-12, .col-md-11, .col-md-10, .col-md-9, .col-md-8, .col-md-7, .col-md-6, .col-md-5, .col-md-4, .col-md-3, .col-md-2, .col-md-1, .col-sm, .col-sm-auto, .col-sm-12, .col-sm-11, .col-sm-10, .col-sm-9, .col-sm-8, .col-sm-7, .col-sm-6, .col-sm-5, .col-sm-4, .col-sm-3, .col-sm-2, .col-sm-1, .col, .col-auto, .col-12, .col-11, .col-10, .col-9, .col-8, .col-7, .col-6, .col-5, .col-4, .col-3, .col-2, .col-1{
    padding-right: 7.5px;
    padding-left: 7.5px;
}
.row{
    margin-right: -7.5px;
    margin-left: -7.5px;
}
.main-content .container-fluid, .main-content .container-sm, .main-content .container-md, .main-content .container-lg, .main-content .container-xl{
    padding-right: 15px !important;
    padding-left: 15px !important;
}

.card-body {
    padding: 15px;
}
.card-header{
    padding: 15px;
}
.card {
    margin-bottom: 15px;
} 

table.vgt-table{
    font-size: 0.9rem !important;
}

table.vgt-table thead tr th span{
    font-size: 0.9rem !important;
}

table.vgt-table td{
    font-size: 0.9rem;
    padding-left: 1em !important;
}

table.vgt-table tr th .filter-th input ::placeholder{
    font-size: 0.9rem;
}

table.vgt-table {
    font-size: 0.9rem;
}

table.vgt-table .vgt-wrap__footer, .footer__row-count__label, .footer__row-count__select, .footer__navigation, .footer__navigation__page-btn span{
    font-size: 0.9rem !important;
    margin: 0px;
}

.vgt-wrap__footer, .vgt-table thead th, .vgt-global-search{
    background-color: var(--background-color) !important;
}

.vgt-inner-wrap {
    box-shadow: none !important;
}

.custom-checkbox.b-custom-control-lg {
	.custom-control-label {
		&::before {
			top: 0; 
		}
	}
}
.input-group-lg {
	.custom-checkbox {
		.custom-control-label {
			&::before {
				top: 0;
			}
		}
	}
}
.custom-stepper .stepper-icon i {
  padding: 17px 10px;
}
.custom-checkbox {
	.custom-control-input {
		~ {
			.custom-control-label {
				line-height: 2;
			}
		}
	}
}


@media (max-width: 1299px){
    .card-stats {
        .card-body {
            padding: 1rem 1.1rem;
        }
    }
   .sidenav .sidenav-toggler {
        padding: 1.5rem;
  }
}
@media (min-width: 768px) {
    .navbar-vertical .navbar-collapse:before {
        content: "";
        display: block;
        margin: 0 !important;
    }
}
@media (max-width: 575.98px) {
  ul.dropdown-menu-right {
    width: auto !important;
  }
  .kc-patient{
      display:none;
  }
   .kc-profile{
       margin-top: 0 !important;
   }
   .kc-custom-control{
        display: block !important;
   }
   .rmb-15{
        margin-bottom: 15px !important;
    }
    .rmt-15{
        margin-top: 15px !important;
    }

}

.kivicare-badge{
  color: white;
  border-radius: 4px;
  padding: 0 2px 2px 2px;
}

.vgt-table.striped tbody tr:nth-of-type(odd) {
  background-color: var(--background-color) !important
}
#time_slot{
    height: 3.0em;
}

.kivi-choose-file .input-group-text {
    width: 70%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: block;
}