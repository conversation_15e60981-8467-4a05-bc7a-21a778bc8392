<template>
  <div>
    <!-- Main Prescription List -->
    <div class="bg-white border rounded mb-3">
      <div class="flex justify-between items-center p-3 border-b">
        <h2 class="font-medium">Prescription</h2>
        <div class="flex gap-2">
          <button @click="printPrescription" class="px-3 py-1.5 bg-black text-white rounded text-sm hover:bg-black">
            View
          </button>
          <button @click="handleAddPrescriptionForm"
            class="px-3 py-1.5 bg-black text-white rounded text-sm hover:bg-black">
            Add Medication
          </button>
        </div>
      </div>
      <table class="w-full text-sm">
        <thead class="bg-gray-50 text-gray-600">
          <tr>
            <th class="text-left p-2 font-medium">Name</th>
            <th class="text-left p-2 font-medium">Frequency</th>
            <th class="text-left p-2 font-medium">Duration</th>
            <th class="text-left p-2 font-medium w-24">Action</th>
          </tr>
        </thead>
        <tbody>
          <tr v-if="prescriptions.length === 0">
            <td colspan="4" class="p-2 text-gray-500">
              No prescriptions found
            </td>
          </tr>
          <tr v-for="prescription in prescriptions" :key="prescription.id">
            <td class="p-2">{{ prescription.medicationName }}</td>
            <td class="p-2">{{ prescription.frequency }}</td>
            <td class="p-2">{{ formatDuration(prescription.duration) }}</td>
            <td class="p-2">
              <button @click="deletePrescription(prescription.id)" class="text-red-500 hover:text-red-600">
                <i class="fa fa-trash"></i>
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Modal -->
    <div v-if="isAddPrescriptionModalOpen" class="fixed inset-0 bg-black opacity-50"
      @click="isAddPrescriptionModalOpen = false"></div>

    <div v-if="isAddPrescriptionModalOpen" class="fixed inset-0 z-50 overflow-y-auto">
      <div class="min-h-screen px-4 py-6 flex items-start justify-center">
        <div class="w-full max-w-6xl bg-white rounded-xl shadow-xl">
          <div class="p-6">
            <div class="bg-white rounded-lg w-full max-w-6xl h-[700px]">
              <!-- Modal Header -->
              <div class="flex justify-between items-center p-4 border-b">
                <h3 class="font-medium">Add New Prescription</h3>
                <button @click="isAddPrescriptionModalOpen = false">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-x w-4 h-4">
                    <path d="M18 6 6 18"></path>
                    <path d="m6 6 12 12"></path>
                  </svg>
                </button>
              </div>

              <!-- Modal Content -->
              <div class="grid grid-cols-12 h-[calc(100%-130px)]">
                <!-- Left Column - Prescription List -->
                <div class="col-span-3 border-r p-4 space-y-2">
                  <div class="flex justify-between items-center mb-4">
                    <h4 class="font-medium">Medications</h4>
                    <button @click="addNewPrescriptionForm" class="p-1 text-blue-500 hover:text-blue-600">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-plus w-4 h-4">
                        <path d="M5 12h14"></path>
                        <path d="M12 5v14"></path>
                      </svg>
                    </button>
                  </div>

                  <div v-for="(form, index) in prescriptionForms" :key="form.id"
                    class="p-2 rounded flex justify-between items-center cursor-pointer border"
                    :class="{ 'bg-blue-50 border-blue-200': form.isSelected }" @click="selectPrescriptionForm(index)">
                    <span class="text-sm truncate">{{
                      form.medicationName || "New Medication"
                    }}</span>
                    <button v-if="prescriptionForms.length > 1" @click.stop="deletePrescriptionForm(index)"
                      class="text-gray-400 hover:text-red-500">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-trash2 w-4 h-4">
                        <path d="M3 6h18"></path>
                        <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                        <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                        <line x1="10" x2="10" y1="11" y2="17"></line>
                        <line x1="14" x2="14" y1="11" y2="17"></line>
                      </svg>
                    </button>
                  </div>
                </div>

                <!-- Middle Column - Form -->
                <div class="col-span-5 p-4 space-y-4 overflow-y-auto">
                  <div>
                    <label class="block text-sm font-medium mb-1">Medication Name</label>
                    <div class="relative">
                      <input v-model="searchQuery" @input="debounceSearch" class="w-full p-2 pl-8 border rounded"
                        placeholder="Search medication..." type="text" />
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="lucide lucide-search w-4 h-4 absolute left-2 top-3 text-gray-400">
                        <circle cx="11" cy="11" r="8"></circle>
                        <path d="m21 21-4.3-4.3"></path>
                      </svg>

                      <!-- Search Results Dropdown -->
                      <div v-if="searchQuery && searchResults.length > 0"
                        class="absolute z-50 w-full mt-1 bg-white border rounded-md shadow-lg max-h-60 overflow-y-auto">
                        <div @click="selectDrug({ name: searchQuery })"
                          class="p-2 hover:bg-gray-50 cursor-pointer border-b last:border-b-0">
                          <div class="text-sm font-medium">
                            {{ searchQuery }}
                          </div>
                        </div>
                        <div v-for="result in searchResults" :key="result.ui" @click="selectDrug(result)"
                          class="p-2 hover:bg-gray-50 cursor-pointer border-b last:border-b-0">
                          <div class="text-sm font-medium">
                            {{ result.name }} - {{ result.rxcui }}
                          </div>
                        </div>
                      </div>

                      <!-- No Results Message -->
                      <div v-if="
                        searchQuery &&
                        searchResults.length === 0 &&
                        !isLoading
                      " class="absolute z-50 w-full mt-1 bg-white border rounded-md shadow-lg p-2">
                        <div class="text-sm text-gray-500" @click="selectDrug({ name: searchQuery })">
                          {{ searchQuery }}
                        </div>
                      </div>

                      <!-- Loading State -->
                      <div v-if="isLoading" class="absolute z-50 w-full mt-1 bg-white border rounded-md shadow-lg p-2">
                        <div class="text-sm text-gray-500">Searching...</div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <label class="block text-sm font-medium mb-1">Dose</label>
                    <input v-model="currentForm.dose" class="w-full p-2 border rounded mb-2" placeholder="Enter dose"
                      type="text" />
                    <div class="flex flex-wrap gap-2">
                      <button v-for="dose in commonDoses" :key="dose" @click="updateFormField('dose', dose)"
                        class="px-2 py-1 text-xs bg-gray-100 rounded hover:bg-gray-200">
                        {{ dose }}
                      </button>
                    </div>
                  </div>

                  <div>
                    <label class="block text-sm font-medium mb-1">Route</label>
                    <input v-model="currentForm.route" class="w-full p-2 border rounded mb-2" placeholder="Enter route"
                      type="text" />
                    <div class="flex flex-wrap gap-2">
                      <button v-for="route in routes" :key="route" @click="updateFormField('route', route)"
                        class="px-2 py-1 text-xs bg-gray-100 rounded hover:bg-gray-200">
                        {{ route }}
                      </button>
                    </div>
                  </div>

                  <div>
                    <label class="block text-sm font-medium mb-1">Frequency</label>
                    <input v-model="currentForm.frequency" class="w-full p-2 border rounded mb-2"
                      placeholder="Enter frequency" type="text" />
                    <div class="flex flex-wrap gap-2">
                      <button v-for="freq in frequencies" :key="freq" @click="updateFormField('frequency', freq)"
                        class="px-2 py-1 text-xs bg-gray-100 rounded hover:bg-gray-200">
                        {{ freq }}
                      </button>
                    </div>
                  </div>

                  <div>
                    <label class="block text-sm font-medium mb-1">Duration</label>
                    <input v-model="currentForm.duration" class="w-full p-2 border rounded mb-2"
                      placeholder="Enter duration" type="text" />
                    <div class="flex flex-wrap gap-2">
                      <button v-for="duration in durations" :key="duration.value"
                        @click="updateFormField('duration', duration)"
                        class="px-2 py-1 text-xs bg-gray-100 rounded hover:bg-gray-200">
                        {{ duration.label }}
                      </button>
                    </div>
                  </div>

                  <div>
                    <label class="block text-sm font-medium mb-1">Special Instructions</label>
                    <textarea v-model="currentForm.specialInstructions" class="w-full p-2 border rounded" rows="3"
                      placeholder="Enter any special instructions"></textarea>
                  </div>
                </div>

                <!-- Right Column - Drug Information -->
                <div class="col-span-4 bg-gray-50 p-4 overflow-y-auto">
                  <div class="space-y-4">
                    <div v-if="isLoadingDetails" class="text-center py-4">
                      <span class="text-sm text-gray-600">Loading drug details...</span>
                    </div>

                    <div v-else-if="selectedDrug">
                      <!-- Basic Information -->
                      <div>
                        <h4 class="font-medium mb-2">Medicine Information</h4>
                        <p class="text-sm text-gray-600">
                          {{ selectedDrug.name }}
                        </p>
                        <p class="text-sm text-gray-600">
                          {{ selectedDrug.semanticTypes.join(", ") }}
                        </p>
                      </div>

                      <!-- FDA Drug Details -->
                      <div v-if="drugDetails">
                        <!-- Description -->
                        <div v-if="drugDetails.description" class="mt-4">
                          <h4 class="font-medium mb-2">Description</h4>
                          <p class="text-sm text-gray-600">
                            {{ drugDetails.description[0] }}
                          </p>
                        </div>

                        <!-- Indications -->
                        <div v-if="drugDetails.indications_and_usage" class="mt-4">
                          <h4 class="font-medium mb-2">Indications & Usage</h4>
                          <p class="text-sm text-gray-600">
                            {{ drugDetails.indications_and_usage[0] }}
                          </p>
                        </div>

                        <!-- Dosage Forms -->
                        <div v-if="drugDetails.dosage_forms_and_strengths" class="mt-4">
                          <h4 class="font-medium mb-2">
                            Available Forms & Strengths
                          </h4>
                          <p class="text-sm text-gray-600">
                            {{
                              Array.isArray(
                                drugDetails.dosage_forms_and_strengths
                              )
                                ? drugDetails.dosage_forms_and_strengths[0]
                                : drugDetails.dosage_forms_and_strengths
                            }}
                          </p>
                        </div>
                      </div>

                      <!-- Warnings -->
                      <div v-if="warnings.length" class="mt-4">
                        <h4 class="font-medium mb-2">Warnings</h4>
                        <ul class="text-sm space-y-1 text-yellow-700">
                          <li v-for="(warning, index) in warnings" :key="index" class="flex items-start">
                            <span class="mr-2">•</span>
                            <span>{{ warning }}</span>
                          </li>
                        </ul>
                      </div>

                      <!-- Contraindications -->
                      <div v-if="contraindications.length" class="mt-4">
                        <h4 class="font-medium mb-2">Contraindications</h4>
                        <ul class="text-sm space-y-1 text-red-600">
                          <li v-for="(
contraindication, index
                            ) in contraindications" :key="index" class="flex items-start">
                            <span class="mr-2">•</span>
                            <span>{{ contraindication }}</span>
                          </li>
                        </ul>
                      </div>

                      <!-- Drug Interactions -->
                      <div v-if="drugInteractions.length" class="mt-4">
                        <h4 class="font-medium mb-2">Drug Interactions</h4>
                        <ul class="text-sm space-y-1 text-blue-600">
                          <li v-for="(interaction, index) in drugInteractions" :key="index" class="flex items-start">
                            <span class="mr-2">•</span>
                            <span>{{ interaction }}</span>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Modal Footer -->
              <div class="flex justify-end gap-2 p-4 border-t">
                <button @click="isAddPrescriptionModalOpen = false" class="px-4 py-2 border rounded hover:bg-gray-50">
                  Cancel
                </button>
                <button @click="handlePrescriptionFormSave" class="px-4 py-2 bg-black text-white rounded hover:bg-black"
                  :disabled="!canSavePrescription">
                  Save Prescription
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import debounce from "lodash/debounce";
import { post, get } from "../../config/request";

export default {
  name: "ApptPrescription",

  props: {
    encounterId: {
      type: [String, Number],
      required: true,
      default: null,
    },
    isEncounterTemp: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      RXNAV_API_URL: "https://rxnav.nlm.nih.gov/REST",
      FDA_API_KEY: "OmwH0fbMUppRho9gXlWc4L6hHglcEZDfMw98YiXo",
      isAddPrescriptionModalOpen: false,
      searchQuery: "",
      searchResults: [],
      selectedDrug: null,
      prescriptions: [],
      paginate: {
        totalRows: 0,
      },
      prescriptionList: [], // Added missing property
      isLoading: false,
      loading: false, // Added missing property
      submitted: false, // Added missing property
      activePrescriptionIndex: 0,
      drugDetails: null,
      drugWarnings: [],
      drugContraindications: [],
      drugInteractions: [],
      isLoadingDetails: false,
      warnings: ["Take with food", "Complete full course"],
      contraindications: ["Known hypersensitivity to the drug"],
      showAddForm: false, // Added missing property

      // Store multiple prescription forms
      prescriptionForms: [
        {
          id: Date.now(),
          medicationName: "",
          dose: "",
          route: "",
          frequency: "",
          duration: "",
          specialInstructions: "",
          isSelected: true,
        },
      ],

      // Common options
      commonDoses: [
        "250mg",
        "500mg",
        "750mg",
        "1000mg",
        "5ml",
        "10ml",
        "15ml",
        "20ml",
      ],
      routes: [
        "Oral",
        "Intravenous",
        "Intramuscular",
        "Subcutaneous",
        "Topical",
        "Inhaled",
        "Sublingual",
        "Rectal",
      ],
      frequencies: [
        "Once daily",
        "Twice daily",
        "Three times daily",
        "Every 4 hours",
        "Every 6 hours",
        "Every 8 hours",
        "As needed",
      ],
      durations: [
        { value: 7, label: "1 week" },
        { value: 14, label: "2 weeks" },
        { value: 30, label: "1 month" },
        { value: 90, label: "3 months" },
        { value: 180, label: "6 months" },
        { value: -1, label: "Ongoing" }, // Using -1 to represent ongoing
      ],
    };
  },

  computed: {
    currentForm() {
      return (
        this.prescriptionForms[this.activePrescriptionIndex] ||
        this.getDefaultForm()
      );
    },

    canSavePrescription() {
      return this.prescriptionForms.every(
        (form) =>
          form.medicationName &&
          form.dose &&
          form.route &&
          form.frequency &&
          form.duration
      );
    },
  },

  watch: {
    encounterId: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          console.log("Consultation ID in child:", newVal); // Add this log
          this.getPrescriptionList();
        }
      },
    },
  },

  mounted() {
    if (this.encounterId) {
      this.getPrescriptionList();
    }
  },

  created() {
    this.debouncedSearch = debounce(this.searchDrugs, 300);
    // Initialize prescriptionData
    this.prescriptionData = this.getDefaultForm();
  },

  methods: {
    getDefaultForm() {
      return {
        id: Date.now(),
        medicationName: "",
        dose: "",
        route: "",
        frequency: "",
        duration: "",
        specialInstructions: "",
        isSelected: true,
        encounter_id: this.encounterId,
      };
    },

    async searchDrugs(query) {
      if (!query || query.length < 3) {
        this.searchResults = [];
        return;
      }

      try {
        this.isLoading = true;
        const response = await axios.get(
          `${this.RXNAV_API_URL}/approximateTerm.json`,
          {
            params: { term: query },
          }
        );

        const candidates = response.data.approximateGroup?.candidate || [];
        const groupedDrugs = this.groupDrugsByRxcui(candidates);
        this.searchResults = this.formatSearchResults(groupedDrugs);
      } catch (error) {
        console.error("Error searching drugs:", error);
        this.searchResults = [];
      } finally {
        this.isLoading = false;
      }
    },

    groupDrugsByRxcui(candidates) {
      return candidates.reduce((acc, curr) => {
        if (!acc[curr.rxcui]) {
          acc[curr.rxcui] = {
            rxcui: curr.rxcui,
            names: new Set(),
            sources: new Set(),
          };
        }
        if (curr.name) acc[curr.rxcui].names.add(curr.name);
        if (curr.source) acc[curr.rxcui].sources.add(curr.source);
        return acc;
      }, {});
    },

    getPrescriptionList() {
      get("prescription_list", {
        encounter_id: this.encounterId,
      })
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            // this.prescriptionList = data.data.data;
            // this.paginate.totalRows = data.data.total_rows;
            // this.prescriptions.push(data.data.data);
            this.prescriptions = response.data.data.map((prescription) => ({
              id: prescription.id,
              medicationName: prescription.name.label,
              frequency: prescription.frequency,
              duration: prescription.duration,
              specialInstructions: prescription.instruction,
              encounter_id: prescription.encounter_id,
            }));

            this.paginate.totalRows = response.data.total_rows;
            console.log(this.prescriptions);
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },

    async deletePrescription(id) {
      try {
        // Show confirmation dialog
        const result = await this.$swal.fire({
          title: "Are you sure?",
          text: "You won't be able to revert this!",
          icon: "warning",
          showCancelButton: true,
          confirmButtonColor: "#3085d6",
          cancelButtonColor: "#d33",
          confirmButtonText: "Yes, delete it!",
        });

        // If user confirms
        if (result.isConfirmed) {
          // Make API call
          const response = await get(
            this.isEncounterTemp
              ? "delete_encounter_template_prescription"
              : "prescription_delete",
            { id: id }
          );

          if (response.data?.status === true) {
            // Remove from local state
            this.prescriptions = this.prescriptions.filter((p) => p.id !== id);

            // Show success message
            this.$swal({
              title: "Deleted!",
              text: "Prescription has been deleted.",
              icon: "success",
              timer: 2000,
              showConfirmButton: false,
            });
          } else {
            throw new Error(
              response.data?.message || "Failed to delete prescription"
            );
          }
        }
      } catch (error) {
        console.error("Error deleting prescription:", error);
        this.$swal({
          title: "Error!",
          text: error.message || "Failed to delete prescription",
          icon: "error",
          confirmButtonText: "OK",
        });
      }
    },

    formatSearchResults(groupedDrugs) {
      return Object.values(groupedDrugs).map((drug) => ({
        rxcui: drug.rxcui,
        name: Array.from(drug.names)[0],
        alternateNames: Array.from(drug.names).slice(1),
        sources: Array.from(drug.sources),
      }));
    },

    async fetchDrugDetails(rxcui) {
      try {
        this.isLoadingDetails = true;
        const response = await axios.get(
          "https://api.fda.gov/drug/label.json",
          {
            params: {
              search: `openfda.rxcui:${rxcui}`,
              api_key: this.FDA_API_KEY,
            },
          }
        );

        if (response.data.results?.length > 0) {
          this.processDrugDetails(response.data.results[0]);
        }
      } catch (error) {
        console.error("Error fetching drug details:", error);
      } finally {
        this.isLoadingDetails = false;
      }
    },

    processDrugDetails(details) {
      this.drugDetails = details;
      this.updateWarnings(details);
      this.updateContraindications(details);
      this.updateDrugInteractions(details);
      this.updateDosageInformation(details);
    },

    updateWarnings(details) {
      this.warnings = [
        ...(details.boxed_warning || []),
        ...(details.warnings || []),
        ...(details.warnings_and_precautions || []),
      ].filter(Boolean);
    },

    updateContraindications(details) {
      this.contraindications = details.contraindications
        ? Array.isArray(details.contraindications)
          ? details.contraindications
          : [details.contraindications]
        : [];
    },

    updateDrugInteractions(details) {
      this.drugInteractions = details.drug_interactions
        ? Array.isArray(details.drug_interactions)
          ? details.drug_interactions
          : [details.drug_interactions]
        : [];
    },

    updateDosageInformation(details) {
      if (details.dosage_and_administration) {
        const dosageInfo = Array.isArray(details.dosage_and_administration)
          ? details.dosage_and_administration[0]
          : details.dosage_and_administration;

        const dosagePattern = /\d+\s*(?:mg|ml|g)/gi;
        const extractedDoses = dosageInfo.match(dosagePattern) || [];
        if (extractedDoses.length > 0) {
          this.commonDoses = [
            ...new Set([...this.commonDoses, ...extractedDoses]),
          ];
        }
      }
    },

    async handlePrescriptionFormSave() {
      try {
        if (!this.canSavePrescription) {
          throw new Error("Please fill in all required fields");
        }

        this.loading = true;
        this.submitted = true;

        // Create an array of promises for saving each prescription
        const savePromises = this.prescriptionForms.map((form) => {
          const payload = this.createPrescriptionPayload(form);
          return post(
            this.isEncounterTemp
              ? "save_encounter_template_prescription"
              : "prescription_save",
            payload
          );
        });

        // Wait for all prescriptions to be saved
        const responses = await Promise.all(savePromises);

        // Check if all responses were successful
        const allSuccess = responses.every(
          (response) => response.data?.status === true
        );

        if (allSuccess) {
          // Update the UI with success message
          if (typeof displayMessage === "function") {
            displayMessage("All prescriptions saved successfully");
          }

          // Update the prescription list with new data
          this.getPrescriptionList();

          // Reset the form and close modal
          this.resetAfterSave();

          // Redirect to the prescription route
          // this.$router.push({
          //   name: "appointment.prescription",
          //   params: { encounter_id: this.encounterId },
          // });

          let currentUrl = window.location.href.split('#')[0];
          window.open(`${currentUrl}#/appointment/prescription/${this.encounterId}`, '_blank');
        } else {
          throw new Error("Failed to save some prescriptions");
        }
      } catch (error) {
        this.handleSaveError(error);
      } finally {
        this.loading = false;
      }
    },

    // Update the createPrescriptionPayload method to handle duration formatting
    createPrescriptionPayload(form) {
      return {
        encounter_id: this.encounterId,
        name: {
          id: form.medicationName,
          label: form.medicationName,
        },
        dose: form.dose,
        route: form.route,
        frequency: form.frequency,
        duration: form.duration, // Send the numeric value directly
        duration_label: this.formatDuration(form.duration), // Optional: send the label for display purposes
        instruction: form.specialInstructions,
        route_name: "prescription_save",
      };
    },

    // Update the resetAfterSave method
    resetAfterSave() {
      this.prescriptionData = this.getDefaultForm();
      this.submitted = false;
      this.resetForms();
      this.isAddPrescriptionModalOpen = false;
      this.getPrescriptionList(); // Refresh the prescription list
      this.$emit("prescription-saved");
    },

    // Add error handling for multiple prescription save
    handleSaveError(error) {
      console.error("Error saving prescriptions:", error);
      if (typeof displayErrorMessage === "function") {
        displayErrorMessage(
          error.message || "Failed to save prescriptions. Please try again."
        );
      }
    },

    handleSaveResponse(response) {
      if (response.data?.status === true) {
        if (typeof displayMessage === "function") {
          displayMessage(response.data.message);
        }
        this.showAddForm = false;
        if (!this.prescriptionData.id) {
          this.prescriptionList.push(response.data.data);
        }
        this.resetAfterSave();

        // Redirect to the prescription route
        this.$router.push({
          name: "appointment.prescription",
          params: { encounter_id: this.encounterId },
        });
      } else {
        throw new Error(
          response.data?.message || "Failed to save prescription"
        );
      }
    },

    resetForms() {
      this.prescriptionForms = [this.getDefaultForm()];
      this.activePrescriptionIndex = 0;
      this.selectedDrug = null;
      this.searchQuery = "";
      this.searchResults = [];
    },

    handleAddPrescriptionForm() {
      this.resetForms();
      this.isAddPrescriptionModalOpen = true;
    },

    async selectDrug(drug) {
      this.selectedDrug = {
        name: drug.name || "",
        rxcui: drug.rxcui || "",
        semanticTypes: [],
        ...drug,
      };

      this.prescriptionForms[this.activePrescriptionIndex].medicationName =
        drug.name;
      this.searchQuery = "";
      this.searchResults = [];

      await this.fetchDrugDetails(drug.rxcui);
    },

    debounceSearch() {
      this.debouncedSearch(this.searchQuery);
    },

    addNewPrescriptionForm() {
      this.prescriptionForms.forEach((form) => (form.isSelected = false));
      this.prescriptionForms.push(this.getDefaultForm());
      this.activePrescriptionIndex = this.prescriptionForms.length - 1;
    },

    selectPrescriptionForm(index) {
      this.prescriptionForms.forEach((form) => (form.isSelected = false));
      this.prescriptionForms[index].isSelected = true;
      this.activePrescriptionIndex = index;
    },

    // deletePrescription(id) {
    //   this.prescriptions = this.prescriptions.filter((p) => p.id !== id);
    // },

    deletePrescriptionForm(index) {
      this.prescriptionForms = this.prescriptionForms.filter(
        (_, i) => i !== index
      );
      if (this.activePrescriptionIndex >= this.prescriptionForms.length) {
        this.activePrescriptionIndex = Math.max(
          0,
          this.prescriptionForms.length - 1
        );
      }
      if (this.prescriptionForms.length === 0) {
        this.addNewPrescriptionForm();
      }
    },

    updateFormField(field, value) {
      if (this.prescriptionForms[this.activePrescriptionIndex]) {
        // For duration, store the numeric value
        if (field === "duration") {
          this.prescriptionForms[this.activePrescriptionIndex][field] =
            value.value;
          // Optionally store the label for display purposes
          this.prescriptionForms[this.activePrescriptionIndex]['duration_label'] = value.label;
        } else {
          this.prescriptionForms[this.activePrescriptionIndex][field] = value;
        }
      }
    },

    // Helper method to format duration for display
    formatDuration(days) {
      if (days === -1) return "Ongoing";
      if (days === 7) return "1 week";
      if (days === 14) return "2 weeks";
      if (days === 30) return "1 month";
      if (days === 90) return "3 months";
      if (days === 180) return "6 months";
      return `${days} days`;
    },
    printPrescription() {
      let currentUrl = window.location.href.split('#')[0];
      window.open(`${currentUrl}#/appointment/prescription/${this.encounterId}`, '_blank');
    }
  },

  beforeDestroy() {
    if (this.debouncedSearch?.cancel) {
      this.debouncedSearch.cancel();
    }
  },
};
</script>
