<?php

namespace Database\Factories;

use App\Models\Patient;
use App\Models\User;
use App\Models\Clinic;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Patient>
 */
class PatientFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Patient::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Allow factory usage in testing environment
        if (app()->environment('testing')) {
            return [
                'user_id' => User::factory(),
                'clinic_id' => Clinic::factory(),
                'created_by_provider_id' => null,
                'gender' => fake()->randomElement(['male', 'female', 'other']),
                'date_of_birth' => fake()->date('Y-m-d', '-18 years'),
                'health_history' => [
                    'chronic_conditions' => [],
                    'surgeries' => [],
                    'family_history' => []
                ],
                'allergies' => [
                    fake()->randomElement(['Penicillin', 'Peanuts', 'Shellfish', 'None'])
                ],
                'medications' => [
                    fake()->randomElement(['Aspirin', 'Ibuprofen', 'None'])
                ],
                'medical_conditions' => [
                    fake()->randomElement(['Hypertension', 'Diabetes', 'None'])
                ],
                'preferences' => [
                    'feed_topics' => ['health_tips', 'nutrition'],
                    'notification_settings' => [
                        'push' => true,
                        'email' => true
                    ]
                ],
                'appointment_preferences' => [
                    'preferred_location' => null,
                    'preferred_time' => 'morning',
                    'reminder_settings' => [
                        'email' => true,
                        'sms' => false
                    ]
                ],
                'communication_preferences' => [
                    'language' => 'en',
                    'contact_method' => 'email'
                ],
                'insurance_provider' => fake()->randomElement(['Blue Cross', 'Aetna', 'Cigna', null]),
                'insurance_policy_number' => fake()->optional()->numerify('POL-########'),
                'insurance_expiry_date' => fake()->optional()->date('Y-m-d', '+2 years'),
                'emergency_contact_name' => fake()->name(),
                'emergency_contact_phone' => fake()->phoneNumber(),
                'emergency_contact_relationship' => fake()->randomElement(['spouse', 'parent', 'sibling', 'friend']),
                'emergency_contact' => [
                    'name' => fake()->name(),
                    'phone' => fake()->phoneNumber(),
                    'relationship' => fake()->randomElement(['spouse', 'parent', 'sibling', 'friend'])
                ],
                'medical_history' => fake()->optional()->paragraph(),
                'current_medications' => fake()->optional()->sentence(),
                'is_active' => true,
            ];
        }

        throw new \Exception('PatientFactory is disabled for production use. Create patients manually through proper channels.');
    }

    /**
     * Indicate that the patient is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the patient has no insurance.
     */
    public function noInsurance(): static
    {
        return $this->state(fn (array $attributes) => [
            'insurance_provider' => null,
            'insurance_policy_number' => null,
            'insurance_expiry_date' => null,
        ]);
    }

    /**
     * Create a patient with minimal data.
     */
    public function minimal(): static
    {
        return $this->state(fn (array $attributes) => [
            'health_history' => [],
            'allergies' => [],
            'medications' => [],
            'medical_conditions' => [],
            'preferences' => [],
            'appointment_preferences' => [],
            'communication_preferences' => [],
            'insurance_provider' => null,
            'insurance_policy_number' => null,
            'insurance_expiry_date' => null,
            'emergency_contact_name' => null,
            'emergency_contact_phone' => null,
            'emergency_contact_relationship' => null,
            'emergency_contact' => null,
            'medical_history' => null,
            'current_medications' => null,
        ]);
    }

    /**
     * Create a patient with specific age.
     */
    public function age(int $age): static
    {
        return $this->state(fn (array $attributes) => [
            'date_of_birth' => now()->subYears($age)->format('Y-m-d'),
        ]);
    }

    /**
     * Create a patient with specific gender.
     */
    public function gender(string $gender): static
    {
        return $this->state(fn (array $attributes) => [
            'gender' => $gender,
        ]);
    }
}
