<template>
  <div>
    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center items-center py-8">
      <div
        class="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"
      ></div>
    </div>

    <!-- Empty State (No Data At All) -->
    <div
      v-else-if="Object.keys(groupedTabs).length === 0"
      class="my-4 flex flex-col items-center justify-center py-12 px-4 border-2 border-dashed border-gray-200 rounded-lg bg-white"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
        class="w-12 h-12 text-gray-400 mb-4"
      >
        <path
          d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"
        ></path>
        <path d="M14 2v6h6"></path>
        <path d="M16 13H8"></path>
        <path d="M16 17H8"></path>
        <path d="M10 9H8"></path>
      </svg>
      <p class="text-lg font-medium text-gray-900">No summary data available</p>
      <p class="text-sm text-gray-500 mt-1">No records have been added yet</p>
    </div>

    <div v-else class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
      <div v-for="(items, type) in groupedTabs" :key="type" class="space-y-6">
        <section class="bg-white rounded-lg border border-gray-200 p-4">
          <h2 class="text-lg font-semibold text-gray-800 mb-3">
            {{ formatTitle(type) }}
          </h2>
          <div class="space-y-2">
            <div
              v-for="item in items"
              :key="item.id"
              class="flex justify-between items-center py-2 border-b border-gray-100"
            >
              <span class="text-gray-700">{{ item.content }}</span>
              <span class="text-sm text-gray-400"
                >Recorded: {{ formatDate(item.created_at) }}</span
              >
            </div>
          </div>
        </section>
      </div>
    </div>
  </div>
</template>

<script>
import { get } from "../../config/request";
import { displayErrorMessage } from "../../utils/message";
import { formatDate } from "../../utils/helper";

export default {
  name: "SummaryTab",

  props: {
    encounterId: {
      type: [String, Number],
      required: true,
    },
  },

  data() {
    return {
      tabs: [],
      loading: false,
    };
  },

  computed: {
    groupedTabs() {
      return this.tabs.reduce((acc, tab) => {
        if (!acc[tab.type]) {
          acc[tab.type] = [];
        }
        acc[tab.type].push(tab);
        return acc;
      }, {});
    },
  },

  watch: {
    encounterId: {
      immediate: true,
      handler(newId) {
        if (newId) {
          this.getTabDetails();
        }
      },
    },
  },

  methods: {
    formatDate,
    formatTitle(type) {
      return type
        .split("_")
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ");
    },

    async getTabDetails() {
      try {
        const response = await get("get_encounter_tabs", {
          encounter_id: this.encounterId,
        });

        if (response.data.status === true) {
          this.tabs = response.data.data;

          // Get all allergy contents and emit
          let allergies = this.tabs
            .filter((tab) => tab.type === "allergies")
            .map((tab) => tab.content)
            .filter(Boolean); // Remove empty/null values
          this.$emit("update:allergies", allergies);

          // this.$emit(
          //   "update:allergies",
          //   this.groupedTabs.allergies?.map((tab) => tab.content) || []
          // );
        }
      } catch (error) {
        console.error("Error fetching tab data:", error);
        displayErrorMessage(this.formTranslation.common.internal_server_error);
      }
    },
  },
};
</script>
