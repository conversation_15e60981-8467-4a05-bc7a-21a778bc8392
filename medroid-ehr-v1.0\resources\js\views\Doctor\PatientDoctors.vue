<template>
  <div>
    <div class="p-6 max-w-7xl mx-auto">
      <!-- Search Section -->
      <div class="mb-6">
        <h1 class="text-2xl font-bold mb-4">Find a Doctor</h1>
        <div class="flex flex-col md:flex-row gap-4">
          <div class="flex-1 relative">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="lucide lucide-search w-5 h-5 absolute left-3 top-3 text-gray-400"
            >
              <!-- ... svg path ... -->
            </svg>
            <input
              v-model="searchTerm"
              @input="handleSearch"
              placeholder="Search by doctor name, specialty, or condition..."
              class="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
              type="text"
            />
          </div>
          <div class="relative">
            <!-- Location input remains same -->
          </div>
        </div>
      </div>

      <!-- Specialty Filters -->
      <div class="flex flex-wrap gap-2 mb-6">
        <button
          v-for="specialty in specialties"
          :key="specialty"
          @click="setSpecialty(specialty)"
          :class="[
            'px-4 py-2 rounded-lg text-sm',
            selectedSpecialty === specialty
              ? 'bg-purple-600 text-white'
              : 'bg-gray-100 text-gray-600 hover:bg-gray-200',
          ]"
        >
          {{ specialty }}
        </button>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading" class="text-center py-8">
        <div
          class="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto"
        ></div>
      </div>

      <!-- Doctors List -->
      <div v-else class="grid grid-cols-1 gap-6">
        <div
          v-for="doctor in filteredDoctors"
          :key="doctor.id"
          class="bg-white text-card-foreground rounded-xl border shadow overflow-hidden"
        >
          <div class="p-4">
            <div class="flex flex-col md:flex-row gap-6">
              <div class="md:w-48">
                <div class="relative w-full h-48">
                  <img
                    v-if="doctor.user_image"
                    :src="doctor.user_image"
                    :alt="doctor.label"
                    @error="handleImageError"
                    class="w-full h-48 object-cover rounded-lg mb-4"
                  />
                  <div
                    v-else
                    class="w-full h-48 rounded-lg mb-4 bg-gray-100 flex items-center justify-center"
                  >
                    <i class="fas fa-user text-4xl text-gray-400"></i>
                  </div>
                </div>
              </div>
              <div class="flex-1">
                <div
                  class="flex flex-col md:flex-row md:items-start md:justify-between"
                >
                  <div>
                    <h3 class="text-xl font-semibold mb-2">
                      {{ getDoctorName(doctor.label) }}
                    </h3>
                    <p class="text-purple-600 mb-2">
                      {{ getDoctorSpecialties(doctor.label) }}
                    </p>
                    <div
                      v-if="doctor.enableTeleMed"
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
                    >
                      Telemedicine Available
                    </div>
                  </div>
                  <div class="mt-4 md:mt-0 space-y-3">
                    <!-- <button
                      @click="bookAppointment(doctor.id)"
                      class="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
                    >
                      Book Appointment
                    </button>
                    <button
                      @click="viewProfile(doctor.id)"
                      class="w-full px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
                    >
                      View Profile
                    </button> -->
                    <button
                      @click="navigateToBooking"
                      class="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
                    >
                      Book Appointment
                    </button>
                    <button
                      @click="navigateToBooking"
                      class="w-full px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
                    >
                      View Profile
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- No Results -->
      <div
        v-if="!isLoading && filteredDoctors.length === 0"
        class="text-center py-8"
      >
        <p class="text-gray-500">No doctors found matching your criteria</p>
      </div>
    </div>
  </div>
</template>

<script>
import { get } from "../../config/request";

export default {
  data: () => ({
    isLoading: false,
    doctors: [],
    searchTerm: "",
    location: "",
    selectedSpecialty: "All",
    specialties: ["All"], // Will be populated from doctor data
  }),

  computed: {
    filteredDoctors() {
      return this.doctors.filter((doctor) => {
        const matchesSearch =
          this.searchTerm === "" ||
          doctor.label.toLowerCase().includes(this.searchTerm.toLowerCase());

        const matchesSpecialty =
          this.selectedSpecialty === "All" ||
          this.getDoctorSpecialties(doctor.label).includes(
            this.selectedSpecialty
          );

        return matchesSearch && matchesSpecialty;
      });
    },
  },

  methods: {
    async fetchDoctors() {
      try {
        this.isLoading = true;
        const response = await get("get_static_data", {
          data_type: "clinic_doctors",
          clinic_id: 1,
        });

        if (response?.data?.status) {
          this.doctors = response.data.data;
          this.updateSpecialties();
        }
      } catch (error) {
        console.error("Error fetching doctors:", error);
        if (this.$toast) {
          this.$toast.error("Failed to fetch doctors");
        }
      } finally {
        this.isLoading = false;
      }
    },

    navigateToBooking() {
      window.location.href = "/appointment";
    },

    updateSpecialties() {
      const specialtiesSet = new Set(["All"]);
      this.doctors.forEach((doctor) => {
        const doctorSpecialties = this.getDoctorSpecialties(doctor.label);
        doctorSpecialties.split(",").forEach((specialty) => {
          specialtiesSet.add(specialty.trim());
        });
      });
      this.specialties = Array.from(specialtiesSet);
    },

    getDoctorName(label) {
      return label.split("(")[0].trim();
    },

    getDoctorSpecialties(label) {
      const match = label.match(/\((.*?)\)/);
      return match ? match[1] : "General Practice";
    },

    handleSearch(event) {
      this.searchTerm = event.target.value;
    },

    setSpecialty(specialty) {
      this.selectedSpecialty = specialty;
    },

    bookAppointment(doctorId) {
      this.$router.push(`/book-appointment/${doctorId}`);
    },

    viewProfile(doctorId) {
      this.$router.push(`/doctor-profile/${doctorId}`);
    },
  },

  mounted() {
    this.fetchDoctors();
  },
};
</script>
