<template>
  <div class="task-comments-container p-4">
    <!-- Loading state -->
    <div v-if="loading" class="flex justify-center items-center py-10">
      <div class="inline-block animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-black"></div>
      <span class="ml-3 text-gray-700">{{ formTranslation.common.loading || 'Loading...' }}</span>
    </div>
    
    <!-- Comments list -->
    <div v-else>
      <!-- Add comment form -->
      <div class="mb-6">
        <h3 class="text-lg font-medium text-gray-800 mb-3">
          {{ formTranslation.task.add_comment || 'Add Comment' }}
        </h3>
        <div class="flex gap-4">
          <div class="flex-shrink-0">
            <div class="inline-flex items-center justify-center h-10 w-10 rounded-full text-sm text-white shadow"
              :style="{ backgroundColor: getAvatarColor(currentUserName) }">
              {{ getInitials(currentUserName) }}
            </div>
          </div>
          <div class="flex-grow">
            <textarea 
              class="block w-full rounded-lg border border-gray-300 px-4 py-3 text-sm focus:outline-none focus:ring-2 focus:ring-black mb-3 resize-y"
              v-model="newComment" 
              :placeholder="formTranslation.task.comment_placeholder || 'Write your comment here...'" 
              rows="3"
            ></textarea>
            <div class="flex justify-end">
              <button 
                class="flex items-center justify-center rounded-lg bg-black px-5 py-2.5 text-sm font-medium text-white transition hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-black focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                @click="addComment" 
                :disabled="!newComment.trim() || submitting"
              >
                <div v-if="submitting" class="inline-block animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                <span v-if="submitting">{{ formTranslation.common.submitting || 'Submitting...' }}</span>
                <template v-else>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-4 h-4 mr-1.5">
                    <path d="M3.478 2.405a.75.75 0 00-.926.94l2.432 7.905H13.5a.75.75 0 010 1.5H4.984l-2.432 7.905a.75.75 0 00.926.94 60.519 60.519 0 0018.445-8.986.75.75 0 000-1.218A60.517 60.517 0 003.478 2.405z" />
                  </svg>
                  {{ formTranslation.task.post_comment || 'Post Comment' }}
                </template>
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Comments counter -->
      <div class="flex items-center justify-between mb-4 pb-3 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-800 flex items-center">
          <span>Comments</span>
          <span class="ml-2 bg-black text-white text-xs font-medium px-2.5 py-0.5 rounded-full">
            {{ comments.length }}
          </span>
        </h3>
        <div v-if="comments.length > 0" class="text-sm text-gray-500">
          Newest first
        </div>
      </div>
      
      <!-- No comments state -->
      <div v-if="!comments.length" class="bg-gray-50 rounded-lg p-8 text-center">
        <div class="flex justify-center mb-3">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-12 h-12 text-gray-300">
            <path fill-rule="evenodd" d="M4.848 2.771A49.144 49.144 0 0112 2.25c2.43 0 4.817.178 7.152.52 1.978.292 3.348 2.024 3.348 3.97v6.02c0 1.946-1.37 3.678-3.348 3.97a48.901 48.901 0 01-3.476.383.39.39 0 00-.297.17l-2.755 4.133a.75.75 0 01-1.248 0l-2.755-4.133a.39.39 0 00-.297-.17 48.9 48.9 0 01-3.476-.384c-1.978-.29-3.348-2.024-3.348-3.97V6.741c0-1.946 1.37-3.68 3.348-3.97zM6.75 8.25a.75.75 0 01.75-.75h9a.75.75 0 010 1.5h-9a.75.75 0 01-.75-.75zm.75 2.25a.75.75 0 000 1.5h9a.75.75 0 000-1.5h-9z" clip-rule="evenodd" />
          </svg>
        </div>
        <p class="text-gray-600 font-medium">{{ formTranslation.task.no_comments || 'No comments yet' }}</p>
        <p class="text-gray-500 text-sm mt-1">Be the first to add a comment to this task</p>
      </div>
      
      <!-- Comments list -->
      <div v-else class="space-y-5">
        <div 
          v-for="comment in sortedComments" 
          :key="comment.id" 
          class="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden"
        >
          <div class="p-4">
            <div class="flex gap-3">
              <div class="flex-shrink-0">
                <div 
                  class="inline-flex items-center justify-center h-10 w-10 rounded-full text-sm text-white shadow-sm"
                  :style="{ backgroundColor: getAvatarColor(comment.user_name) }"
                >
                  {{ getInitials(comment.user_name) }}
                </div>
              </div>
              <div class="flex-grow min-w-0">
                <div class="flex justify-between items-start">
                  <div>
                    <div class="font-medium text-gray-900">{{ comment.user_name }}</div>
                    <div class="text-xs text-gray-500 mt-0.5">
                      {{ formatDate(comment.created_at) }}
                    </div>
                  </div>
                  <!-- Comment actions dropdown -->
                  <div class="relative" v-if="canEditComment(comment)">
                    <button 
                      class="text-gray-400 hover:text-gray-600 focus:outline-none p-1 rounded-full hover:bg-gray-100" 
                      @click.stop="toggleDropdown(comment.id, $event)"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
                        <path fill-rule="evenodd" d="M10.5 6a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0zm0 6a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0zm0 6a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0z" clip-rule="evenodd" />
                      </svg>
                    </button>
                    <!-- Dropdown overlay to catch clicks outside when open -->
                    <div v-if="activeDropdown === comment.id" class="fixed inset-0 z-40" @click.stop="activeDropdown = null"></div>
                    <div 
                      v-if="activeDropdown === comment.id"
                      class="absolute right-0 z-50 mt-1 w-48 rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
                      style="top: 100%; min-width: 10rem;"
                      @click.stop
                    >
                      <button 
                        class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" 
                        @click="editComment(comment)"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-4 h-4 mr-2 text-gray-500">
                          <path d="M21.731 2.269a2.625 2.625 0 00-3.712 0l-1.157 1.157 3.712 3.712 1.157-1.157a2.625 2.625 0 000-3.712zM19.513 8.199l-3.712-3.712-8.4 8.4a5.25 5.25 0 00-1.32 2.214l-.8 2.685a.75.75 0 00.933.933l2.685-.8a5.25 5.25 0 002.214-1.32l8.4-8.4z" />
                          <path d="M5.25 5.25a3 3 0 00-3 3v10.5a3 3 0 003 3h10.5a3 3 0 003-3V13.5a.75.75 0 00-1.5 0v5.25a1.5 1.5 0 01-1.5 1.5H5.25a1.5 1.5 0 01-1.5-1.5V8.25a1.5 1.5 0 011.5-1.5h5.25a.75.75 0 000-1.5H5.25z" />
                        </svg>
                        {{ formTranslation.common.edit || 'Edit' }}
                      </button>
                      <button 
                        class="flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-gray-100" 
                        @click="confirmDeleteComment(comment)"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-4 h-4 mr-2 text-red-500">
                          <path fill-rule="evenodd" d="M16.5 4.478v.227a48.816 48.816 0 013.878.512.75.75 0 11-.256 1.478l-.209-.035-1.005 13.07a3 3 0 01-2.991 2.77H8.084a3 3 0 01-2.991-2.77L4.087 6.66l-.209.035a.75.75 0 01-.256-1.478A48.567 48.567 0 017.5 4.705v-.227c0-1.564 1.213-2.9 2.816-2.951a52.662 52.662 0 013.369 0c1.603.051 2.815 1.387 2.815 2.951zm-6.136-1.452a51.196 51.196 0 013.273 0C14.39 3.05 15 3.684 15 4.478v.113a49.488 49.488 0 00-6 0v-.113c0-.794.609-1.428 1.364-1.452zm-.355 5.945a.75.75 0 10-1.5.058l.347 9a.75.75 0 101.499-.058l-.346-9zm5.48.058a.75.75 0 10-1.498-.058l-.347 9a.75.75 0 001.5.058l.345-9z" clip-rule="evenodd" />
                        </svg>
                        {{ formTranslation.common.delete || 'Delete' }}
                      </button>
                    </div>
                  </div>
                </div>
                <!-- Editing interface -->
                <div v-if="editingCommentId === comment.id" class="mt-3">
                  <textarea
                    class="block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-black mb-3 resize-y"
                    v-model="editedCommentText"
                    rows="3"
                  ></textarea>
                  <div class="flex justify-end gap-2">
                    <button 
                      class="px-3 py-1.5 rounded-lg border border-gray-300 text-sm text-gray-700 hover:bg-gray-50 focus:outline-none"
                      @click="cancelEditComment"
                    >
                      {{ formTranslation.common.cancel || 'Cancel' }}
                    </button>
                    <button 
                      class="px-3 py-1.5 rounded-lg bg-black text-sm text-white hover:bg-opacity-90 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed"
                      @click="saveEditedComment(comment)"
                      :disabled="!editedCommentText.trim() || submitting"
                    >
                      <div v-if="submitting" class="inline-block animate-spin rounded-full h-3 w-3 border-t-2 border-b-2 border-white mr-1.5"></div>
                      <span v-if="submitting">{{ formTranslation.common.saving || 'Saving...' }}</span>
                      <span v-else>{{ formTranslation.common.save || 'Save' }}</span>
                    </button>
                  </div>
                </div>
                <!-- Comment text -->
                <div v-else class="mt-3 text-gray-800 text-sm leading-relaxed whitespace-pre-wrap break-words" v-html="formatCommentText(comment.comment)"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { post, get } from '../../config/request';

export default {
  name: 'TaskComments',
  props: {
    taskId: {
      type: [Number, String],
      required: true
    },
    userData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      submitting: false,
      comments: [],
      newComment: '',
      editingCommentId: null,
      editedCommentText: '',
      activeDropdown: null
    }
  },
  computed: {
    userData() {
      if (
        this.$store.state.userDataModule !== undefined &&
        this.$store.state.userDataModule.user !== undefined
      ) {
        return this.$store.state.userDataModule.user;
      } else {
        return [];
      }
    },
    currentUserName() {
      return this.userData.display_name || this.userData.user_login || 'User';
    },
    
    sortedComments() {
      // Sort comments from newest to oldest
      return [...this.comments].sort((a, b) => {
        return new Date(b.created_at) - new Date(a.created_at);
      });
    }
  },
  mounted() {
    this.fetchComments();
    // Add click event listener to document to close dropdown when clicking outside
    document.addEventListener('click', this.handleClickOutside);
  },
  
  beforeDestroy() {
    // Remove event listener when component is destroyed
    document.removeEventListener('click', this.handleClickOutside);
  },
  methods: {
    async fetchComments() {
      if (!this.taskId) return;
      
      try {
        this.loading = true;
        
        const response = await get("get_task_comments", {
          task_id: this.taskId
        });
        
        if (response && response.data) {
          this.comments = response.data.data || [];
          // Update comment count in the parent component
          this.$emit('comment-added', this.comments);
        } else {
          console.error('Error: Invalid response format or empty response');
        }
      } catch (error) {
        console.error('Error fetching comments:', error);
      } finally {
        this.loading = false;
      }
    },
    
    async addComment() {
      if (!this.newComment.trim()) return;
      
      try {
        this.submitting = true;
        
        const response = await post("add_task_comment", {
          task_id: this.taskId,
          comment: this.newComment.trim()
        });
        
        if (response.data && response.data.status) {
          // Update comments list
          this.comments = response.data.data || [];
          this.newComment = '';
          
          // Emit event to parent with updated comments
          this.$emit('comment-added', this.comments);
        } else {
          this.$swal.fire({
            icon: 'error',
            title: formTranslation.common.error || 'Error',
            text: response.data.message || formTranslation.task.comment_add_error || 'Failed to add comment'
          });
        }
      } catch (error) {
        console.error('Error adding comment:', error);
        this.$swal.fire({
          icon: 'error',
          title: formTranslation.common.error || 'Error',
          text: formTranslation.task.comment_add_error || 'Failed to add comment'
        });
      } finally {
        this.submitting = false;
      }
    },
    
    toggleDropdown(commentId, event) {
      // Stop event propagation to prevent immediate closing
      if (event) {
        event.stopPropagation();
      }
      this.activeDropdown = this.activeDropdown === commentId ? null : commentId;
    },
    
    handleClickOutside(event) {
      // Close dropdown menu when clicking outside
      if (this.activeDropdown !== null) {
        // Check if the click is outside the dropdown
        const dropdownElements = document.querySelectorAll('.dropdown, .dropdown-menu');
        let clickedInside = false;
        
        dropdownElements.forEach(element => {
          if (element.contains(event.target)) {
            clickedInside = true;
          }
        });
        
        if (!clickedInside) {
          this.activeDropdown = null;
        }
      }
    },
    
    editComment(comment) {
      this.activeDropdown = null;
      this.editingCommentId = comment.id;
      this.editedCommentText = comment.comment;
    },
    
    cancelEditComment() {
      this.editingCommentId = null;
      this.editedCommentText = '';
    },
    
    async saveEditedComment(comment) {
      if (!this.editedCommentText.trim()) return;
      
      try {
        this.submitting = true;
        
        const response = await post("add_task_comment", {
          id: comment.id,
          task_id: this.taskId,
          comment: this.editedCommentText.trim()
        });
        
        if (response.data && response.data.status) {
          // Update comments list
          this.comments = response.data.data || [];
          this.cancelEditComment();
          
          // Emit event to parent with updated comments
          this.$emit('comment-added', this.comments);
        } else {
          this.$swal.fire({
            icon: 'error',
            title: formTranslation.common.error || 'Error',
            text: response.data.message || formTranslation.task.comment_update_error || 'Failed to update comment'
          });
        }
      } catch (error) {
        console.error('Error updating comment:', error);
        this.$swal.fire({
          icon: 'error',
          title: formTranslation.common.error || 'Error',
          text: formTranslation.task.comment_update_error || 'Failed to update comment'
        });
      } finally {
        this.submitting = false;
      }
    },
    
    confirmDeleteComment(comment) {
      this.activeDropdown = null;
      
      this.$swal.fire({
        title: formTranslation.task.confirm_delete_comment || 'Confirm Delete Comment',
        text: `${formTranslation.task.delete_comment_confirmation_text || 'Are you sure you want to delete this comment? This action cannot be undone.'}`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: formTranslation.common.delete || 'Delete',
        cancelButtonText: formTranslation.common.cancel || 'Cancel'
      }).then((result) => {
        if (result.isConfirmed) {
          this.executeDeleteComment(comment);
        }
      });
    },
    
    async executeDeleteComment(comment) {
      try {
        const response = await post("delete_task_comment", {
          id: comment.id,
          task_id: this.taskId
        });
        
        if (response.data && response.data.status) {
          // Update comments list
          this.comments = response.data.data || [];
          
          this.$swal.fire({
            icon: 'success',
            title: formTranslation.common.success || 'Success',
            text: formTranslation.task.comment_deleted_successfully || 'Comment deleted successfully',
            showConfirmButton: false,
            timer: 1500
          });
          
          // Emit event to parent with updated comments
          this.$emit('comment-added', this.comments);
        } else {
          this.$swal.fire({
            icon: 'error',
            title: formTranslation.common.error || 'Error',
            text: response.data.message || formTranslation.task.comment_delete_error || 'Failed to delete comment'
          });
        }
      } catch (error) {
        console.error('Error deleting comment:', error);
        this.$swal.fire({
          icon: 'error',
          title: formTranslation.common.error || 'Error',
          text: formTranslation.task.comment_delete_error || 'Failed to delete comment'
        });
      }
    },
    
    canEditComment(comment) {
      let isAdmin = ["clinic_admin", "administrator"].includes(this.getUserRole()) ? true : false;
      let isOwner = parseInt(comment.user_id) === parseInt(this.userData.ID);
      
      return isAdmin || isOwner;
    },
    
    formatDate(dateString) {
      if (!dateString) return '';
      
      const date = new Date(dateString);
      const now = new Date();
      const diffMs = now - date;
      const diffMinutes = Math.floor(diffMs / (1000 * 60));
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
      
      // Format relative time for recent comments
      if (diffMinutes < 1) {
        return formTranslation.common.just_now || 'Just now';
      } else if (diffMinutes < 60) {
        return formTranslation.common.minutes_ago?.replace('{count}', diffMinutes) || `${diffMinutes} minutes ago`;
      } else if (diffHours < 24) {
        return formTranslation.common.hours_ago?.replace('{count}', diffHours) || `${diffHours} hours ago`;
      } else if (diffDays < 7) {
        return formTranslation.common.days_ago?.replace('{count}', diffDays) || `${diffDays} days ago`;
      }
      
      // Format absolute date for older comments
      return new Intl.DateTimeFormat('en', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }).format(date);
    },
    
    getInitials(name) {
      if (!name) return '?';
      
      return name.split(' ')
        .map(word => word.charAt(0).toUpperCase())
        .slice(0, 2)
        .join('');
    },
    
    getAvatarColor(name) {
      if (!name) return '#6c757d';
      
      // Generate consistent color based on name
      let hash = 0;
      for (let i = 0; i < name.length; i++) {
        hash = name.charCodeAt(i) + ((hash << 5) - hash);
      }
      
      const colors = [
        '#3498db', '#2ecc71', '#e74c3c', '#f39c12', '#9b59b6',
        '#1abc9c', '#d35400', '#c0392b', '#16a085', '#8e44ad',
        '#27ae60', '#2980b9', '#f1c40f', '#e67e22', '#ecf0f1'
      ];
      
      return colors[Math.abs(hash) % colors.length];
    },
    
    formatCommentText(text) {
      if (!text) return '';
      
      // Convert URLs to links
      const urlRegex = /(https?:\/\/[^\s]+)/g;
      text = text.replace(urlRegex, url => `<a href="${url}" target="_blank" rel="noopener noreferrer" class="text-black hover:underline">${url}</a>`);
      
      // Convert line breaks to <br>
      text = text.replace(/\n/g, '<br>');
      
      return text;
    },
    
    getUserRole() {
      // Implement role retrieval similar to attachments component
      // This might need to be passed as a prop or retrieved from userData
      return this.userData.role || '';
    }
  }
}
</script>