<template>
    <div class="min-h-screen p-6 bg-gradient-to-br from-pink-50 to-purple-50">
      <!-- Header Section -->
      <div class="mb-8 flex justify-between items-center">
        <div class="flex items-center gap-4">
          <h1 class="text-2xl font-semibold text-gray-800">{{ cardTitle }}</h1>
        </div>
      </div>
  
      <!-- Main Content -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <!-- Form Content -->
        <div class="p-6">
          <!-- Current Password -->
          <div class="mb-6">
            <div class="w-full md:w-1/2">
              <label 
                for="current_password" 
                class="block text-xs font-medium text-gray-500 uppercase tracking-wider mb-2"
              >
                {{ formTranslation.widgets.current_pwd }}
              </label>
              <div class="relative">
                <input
                  v-model="passwordChangeRequest.currentPassword"
                  :type="currentPasswordType"
                  id="current_password"
                  :placeholder="formTranslation.change_password.plh_old_pwd"
                  class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
                  :class="{ 'border-red-500': submitted && !$v.passwordChangeRequest.currentPassword.required }"
                />
                <button
                  type="button"
                  class="absolute inset-y-0 right-0 pr-3 flex items-center"
                  @click="viewPassword('current_password')"
                >
                  <i :class="currentPasswordIcon" class="text-gray-400"></i>
                </button>
              </div>
              <div 
                v-if="submitted && !$v.passwordChangeRequest.currentPassword.required"
                class="mt-1 text-sm text-red-600"
              >
                {{formTranslation.widgets.current_pwd_required}}
              </div>
            </div>
          </div>
  
          <!-- New Password -->
          <div class="mb-6">
            <div class="w-full md:w-1/2">
              <label 
                for="new_password" 
                class="block text-xs font-medium text-gray-500 uppercase tracking-wider mb-2"
              >
                {{ formTranslation.change_password.plh_new_password }}
              </label>
              <div class="relative">
                <input
                  v-model="passwordChangeRequest.newPassword"
                  :type="newPasswordType"
                  id="new_password"
                  :placeholder="formTranslation.change_password.plh_new_pwd"
                  class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
                  :class="{ 'border-red-500': submitted && !$v.passwordChangeRequest.newPassword.required }"
                />
                <button
                  type="button"
                  class="absolute inset-y-0 right-0 pr-3 flex items-center"
                  @click="viewPassword('new_password')"
                >
                  <i :class="newPasswordIcon" class="text-gray-400"></i>
                </button>
              </div>
              <div 
                v-if="submitted && !$v.passwordChangeRequest.newPassword.required"
                class="mt-1 text-sm text-red-600"
              >
                {{formTranslation.widgets.new_pwd_required}}
              </div>
            </div>
          </div>
  
          <!-- Confirm Password -->
          <div class="mb-6">
            <div class="w-full md:w-1/2">
              <label 
                for="confirm_password" 
                class="block text-xs font-medium text-gray-500 uppercase tracking-wider mb-2"
              >
                {{ formTranslation.change_password.plh_confirm_password }}
              </label>
              <div class="relative">
                <input
                  v-model="passwordChangeRequest.confirmPassword"
                  :type="confirmPasswordType"
                  id="confirm_password"
                  :placeholder="formTranslation.change_password.plh_confirm_pwd"
                  class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
                  :class="{ 'border-red-500': submitted && !$v.passwordChangeRequest.confirmPassword.required }"
                />
                <button
                  type="button"
                  class="absolute inset-y-0 right-0 pr-3 flex items-center"
                  @click="viewPassword('confirm_password')"
                >
                  <i :class="confirmPasswordIcon" class="text-gray-400"></i>
                </button>
              </div>
              <div 
                v-if="submitted && !$v.passwordChangeRequest.confirmPassword.required"
                class="mt-1 text-sm text-red-600"
              >
                {{formTranslation.widgets.confirm_pwd_required}}
              </div>
              <div 
                v-if="submitted && !isConfimMatched" 
                class="mt-1 text-sm text-red-600"
              >
                {{formTranslation.common.pwd_not_match}}
              </div>
            </div>
          </div>
        </div>
  
        <!-- Footer -->
        <div class="bg-white px-4 py-3 flex items-center justify-end border-t border-gray-200">
          <button
            type="button"
            class="px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 flex items-center gap-2"
            @click="saveChangePassword"
            :disabled="loading"
          >
            <span>{{formTranslation.common.change_password}}</span>
          </button>
        </div>
      </div>
    </div>
  </template>
  
  <script>
  import { post } from "../../config/request";
  import { required } from "vuelidate/lib/validators";
  
  export default {
    validations: {
      passwordChangeRequest: {
        currentPassword: { required },
        newPassword: { required },
        confirmPassword: { required }
      },
    },
    data: () => ({
      submitted: false,
      passwordChangeRequest: {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      emailChangeRequest: {
        newEmail: ''
      },
      cardTitle: 'Change Password',
      currentPasswordType: 'password',
      newPasswordType: 'password',
      confirmPasswordType: 'password',
      currentPasswordIcon: 'fa fa-eye',
      newPasswordIcon: 'fa fa-eye',
      confirmPasswordIcon: 'fa fa-eye',
      loading: false
    }),
    
    mounted() {
      this.init();
    },
  
    methods: {
      init() {
        this.cardTitle = this.formTranslation.common.change_password;
      },
  
      saveChangePassword() {
        this.submitted = true;
        this.loading = true;
  
        this.$v.$touch();
  
        if (this.$v.passwordChangeRequest.$invalid) {
          this.loading = false;
          return;
        }
  
        if (!this.isConfimMatched) {
          this.loading = false;
          return false;
        }
  
        post('change_password', this.passwordChangeRequest)
          .then((response) => {
            this.loading = false;
            this.submitted = false;
            if (response.data.status !== undefined && response.data.status === true) {
              this.displayMessage(response.data.message);
              setTimeout(() => {
                location.reload();
              }, 1000);
            } else {
              this.displayErrorMessage(response.data.message);
            }
          })
          .catch((error) => {
            console.log(error);
            this.loading = false;
            this.submitted = false;
            this.displayErrorMessage(this.formTranslation.common.internal_server_error);
          });
      },
  
      viewPassword(value) {
        switch(value) {
          case 'current_password':
            this.currentPasswordType = this.currentPasswordType === 'password' ? 'text' : 'password';
            this.currentPasswordIcon = this.currentPasswordType === 'password' ? 'fa fa-eye' : 'fa fa-eye-slash';
            break;
          case 'new_password':
            this.newPasswordType = this.newPasswordType === 'password' ? 'text' : 'password';
            this.newPasswordIcon = this.newPasswordType === 'password' ? 'fa fa-eye' : 'fa fa-eye-slash';
            break;
          case 'confirm_password':
            this.confirmPasswordType = this.confirmPasswordType === 'password' ? 'text' : 'password';
            this.confirmPasswordIcon = this.confirmPasswordType === 'password' ? 'fa fa-eye' : 'fa fa-eye-slash';
            break;
        }
      },
    },
  
    computed: {
      isConfimMatched() {
        return this.passwordChangeRequest.newPassword === this.passwordChangeRequest.confirmPassword;
      }
    }
  };
  </script>