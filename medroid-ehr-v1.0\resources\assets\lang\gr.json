{"common": {"kivicare_Management_system": "KiviCare - Σύστημα Διαχείρισης Κλινικής & Ασθενών (EHR)", "no_appointments": "Δεν βρέθηκαν ραντεβού", "loading": "Φόρτωση ...", "cancel": "Ακύρωση", "date": "Ημερομηνία", "close": "Κλείσιμο", "closed": "Κλειστό", "select_option": "- Επιλογή επιλογής -", "all": "Όλα", "back_to_wordpress": "Επιστροφή στο Wordpress ", "update": "Δεν βρέθηκαν ραντεβού0", "my_profile": "Δεν βρέθηκαν ραντεβού1", "change_password": "Δεν βρέθηκαν ραντεβού2", "logout": "Δεν βρέθηκαν ραντεβού3", "full_screen": "Δεν βρέθηκαν ραντεβού4", "warning_zoom_configuration": "Δεν βρέθηκαν ραντεβού5", "zoom_configuration_link": "Δεν βρέθηκαν ραντεβού6", "dob": "Δεν βρέθηκαν ραντεβού7", "dob_required": "Δεν βρέθηκαν ραντεβού8", "gender": "Δεν βρέθηκαν ραντεβού9", "gender_required": "Φόρτωση ...0", "male": "Φόρτωση ...1", "female": "Φόρτωση ...2", "other": "Φόρτωση ...3", "service": "Φόρτωση ...4", "services": "Φόρτωση ...5", "sr_no": "Φόρτωση ...6", "item_name": "Φόρτωση ...7", "price": "Φόρτωση ...8", "quantity": "Φόρτωση ...9", "total": "Ακύρωση0", "no_records_found": "Ακύρωση1", "_note": "Ακύρωση2", "note": "Ακύρωση3", "status": "Ακύρωση4", "action": "Ακύρωση5", "title": "Ακύρωση6", "name": "Ακύρωση7", "doctor": "Ακύρωση8", "doctors": "Ακύρωση9", "patient": "Ημερομηνία0", "fname": "Ημερομηνία1", "fname_required": "Ημερομηνία2", "lname": "Ημερομηνία3", "lname_required": "Ημερομηνία4", "email": "Ημερομηνία5", "email_required": "Ημερομηνία6", "password": "Ημερομηνία7", "pwd_required": "Ημερομηνία8", "repeat_pwd": "Ημερομηνία9", "repeat_password_required": "Κλείσιμο0", "pwd_not_match": "Κλείσιμο1", "login_btn": "Κλείσιμο2", "sign_up": "Κλείσιμο3", "no": "Κλείσιμο4", "dr": "Dr.", "filters": "φίλτρα", "back": "Πίσω", "save": "Αποθήκευση", "invalid_email": "Μη έγκυρη μορφή email", "active": "Ενεργή", "inactive": "Ανενεργή", "name_required": "Απαιτείται όνομα", "email_address": "Διεύθυνση email", "contact_info": "Στοιχεία επικοινωνίας", "settings": "φίλτρα0", "fname_validation_1": "φίλτρα1", "fname_validation_2": "φίλτρα2", "lname_validation_1": "φίλτρα3", "lname_validation_2": "φίλτρα4", "contact": "φίλτρα5", "contact_required": "φίλτρα6", "contact_validation_1": "φίλτρα7", "contact_validation_2": "φίλτρα8", "telemed": "φίλτρα9", "to": "Πίσω0", "time": "Πίσω1", "contact_no": "Πίσω2", "contact_num_required": "Πίσω3", "city": "Πίσω4", "city_required": "Πίσω5", "city_validation_1": "Πίσω6", "city_validation_2": "Πίσω7", "state": "Πίσω8", "state_validation_1": "Πίσω9", "state_validation_2": "Αποθήκευση0", "country": "Αποθήκευση1", "country_required": "Αποθήκευση2", "country_validation_1": "Αποθήκευση3", "country_validation_2": "Αποθήκευση4", "address": "Αποθήκευση5", "address_required": "Αποθήκευση6", "postal_code": "Αποθήκευση7", "postal_code_required": "Αποθήκευση8", "postal_code_validation_1": "Αποθήκευση9", "postal_code_validation_2": "Μη έγκυρη μορφή email0", "profile": "Μη έγκυρη μορφή email1", "static_data": "Μη έγκυρη μορφή email2", "handle_request": "Μη έγκυρη μορφή email3", "email_to_get_help": "Μη έγκυρη μορφή email4", "note_options": "Μη έγκυρη μορφή email5", "note_1": "Μη έγκυρη μορφή email6", "note_2": "Μη έγκυρη μορφή email7", "wp_rollback": "Μη έγκυρη μορφή email8", "plugin": "Μη έγκυρη μορφή email9", "keep_improving": "Ενεργή0", "currency_setting": "Ενεργή1", "module": "Ενεργή2", "i_understand": "Ενεργή3", "version": "Ενεργή4", "read_notice": "Διαβάστε αυτό το παρακάτω αρχεί<PERSON> καταγραφής πριν προχωρήσετε:", "faced_issue": "Αντιμετωπίζετε προβλήματα;", "if_use_older_version": "Εάν αντιμετωπίζετε προβλήματα με αυτήν την έκδοση και θέλετε να συνεχίσετε με την παλιά έκδοση, εγκαταστήστε και χρησιμοποιήστε", "check_video": "Για ομαλή μετάβαση σε νέα έκδοση, ελέγξτε τον ακόλουθο Οδηγό βίντεο: ", "kivicare_v2": "Kivicare Upgrade V2.0.0", "appointment_flow": "Ροή ραντεβού", "basic_details": "Βα<PERSON>ι<PERSON><PERSON><PERSON> λεπτομέρειες"}, "dashboard": {"dashboard": "Π<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ελέγχου", "total_patients": "Σύνολο ασθενών", "total_visited_patients": "Σύνολο επισκέψεων ασθενών", "total_doctors": "Σύν<PERSON>λ<PERSON> ιατρών", "total_clinic_doctors": "Σύν<PERSON>λ<PERSON> ιατρών κλινικής", "total_appointments": "Σύνολο ραντεβού", "total_clinic_appointments": "Σύνολο ραντεβού στην κλινική", "latest_appointments": "Τελευτα<PERSON>α ραντεβού", "reload": "Φόρτωση", "view_all": "Προβολή όλων", "weekly_appointments": "Σύνολο ασθενών0", "weekly_total_appointments": "Σύνολο ασθενών1", "today_appointment_list": "Σύνολο ασθενών2", "total_revenue": "Σύνολο ασθενών3", "total_clinic_revenue": "Σύνολο ασθενών4", "total_generated_revenue": "Σύνολο ασθενών5", "filter": "Σύνολο ασθενών6", "reset": "Σύνολο ασθενών7", "total_today_appointments": "Σύνολο ασθενών8", "total_service": "Σύνολο ασθενών9", "patients": "Σύνολο επισκέψεων ασθενών0", "medical_dashboard": "Σύνολο επισκέψεων ασθενών1"}, "doctor": {"doctor_name": "Όνομα γιατρού", "doctor_specialization_required": "Απαιτείτ<PERSON>ι εξειδίκευση γιατρού", "experience_year": "Εμπειρία (σε έτος)", "address_details": "Λεπτομέρειες διεύθυνσης", "degree": "Πτυ<PERSON><PERSON><PERSON>", "degree_required": "Απαιτείται πτυχίο", "degree_validation_1": "Πτυ<PERSON><PERSON><PERSON> επιτρέπεται μόνο τιμή χαρακτήρων", "university": "Πανεπιστήμιο", "university_required": "Πανεπιστήμιο απαιτείται", "university_validation": "Πανεπιστή<PERSON>ι<PERSON> επιτρέπεται μόνο τιμή χαρακτήρα ", "year": "Απαιτείτ<PERSON><PERSON> εξειδίκευση γιατρού0", "select_year": "Απαιτείτ<PERSON>ι εξειδίκευση γιατρού1", "year_required": "Απαιτείτ<PERSON>ι εξειδίκευση γιατρού2", "college_university": "Απαιτείτ<PERSON><PERSON> εξειδίκευση γιατρού3", "api_key": "Απαιτείτ<PERSON><PERSON> εξειδίκευση γιατρού4", "api_secret": "Απαιτεί<PERSON><PERSON><PERSON> εξειδίκευση γιατρού5", "api_secret_required": "Απαιτεί<PERSON><PERSON><PERSON> εξειδίκευση γιατρού6", "api_key_required": "Απαιτεί<PERSON><PERSON><PERSON> εξειδίκευση γιατρού7", "zoom_configuration_guide": "Απαιτεί<PERSON><PERSON><PERSON> εξειδίκευση γιατρού8", "zoom_step1": "Απαιτείτ<PERSON><PERSON> εξειδίκευση γιατρού9", "zoom_step2": "Εμπειρία (σε έτος)0", "zoom_step3": "Εμπειρία (σε έτος)1", "zoom_step4": "Εμπειρία (σε έτος)2", "zoom_step5": "Εμπειρία (σε έτος)3", "other_detail": "Εμπειρία (σε έτος)4", "consultation_fees": "Εμπειρία (σε έτος)5", "video_consultation_fees": "Εμπειρία (σε έτος)6", "doctor_fees_required": "Εμπειρία (σε έτος)7", "zoom_market_place_portal": "Εμπειρία (σε έτος)8", "create_app": "Εμπειρία (σε έτος)9", "doctors_list": "Λεπτομέρειες διεύθυνσης0", "other_details": "Λεπτομέρειες διεύθυνσης1", "extra_detail": "Λεπτομέρειες διεύθυνσης2", "add_doctor": "Λεπτομέρειες διεύθυνσης3", "edit_profile": "Λεπτομέρειες διεύθυνσης4", "basic_information": "Λεπτομέρειες διεύθυνσης5", "basic_settings": "Λεπτομέρειες διεύθυνσης6", "type": "Λεπτομέρειες διεύθυνσης7", "type_required": "Απαιτείται τύπος", "fees_type": "Τύ<PERSON><PERSON> αμοιβής", "range": "Εύρ<PERSON>", "fixed": "Διορθώθηκε", "fees": "Τέλη", "fees_type_required": "Απαιτείται τύπος αμοιβής", "doc_fee_required": "Απαιτείται αμοιβή γιατρού", "doc_fee_validation_1": "Οι αμοιβές γιατρού πρέπει να είναι μεγαλύτερες από μηδέν", "doc_fee_validation_2": "Οι αμοιβές γιατρού πρέπει να κυμαίνονται μεταξύ 0 έως 1000000000000000000", "doc_fee_validation_3": " Για αμοιβές γιατρού Απαιτούνται ελάχιστες αμοιβές και μέγιστες αμοιβές", "doc_fee_validation_4": "Τύπος αμοιβής0", "doc_fee_validation_5": "Τύπος αμοιβής1", "doc_fee_validation_6": "Τύπος αμοιβής2", "qualification_information": "Τύπος αμοιβής3", "qualification_speciality_details": "Τύπος αμοιβής4", "doctor_working_days_sessions": "Τύπος αμοιβής5", "charge_n_doc_selection": "Τύ<PERSON>ος αμοιβής6", "doc_field_customization": "Τύ<PERSON>ος αμοιβής7"}, "patient": {"patient_name": "Όνομα ασθενούς", "add_patient": "Προσθήκη ασθενούς", "patients_lists": "Λίστ<PERSON><PERSON> ασθενών", "medical_report": "Ιατρική αναφορά", "add_medical_report": "Προσθήκη ιατρικής αναφοράς", "upload_report": "Μεταφόρτωση αναφοράς"}, "clinic": {"clinic": "Κλινική", "receptionist": "Ρεσεψιονίστ", "receptionists_list": "Λίστα ρεσεψιονίστ", "add_receptionist": "Προσθήκη ρεσεψιονίστ", "clinic_name": "Όνομα κλινικής", "clinic_info": "Πληροφορίες κλινικής", "clinic_profile": "Προφίλ κλινικής", "add_clinic": "Προσθήκη κλινικής", "admin_profile": "Προ<PERSON>ί<PERSON> διαχειριστή", "clinic_admin_detail": "Λεπτομέρεια διαχειριστή κλινικής", "clinic_name_validation_1": "Ρεσεψιονίστ0", "clinic_name_validation_2": "Ρεσεψιονίστ1", "select_clinic": "Ρεσεψιονίστ2", "speciality": "Ρεσεψιονίστ3", "specialties": "Ρεσεψιονίστ4", "specialities": "Ρεσεψιονίστ5", "note_specialization": "Ρεσεψιονίστ6", "clinic_specialities_required": "Ρεσεψιονίστ7", "currency_prefix": "Ρεσεψιονίστ8", "currency_postfix": "Ρεσεψιονίστ9", "currency_decimals": "Λίστα ρεσεψιονίστ0", "profile_img": "Λίστα ρεσεψιονίστ1", "doctor_record_not_found": "Λίστα ρεσεψιονίστ2", "blood_group": "Λίστα ρεσεψιονίστ3", "select_blood_group": "Λίστα ρεσεψιονίστ4", "update_profile": "Λίστα ρεσεψιονίστ5"}, "appointments": {"appointment": "Ραντεβού", "appointments": "Ραντεβού", "description": "Περιγραφή", "booked": "Κράτηση", "cancelled": "Ακυρώθηκε", "arrived": "Άφιξη", "check_in": "Άφιξη", "check_out": "Αναχώρηση", "start": "Έναρξη", "join": "Συμμετοχή", "doc_required": "Ραντεβού0", "visit_type_required": "Ραντεβού1", "appointment_date": "Ραντεβού2", "appointment_date_required": "Ραντεβού3", "select_status": "Ραντεβού4", "status_required": "Ραντεβού5", "available_slot": "Ραντεβού6", "session": "Ραντεβού7", "no_time_slots_found": "Ραντεβού8", "time_slot_required": "Ραντεβού9", "appointment_details": "Περιγραφή0", "appointment_type": "Περιγραφή1", "completed": "Περιγραφή2", "appointment_time": "Περιγραφή3", "appointment_time_required": "Περιγραφή4", "book_appointment": "Περιγραφή5", "today_appointment": "Περιγραφή6", "tomorrow_appointment": "Περιγραφή7", "appointment_booking": "Περιγραφή8", "available_appointments_on": "Περιγραφή9", "appointment_visit_type_required": "Κράτηση0", "appointment_detail": "Κράτηση1", "save_appointment": "Κράτηση2", "appointment_list": "Κράτηση3"}, "clinic_schedule": {"schedule": "Πρόγραμμα", "holiday_of": "Αργία", "module_type_required": "Απαιτείται τύπος ενότητας", "schedule_date": "Ημερομηνία προγραμματισμού", "schedule_date_required": "Απαιτεί<PERSON><PERSON>ι ημερομηνία προγραμματισμού", "holiday_list": "Λίστα διακοπών"}, "doctor_session": {"doc_sessions": "Για συνεδρίες γιατρού", "session_doc_required": "Απαιτείτ<PERSON><PERSON> γιατρός συνεδρίας", "doc_already_added": "Ο επιλεγμένος γιατρ<PERSON>ς έχει ήδη προστεθεί σε άλλη συνεδρία", "week_days": "Ημέρες εβδομάδας", "days_required": "Απαιτούνται ημέρες", "days_already_exist": "Οι επιλεγμένες ημέρες υπάρχουν ήδη στην άλλη συνεδρία", "morning_session": "Πρωινή συνεδρία", "start_time_required": "Απαιτείτα<PERSON> ώρα έναρξης", "start_time_smaller_then_end": "Η ώρα έναρξης πρέπει να είναι μικρότερη και η ώρα λήξης", "end_time_required": "Απαιτείται ώρα λήξης", "end_time_bigger_then_start": "Απαιτείτ<PERSON><PERSON> γιατρός συνεδρίας0", "evening_session": "Απαιτείτ<PERSON><PERSON> γιατρός συνεδρίας1", "start_time_smaller_then_first_session_end_time": "Απαιτείτ<PERSON><PERSON> γιατρός συνεδρίας2", "set_session_for_doc": "Απαιτείτ<PERSON><PERSON> γιατρός συνεδρίας3"}, "patient_encounter": {"encounters": "Συναντή<PERSON><PERSON>ις", "encounter_dashboard": "Πί<PERSON><PERSON><PERSON><PERSON><PERSON> ελέγχου αντιμετώπισης", "is_required": "απαιτείται", "note_prescription": "Σημείωση: Πληκτρολογήστε και πατήστε enter για να δημιουργήσετε νέα συνταγή", "frequency": "Συχνότητα", "frequency_required": "Απαιτείτ<PERSON>ι συχνότητα", "duration_Days": "Διάρκεια (σε ημέρες)", "duration_required": "Απαιτείται διάρκεια", "instruction": "Οδηγίες", "duration": "Διάρκεια", "no_prescription_found": "Πί<PERSON><PERSON><PERSON><PERSON><PERSON> ελέγχου αντιμετώπισης0", "add_prescription": "Πίν<PERSON><PERSON><PERSON><PERSON> ελέγχου αντιμετώπισης1", "encounter_date": "Πίν<PERSON><PERSON><PERSON><PERSON> ελέγχου αντιμετώπισης2", "encounter_date_required": "Πίν<PERSON><PERSON><PERSON><PERSON> ελέγχου αντιμετώπισης3", "encounter_module": "Πίν<PERSON><PERSON><PERSON><PERSON> ελέγχου αντιμετώπισης4", "prescription": "Πί<PERSON><PERSON><PERSON><PERSON><PERSON> ελέγχου αντιμετώπισης5", "encounter_details": "Πί<PERSON><PERSON><PERSON><PERSON><PERSON> ελέγχου αντιμετώπισης6"}, "medical_records": {"problem_type": "Τύ<PERSON>ος προβλήματος", "problem_start_date_required": "Απαιτε<PERSON><PERSON><PERSON><PERSON> ημερομηνία έναρξης προβλήματος", "problem_start_date": "Ημερομηνία έναρξης προβλήματος", "problem_end_date": "Ημερομηνία λήξης προβλήματος", "problem_outcome": "Αποτέλεσμα προβλήματος", "medical_records": "Ιατρικά αρχεία", "add_medical_problems": "Προσθήκη ιατρικών προβλημάτων"}, "reports": {"reports": "Αναφορές", "filter_by": "Φίλτρ<PERSON> κατά", "clinic_revenue_overall": "Έσοδα κλινικής (Συνολικά)", "clinic_revenue_detail": "Έσοδα κλινικής (Λεπτομέρεια)", "clinic_doctor_revenue": "Έσοδα κλινικού γιατρού", "prescription_module": "Ενότητα συνταγογράφησης", "report_required": "Απαιτε<PERSON><PERSON><PERSON><PERSON> αναφορά."}, "patient_front_widget": {"specialization": "Ειδίκευση", "username_email": "Όνομα χρήστη ή Email", "fill_form": "Συμπληρώστε αυτήν τη φόρμα για να δημιουργήσετε λογαριασμό."}, "service": {"service_list": "Λίστα υπηρεσιών", "service_category": "Κατηγορία υπηρεσίας", "service_category_required": "Απαιτ<PERSON><PERSON><PERSON><PERSON><PERSON> κατηγορία υπηρεσίας", "note_category": "Σημείωση: Πληκτρολογήστε και πατήστε enter για να προσθέσετε νέα κατηγορία", "service_name": "Όνομα υπηρεσίας", "service_name_required": "Απαιτεί<PERSON><PERSON><PERSON> όνομα υπηρεσίας", "service_validation": "Το μήκος του ονόματος υπηρεσίας πρέπει να είναι μεταξύ 2 έως 100 χαρακτήρων", "charges": "Χρε<PERSON><PERSON><PERSON><PERSON>ς ", "service_charge": "χρέωση υπηρεσίας από τη μονάδα σέρβις. ", "service_charges_required": "Απαιτείται χρέωση υπηρεσίας", "service_charge_length": "Κατηγορία υπηρεσίας0", "select_all": "Κατηγορία υπηρεσίας1"}, "patient_bill": {"invoice_id": "Αναγνω<PERSON>ιστικ<PERSON> τιμολογίου:", "created_at": "Δημιουργήθηκε στις:", "payment_status": "Κατάσταση πληρωμής:", "paid": "Πληρωμή", "unpaid": "<PERSON><PERSON><PERSON><PERSON><PERSON> πληρωμή", "patient_details": "Λεπτομέρειες ασθενούς", "amount_due": "Οφειλόμενο ποσό", "print": "Εκτύπωση", "service_required": "Απαιτείται υπηρεσία", "price_required": "Απαιτείται τιμή", "prize_greater_then_0": "Δημιουργήθηκε στις:0", "prize_between_number": "Δημιουργήθηκε στις:1", "quantity_required": "Δημιουργήθηκε στις:2", "please_add_bill_items": "Δημιουργήθηκε στις:3", "bill_total_required": "Δημιουργήθηκε στις:4", "discount": "Δημιουργήθηκε στις:5", "discount_amount": "Δημιουργήθηκε στις:6", "discount_required": "Δημιουργήθηκε στις:7", "discount_greater_then_0": "Δημιουργήθηκε στις:8", "discount_less_then_total_bill_amount": "Δημιουργήθηκε στις:9", "payable_amount": "Κατάσταση πληρωμής:0", "bill_title": "Κατάσταση πληρωμής:1", "bill_title_required": "Κατάσταση πληρωμής:2", "bill_items": "Κατάσταση πληρωμής:3", "grand_total": "Κατάσταση πληρωμής:4", "grand_total_required": "Κατάσταση πληρωμής:5", "print_bill": "Κατάσταση πληρωμής:6", "billing_records": "Κατάσταση πληρωμής:7", "add_bill": "Κατάσταση πληρωμής:8", "patient_required": "Κατάσταση πληρωμής:9", "encounter_close": "Πληρωμή0", "bill_details": "Πληρωμή1", "other_info": "Πληρωμή2", "patients_encounter_list": "Πληρωμή3", "bills": "Πληρωμή4", "payment_setting": "Πληρωμή5", "woocommerce_payment_gateway": "Πληρωμή6", "amount": "Πληρωμή7", "items": "Πληρωμή8", "notes": "Πληρωμή9", "invoice_n_payment": "<PERSON><PERSON><PERSON><PERSON><PERSON> πληρωμή0", "currency": "<PERSON><PERSON><PERSON><PERSON><PERSON> πληρωμή1"}, "settings": {"general": "Γενικά", "holidays": "Διακ<PERSON><PERSON><PERSON>ς", "configurations": "Διαμορφώσεις", "email_template": "Πρότυπο email", "sms_template": "Πρότυπο SMS", "listings": "Λίστα", "custom_field": "Προσαρμοσμένο πεδίο", "payment": "Πληρωμή", "new_setting": "Νέες βελτιωμένες ρυθμίσεις με διάφορες ρυθμίσεις όπως email, τιμολόγιο, νόμισμα κ.λπ.", "pro_settings": "Ρυθμίσεις Pro", "language_settings": "Ρυθμίσεις γλώσσας"}, "pro_setting": {"set_site_logo": "Ορισμ<PERSON><PERSON> λογότυπου ιστότοπου", "set_language": "Ορισμ<PERSON>ς γλώσσας", "set_theme_color": "Ορισμός χρώματος θέματος", "rtl_mode": "Λειτουργία RTL", "on": "on", "twilo_sms_configration": "Twilo SMS Configration", "account_sid": "ACCOUNT SID", "auth_token": "AUTH TOKEN", "phone_number": "PHONE NUMBER (προαιρετικό)", "twilo_sms_guide": "Twilo SMS guide", "twilio_step_1": "Ορισμ<PERSON>ς γλώσσας0", "twilo_sms_portal": "Ορισμός γλώσσας1", "twilio_step_2": "Ορισμός γλώσσας2", "get_console": "Ορισμός γλώσσας3", "unique_sid": "Ορισμ<PERSON>ς γλώσσας4", "twilio_step_3": "Ορισμ<PERSON>ς γλώσσας5", "twilio_step_4": "Ορισμ<PERSON>ς γλώσσας6", "head_on_console": "Ορισμ<PERSON>ς γλώσσας7", "phone_msg_sid": "Ορισμ<PERSON><PERSON> γλώσσας8"}, "custom_field": {"label_name_required": "Απαιτείτ<PERSON>ι όνομα ετικέτας", "label_name_validation": "Το όνομα της ετικέτας επιτρέπει μόνο αλφαβητική τιμή", "where_it_look_like": "Πού μοιάζει", "shows_in_doc_creation_form": "Εμφανίζεται σε μορφή δημιουργίας γιατρού", "shows_in_patient_encounter_dashboard": "Εμφαν<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> στον πίνακα ελέγχου συνάντησης ασθενούς", "shows_in_patient_creation_form": "Εμφανίζ<PERSON>ται σε μορφή δημιουργ<PERSON><PERSON>ς ασθενούς", "filed_name": "Όνομα αρχειοθέτησης:", "invalid_label_name": " Μη έγκυρο όνομα ετικέτας", "label_required": "Απαιτείται ετικέτα", "field_name_used": "Το όνομα πεδίου χρησιμοποιείται ήδη.", "input_type": "Το όνομα της ετικέτας επιτρέπει μόνο αλφαβητική τιμή0", "input_type_required": "Το όνομα της ετικέτας επιτρέπει μόνο αλφαβητική τιμή1", "placeholder": "Το όνομα της ετικέτας επιτρέπει μόνο αλφαβητική τιμή2", "options": "Το όνομα της ετικέτας επιτρέπει μόνο αλφαβητική τιμή3", "validation": "Το όνομα της ετικέτας επιτρέπει μόνο αλφαβητική τιμή4", "mandatory_field": "Το όνομα της ετικέτας επιτρέπει μόνο αλφαβητική τιμή5", "custom_field_list": "Το όνομα της ετικέτας επιτρέπει μόνο αλφαβητική τιμή6", "add_custom_field": "Το όνομα της ετικέτας επιτρέπει μόνο αλφαβητική τιμή7"}, "setup_wizard": {"previous": "Προηγούμενο", "add_session_details": "Προσθήκη λεπτομερειών συνεδρίας", "session_doctors": "Γιατροί συνεδρίας", "days": "Ημέρες", "no_sessions_found": "Δεν βρέθηκαν συνεδρίες", "time_slot_minute": "<PERSON>ρ<PERSON><PERSON><PERSON> χρόνου (σε λεπτό)", "open_time": "<PERSON><PERSON><PERSON><PERSON><PERSON> ανοίγματος", "close_time": "<PERSON><PERSON><PERSON><PERSON><PERSON> κλεισίματος", "session_demo": "Επίδειξη συνεδρίας", "invalid_time_slot": "Βρέθηκε μη έγκυρος χρονικός χρόνος. ο μη έγκυρος χρόνος υποδοχής είναι", "doctor_list": "Προσθήκη λεπτομερειών συνεδρίας0", "kivicare_ehr": "Προσθήκη λεπτομερειών συνεδρίας1", "prev": "Προσθήκη λεπτομερειών συνεδρίας2"}, "notification": {"notification": "Αποστολή δοκιμαστικού email", "test_sender_email_required": "Απαιτεί<PERSON><PERSON><PERSON> δοκιμαστικό email αποστολέα", "test_content": "Περιεχόμενο δοκιμής", "test_content_required": "Απαιτείτ<PERSON><PERSON> δοκιμαστικό περιεχόμενο", "email_notification": "Ενεργοποίηση / Απενεργοποίηση ειδοποίησης email.", "forbidden_403": "403 | απαγορευμένος"}, "static_data": {"listing_data": "Δεδομένα καταχώρησης", "terms_n_condition": "Όροι και προϋποθέσεις", "version_update": "Ενημέρωση έκδοσης (V2.0.0)", "new_filters_n_view": "Νέα βελτιωμένα φίλτρα και προβολή", "booking_widget_updated": "Το widget κράτησης ενημερώνεται", "visit_type_replaced": "Ο τύπος επίσκεψης αντικαθίσταται με υπηρεσίες (ελέγξτε την καρτέλα υπηρεσίας)", "appointment_flow_update": "Έλεγχος ραντεβού- ενημέρωση ροής και αναχώρησης"}, "widgets": {"doc_not_found": "Ο γιατρός δεν βρέθηκε", "zoom_config": "Διαμόρφωση ζουμ", "terms_condition": "Όροι και προϋποθέσεις", "date_required": "Απαιτείτ<PERSON>ι ημερομηνία", "current_pwd": "Τρέχων κωδικ<PERSON>ς πρόσβασης", "current_pwd_required": "Απαιτείται τρέχων κωδικός πρόσβασης", "new_pwd": "<PERSON><PERSON><PERSON> κωδικ<PERSON>ς πρόσβασης", "appointment_info": "Πληροφορίες ραντεβού", "available_slots": "Διαθέσιμοι κουλοχέρηδες", "service_detail": "Λεπτομέρειες υπηρεσίας", "no_service_detail_found": "Διαμόρφωση ζουμ0", "book_now": "Διαμόρφωση ζουμ1", "registration_success": "Διαμόρφωση ζουμ2", "more_detail": "Διαμόρφωση ζουμ3", "username_email_required": "Διαμόρφωση ζουμ4", "new_pwd_required": "Διαμόρφωση ζουμ5", "confirm_pwd": "Διαμόρφωση ζουμ6", "confirm_pwd_required": "Διαμόρφωση ζουμ7", "pwd_validation": "Διαμόρφωση ζουμ8", "home": "Διαμόρφωση ζουμ9", "change_pwd": "Όροι και προϋποθέσεις0", "logging_out": "Όροι και προϋποθέσεις1", "total_visits": "Όροι και προϋποθέσεις2", "upcoming_visits": "Όροι και προϋποθέσεις3", "example_component": "Όροι και προϋποθέσεις4", "email_to_get_help_1": "Όροι και προϋποθέσεις5", "email_to_get_help_2": "Όροι και προϋποθέσεις6", "email_to_get_help_3": "Όροι και προϋποθέσεις7", "feedback_note": "Όροι και προϋποθέσεις8", "imp_version_update": "Όροι και προϋποθέσεις9", "replace_appointment": "Απαιτεί<PERSON><PERSON><PERSON> ημερομηνία0", "option_as": "Απαιτείτ<PERSON>ι ημερομηνία1", "service_type": "Απαιτείτ<PERSON>ι ημερομηνία2", "add_charges": "Απαιτείτ<PERSON>ι ημερομηνία3", "manage_doctor": "Απαιτεί<PERSON><PERSON><PERSON> ημερομηνία4", "send_test_email": "Απαιτεί<PERSON><PERSON><PERSON> ημερομηνία5"}}