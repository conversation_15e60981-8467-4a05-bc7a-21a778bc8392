<template>
  <!-- Exact KiviCare MedicalRecordForm Component -->
  <div class="bg-white rounded border p-3">
    <div class="flex justify-between items-center mb-2">
      <div class="flex items-center gap-2">
        <h2 class="font-medium">{{ form?.title }}</h2>
        <!-- AI populated badge - shows when content has been populated by AI and not yet approved -->
        <div v-if="isAIGenerated"
          class="px-2 py-0.5 rounded-full bg-blue-100 text-blue-600 text-xs font-medium flex items-center gap-1">
          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
            class="lucide lucide-sparkles">
            <path
              d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z" />
            <path d="M5 3v4" />
            <path d="M19 17v4" />
            <path d="M3 5h4" />
            <path d="M17 19h4" />
          </svg>
          AI populated
        </div>
      </div>

      <div class="flex gap-2">
        <!-- Clone button -->
        <button v-if="displayPlusBtn" @click="handleClone" class="text-blue-500 hover:text-blue-600">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
            class="lucide lucide-plus w-4 h-4">
            <path d="M5 12h14"></path>
            <path d="M12 5v14"></path>
          </svg>
        </button>
      </div>
    </div>

    <div class="flex flex-col gap-2">
      <textarea v-model="localContent" class="w-full h-28 text-sm resize-none focus:outline-none border-b"
        :placeholder="'Enter ' + form?.title.toLowerCase() + '...'" @input="handleContentChange"></textarea>

      <div class="flex justify-between items-center w-full">
        <!-- Prefill Button -->
        <button @click="handlePrefill" class="text-sm text-gray-500 hover:text-gray-600"
          :disabled="!hasPrefillTemplate">
          Prefill
        </button>

        <!-- Right side container with flex -->
        <div class="flex items-center gap-3">
          <!-- Reset Button - shows only when AI populated -->
          <button v-if="isAIGenerated" @click="handleResetContent"
            class="text-sm text-gray-500 hover:text-gray-700 transition-colors duration-150">
            Reset
          </button>

          <!-- Approve AI button - only shown when content has been AI populated -->
          <button v-if="isAIGenerated" @click="handleApproveAI"
            class="py-1 pl-1 pr-1.5 hover:bg-green-600 hover:text-white text-green-500 border border-1 border-green-500 rounded-xs flex items-center text-sm transition-colors duration-150 shadow-sm">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"
              class="flex-shrink-0">
              <path d="M5 12L10 17L19 8" />
            </svg>
            Approve
          </button>
        </div>
      </div>

      <div v-if="showTemplates" class="mt-2 border-t pt-2">
        <h4 class="text-sm font-medium mb-2">Quick Templates</h4>
        <div class="flex flex-wrap gap-2">
          <button v-for="template in form.templates" :key="template" @click="applyTemplate(template)"
            class="px-2 py-1 text-xs bg-gray-100 rounded hover:bg-gray-200">
            {{ template }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "MedicalRecordForm",

  props: {
    form: {
      type: [Object, null],
      validator: (value) => {
        return (
          value &&
          typeof value.title === "string" &&
          typeof value.type === "string" &&
          Array.isArray(value.templates)
        );
      },
    },
    displayPlusBtn: {
      type: Boolean,
      default: false,
    },
    aiData: {
      type: [Object, String],
      default: null,
    },
    approvedInstances: {
      type: Object,
      default: () => ({}),
    },
  },

  data() {
    return {
      localContent: "",
      isAIGenerated: false,
      localApproved: false,
      isManuallyEdited: false,
      originalContent: "",
      pendingAiContent: null,
      saveTimeout: null,
      showTemplates: false,
    };
  },

  computed: {
    hasPrefillTemplate() {
      return this.form?.templates && this.form.templates.length > 0;
    },
  },

  watch: {
    aiData: {
      handler(newData) {
        if (newData) {
          this.populateWithAI(newData);
        }
      },
      immediate: true,
      deep: true,
    },

    "form.content": {
      handler(newContent) {
        if (newContent !== undefined && newContent !== this.localContent) {
          this.localContent = newContent;
        }
      },
      immediate: true,
    },

    approvedInstances: {
      handler(newApproved) {
        const instanceId = this.form?.instanceId;
        if (instanceId && newApproved[instanceId]) {
          this.localApproved = true;
          this.isAIGenerated = false;
        }
      },
      immediate: true,
      deep: true,
    },
  },

  mounted() {
    // Check if this instance was previously approved
    const instanceId = this.form?.instanceId;
    if (instanceId) {
      const wasApproved = localStorage.getItem(`approved_${instanceId}`);
      if (wasApproved === 'true') {
        this.localApproved = true;
        this.isAIGenerated = false;
      }
    }

    // Set initial content
    if (this.form?.content) {
      this.localContent = this.form.content;
    }
  },

  methods: {
    handleContentChange() {
      // Mark as manually edited if user types
      if (!this.isAIGenerated) {
        this.isManuallyEdited = true;
      }

      this.$emit("update:content", {
        type: this.form.type,
        content: this.localContent,
        instanceId: this.form.instanceId,
        isAIGenerated: this.isAIGenerated
      });

      // Auto-save with debounce
      if (this.saveTimeout) {
        clearTimeout(this.saveTimeout);
      }

      this.saveTimeout = setTimeout(() => {
        this.handleSave();
      }, 1500);
    },

    populateWithAI(aiData) {
      // If this content was previously approved, manually edited, or aiData is null, exit early
      if (this.localApproved || this.isManuallyEdited || !aiData) return;

      let contentToUse = "";
      let isAIGenerated = true;

      if (typeof aiData === 'string') {
        contentToUse = aiData;
      } else if (typeof aiData === 'object') {
        if (aiData.content) {
          contentToUse = aiData.content;
          if (aiData.isAIGenerated !== undefined) {
            isAIGenerated = aiData.isAIGenerated;
          }
        } else {
          contentToUse = aiData[this.form.type] || "";
        }
      }

      if (contentToUse.trim()) {
        // Ensure these flags are set correctly
        this.isAIGenerated = true; // Force this to true regardless of the input value
        this.originalContent = this.localContent || "";
        this.pendingAiContent = contentToUse;
        this.localContent = contentToUse;

        this.$emit("update:content", {
          type: this.form.type,
          content: this.localContent,
          instanceId: this.form.instanceId,
          isAIGenerated: true
        });
      }
    },

    // Handle AI approval
    handleApproveAI() {
      if (this.isAIGenerated) {
        this.isAIGenerated = false;
        this.pendingAiContent = null;
        this.localApproved = true;  // Set local approval state

        // Store in localStorage
        const instanceId = this.form?.instanceId;
        if (instanceId) {
          localStorage.setItem(`approved_${instanceId}`, 'true');
        }

        // Emit events
        this.$emit("ai-approved", {
          type: this.form.type,
          content: this.localContent,
          instanceId: this.form.instanceId
        });

        this.$emit("update:content", {
          type: this.form.type,
          content: this.localContent,
          instanceId: this.form.instanceId,
          isAIGenerated: false
        });

        // Trigger a save immediately
        this.handleSave();
      }
    },

    // Reset to pre-AI content
    handleResetContent() {
      if (this.isAIGenerated) {
        this.localContent = this.originalContent || "";
        this.isAIGenerated = false;
        this.pendingAiContent = null;

        // Mark as manually edited to prevent re-population
        this.isManuallyEdited = true;

        // Emit the update
        this.$emit("update:content", {
          type: this.form.type,
          content: this.localContent,
          instanceId: this.form.instanceId,
          isAIGenerated: false
        });

        // Trigger save
        this.handleSave();
      }
    },

    handleSave() {
      this.$emit("save", {
        type: this.form.type,
        content: this.localContent,
        instanceId: this.form.instanceId,
        isAIGenerated: this.isAIGenerated
      });
    },

    handleClone() {
      this.$emit("clone", this.form.type);
    },

    handlePrefill() {
      this.showTemplates = !this.showTemplates;
    },

    applyTemplate(template) {
      this.localContent = template;
      this.showTemplates = false;
      this.handleContentChange();
    },
  },
};
</script>
