<div class="p-6">
    <!-- Header with title and search -->
    <div class="flex justify-between items-center mb-6 flex-wrap gap-4">
        <h3 class="text-xl font-semibold text-gray-900">Select Category</h3>
        <div class="relative w-full">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search w-4 h-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2">
                <circle cx="11" cy="11" r="8"></circle>
                <path d="m21 21-4.3-4.3"></path>
            </svg>
            <input type="text" id="serviceCategorySearch" placeholder="Search..."
             style="padding-left: 2.5rem;"
                class="pl-10 py-2 rounded-md border border-gray-300 focus:ring-2 focus:ring-indigo-500 focus:outline-none text-sm w-full" />
        </div>
    </div>

    <!-- Service list -->
    <div id="serviceCategoryLists" class="space-y-4">
        <span class="loader-class hidden">
            <!-- Loader placeholder -->
            <div class="flex justify-center items-center py-6">
                <div class="double-lines-spinner"></div>
            </div>
        </span>

        <!-- Category cards can be loaded dynamically -->
    </div>
</div>

<script>
    // Get the filter buttons and service list container
    const categoryFilterButtons = document.querySelectorAll('.filter-button');
    const categoryList = document.getElementById('serviceCategoryLists');

    // Add click event listeners to the filter buttons
    categoryFilterButtons.forEach(button => {
        button.addEventListener('click', () => {
            const filterType = button.getAttribute('data-filter');

            // Remove 'active' class from all buttons
            categoryFilterButtons.forEach(btn => btn.classList.remove('bg-indigo-600', 'text-white'));

            // Add 'active' class to the clicked button
            button.classList.remove('text-gray-600');
            button.classList.add('bg-indigo-600', 'text-white');

            // Filter the service items based on the selected type
            filterServices(filterType);
        });
    });

    // Function to filter the service items
    function filterServices(type) {
        const categoryItems = categoryList.querySelectorAll('.service-item');

        categoryItems.forEach(item => {
            if (type === 'all') {
                item.style.display = 'block';
            } else if (type === 'virtual' && item.classList.contains('virtual')) {
                item.style.display = 'block';
            } else if (type === 'clinic' && item.classList.contains('clinic')) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    }
</script>