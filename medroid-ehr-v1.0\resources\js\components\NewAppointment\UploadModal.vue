<template>
  <div v-if="show" class="overflow-y-auto overflow-x-hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg max-w-lg w-full mx-4">
      <div class="p-4 border-b">
        <h3 class="text-lg font-medium">{{ editingDocument ? 'Edit Document' : 'Upload Document' }}</h3>
      </div>
 
      <div class="p-4">
        <form @submit.prevent="handleSubmit">
          <!-- Document Name -->
          <div class="mb-4">
            <label class="block text-sm font-medium mb-1">Document Name</label>
            <input
              v-model="form.name"
              type="text"
              class="w-full border rounded px-3 py-2 focus:outline-none focus:border-blue-500"
              placeholder="Enter document name"
              required
            />
          </div>
 
          <!-- Document Type -->
          <div class="mb-4">
            <label class="block text-sm font-medium mb-1">Document Type</label>
            <select
              v-model="form.type"
              class="w-full border rounded px-3 py-2 focus:outline-none focus:border-blue-500"
              required
              :disabled="editingDocument?.type === 'encounter_document'"
            >
              <option value="">Select type</option>
              <option
                v-for="type in documentTypes"
                :key="type.value"
                :value="type.value"
              >
                {{ type.text }}
              </option>
            </select>
          </div>
 
          <!-- File Upload -->
          <div class="mb-4">
            <label class="block text-sm font-medium mb-1">File</label>
            <div
              class="border-2 border-dashed rounded p-4 text-center hover:bg-gray-50 cursor-pointer"
              @click="selectFile"
            >
              <div v-if="!selectedFileName">
                <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                  <polyline points="17 8 12 3 7 8" />
                  <line x1="12" y1="3" x2="12" y2="15" />
                </svg>
                <p class="mt-2 text-sm text-gray-600">Click to browse</p>
              </div>
              <div v-else class="text-sm text-gray-600">
                {{ selectedFileName }}
              </div>
            </div>
          </div>
 
          <!-- Description -->
          <div class="mb-4">
            <label class="block text-sm font-medium mb-1">Description</label>
            <textarea
              v-model="form.description"
              rows="3"
              class="w-full border rounded px-3 py-2 focus:outline-none focus:border-blue-500"
              placeholder="Enter description..."
            ></textarea>
          </div>
 
          <!-- Buttons -->
          <div class="flex justify-end gap-2">
            <button
              type="button"
              class="px-4 py-2 text-sm border rounded hover:bg-gray-50"
              @click="$emit('close')"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="px-4 py-2 text-sm bg-black text-white rounded hover:bg-black disabled:opacity-50"
              :disabled="!isFormValid || uploading"
            >
              <template v-if="uploading">
                <span class="inline-block animate-spin mr-2">↻</span>
                {{ editingDocument ? 'Updating...' : 'Uploading...' }}
              </template>
              <template v-else>{{ editingDocument ? 'Update' : 'Upload' }}</template>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>
 
<script>
import { post } from "../../config/request";
import { displayErrorMessage } from "../../utils/message";
 
export default {
  name: "UploadModal",
 
  props: {
    show: {
      type: Boolean,
      required: true,
    },
    encounterId: {
      type: [String, Number], 
      required: true,
    },
    patientDetails: {
      type: Object,
      required: true,
    },
    editingDocument: {
      type: Object,
      default: null
    }
  },
 
  data() {
    return {
      form: {
        name: "",
        type: "",
        description: "",
      },
      selectedFileName: "",
      selectedDocumentId: null,
      uploading: false,
      documentTypes: [
        { value: "identity_document", text: "Identity Document" },
        { value: "lab_report", text: "Lab Report" },
        { value: "prescription", text: "Prescription" },
        { value: "scan", text: "Scan" },
        { value: "other", text: "Other" },
      ],
    };
  },
 
  computed: {
    isFormValid() {
      if (this.editingDocument) {
        return this.form.name && this.form.type;
      }
      return this.form.name && this.form.type && this.selectedDocumentId;
    },
  },

  watch: {
    editingDocument: {
      immediate: true,
      handler(newDoc) {
        if (newDoc) {
          this.form = {
            name: newDoc.name || "",
            type: newDoc.type || "",
            description: newDoc.description || ""
          };
          this.selectedDocumentId = newDoc.document_id;
        } else {
          this.resetForm();
        }
      }
    },
    show(newVal) {
      if (!newVal) {
        this.resetForm();
      }
    }
  },
 
  methods: {
    resetForm() {
      this.form = {
        name: "",
        type: "",
        description: "",
      };
      this.selectedFileName = "";
      this.selectedDocumentId = null;
    },

    selectFile() {
      if (!this.form.type) {
        displayErrorMessage("Please select a document type first");
        return;
      }
 
      const custom_uploader = kivicareCustomImageUploader(this.formTranslation);
 
      custom_uploader.on("select", () => {
        const attachment = custom_uploader.state().get("selection").first().toJSON();
        this.selectedFileName = attachment.filename;
        this.selectedDocumentId = attachment.id;
      });
 
      custom_uploader.open();
    },
 
    async handleSubmit() {
      if (!this.isFormValid) return;
 
      this.uploading = true;
      try {
        const formData = new FormData();
        formData.append("appointment_id", this.encounterId);
        formData.append("name", this.form.name);
        formData.append("type", this.form.type);
        formData.append("description", this.form.description || "");
        formData.append("patient_id", this.patientDetails.patient_id);
        formData.append("document_id", this.selectedDocumentId);

        if (this.editingDocument) {
          formData.append("id", this.editingDocument.id);
          const response = await post("edit_patient_document", formData);

          if (response?.data?.status) {
            this.$swal.fire({
              icon: "success",
              title: response?.data?.message || "Document updated successfully",
              text: "",
            });
            this.$emit("upload", response.data);
            this.$emit("close");
          } else {
            throw new Error(response?.data?.message || "Update failed");
          }
        } else {
          const response = await post("upload_patient_document", formData);
          
          if (response?.data?.status) {
            this.$swal.fire({
              icon: "success",
              title: response?.data?.message || "Document uploaded successfully",
              text: "",
            });
            this.$emit("upload", response.data);
            this.$emit("close");
          } else {
            throw new Error(response?.data?.message || "Upload failed");
          }
        }
      } catch (error) {
        console.error(this.editingDocument ? "Update error:" : "Upload error:", error);
        displayErrorMessage(error.message || `Failed to ${this.editingDocument ? 'update' : 'upload'} document`);
      } finally {
        this.uploading = false;
      }
    },
  },
};
</script>