<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ProviderController;
use App\Http\Controllers\AppointmentController;
use App\Http\Controllers\ChatController;
use App\Http\Controllers\AIChatController;
use App\Http\Controllers\WebController;
use App\Http\Controllers\VideoConsultationController;
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\ServiceController;
use App\Http\Controllers\ProviderAvailabilityController;
use App\Http\Controllers\AnalyticsController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\ReferralController;
use App\Http\Controllers\ManagementController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\ShoppingCartController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\DigitalDownloadController;
use App\Http\Controllers\Admin\ProductManagementController;
use App\Http\Controllers\Admin\OrderManagementController;

// Test route without database dependency
Route::get('/test', function () {
    return response()->json(['status' => 'success', 'message' => 'Laravel is running!']);
});

// CSRF token endpoint
Route::get('/csrf-token', function () {
    return response()->json(['csrf_token' => csrf_token()]);
});

// Instagram Webhook Routes - No authentication required (must be before other routes)
Route::get('webhooks/instagram', [\App\Http\Controllers\InstagramWebhookController::class, 'verify']);
Route::post('webhooks/instagram', [\App\Http\Controllers\InstagramWebhookController::class, 'handle']);

// Instagram Auth Routes - No authentication required for callback
Route::get('auth/instagram/callback', [\App\Http\Controllers\InstagramController::class, 'handleCallback']);

// Media proxy routes removed - causing issues with web version

// Instagram Test Route
Route::get('instagram-test', function () {
    return view('instagram-test');
})->name('instagram.test');

// Instagram Debug Route
Route::get('instagram-debug', function () {
    $instagramService = app(\App\Services\InstagramService::class);

    return response()->json([
        'app_id' => config('services.instagram.app_id'),
        'redirect_uri' => config('services.instagram.redirect_uri'),
        'auth_url' => $instagramService->getAuthorizationUrl('test_state'),
    ]);
})->name('instagram.debug');

// Test route to create sample Instagram content
Route::get('test-instagram-content', function () {
    if (!auth()->check()) {
        return redirect()->route('login');
    }

    // Create a test Instagram account if it doesn't exist
    $instagramAccount = \App\Models\InstagramAccount::firstOrCreate([
        'user_id' => auth()->id(),
        'instagram_user_id' => 'test_user_' . auth()->id()
    ], [
        'username' => 'test_user_' . auth()->id(),
        'access_token' => 'test_token',
        'account_type' => 'PERSONAL',
        'media_count' => 1,
        'expires_at' => now()->addDays(60),
        'is_active' => true,
    ]);

    // Create test Instagram posts
    $testPosts = [
        [
            'id' => 'test_post_1_' . time(),
            'media_type' => 'IMAGE',
            'media_url' => 'https://images.unsplash.com/photo-*************-1cb2f99b2d8b?w=400',
            'caption' => 'Starting my #wellness journey today! 🌱 #health #fitness #mindfulness #selfcare',
            'permalink' => 'https://instagram.com/p/test1',
            'timestamp' => now()->subHours(2)->toISOString(),
        ],
        [
            'id' => 'test_post_2_' . time(),
            'media_type' => 'IMAGE',
            'media_url' => 'https://images.unsplash.com/photo-**********-0f2fcb009e0b?w=400',
            'caption' => 'Morning #yoga session complete! 🧘‍♀️ #meditation #mentalhealth #balance',
            'permalink' => 'https://instagram.com/p/test2',
            'timestamp' => now()->subHours(5)->toISOString(),
        ],
        [
            'id' => 'test_post_3_' . time(),
            'media_type' => 'VIDEO',
            'media_url' => 'https://images.unsplash.com/photo-*************-1cb2f99b2d8b?w=400',
            'thumbnail_url' => 'https://images.unsplash.com/photo-*************-1cb2f99b2d8b?w=400',
            'caption' => 'Quick #workout routine for busy days! 💪 #fitness #exercise #health #motivation',
            'permalink' => 'https://instagram.com/p/test3',
            'timestamp' => now()->subDay()->toISOString(),
        ]
    ];

    $instagramService = app(\App\Services\InstagramService::class);
    $created = 0;

    foreach ($testPosts as $postData) {
        try {
            // Use reflection to call the protected method
            $reflection = new \ReflectionClass($instagramService);
            $method = $reflection->getMethod('syncMediaItem');
            $method->setAccessible(true);
            $method->invoke($instagramService, $instagramAccount, $postData);
            $created++;
        } catch (\Exception $e) {
            \Log::error('Error creating test post: ' . $e->getMessage());
        }
    }

    return redirect('/discover')->with('success', "Created {$created} test Instagram posts! Check the discover feed.");
})->name('test.instagram.content');

// Public shared post routes (no authentication required) - MUST be before authenticated routes
Route::get('post/{postId}', [WebController::class, 'publicSharedPost'])->name('public.shared-post')->where('postId', '[0-9]+');
Route::get('discover-public', [WebController::class, 'publicDiscover'])->name('public.discover');

Route::get('/', [WebController::class, 'index'])->name('home');

// Public routes - no authentication required
Route::get('terms-and-conditions', [WebController::class, 'termsAndConditions'])->name('terms-and-conditions');
Route::get('privacy-policy', [WebController::class, 'privacyPolicy'])->name('privacy-policy');

// Public appointment booking routes - no authentication required
Route::prefix('book-appointment')->group(function () {
    Route::get('{clinicSlug}', [\App\Http\Controllers\AppointmentBookingController::class, 'index'])->name('appointment.booking');
    Route::post('{clinicSlug}/check-email', [\App\Http\Controllers\AppointmentBookingController::class, 'checkEmail'])->name('appointment.booking.check-email');
    Route::post('{clinicSlug}/verify-login', [\App\Http\Controllers\AppointmentBookingController::class, 'verifyLogin'])->name('appointment.booking.verify-login');
    Route::post('{clinicSlug}/forgot-password', [\App\Http\Controllers\AppointmentBookingController::class, 'forgotPassword'])->name('appointment.booking.forgot-password');
    Route::get('{clinicSlug}/categories', [\App\Http\Controllers\AppointmentBookingController::class, 'getCategories'])->name('appointment.booking.categories');
    Route::get('{clinicSlug}/categories/{categoryId}/services', [\App\Http\Controllers\AppointmentBookingController::class, 'getServices'])->name('appointment.booking.services');
    Route::get('{clinicSlug}/services/{serviceId}/dates', [\App\Http\Controllers\AppointmentBookingController::class, 'getAvailableDates'])->name('appointment.booking.dates');
    Route::get('{clinicSlug}/services/{serviceId}/slots', [\App\Http\Controllers\AppointmentBookingController::class, 'getTimeSlots'])->name('appointment.booking.slots');
    Route::post('{clinicSlug}/create-payment-intent', [\App\Http\Controllers\AppointmentBookingController::class, 'createPaymentIntent'])->name('appointment.booking.payment-intent');
    Route::post('{clinicSlug}/complete-booking', [\App\Http\Controllers\AppointmentBookingController::class, 'completeBooking'])->name('appointment.booking.complete');
    Route::post('{clinicSlug}/book', [\App\Http\Controllers\AppointmentBookingController::class, 'bookAppointment'])->name('appointment.booking.book');
    Route::post('{clinicSlug}/saved-cards', [\App\Http\Controllers\AppointmentBookingController::class, 'getSavedCards'])->name('appointment.booking.saved-cards');
    Route::delete('{clinicSlug}/saved-cards/{cardId}', [\App\Http\Controllers\AppointmentBookingController::class, 'deleteSavedCard'])->name('appointment.booking.delete-card');
});

// Provider registration route - no authentication required
Route::get('providers/register', [\App\Http\Controllers\ProviderRegistrationController::class, 'showRegistrationPage'])->name('providers.register');

// Founder Club shareable link - no authentication required
Route::get('join-founders', [\App\Http\Controllers\Auth\RegisteredUserController::class, 'showFounderSignup'])->name('founder.signup');
Route::post('join-founders', [\App\Http\Controllers\Auth\RegisteredUserController::class, 'storeFounderSignup'])->name('founder.signup.store');







Route::get('dashboard', [DashboardController::class, 'index'])
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

// Dashboard data routes for authenticated users
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard/data', [DashboardController::class, 'getDashboardData'])->name('dashboard.data');
    Route::get('provider/dashboard-data', [\App\Http\Controllers\ProviderDashboardController::class, 'getDashboardData'])->name('provider.dashboard.data');
    Route::get('management/dashboard/kpi', [\App\Http\Controllers\AnalyticsController::class, 'getKpiMetrics'])->name('management.dashboard.kpi');

    Route::get('provider/get-availability', [\App\Http\Controllers\ProviderAvailabilityController::class, 'getWeeklyAvailability'])->name('provider.availability.get');
    Route::get('provider/get-absences', [\App\Http\Controllers\ProviderAvailabilityController::class, 'getAbsences'])->name('provider.absences.get');
});

// Healthcare routes
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('providers/{provider}', [ProviderController::class, 'webShow'])->name('providers.show');



    // Appointment detail routes (role-agnostic for individual appointments)
    Route::get('appointments/create', [AppointmentController::class, 'webCreate'])->name('appointments.create');
    Route::get('appointments/{appointment}', [AppointmentController::class, 'webShow'])->name('appointments.show');
    Route::get('appointments/{appointment}/edit', [AppointmentController::class, 'webEdit'])->name('appointments.edit');
    Route::get('appointments/{appointment}/payment', [PaymentController::class, 'showPaymentPage'])->name('appointments.payment');

    // Video consultation web routes (for webapp)
    Route::post('video/initialize/{appointmentId}', [VideoConsultationController::class, 'initializeSession'])->name('video.initialize');
    Route::get('video/session/{appointmentId}', [VideoConsultationController::class, 'getSessionData'])->name('video.session');
    Route::post('video/join/{appointmentId}', [VideoConsultationController::class, 'joinSession'])->name('video.join');
    Route::post('video/leave/{appointmentId}', [VideoConsultationController::class, 'leaveSession'])->name('video.leave');
    Route::get('video/status/{appointmentId}', [VideoConsultationController::class, 'getSessionStatus'])->name('video.status');
    Route::post('video/participant-disconnected/{appointmentId}', [VideoConsultationController::class, 'markParticipantDisconnected'])->name('video.participant-disconnected');
    Route::post('video/end/{appointmentId}', [VideoConsultationController::class, 'endSession'])->name('video.end');

    Route::get('chat', [ChatController::class, 'webIndex'])->name('chat');

    // Chat API routes for web (session-based authentication) - using different path to avoid API conflicts
    Route::prefix('web-api/chat')->group(function () {
        Route::post('start', [AIChatController::class, 'startConversation'])->name('web.api.chat.start');
        Route::post('message', [AIChatController::class, 'sendMessage'])->name('web.api.chat.message');
        Route::get('history', [AIChatController::class, 'getHistory'])->name('web.api.chat.history');
        Route::get('conversation/{conversationId}', [AIChatController::class, 'getConversation'])->name('web.api.chat.conversation');

        // Anonymous conversation transfer (session-based authentication)
        Route::post('transfer-anonymous', [ChatController::class, 'transferAnonymousConversation'])->name('web.api.chat.transfer-anonymous');

        // Chat utility routes
        Route::post('update-titles', [ChatController::class, 'updateTitles'])->name('web.api.chat.update-titles');
    });

    // Maya Wellness Chat API routes for web
    Route::prefix('web-api/wellness')->group(function () {
        Route::post('start', [\App\Http\Controllers\WellnessController::class, 'startConversation'])->name('web.api.wellness.start');
        Route::post('message', [\App\Http\Controllers\WellnessController::class, 'sendMessage'])->name('web.api.wellness.message');
        Route::get('conversations', [\App\Http\Controllers\WellnessController::class, 'getConversations'])->name('web.api.wellness.conversations');
        Route::get('conversation/{id}', [\App\Http\Controllers\WellnessController::class, 'getConversation'])->name('web.api.wellness.conversation');
        Route::get('status', [\App\Http\Controllers\WellnessController::class, 'getStatus'])->name('web.api.wellness.status');
    });

    // Patient-specific routes
    Route::get('discover', [WebController::class, 'discover'])->name('discover');
    Route::get('discover/post/{postId}', [WebController::class, 'sharedPost'])->name('discover.shared-post');
    Route::get('instagram-auth-demo', function () {
        return Inertia::render('InstagramAuthDemo');
    })->name('instagram-auth-demo');
    Route::get('shop', [WebController::class, 'shop'])->name('shop');
    Route::get('chat-history', [WebController::class, 'chatHistory'])->name('chat-history');
    Route::get('credit-history', [WebController::class, 'creditHistory'])->name('credit-history');

    // Chat utility routes
    Route::post('web-api/chat/update-titles', [App\Http\Controllers\ChatController::class, 'updateTitles']);

    // AI Content Generation routes
    Route::get('web-api/ai/test', [\App\Http\Controllers\AIContentController::class, 'test']);
    Route::post('web-api/ai/generate-content', [\App\Http\Controllers\AIContentController::class, 'generateContent']);

    // PROTECTED FEED ROUTES (Authentication required for interactions)
    Route::prefix('web-api/feed')->group(function () {
        // Interactive routes that require authentication
        Route::post('create', [\App\Http\Controllers\SocialFeedController::class, 'create']);
        Route::post('like/{contentId}', [\App\Http\Controllers\SocialFeedController::class, 'likeContent']);
        Route::post('save/{contentId}', [\App\Http\Controllers\SocialFeedController::class, 'saveContent']);
        Route::post('{contentId}/share', [\App\Http\Controllers\SocialFeedController::class, 'shareContent']); // Track shares
        Route::post('{contentId}/report', [\App\Http\Controllers\SocialFeedController::class, 'reportContent']);
        Route::post('{contentId}/comments', [\App\Http\Controllers\SocialFeedController::class, 'addComment']);
        Route::delete('{contentId}', [\App\Http\Controllers\SocialFeedController::class, 'deleteContent']);

        // Comment management routes (authentication required)
        Route::prefix('{contentId}/comments')->group(function () {
            Route::delete('{commentId}', [\App\Http\Controllers\CommentController::class, 'destroy']);
            Route::post('{commentId}/react', [\App\Http\Controllers\CommentController::class, 'react']);
            Route::post('{commentId}/reply', [\App\Http\Controllers\CommentController::class, 'storeReply']);
        });
    });

    // Saved posts route (authentication required)
    Route::get('web-api/saved-posts', [\App\Http\Controllers\SocialFeedController::class, 'getSavedPosts']);

    // Stories routes for web platform
    Route::prefix('web-api/stories')->group(function () {
        Route::get('/', [\App\Http\Controllers\StoryController::class, 'index']);
        Route::post('/', [\App\Http\Controllers\StoryController::class, 'store']);
        Route::get('user/{userId}', [\App\Http\Controllers\StoryController::class, 'getUserStories']);
        Route::post('{storyId}/view', [\App\Http\Controllers\StoryController::class, 'markAsViewed']);
        Route::delete('{storyId}', [\App\Http\Controllers\StoryController::class, 'destroy']);
        Route::get('{storyId}/viewers', [\App\Http\Controllers\StoryController::class, 'getViewers']);
    });

    // User profile routes for web platform
    Route::get('web-api/user/profile-stats', [\App\Http\Controllers\UserController::class, 'getProfileStats']);
    Route::get('web-api/user/{userId}/profile-stats', [\App\Http\Controllers\UserController::class, 'getUserProfileStats']);
    Route::post('web-api/user/profile-image', [\App\Http\Controllers\UserController::class, 'updateProfileImage']);
    Route::post('web-api/user/bio', [\App\Http\Controllers\UserController::class, 'updateBio']);
    Route::post('web-api/user/{userId}/follow', [\App\Http\Controllers\UserController::class, 'followUser']);
    Route::post('web-api/user/{userId}/unfollow', [\App\Http\Controllers\UserController::class, 'unfollowUser']);
    Route::get('web-api/user/{userId}/followers', [\App\Http\Controllers\UserController::class, 'getFollowers']);
    Route::get('web-api/user/{userId}/following', [\App\Http\Controllers\UserController::class, 'getFollowing']);

    // File Management routes for web platform
    Route::prefix('web-api/files')->group(function () {
        Route::get('/', [\App\Http\Controllers\FileController::class, 'index']);
        Route::post('/', [\App\Http\Controllers\FileController::class, 'store']);
        Route::get('/categories', [\App\Http\Controllers\FileController::class, 'getCategories']);
        Route::get('/stats', [\App\Http\Controllers\FileController::class, 'getStats']);
        Route::post('/bulk-delete', [\App\Http\Controllers\FileController::class, 'bulkDelete']);
        Route::get('/{id}', [\App\Http\Controllers\FileController::class, 'show']);
        Route::put('/{id}', [\App\Http\Controllers\FileController::class, 'update']);
        Route::delete('/{id}', [\App\Http\Controllers\FileController::class, 'destroy']);
        Route::get('/{id}/download', [\App\Http\Controllers\FileController::class, 'download']);
        Route::get('/{id}/secure-url', [\App\Http\Controllers\FileController::class, 'getSecureUrl']);
        Route::post('/{id}/optimize', [\App\Http\Controllers\FileController::class, 'optimizeImage']);
        Route::post('/{id}/thumbnails', [\App\Http\Controllers\FileController::class, 'generateThumbnails']);
        Route::get('/{id}/thumbnail', [\App\Http\Controllers\FileController::class, 'getThumbnail']);
    });

    // File Manager page route
    Route::get('files', function () {
        return Inertia::render('FileManager');
    })->name('files');

    // File Picker Test page route (for development/testing)
    Route::get('file-picker-test', function () {
        return Inertia::render('FilePickerTest');
    })->name('file-picker-test');

    // File Management routes for web platform
    Route::prefix('web-api/files')->group(function () {
        Route::get('/', [\App\Http\Controllers\FileController::class, 'index']);
        Route::post('/', [\App\Http\Controllers\FileController::class, 'store']);
        Route::get('/categories', [\App\Http\Controllers\FileController::class, 'getCategories']);
        Route::get('/stats', [\App\Http\Controllers\FileController::class, 'getStats']);
        Route::post('/bulk-delete', [\App\Http\Controllers\FileController::class, 'bulkDelete']);
        Route::get('/{id}', [\App\Http\Controllers\FileController::class, 'show']);
        Route::put('/{id}', [\App\Http\Controllers\FileController::class, 'update']);
        Route::delete('/{id}', [\App\Http\Controllers\FileController::class, 'destroy']);
        Route::get('/{id}/download', [\App\Http\Controllers\FileController::class, 'download']);
        Route::get('/{id}/secure-url', [\App\Http\Controllers\FileController::class, 'getSecureUrl']);
        Route::post('/{id}/optimize', [\App\Http\Controllers\FileController::class, 'optimizeImage']);
        Route::post('/{id}/thumbnails', [\App\Http\Controllers\FileController::class, 'generateThumbnails']);
        Route::get('/{id}/thumbnail', [\App\Http\Controllers\FileController::class, 'getThumbnail']);
    });

    // File Manager page route
    Route::get('files', function () {
        return Inertia::render('FileManager');
    })->name('files');

    // File Picker Test page route (for development/testing)
    Route::get('file-picker-test', function () {
        return Inertia::render('FilePickerTest');
    })->name('file-picker-test');


    // Admin Social Media Manager routes
    Route::middleware(['auth', 'role:admin'])->prefix('admin/api/social-media')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\SocialMediaManagerController::class, 'index']);
        Route::get('posts', [\App\Http\Controllers\Admin\SocialMediaManagerController::class, 'getPosts']);
        Route::get('stories', [\App\Http\Controllers\Admin\SocialMediaManagerController::class, 'getStories']);
        Route::patch('posts/{postId}/status', [\App\Http\Controllers\Admin\SocialMediaManagerController::class, 'updatePostStatus']);
        Route::delete('posts/{postId}', [\App\Http\Controllers\Admin\SocialMediaManagerController::class, 'deletePost']);
        Route::delete('stories/{storyId}', [\App\Http\Controllers\Admin\SocialMediaManagerController::class, 'deleteStory']);
    });

    // Admin Social Media Manager page route
    Route::middleware(['auth', 'role:admin'])->get('/admin/social-media', function () {
        return inertia('Admin/SocialMediaManager');
    });
});

// Provider-specific routes
Route::middleware(['auth', 'verified', 'role:provider'])->prefix('provider')->group(function () {
    Route::get('dashboard', [\App\Http\Controllers\ProviderDashboardController::class, 'index'])->name('provider.dashboard');
    Route::get('appointments', [AppointmentController::class, 'webIndex'])->name('provider.appointments');
    Route::get('availability', [ProviderController::class, 'availability'])->name('provider.availability');
    Route::get('services', [ProviderController::class, 'services'])->name('provider.services');
    Route::get('schedule', [ProviderController::class, 'schedule'])->name('provider.schedule');
    Route::get('patients', [ProviderController::class, 'patients'])->name('provider.patients');
    Route::get('earnings', [ProviderController::class, 'earnings'])->name('provider.earnings');
    // Redirect to unified settings profile page
    Route::get('profile', function () {
        return redirect('/settings/profile');
    })->name('provider.profile');

    // Provider bulk import routes (must be before products/{id} routes)
    Route::get('products/import-template', [ProductManagementController::class, 'downloadImportTemplate']);
    Route::get('products/import-instructions', [ProductManagementController::class, 'downloadImportInstructions']);
    Route::post('products/validate-import', [ProductManagementController::class, 'validateImport']);
    Route::post('products/bulk-import', [ProductManagementController::class, 'bulkImport']);

    // Provider product management (providers can manage their own products by default)
    Route::get('products', [ProductManagementController::class, 'index'])->name('provider.products');
    Route::get('products/create', [ProductManagementController::class, 'create'])->name('provider.products.create');
    Route::post('products', [ProductManagementController::class, 'store'])->name('provider.products.store');
    Route::get('products/{id}', [ProductManagementController::class, 'show'])->name('provider.products.show');
    Route::get('products/{id}/edit', [ProductManagementController::class, 'edit'])->name('provider.products.edit');
    Route::put('products/{id}', [ProductManagementController::class, 'update'])->name('provider.products.update');
    Route::delete('products/{id}', [ProductManagementController::class, 'destroy'])->name('provider.products.destroy');
    Route::patch('products/{id}/toggle-status', [ProductManagementController::class, 'toggleStatus'])->name('provider.products.toggle-status');

    // Provider order fulfillment
    Route::get('orders', [\App\Http\Controllers\Provider\OrderFulfillmentController::class, 'index'])->name('provider.orders');
    Route::get('orders/{id}', [\App\Http\Controllers\Provider\OrderFulfillmentController::class, 'show'])->name('provider.orders.show');
    Route::post('orders/{id}/dispatch', [\App\Http\Controllers\Provider\OrderFulfillmentController::class, 'dispatch'])->name('provider.orders.dispatch');
    Route::patch('orders/{id}/status', [\App\Http\Controllers\Provider\OrderFulfillmentController::class, 'updateStatus'])->name('provider.orders.update-status');
    Route::get('shipping-companies', [\App\Http\Controllers\Provider\OrderFulfillmentController::class, 'getShippingCompanies'])->name('provider.shipping-companies');
});

// Patient-specific routes
Route::middleware(['auth', 'verified', 'role:patient'])->prefix('patient')->group(function () {
    Route::get('appointments', [AppointmentController::class, 'webIndex'])->name('patient.appointments');
});

// Admin/Manager routes that don't require specific permissions (fallback for role-based access)
Route::middleware(['auth', 'verified'])->group(function () {
    // Generic admin appointments route (redirects to manage/appointments if user has permission)
    Route::get('admin/appointments', function (Request $request) {
        $user = $request->user();
        if ($user->can('view appointments') || in_array($user->role, ['admin', 'clinic_admin', 'super_admin'])) {
            return redirect()->route('appointments.manage');
        }
        abort(403, 'Unauthorized');
    })->name('admin.appointments');
});

// Management routes - protected by role-based permissions
Route::middleware(['auth', 'verified'])->group(function () {
    // User management - requires 'view users' permission
    Route::get('users', [ManagementController::class, 'users'])->name('users')->middleware('permission:view users');

    // Patient management - requires 'view patients' permission
    Route::get('patients', [ManagementController::class, 'patients'])->name('patients')->middleware('permission:view patients');

    // Appointment management - requires 'view appointments' permission
    Route::get('manage/appointments', [ManagementController::class, 'appointments'])->name('appointments.manage')->middleware('permission:view appointments');

    // Payment management - requires 'view payments' permission
    Route::get('payments', [ManagementController::class, 'payments'])->name('payments')->middleware('permission:view payments');

    // Chat management - requires 'view chats' permission
    Route::get('chats', [ManagementController::class, 'chats'])->name('chats.manage')->middleware('permission:view chats');

    // Permission management - requires 'view permissions' permission
    Route::get('permissions', [ManagementController::class, 'permissions'])->name('permissions')->middleware('permission:view permissions');

    // Email template management - requires 'view email templates' permission
    Route::get('email-templates', [ManagementController::class, 'emailTemplates'])->name('email-templates')->middleware('permission:view email templates');



    // Notification management - requires 'view notifications' permission
    Route::get('notifications', [ManagementController::class, 'notifications'])->name('notifications')->middleware('permission:view notifications');

    // Waitlist management - requires 'view users' permission
    Route::get('waitlist', function () {
        return Inertia::render('Waitlist');
    })->name('waitlist')->middleware('permission:view users');

    // Service management - requires 'view services' permission
    Route::get('services', [ManagementController::class, 'services'])->name('services')->middleware('permission:view services');

    // Pending approvals - requires admin role
    Route::get('pending-approvals', function () {
        return Inertia::render('PendingApprovals');
    })->name('pending-approvals')->middleware('role:admin');

    // System verification - requires admin permissions
    Route::get('system-verification', [ManagementController::class, 'systemVerification'])->name('system-verification')->middleware('permission:view users');

    // Referral management - requires 'view referrals' permission
    Route::get('referrals', [ManagementController::class, 'referrals'])->name('referrals')->middleware('permission:view referrals');

    // Credit management - requires 'view credits' permission
    Route::get('credits', [ManagementController::class, 'credits'])->name('credits')->middleware('permission:view credits');

    // Club management - requires 'view clubs' permission
    Route::get('clubs', [ManagementController::class, 'clubs'])->name('clubs')->middleware('permission:view clubs');

    // Ecommerce management routes
    Route::prefix('admin')->group(function () {
        // Bulk import routes (must be before products/{id} routes)
        Route::get('products/import-template', [ProductManagementController::class, 'downloadImportTemplate'])->middleware('permission:create products');
        Route::get('products/import-instructions', [ProductManagementController::class, 'downloadImportInstructions'])->middleware('permission:create products');
        Route::post('products/validate-import', [ProductManagementController::class, 'validateImport'])->middleware('permission:create products');
        Route::post('products/bulk-import', [ProductManagementController::class, 'bulkImport'])->middleware('permission:create products');

        // Product management - requires product permissions
        Route::get('products', [ProductManagementController::class, 'index'])->name('admin.products')->middleware('permission:view products');
        Route::get('products/create', [ProductManagementController::class, 'create'])->name('admin.products.create')->middleware('permission:create products');
        Route::post('products', [ProductManagementController::class, 'store'])->name('admin.products.store')->middleware('permission:create products');
        Route::get('products/{id}', [ProductManagementController::class, 'show'])->name('admin.products.show')->middleware('permission:view products');
        Route::get('products/{id}/edit', [ProductManagementController::class, 'edit'])->name('admin.products.edit')->middleware('permission:edit products');
        Route::put('products/{id}', [ProductManagementController::class, 'update'])->name('admin.products.update')->middleware('permission:edit products');
        Route::delete('products/{id}', [ProductManagementController::class, 'destroy'])->name('admin.products.destroy')->middleware('permission:delete products');
        Route::patch('products/{id}/toggle-status', [ProductManagementController::class, 'toggleStatus'])->name('admin.products.toggle-status')->middleware('permission:edit products');

        // Product category management - requires product permissions
        Route::get('categories', [\App\Http\Controllers\Admin\ProductCategoryController::class, 'index'])->name('admin.categories')->middleware('permission:view products');
        Route::get('categories/create', [\App\Http\Controllers\Admin\ProductCategoryController::class, 'create'])->name('admin.categories.create')->middleware('permission:create products');
        Route::post('categories', [\App\Http\Controllers\Admin\ProductCategoryController::class, 'store'])->name('admin.categories.store')->middleware('permission:create products');
        Route::get('categories/{id}', [\App\Http\Controllers\Admin\ProductCategoryController::class, 'show'])->name('admin.categories.show')->middleware('permission:view products');
        Route::get('categories/{id}/edit', [\App\Http\Controllers\Admin\ProductCategoryController::class, 'edit'])->name('admin.categories.edit')->middleware('permission:edit products');
        Route::put('categories/{id}', [\App\Http\Controllers\Admin\ProductCategoryController::class, 'update'])->name('admin.categories.update')->middleware('permission:edit products');
        Route::delete('categories/{id}', [\App\Http\Controllers\Admin\ProductCategoryController::class, 'destroy'])->name('admin.categories.destroy')->middleware('permission:delete products');
        Route::patch('categories/{id}/toggle-status', [\App\Http\Controllers\Admin\ProductCategoryController::class, 'toggleStatus'])->name('admin.categories.toggle-status')->middleware('permission:edit products');

        // Order management - requires 'manage orders' permission or admin role
        Route::get('orders', [OrderManagementController::class, 'index'])->name('admin.orders');
        Route::get('orders/{id}', [OrderManagementController::class, 'show'])->name('admin.orders.show');
        Route::patch('orders/{id}/status', [OrderManagementController::class, 'updateStatus'])->name('admin.orders.update-status');
        Route::patch('orders/{id}/payment-status', [OrderManagementController::class, 'updatePaymentStatus'])->name('admin.orders.update-payment-status');
        Route::post('orders/{id}/notes', [OrderManagementController::class, 'addNote'])->name('admin.orders.add-note');
        Route::get('orders/stats', [OrderManagementController::class, 'getOrderStats'])->name('admin.orders.stats');
        Route::get('orders/export', [OrderManagementController::class, 'exportOrders'])->name('admin.orders.export');
    });
});

// API routes for web application (session-based authentication)
Route::middleware(['auth', 'verified'])->group(function () {
    // Services management routes (following availability pattern)
    Route::get('services-list', [ServiceController::class, 'index']);
    Route::post('save-service', [ServiceController::class, 'store']);
    Route::put('save-service/{id}', [ServiceController::class, 'update']);
    Route::delete('delete-service/{id}', [ServiceController::class, 'destroy']);
    Route::get('get-service-categories', [ServiceController::class, 'getCategories']);

    // Providers API routes (clean URLs for Vue rendering)
    Route::get('providers', [ManagementController::class, 'providers'])->name('providers')->middleware('permission:view providers');
    Route::get('providers/{id}', [ProviderController::class, 'show']);

    // Providers management routes (following availability pattern)
    Route::get('providers-list', [ProviderController::class, 'index']);
    Route::get('get-providers', [ProviderController::class, 'index']);
    Route::get('get-providers/{id}', [ProviderController::class, 'show']);
    Route::post('save-provider', [ProviderController::class, 'store']);
    Route::post('save-provider-with-user', [ProviderController::class, 'storeWithUser']);
    Route::put('update-provider/{id}', [ProviderController::class, 'update']);
    Route::delete('delete-provider/{id}', [ProviderController::class, 'destroy']);

    // Users management routes
    Route::get('users-list', [\App\Http\Controllers\UserController::class, 'index']);
    Route::get('users-without-provider', [\App\Http\Controllers\UserController::class, 'getUsersWithoutProvider']);
    Route::put('users/{id}', [\App\Http\Controllers\UserController::class, 'update'])->middleware('permission:edit users');
    Route::patch('users/{id}/toggle-status', [\App\Http\Controllers\UserController::class, 'toggleStatus']);
    Route::post('users/{id}/assign-role', [\App\Http\Controllers\UserController::class, 'assignRole']);
    Route::post('users', [\App\Http\Controllers\UserController::class, 'store']);

    // Clinician access management
    Route::post('users/{user}/toggle-clinician', [\App\Http\Controllers\Admin\ClinicianManagementController::class, 'toggleClinicianAccess']);

    // Clinical functionality routes (protected by clinician middleware)
    Route::middleware(['auth', 'verified', 'clinician'])->group(function () {
        // Consultations (following KiviCare pattern)
        Route::get('consultations', function () {
            return Inertia::render('Consultations/Index');
        })->name('consultations.index');

        // Patient Encounter Routes (KiviCare style)
        Route::get('patient-encounter/create/{patientId}', function ($patientId) {
            return Inertia::render('Consultations/Consultation', ['patientId' => $patientId]);
        })->name('patient-encounter.create');

        Route::get('patient-encounter/edit/{id}', function ($id) {
            return Inertia::render('Consultations/Consultation', ['consultationId' => $id]);
        })->name('patient-encounter.edit');

        // Consultation Dashboard (KiviCare style)
        Route::get('consultation/dashboard/{id}', function ($id) {
            return Inertia::render('Consultations/Consultation', ['consultationId' => $id]);
        })->name('consultation.dashboard');

        // Legacy routes for backward compatibility
        Route::get('consultations/create', function () {
            return Inertia::render('Consultations/Consultation');
        })->name('consultations.create');

        Route::get('consultations/{id}', function ($id) {
            return Inertia::render('Consultations/Show', ['consultationId' => $id]);
        })->name('consultations.show');

        Route::get('consultations/{id}/edit', function ($id) {
            return Inertia::render('Consultations/Consultation', ['consultationId' => $id]);
        })->name('consultations.edit');

        Route::get('consultations/from-appointment/{appointmentId}', function ($appointmentId) {
            return Inertia::render('Consultations/Consultation', ['appointmentId' => $appointmentId]);
        })->name('consultations.from-appointment');

        // Prescriptions
        Route::get('prescriptions', function () {
            return Inertia::render('Prescriptions/Index');
        })->name('prescriptions.index');

        // Medical Letters
        Route::get('medical-letters', function () {
            return Inertia::render('MedicalLetters/Index');
        })->name('medical-letters.index');

        // Doctor Settings
        Route::get('doctor-settings', function () {
            return Inertia::render('DoctorSettings/Index');
        })->name('doctor-settings.index');
    });

    // Clinical API routes (protected by clinician middleware)
    Route::middleware(['auth', 'verified', 'clinician'])->prefix('api')->group(function () {
        // Consultations API
        Route::get('consultations/template', [\App\Http\Controllers\ConsultationController::class, 'getTemplate']);
        Route::get('consultations', [\App\Http\Controllers\ConsultationController::class, 'index']);
        Route::post('consultations', [\App\Http\Controllers\ConsultationController::class, 'store']);
        Route::get('consultations/{id}', [\App\Http\Controllers\ConsultationController::class, 'show']);
        Route::put('consultations/{id}', [\App\Http\Controllers\ConsultationController::class, 'update']);
        Route::delete('consultations/{id}', [\App\Http\Controllers\ConsultationController::class, 'destroy']);

        // Consultation diagnoses routes
        Route::post('consultations/{consultation}/diagnoses', [\App\Http\Controllers\DiagnosisController::class, 'store']);
        Route::put('consultations/{consultation}/diagnoses/{diagnosis}', [\App\Http\Controllers\DiagnosisController::class, 'update']);
        Route::delete('consultations/{consultation}/diagnoses/{diagnosis}', [\App\Http\Controllers\DiagnosisController::class, 'destroy']);

        // Consultation treatment plans routes
        Route::post('consultations/{consultation}/treatment-plans', [\App\Http\Controllers\TreatmentPlanController::class, 'store']);
        Route::put('consultations/{consultation}/treatment-plans/{treatmentPlan}', [\App\Http\Controllers\TreatmentPlanController::class, 'update']);
        Route::delete('consultations/{consultation}/treatment-plans/{treatmentPlan}', [\App\Http\Controllers\TreatmentPlanController::class, 'destroy']);

        // Prescriptions API
        Route::get('prescriptions', [\App\Http\Controllers\PrescriptionController::class, 'index']);
        Route::post('prescriptions', [\App\Http\Controllers\PrescriptionController::class, 'store']);
        Route::get('prescriptions/{id}', [\App\Http\Controllers\PrescriptionController::class, 'show']);
        Route::put('prescriptions/{id}', [\App\Http\Controllers\PrescriptionController::class, 'update']);
        Route::delete('prescriptions/{id}', [\App\Http\Controllers\PrescriptionController::class, 'destroy']);

        // Medical Letters API
        Route::get('medical-letters', [\App\Http\Controllers\MedicalLetterController::class, 'index']);
        Route::post('medical-letters', [\App\Http\Controllers\MedicalLetterController::class, 'store']);
        Route::get('medical-letters/{id}', [\App\Http\Controllers\MedicalLetterController::class, 'show']);
        Route::put('medical-letters/{id}', [\App\Http\Controllers\MedicalLetterController::class, 'update']);
        Route::delete('medical-letters/{id}', [\App\Http\Controllers\MedicalLetterController::class, 'destroy']);
        Route::get('medical-letters/{id}/pdf', [\App\Http\Controllers\MedicalLetterController::class, 'generatePdf']);

        // Doctor Settings API
        Route::get('doctor-settings', [\App\Http\Controllers\DoctorSettingsController::class, 'show']);
        Route::put('doctor-settings', [\App\Http\Controllers\DoctorSettingsController::class, 'update']);

        // Medications API (for prescription forms)
        Route::get('medications', [\App\Http\Controllers\MedicationController::class, 'index']);
        Route::get('medications/search', [\App\Http\Controllers\MedicationController::class, 'search']);

        // ICD-10 API routes
        Route::prefix('icd10')->group(function () {
            Route::get('search', [\App\Http\Controllers\Icd10Controller::class, 'search']);
            Route::get('chapters', [\App\Http\Controllers\Icd10Controller::class, 'chapters']);
            Route::get('chapter/{chapter}', [\App\Http\Controllers\Icd10Controller::class, 'byChapter']);
            Route::get('popular', [\App\Http\Controllers\Icd10Controller::class, 'popular']);
            Route::get('{id}', [\App\Http\Controllers\Icd10Controller::class, 'show']);
        });
    });

    // Patients management routes
    Route::get('patients-list', [\App\Http\Controllers\PatientController::class, 'index']);
    Route::post('save-patient', [\App\Http\Controllers\PatientController::class, 'store']);
    Route::put('update-patient/{id}', [\App\Http\Controllers\PatientController::class, 'update']);

    // Debug route to check user permissions (can be removed in production)
    Route::get('debug-permissions', [UserController::class, 'debugPermissions'])->middleware('auth');

    // Debug route to check current user info
    Route::get('debug-user', function (Request $request) {
        $user = $request->user();
        return response()->json([
            'user' => $user,
            'roles' => $user->getRoleNames(),
            'permissions' => $user->getAllPermissions()->pluck('name'),
            'can_view_users' => $user->can('view users'),
            'total_users_in_db' => \App\Models\User::count()
        ]);
    })->middleware('auth');

    // Clinics management routes
    Route::get('clinics', [ManagementController::class, 'clinics'])->name('clinics')->middleware('permission:view clinics');
    Route::get('clinics-list', [\App\Http\Controllers\ClinicController::class, 'index']);
    Route::post('save-clinic', [\App\Http\Controllers\ClinicController::class, 'store']);
    Route::get('get-clinic/{id}', [\App\Http\Controllers\ClinicController::class, 'show']);
    Route::put('update-clinic/{id}', [\App\Http\Controllers\ClinicController::class, 'update']);
    Route::delete('delete-clinic/{id}', [\App\Http\Controllers\ClinicController::class, 'destroy']);
    Route::get('clinic-stats/{id}', [\App\Http\Controllers\ClinicController::class, 'getStats']);
    Route::get('clinic-users/{id}', [\App\Http\Controllers\ClinicController::class, 'getUsers']);

    // Service Categories management route (Vue component)
    Route::get('categories', [\App\Http\Controllers\CategoryController::class, 'indexAll'])->name('categories.index')->middleware('permission:view categories');

    // Categories API routes for Vue component
    Route::get('service-categories-list', [\App\Http\Controllers\CategoryController::class, 'getCategoriesList'])->middleware('permission:view categories');
    Route::post('categories-api', [\App\Http\Controllers\CategoryController::class, 'apiStore'])->middleware('permission:create categories');
    Route::put('categories-api/{id}', [\App\Http\Controllers\CategoryController::class, 'apiUpdate'])->middleware('permission:edit categories');
    Route::delete('categories-api/{id}', [\App\Http\Controllers\CategoryController::class, 'apiDestroy'])->middleware('permission:delete categories');
    Route::patch('categories-api/{id}/toggle-status', [\App\Http\Controllers\CategoryController::class, 'apiToggleStatus'])->middleware('permission:edit categories');

    // Categories management routes (clinic-specific API)
    Route::get('clinics/{clinicId}/categories', [\App\Http\Controllers\CategoryController::class, 'index']);
    Route::post('clinics/{clinicId}/categories', [\App\Http\Controllers\CategoryController::class, 'store']);
    Route::get('clinics/{clinicId}/categories/{id}', [\App\Http\Controllers\CategoryController::class, 'show']);
    Route::put('clinics/{clinicId}/categories/{id}', [\App\Http\Controllers\CategoryController::class, 'update']);
    Route::delete('clinics/{clinicId}/categories/{id}', [\App\Http\Controllers\CategoryController::class, 'destroy']);
    Route::get('clinics/{clinicId}/categories-active', [\App\Http\Controllers\CategoryController::class, 'getActiveCategories']);

    // Payments management routes
    Route::get('payments-list', [\App\Http\Controllers\PaymentController::class, 'index']);
    Route::get('payments-detail/{id}', [\App\Http\Controllers\PaymentController::class, 'show']);

    // Chats management routes
    Route::get('chats-list', [\App\Http\Controllers\ChatController::class, 'managementIndex']);
    Route::get('chats-stats', [\App\Http\Controllers\ChatController::class, 'getStats']);
    Route::get('chats-detail/{id}', [\App\Http\Controllers\ChatController::class, 'managementShow']);
    Route::get('chats-messages/{id}', [\App\Http\Controllers\ChatController::class, 'getMessages']);
    Route::post('chats-flag/{id}', [\App\Http\Controllers\ChatController::class, 'flagChat']);
    Route::post('chats-unflag/{id}', [\App\Http\Controllers\ChatController::class, 'unflagChat']);
    Route::post('chats-archive/{id}', [\App\Http\Controllers\ChatController::class, 'archiveChat']);
    Route::post('chats-add-message/{id}', [\App\Http\Controllers\ChatController::class, 'addManagementMessage']);

    // Permissions management routes
    Route::get('roles-list', [\App\Http\Controllers\Auth\PermissionController::class, 'getRoles']);
    Route::get('permissions-list', [\App\Http\Controllers\Auth\PermissionController::class, 'index']);
    Route::post('roles', [\App\Http\Controllers\Auth\PermissionController::class, 'createRole']);
    Route::put('roles/{id}', [\App\Http\Controllers\Auth\PermissionController::class, 'updateRole']);
    Route::delete('roles/{id}', [\App\Http\Controllers\Auth\PermissionController::class, 'deleteRole']);
    Route::post('assign-permissions', [\App\Http\Controllers\Auth\PermissionController::class, 'assignPermissions']);

    // Impersonation routes
    Route::middleware('role:admin')->group(function () {
        Route::post('impersonate/{userId}', [\App\Http\Controllers\ImpersonationController::class, 'start'])->name('impersonate.start');
    });

    // These routes need to be accessible to all authenticated users (including impersonated users)
    Route::middleware('auth')->group(function () {
        Route::post('stop-impersonation', [\App\Http\Controllers\ImpersonationController::class, 'stop'])->name('impersonate.stop');
        Route::get('impersonation-status', [\App\Http\Controllers\ImpersonationController::class, 'status'])->name('impersonate.status');
    });

    // Approval routes (admin only)
    Route::middleware('role:admin')->group(function () {
        // Product approval routes
        Route::get('pending-products', [ProductManagementController::class, 'getPendingProducts'])->name('admin.products.pending');
        Route::post('approve-product/{id}', [ProductManagementController::class, 'approveProduct'])->name('admin.products.approve');
        Route::post('reject-product/{id}', [ProductManagementController::class, 'rejectProduct'])->name('admin.products.reject');

        // Service approval routes
        Route::get('pending-services', [ServiceController::class, 'getPendingServices'])->name('admin.services.pending');
        Route::post('approve-service/{id}', [ServiceController::class, 'approveService'])->name('admin.services.approve');
        Route::post('reject-service/{id}', [ServiceController::class, 'rejectService'])->name('admin.services.reject');
    });

    // Email templates routes (session-based authentication for web app)
    Route::get('email-templates-list', [\App\Http\Controllers\EmailTemplateController::class, 'index']);
    Route::get('email-templates/{id}', [\App\Http\Controllers\EmailTemplateController::class, 'show']);
    Route::post('email-templates', [\App\Http\Controllers\EmailTemplateController::class, 'store']);
    Route::put('email-templates/{id}', [\App\Http\Controllers\EmailTemplateController::class, 'update']);
    Route::delete('email-templates/{id}', [\App\Http\Controllers\EmailTemplateController::class, 'destroy']);
    Route::post('email-templates/{id}/test', [\App\Http\Controllers\EmailTemplateController::class, 'sendTestEmail']);


    Route::post('test-email-config', [\App\Http\Controllers\EmailTemplateController::class, 'testEmailConfiguration']);

    // System verification routes
    Route::post('verify-transaction-system', [\App\Http\Controllers\SystemVerificationController::class, 'verifyTransactionSystem']);
    Route::post('verify-anonymous-chat-mapping', [\App\Http\Controllers\SystemVerificationController::class, 'verifyAnonymousChatMapping']);

    // Notifications routes (session-based authentication for web app)
    Route::get('notifications-list', [\App\Http\Controllers\NotificationTemplateController::class, 'index']);
    Route::get('notifications/{id}', [\App\Http\Controllers\NotificationTemplateController::class, 'show']);
    Route::put('notifications/{id}', [\App\Http\Controllers\NotificationTemplateController::class, 'update']);

    // Enhanced notification management routes
    Route::post('notifications/send-by-device-type', [\App\Http\Controllers\NotificationManagementController::class, 'sendByDeviceType']);
    Route::post('notifications/send-by-browser', [\App\Http\Controllers\NotificationManagementController::class, 'sendByBrowser']);
    Route::post('notifications/send-by-platform', [\App\Http\Controllers\NotificationManagementController::class, 'sendByPlatform']);

    // Referrals routes (session-based authentication for web app)
    Route::get('referrals-code', [ReferralController::class, 'getReferralCode']);
    Route::post('send-referral-invite', [ReferralController::class, 'createReferral']);
    Route::get('referrals-my', [ReferralController::class, 'getUserReferrals']);
    Route::get('referrals-list', [ReferralController::class, 'index']);
    Route::get('referrals-stats', [ReferralController::class, 'getStats']);

    // Credits routes (session-based authentication for web app)
    Route::get('credits-list', [\App\Http\Controllers\UserCreditController::class, 'index']);
    Route::get('credits-balance', [\App\Http\Controllers\UserCreditController::class, 'getCreditBalance']);
    Route::get('credits-transactions', [\App\Http\Controllers\UserCreditController::class, 'getTransactionHistory']);
    Route::get('credits-all-transactions', [\App\Http\Controllers\UserCreditController::class, 'getAllCreditTransactions']);
    Route::get('credits-stats', [\App\Http\Controllers\UserCreditController::class, 'getCreditsStats']);
    Route::post('credits-add', [\App\Http\Controllers\UserCreditController::class, 'addCredits']);
    Route::get('users-search', [\App\Http\Controllers\UserController::class, 'index']);

    // Clubs routes (session-based authentication for web app)
    Route::get('clubs-list', [\App\Http\Controllers\Admin\ClubManagementController::class, 'getClubMembers']);
    Route::get('clubs-stats', [\App\Http\Controllers\Admin\ClubManagementController::class, 'getClubStats']);
    Route::get('clubs-codes', [\App\Http\Controllers\Admin\ClubManagementController::class, 'getFounderCodes']);

    // Waitlist management routes
    Route::get('waitlist-stats', [\App\Http\Controllers\Admin\WaitlistController::class, 'getStats']);
    Route::get('waitlist-analytics', [\App\Http\Controllers\Admin\WaitlistController::class, 'getSignupAnalytics']);
    Route::post('waitlist-toggle', [\App\Http\Controllers\Admin\WaitlistController::class, 'toggleWaitlistMode']);

    // Admin ecommerce API routes (session-based authentication for web app)
    Route::prefix('admin')->group(function () {
        // Bulk import routes (must be before products/{id} routes)
        Route::get('products/import-template', [ProductManagementController::class, 'downloadImportTemplate']);
        Route::get('products/import-instructions', [ProductManagementController::class, 'downloadImportInstructions']);
        Route::post('products/validate-import', [ProductManagementController::class, 'validateImport']);
        Route::post('products/bulk-import', [ProductManagementController::class, 'bulkImport']);

        // Product management API
        Route::get('products-list', [ProductManagementController::class, 'index']);
        Route::post('save-product', [ProductManagementController::class, 'store']);
        Route::put('save-product/{id}', [ProductManagementController::class, 'update']);
        Route::delete('delete-product/{id}', [ProductManagementController::class, 'destroy']);
        Route::patch('toggle-product-status/{id}', [ProductManagementController::class, 'toggleStatus']);

        // Product category management API
        Route::get('categories-list', [\App\Http\Controllers\Admin\ProductCategoryController::class, 'index']);
        Route::post('save-category', [\App\Http\Controllers\Admin\ProductCategoryController::class, 'store']);
        Route::put('save-category/{id}', [\App\Http\Controllers\Admin\ProductCategoryController::class, 'update']);
        Route::delete('delete-category/{id}', [\App\Http\Controllers\Admin\ProductCategoryController::class, 'destroy']);
        Route::patch('toggle-category-status/{id}', [\App\Http\Controllers\Admin\ProductCategoryController::class, 'toggleStatus']);

        // Order management API
        Route::get('orders-list', [OrderManagementController::class, 'index']);
        Route::get('order-details/{id}', [OrderManagementController::class, 'show']);
        Route::patch('update-order-status/{id}', [OrderManagementController::class, 'updateStatus']);
        Route::patch('update-payment-status/{id}', [OrderManagementController::class, 'updatePaymentStatus']);
        Route::post('add-order-note/{id}', [OrderManagementController::class, 'addNote']);
        Route::get('order-stats', [OrderManagementController::class, 'getOrderStats']);
        Route::get('export-orders', [OrderManagementController::class, 'exportOrders']);
    });

    // Waitlist invitation management routes
    Route::get('waitlist-requests', [\App\Http\Controllers\WaitlistController::class, 'getWaitlistRequests']);
    Route::post('waitlist-requests/{requestId}/invite', [\App\Http\Controllers\WaitlistController::class, 'sendInvitation']);
    Route::post('waitlist-requests/bulk-invite', [\App\Http\Controllers\WaitlistController::class, 'sendBulkInvitations']);
    Route::post('send-bulk-invitations', [\App\Http\Controllers\WaitlistController::class, 'sendBulkInvitations']);
    Route::get('waitlist-invitation-stats', [\App\Http\Controllers\WaitlistController::class, 'getInvitationStats']);

    // Club invitation routes
    Route::post('send-club-invitation', [\App\Http\Controllers\Admin\ClubManagementController::class, 'sendClubInvitation']);
    Route::post('send-bulk-club-invitations', [\App\Http\Controllers\Admin\ClubManagementController::class, 'sendBulkClubInvitations']);

    // Provider availability API routes
    Route::middleware(['auth', 'verified'])->prefix('provider')->group(function () {
        Route::get('get-availability', [ProviderAvailabilityController::class, 'getWeeklyAvailability']);
        Route::post('save-availability', [ProviderAvailabilityController::class, 'postWeeklyAvailability']);
        Route::put('save-availability', [ProviderAvailabilityController::class, 'updateWeeklyAvailability']);
        Route::get('availability-data', [ProviderAvailabilityController::class, 'availability']);

        // Provider absences routes
        Route::get('get-absences', [ProviderAvailabilityController::class, 'getAbsences']);
        Route::post('absences', [ProviderAvailabilityController::class, 'postAbsences']);
        Route::put('absences', [ProviderAvailabilityController::class, 'updateAbsences']);

        // Provider dashboard data
        Route::get('get-dashboard-data', [ProviderController::class, 'getDashboardData']);
        Route::get('get-appointments', [ProviderController::class, 'getAppointments']);
        Route::get('get-patients', [ProviderController::class, 'getPatients']);
        Route::get('search-patients', [ProviderController::class, 'searchPatients']);
        Route::post('create-patient', [ProviderController::class, 'createPatient']);

        // Debug route to check user roles and permissions
        Route::get('debug-user-info', function(Request $request) {
            $user = Auth::user();
            return response()->json([
                'user_id' => $user->id,
                'user_role' => $user->role,
                'spatie_roles' => $user->roles->pluck('name')->toArray(),
                'has_provider_role' => $user->hasRole('provider'),
                'can_view_patients' => $user->can('view patients'),
                'all_permissions' => $user->getAllPermissions()->pluck('name')->toArray(),
                'clinic_id' => $user->clinic_id,
                'is_clinician' => $user->is_clinician
            ]);
        });

        // Patient-Provider assignment routes (for clinic admins and admins)
        Route::post('assign-patient-to-provider', [ProviderController::class, 'assignPatientToProvider']);
        Route::post('remove-patient-from-provider', [ProviderController::class, 'removePatientFromProvider']);
        Route::get('patient/{patientId}/providers', [ProviderController::class, 'getPatientProviders']);

        // Debug route for testing patient creation
        Route::post('debug-create-patient', function(Request $request) {
            \Log::info('Debug patient creation request', [
                'all_data' => $request->all(),
                'headers' => $request->headers->all(),
                'user' => auth()->user(),
            ]);

            return response()->json([
                'message' => 'Debug data logged',
                'received_data' => $request->all(),
                'user' => auth()->user(),
            ]);
        });

        // Provider profile
        Route::get('get-profile', [ProviderController::class, 'getProfile']);
        Route::post('profile', [ProviderController::class, 'createOrUpdateProfile']);



        // Provider product management API (providers can manage their own products by default)
        Route::get('products-list', [ProductManagementController::class, 'index']);
        Route::post('save-product', [ProductManagementController::class, 'store']);
        Route::put('save-product/{id}', [ProductManagementController::class, 'update']);
        Route::delete('delete-product/{id}', [ProductManagementController::class, 'destroy']);
        Route::patch('toggle-product-status/{id}', [ProductManagementController::class, 'toggleStatus']);

        // Provider earnings
        Route::get('get-earnings', [ProviderController::class, 'getDashboardData']);
    });

    // Management dashboard API routes - KPI route is defined earlier without permission restriction

    // Appointment management routes (following availability and services pattern)
    Route::get('appointments-list', [AppointmentController::class, 'userAppointments']);
    Route::get('get-appointments', [AppointmentController::class, 'userAppointments']);
    Route::post('save-appointment', [AppointmentController::class, 'store']);
    Route::put('save-appointment/{id}', [AppointmentController::class, 'update']);
    Route::delete('delete-appointment/{id}', [AppointmentController::class, 'destroy']);

    // Provider routes (following standard pattern)
    Route::get('get-providers/{id}/services', [ServiceController::class, 'index']);
    Route::get('get-providers/{providerId}/available-slots', [ProviderAvailabilityController::class, 'getAvailableTimeSlots']);

    // Payment routes (following standard pattern)
    Route::post('save-appointment-with-payment', [PaymentController::class, 'createAppointmentWithPayment']);
    Route::post('process-web-payment', [PaymentController::class, 'processWebPayment']);
    Route::post('confirm-elements-payment', [PaymentController::class, 'confirmElementsPayment']);

    Route::get('api/appointments/{id}', [AppointmentController::class, 'show']);

    // Ecommerce routes (session-based authentication for web app)
    Route::prefix('shop')->group(function () {
        // Product routes
        Route::get('products', [ProductController::class, 'index']);
        Route::get('products/{slug}', [ProductController::class, 'show']);
        Route::get('categories', [ProductController::class, 'categories']);
        Route::get('featured-products', [ProductController::class, 'featured']);
        Route::get('search-products', [ProductController::class, 'search']);

        // Product review routes
        Route::get('products/{product}/reviews', [\App\Http\Controllers\ProductReviewController::class, 'index']);
        Route::middleware('auth')->group(function () {
            Route::post('products/{product}/reviews', [\App\Http\Controllers\ProductReviewController::class, 'store']);
            Route::put('products/{product}/reviews/{review}', [\App\Http\Controllers\ProductReviewController::class, 'update']);
            Route::delete('products/{product}/reviews/{review}', [\App\Http\Controllers\ProductReviewController::class, 'destroy']);
            Route::get('products/{product}/can-review', [\App\Http\Controllers\ProductReviewController::class, 'canReview']);
        });

        // Shopping cart routes
        Route::get('cart', [ShoppingCartController::class, 'index']);
        Route::post('cart/add', [ShoppingCartController::class, 'add']);
        Route::put('cart/{productId}', [ShoppingCartController::class, 'update']);
        Route::delete('cart/{productId}', [ShoppingCartController::class, 'remove']);
        Route::delete('cart', [ShoppingCartController::class, 'clear']);
        Route::get('cart/count', [ShoppingCartController::class, 'count']);

        // Order routes
        Route::get('orders', [OrderController::class, 'index']);
        Route::get('orders/{orderNumber}', [OrderController::class, 'show']);
        Route::post('orders/{orderNumber}/cancel', [OrderController::class, 'cancel']);
        Route::get('checkout', [OrderController::class, 'showCheckout']);
        Route::post('checkout', [OrderController::class, 'checkout']);
        Route::post('orders/{orderNumber}/confirm-payment', [OrderController::class, 'confirmPayment']);

        // Digital downloads
        Route::get('downloads', [DigitalDownloadController::class, 'index']);
        Route::get('downloads/user', [DigitalDownloadController::class, 'userDownloads']);
        Route::get('downloads/{token}', [DigitalDownloadController::class, 'show']);
    });

    // Public digital download route (no auth required)
    Route::get('download/{token}', [DigitalDownloadController::class, 'download'])->name('digital-download');

    // Instagram Integration Routes
    Route::get('auth/instagram', [\App\Http\Controllers\InstagramAuthController::class, 'redirectToInstagram'])->name('instagram.auth');
    Route::post('instagram/disconnect', [\App\Http\Controllers\InstagramAuthController::class, 'disconnect'])->name('instagram.disconnect');
    Route::post('instagram/sync', [\App\Http\Controllers\InstagramAuthController::class, 'syncContent'])->name('instagram.sync');

    // Web API Instagram routes (session-based authentication)
    Route::prefix('web-api/instagram')->group(function () {
        Route::get('auth-url', [\App\Http\Controllers\InstagramController::class, 'getAuthUrl']);
        Route::get('account-status', [\App\Http\Controllers\InstagramController::class, 'getAccountStatus']);
        Route::get('connection-progress', [\App\Http\Controllers\InstagramController::class, 'getConnectionProgress']);
        Route::post('disconnect', [\App\Http\Controllers\InstagramController::class, 'disconnect']);
        Route::post('sync', [\App\Http\Controllers\InstagramController::class, 'syncContent']);
        Route::post('refresh-token', [\App\Http\Controllers\InstagramController::class, 'refreshToken']);
        Route::get('feed-content', [\App\Http\Controllers\InstagramController::class, 'getFeedContent']);
        Route::get('stories', [\App\Http\Controllers\InstagramController::class, 'getStories']);
    });
});

// PUBLIC FEED ROUTES (No authentication required for viewing)
Route::prefix('web-api/feed')->group(function () {
    // Public read-only routes
    Route::get('/', [\App\Http\Controllers\SocialFeedController::class, 'index'])
        ->middleware('performance:feed_loading');
    Route::get('search', [\App\Http\Controllers\SocialFeedController::class, 'search'])
        ->middleware('performance:feed_loading');
    Route::get('post/{postId}', [\App\Http\Controllers\SocialFeedController::class, 'getPost']);
    Route::get('topics', [\App\Http\Controllers\SocialFeedController::class, 'topics']);
    Route::get('{contentId}', [\App\Http\Controllers\SocialFeedController::class, 'getPost']); // For sharing
    Route::get('{contentId}/comments', [\App\Http\Controllers\SocialFeedController::class, 'getComments']);
    Route::get('{contentId}/replies', [\App\Http\Controllers\CommentController::class, 'getReplies']);
});

// Debug route for feed testing (REMOVE AFTER DEBUGGING)
Route::get('/debug/feed-test', function() {
    try {
        // Test basic database connection
        $dbTest = DB::select('SELECT 1 as test');

        // Test SocialContent model
        $contentCount = \App\Models\SocialContent::count();

        // Test the actual feed query
        $content = \App\Models\SocialContent::with(['user:id,name,profile_image', 'comments:id,social_content_id'])
            ->select([
                'id', 'user_id', 'caption', 'media_url', 'thumbnail_url', 'video_url',
                'content_type', 'source', 'published_at', 'created_at', 'engagement_metrics',
                'relevance_score', 'health_topics', 'instagram_username'
            ])
            ->where('filtered_status', 'approved')
            ->whereIn('source', ['internal', 'bot_generated', 'instagram'])
            ->orderByRaw('CASE WHEN source = "internal" THEN 0 WHEN source = "bot_generated" THEN 1 ELSE 2 END')
            ->orderBy('published_at', 'desc')
            ->paginate(10);

        return response()->json([
            'success' => true,
            'db_connection' => 'OK',
            'content_count' => $contentCount,
            'feed_items' => $content->count(),
            'sample_data' => $content->take(2)
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
});

// Bot Management Routes (Admin Only)
Route::middleware(['auth'])->prefix('admin/bot-management')->name('admin.bot-management.')->group(function () {
    // Temporarily removed role:admin middleware for testing
    Route::get('/', [\App\Http\Controllers\Admin\BotManagementController::class, 'dashboard'])->name('dashboard');
    Route::post('/generate-all', [\App\Http\Controllers\Admin\BotManagementController::class, 'generateAllPosts'])->name('generate-all');
    Route::post('/generate/{persona}', [\App\Http\Controllers\Admin\BotManagementController::class, 'generatePersonaPost'])->name('generate-persona');
    Route::get('/analytics', [\App\Http\Controllers\Admin\BotManagementController::class, 'analytics'])->name('analytics');
    Route::get('/post/{post}/details', [\App\Http\Controllers\Admin\BotManagementController::class, 'getPostDetails'])->name('post-details');
    Route::get('/test-system', [\App\Http\Controllers\Admin\BotManagementController::class, 'testSystem'])->name('test-system');
    Route::get('/job-progress', [\App\Http\Controllers\Admin\BotManagementController::class, 'getJobProgress'])->name('job-progress');
    Route::get('/pending-jobs', [\App\Http\Controllers\Admin\BotManagementController::class, 'getPendingJobs'])->name('pending-jobs');
    Route::post('/cancel-jobs', [\App\Http\Controllers\Admin\BotManagementController::class, 'cancelJobs'])->name('cancel-jobs');
    Route::post('/cancel-specific-job', [\App\Http\Controllers\Admin\BotManagementController::class, 'cancelSpecificJob'])->name('cancel-specific-job');
    Route::post('/delete-failed-post', [\App\Http\Controllers\Admin\BotManagementController::class, 'deleteFailedPost'])->name('delete-failed-post');
    Route::get('/scheduled-posts', [\App\Http\Controllers\Admin\BotManagementController::class, 'getScheduledPosts'])->name('scheduled-posts');
    Route::post('/update-settings', [\App\Http\Controllers\Admin\BotManagementController::class, 'updateSettings'])->name('update-settings');
    Route::post('/trigger-scheduling', [\App\Http\Controllers\Admin\BotManagementController::class, 'triggerScheduling'])->name('trigger-scheduling');
    Route::post('/clear-cache', [\App\Http\Controllers\Admin\BotManagementController::class, 'clearCache'])->name('clear-cache');
    Route::get('/real-time-stats', [\App\Http\Controllers\Admin\BotManagementController::class, 'getRealTimeStats'])->name('real-time-stats');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';

// Stripe Configuration Routes for Vue.js components (no auth required for public booking)
Route::get('stripe/config', [App\Http\Controllers\StripeConfigController::class, 'getConfig']);
Route::get('stripe/check-clinic', [App\Http\Controllers\StripeConfigController::class, 'checkClinicConfig']);
Route::get('stripe/test-integration', [App\Http\Controllers\StripeConfigController::class, 'testIntegration']);

