<div class="">
    <div class="flex items-center justify-between mb-6">
        <div class="iq-kivi-tab-panel-title-animation">
            <h3 class="text-xl font-semibold text-gray-800"><?php echo esc_html__('Enter Details', 'kc-lang'); ?></h3>
        </div>
    </div>

    <!-- Tab Navigation -->
    <div class="flex space-x-4 mb-6">
        <a href="#kc_register" id="register-tab" data-iq-toggle="tab"
            class="flex-1 py-2 px-4 rounded-lg font-medium transition-colors bg-purple-600 text-white text-center active">
            <?php echo esc_html__('Register', 'kc-lang'); ?>
        </a>
        <a href="#kc_login" id="login-tab" data-iq-toggle="tab"
            class="flex-1 py-2 px-4 rounded-lg font-medium transition-colors bg-gray-100 text-gray-600 hover:bg-gray-200 text-center">
            <?php echo esc_html__('Login', 'kc-lang'); ?>
        </a>
    </div>

    <div class="widget-content">
        <div id="login-register-panel" class="card-list-data">
            <!-- Register Form -->
            <div id="kc_register" class="iq-tab-pannel kivicare-register-form-data iq-fade active authActive">
                <div id="kivicare-register-form">
                    <div class="space-y-6" id="kivicare-register">
                        <?php if (kcGoogleCaptchaData('status') === 'on') { ?>
                            <input type="hidden" id="g-recaptcha-response" name="g-recaptcha-response">
                            <input type="hidden" name="captcha_action" value="validate_captcha">
                        <?php } ?>
                        <input type="hidden" name="widgettype" value="new_appointment_widget" id="widgettype">
                        <input type="hidden" id="registerClinicId">

                        <!-- Name Fields -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1 text-left" for="firstName">
                                    <?php echo esc_html__('First Name', 'kc-lang'); ?><span
                                        class="text-red-500">*</span>
                                </label>
                                <input type="text" name="first_name" id="firstName"
                                    class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-black
                                    placeholder="<?php echo esc_html__('Enter your first name', 'kc-lang'); ?>"
                                    required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1 text-left" for="lastName">
                                    <?php echo esc_html__('Last Name', 'kc-lang'); ?><span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="last_name" id="lastName"
                                    class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-black
                                    placeholder="<?php echo esc_html__('Enter your last name', 'kc-lang'); ?>" required>
                            </div>
                        </div>

                        <!-- Email Field -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1 text-left" for="userEmail">
                                <?php echo esc_html__('Email', 'kc-lang'); ?><span class="text-red-500">*</span>
                            </label>
                            <input type="email" name="user_email" id="userEmail"
                                class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-black
                                placeholder="<?php echo esc_html__('Enter your email', 'kc-lang'); ?>" required>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Contact Field -->
                            <div class="">
                                <label class="block text-sm font-medium text-gray-700 mb-1 text-left" for="userContact">
                                    <?php echo esc_html__('Contact', 'kc-lang'); ?>
                                    <span>*</span></label>
                                <div class="contact-box-inline">
                                    <?php
                                    if (file_exists(KIVI_CARE_DIR . 'assets/helper_assets/CountryCodes.json')) {
                                        $json_country_code = file_get_contents(KIVI_CARE_DIR . 'assets/helper_assets/CountryCodes.json');
                                        $country_code = json_decode($json_country_code, true);

                                        ?>

                                        <select name="country_code"
                                            class="iq-kivicare-form-control w-1/4 px-4 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                                            id="CountryCode">
                                            <?php
                                            foreach ($country_code as $id => $code) {
                                                $valueString = '{"countryCallingCode":"' . $code['dial_code'] . '","countryCode":"' . $code['code'] . '"}';
                                                ?>
                                                <option value="<?php echo esc_html($valueString, 'kc-lang'); ?>" <?php echo esc_html(($code['code'] == 'US') ? "selected" : "", 'kc-lang') ?>>
                                                    <?php echo esc_html($code['dial_code'] . " - " . $code['name'], 'kc-lang'); ?>
                                                </option>
                                                <?php
                                            }
                                            ?>
                                        </select>
                                        <?php
                                    } else {
                                        ?>
                                        <input type="text" name="country_code"
                                            class="iq-kivicare-form-control w-3/4 px-4 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                                            id="txt_CountryCode"
                                            placeholder="<?php echo esc_html('Enter your country code', 'kc-lang'); ?>"
                                            required>
                                        <?php
                                    }
                                    ?>

                                    <input type="tel" name="mobile_number"
                                        class="iq-kivicare-form-control w-3/4 px-4 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                                        id="userContact"
                                        placeholder="<?php echo esc_html__('Enter your contact number', 'kc-lang'); ?>"
                                        required style="width: unset;display: inline-block;">
                                </div>

                            </div>


                            <!-- Date of Birth Field -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1 text-left " for="dob">
                                    <?php echo esc_html__('Date of Birth', 'kc-lang'); ?><span
                                        class="text-red-500">*</span>
                                </label>
                                <input type="date" name="dob" id="dob"
                                    class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-black
                                    required style="text-transform: uppercase;">
                            </div>

                            <!-- NHS Number Field -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1 text-left" for="nhs">
                                    <?php echo esc_html__('NHS No.', 'kc-lang'); ?>
                                </label>
                                <input type="text" name="nhs" id="nhs"
                                    class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-black
                                    placeholder="<?php echo esc_html__('Enter NHS No.', 'kc-lang'); ?>">
                            </div>

                            <!-- Gender Field -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2 text-left">
                                    <?php echo esc_html__('Gender', 'kc-lang'); ?><span class="text-red-500">*</span>
                                </label>
                                <div class="flex space-x-4">
                                    <label class="inline-flex items-center">
                                        <input type="radio" name="gender" value="male"
                                            class="form-radio text-purple-600 focus:ring-purple-500" required>
                                        <span class="ml-2"><?php echo esc_html__('Male', 'kc-lang'); ?></span>
                                    </label>
                                    <label class="inline-flex items-center">
                                        <input type="radio" name="gender" value="female"
                                            class="form-radio text-purple-600 focus:ring-purple-500">
                                        <span class="ml-2"><?php echo esc_html__('Female', 'kc-lang'); ?></span>
                                    </label>
                                    <label class="inline-flex items-center">
                                        <input type="radio" name="gender" value="other"
                                            class="form-radio text-purple-600 focus:ring-purple-500">
                                        <span class="ml-2"><?php echo esc_html__('Other', 'kc-lang'); ?></span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Address Field -->
                        <div class="mb-4">
                            <label for="address" class="block text-sm font-medium text-gray-700 mb-1 text-left">
                                <?php echo esc_html__('Address', 'kc-lang'); ?> <span class="text-red-500">*</span>
                            </label>
                            <textarea name="address" id="address" rows="3"
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 outline-none transition duration-200 placeholder-gray-400"
                                placeholder="<?php echo esc_html__('Enter your address', 'kc-lang'); ?>"
                                required></textarea>
                        </div>

                        <?php do_action('kivicare_widget_register_form_field_add'); ?>
                    </div>
                    <div class="border rounded-lg overflow-hidden bg-white transition-all duration-200 mb-2 mt-2">
                        <button data-toggle="" type="button"
                            class="w-full px-4 py-3 flex items-center justify-between bg-white hover:bg-gray-50">
                            <div class="flex items-center space-x-3">
                                <div class="p-1 rounded-full bg-gray-100">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-user w-5 h-5 text-fuchsia-600">
                                        <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
                                        <circle cx="12" cy="7" r="4"></circle>
                                    </svg>
                                </div>
                                <h3 class="text-lg font-medium text-gray-900">
                                    Identity Information </h3>
                            </div>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round"
                                class="lucide lucide-chevron-right w-5 h-5 text-gray-400 transition-transform duration-200 rotate-90">
                                <path d="m9 18 6-6-6-6"></path>
                            </svg>
                        </button>
                        <div class="toggle-section transition-all duration-200 block">
                            <div class="p-4 border-t">
                                <div class="space-y-4">
                                    <!-- <div class="space-y-2">
                                        <label class="block text-sm font-medium text-gray-700 px-1 text-left">
                                            Identity Document 
                                        </label>
                                        <div class="file-upload-div border-2 border-dashed rounded-lg p-4">
                                            <div class="flex flex-col items-center justify-center space-y-2">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="lucide lucide-upload w-8 h-8 text-gray-400">
                                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                                    <polyline points="17 8 12 3 7 8"></polyline>
                                                    <line x1="12" x2="12" y1="3" y2="15"></line>
                                                </svg>
                                                <span class="text-sm text-gray-600">Drop files here or click to
                                                    upload</span>
                                            </div>
                                            <input class="hidden" type="file"
                                                accept="image/png, application/pdf, image/jpeg" name="identity_document"
                                                style="border: 1px solid rgb(238, 238, 238);">
                                        </div>
                                    </div> -->
                                    <div class="space-y-2">
                                        <label class="block text-sm font-medium text-gray-700 px-1 text-left">
                                            Your registered GP Name? <span class="text-red-500">*</span>
                                        </label>
                                        <input id="Your registered GP Name?_2" placeholder="" name="registered_gp_name"
                                            type="text"
                                            class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-black">
                                    </div>
                                    <div class="space-y-2">
                                        <label class="block text-sm font-medium text-gray-700 px-1 text-left">
                                            Registered GP's address? <span class="text-red-500">*</span>
                                        </label>
                                        <input id="Registered GP's address?_3" placeholder=""
                                            name="registered_gp_address" type="text"
                                            class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-black">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="customFieldsList">
                        <?php // kcGetCustomFieldsList_delete('patient_module', 0); ?>
                        <?php kcGetCustomFieldsListNewDesign('patient_module', 0); ?>
                    </div>
                </div>
            </div>

            <!-- Login Form -->
            <div id="kc_login" class="iq-tab-pannel kivicare-login-form-data iq-fade authActive hidden">
                <div id="kivicare-login-form">
                    <div class="space-y-4" id="kivicare-login">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1 text-left" for="loginUsername">
                                <?php echo esc_html__('Username or Email', 'kc-lang'); ?><span
                                    class="text-red-500">*</span>
                            </label>
                            <input type="text" name="username" id="loginUsername"
                                class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-black
                                placeholder="<?php echo esc_html__('Enter your username or email', 'kc-lang'); ?>">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1 text-left" for="loginPassword">
                                <?php echo esc_html__('Password', 'kc-lang'); ?><span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <input type="password" name="password" id="loginPassword"
                                    class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-black
                                    placeholder="***********">
                                <button type="button" id="togglePassword"
                                    class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        <div class="text-right">
                            <a href="<?php echo esc_url(wp_lostpassword_url()); ?>" target="_blank"
                                class="text-sm text-purple-600 hover:text-purple-700">
                                <?php echo esc_html__('Forgot Password ?', 'kc-lang'); ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add this JavaScript at the bottom of your page or in your scripts file -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        initializeTabSwitching();
        initializePasswordToggle();
        initializeFormValidation();
        initializeFileUpload();
    });

    // Tab switching functionality
    function initializeTabSwitching() {
        const tabs = document.querySelectorAll('[data-iq-toggle="tab"]');
        const panels = document.querySelectorAll('.iq-tab-pannel');

        tabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                e.preventDefault();

                // Remove active classes
                tabs.forEach(t => {
                    t.classList.remove('bg-purple-600', 'text-white');
                    t.classList.add('bg-gray-100', 'text-gray-600');
                });
                panels.forEach(p => {
                    p.classList.add('hidden');
                });

                // Add active classes
                tab.classList.remove('bg-gray-100', 'text-gray-600');
                tab.classList.add('bg-purple-600', 'text-white');

                const targetId = tab.getAttribute('href');
                document.querySelector(targetId).classList.remove('hidden');
            });
        });

        // Show default tab on page load
        const defaultTab = document.querySelector('[data-iq-toggle="tab"].active');
        if (defaultTab) {
            const defaultTargetId = defaultTab.getAttribute('href');
            document.querySelector(defaultTargetId).classList.remove('hidden');
        }
    }

    // Password visibility toggle
    function initializePasswordToggle() {
        const togglePassword = document.getElementById('togglePassword');
        const passwordInput = document.getElementById('loginPassword');

        if (togglePassword && passwordInput) {
            togglePassword.addEventListener('click', () => {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);

                // Toggle eye icon
                togglePassword.querySelector('i').classList.toggle('fa-eye');
                togglePassword.querySelector('i').classList.toggle('fa-eye-slash');
            });
        }
    }

    // Form validation
    function initializeFormValidation() {
        const registerForm = document.getElementById('kivicare-register-form');
        const loginForm = document.getElementById('kivicare-login-form');

        function validateForm(form) {
            let isValid = true;
            const requiredFields = form.querySelectorAll('[required]');

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    field.classList.add('border-red-500');
                    // Add error message
                    let errorMsg = field.nextElementSibling;
                    if (!errorMsg || !errorMsg.classList.contains('error-message')) {
                        errorMsg = document.createElement('span');
                        errorMsg.classList.add('error-message', 'text-red-500', 'text-sm', 'mt-1');
                        field.parentNode.insertBefore(errorMsg, field.nextSibling);
                    }
                    errorMsg.textContent = 'This field is required';
                }
            });

            return isValid;
        }

        // Add input listeners for validation
        const inputs = document.querySelectorAll('input[required], textarea[required], select[required]');
        inputs.forEach(input => {
            input.addEventListener('input', () => {
                input.classList.remove('border-red-500');
                const errorMsg = input.nextElementSibling;
                if (errorMsg && errorMsg.classList.contains('error-message')) {
                    errorMsg.remove();
                }
            });
        });

        // Handle form submissions
        if (registerForm) {
            registerForm.addEventListener('submit', (e) => {
                e.preventDefault();
                if (validateForm(registerForm)) {
                    console.log('Register form is valid, submitting...');
                }
            });
        }

        if (loginForm) {
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                if (validateForm(loginForm)) {
                    console.log('Login form is valid, submitting...');
                }
            });
        }
    }

    // File upload functionality
    function initializeFileUpload() {
        const fileUploadDivs = document.querySelectorAll('.file-upload-div');

        fileUploadDivs.forEach(div => {
            const fileInput = div.querySelector('input[type="file"]');
            if (!fileInput) return;

            // Store the default content
            const defaultContent = div.innerHTML;
            div.setAttribute('data-default-content', defaultContent);

            // Remove previous event listeners if any
            const newFileInput = fileInput.cloneNode(true);
            fileInput.parentNode.replaceChild(newFileInput, fileInput);

            // Make only the upload zone clickable, not the entire div
            const uploadZone = div.querySelector('.flex.flex-col');
            if (uploadZone) {
                uploadZone.addEventListener('click', function (e) {
                    e.stopPropagation(); // Prevent event from bubbling to parent div
                    if (!div.querySelector('.file-info')) {
                        newFileInput.click();
                    }
                });
            }

            // Handle file selection
            newFileInput.addEventListener('change', function (e) {
                const file = this.files[0];
                if (file) {
                    // Validate file type
                    const validTypes = ['image/png', 'application/pdf', 'image/jpeg'];
                    if (!validTypes.includes(file.type)) {
                        alert('Please upload only PNG, PDF, or JPEG files');
                        resetFileUpload(div, defaultContent, newFileInput);
                        return;
                    }

                    // Validate file size (5MB)
                    const maxSize = 5 * 1024 * 1024;
                    if (file.size > maxSize) {
                        alert('File size should not exceed 5MB');
                        resetFileUpload(div, defaultContent, newFileInput);
                        return;
                    }

                    // Update UI to show file info
                    updateFileUploadUI(div, file, newFileInput);
                }
            });

            // Handle drag and drop
            initializeDragAndDrop(div, newFileInput);
        });
    }

    // Rest of the helper functions remain the same
    function updateFileUploadUI(div, file, fileInput) {
        div.innerHTML = `
        <div class="file-info p-4 flex items-center justify-between">
            <div class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" 
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" 
                    class="text-gray-500 mr-2">
                    <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                    <polyline points="13 2 13 9 20 9"></polyline>
                </svg>
                <span class="text-sm text-gray-700">${file.name}</span>
            </div>
            <button type="button" class="remove-file text-gray-500 hover:text-gray-700">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" 
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
            </button>
        </div>
    `;

        // Re-append the file input
        div.appendChild(fileInput);

        // Add remove button functionality
        const removeButton = div.querySelector('.remove-file');
        if (removeButton) {
            removeButton.addEventListener('click', function (e) {
                e.stopPropagation();
                resetFileUpload(div, div.getAttribute('data-default-content'), fileInput);
            });
        }
    }

    function initializeDragAndDrop(div, fileInput) {
        div.addEventListener('dragover', function (e) {
            e.preventDefault();
            this.classList.add('border-purple-500');
        });

        div.addEventListener('dragleave', function (e) {
            e.preventDefault();
            this.classList.remove('border-purple-500');
        });

        div.addEventListener('drop', function (e) {
            e.preventDefault();
            this.classList.remove('border-purple-500');

            const file = e.dataTransfer.files[0];
            if (file) {
                fileInput.files = e.dataTransfer.files;
                // Trigger the change event manually
                const event = new Event('change', { bubbles: true });
                fileInput.dispatchEvent(event);
            }
        });
    }

    function resetFileUpload(div, defaultContent, fileInput) {
        div.innerHTML = defaultContent;
        fileInput.value = '';
        div.appendChild(fileInput);
    }
</script>