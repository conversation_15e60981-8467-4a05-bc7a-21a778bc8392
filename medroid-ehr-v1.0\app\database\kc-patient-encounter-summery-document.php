<?php
require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

global $wpdb;

$kc_charset_collate = $wpdb->get_charset_collate();

// Define the table name with WordPress prefix
$table_name = $wpdb->prefix . 'kc_patient_encounters_summery_document';

$sql = "CREATE TABLE `{$table_name}` (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    encounter_id bigint(20) UNSIGNED NOT NULL,
    attachment_id bigint(20) UNSIGNED NOT NULL,
    name varchar(255) NOT NULL,
    description varchar(255) NOT NULL,
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY  (id)
) $kc_charset_collate;";

// Create the table if it doesn't exist
maybe_create_table($table_name, $sql);
