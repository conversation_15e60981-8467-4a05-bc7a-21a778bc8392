<!-- AddPatientModal.vue -->
<template>
  <div v-if="show" class="fixed inset-0 z-50 overflow-y-auto">
    <!-- Backdrop -->
    <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity" @click="closeModal"></div>

    <!-- Modal -->
    <div class="flex min-h-screen items-start justify-center p-4">
      <div class="relative w-full max-w-4xl bg-white rounded-lg shadow-xl">
        <form @submit.prevent="handleSubmit" :novalidate="true" id="patientDataForm">
          <!-- Header -->
          <div class="flex items-center justify-between p-4 border-b">
            <h3 class="text-xl font-semibold text-gray-900">
              {{ formTranslation.patient.add_patient }}
            </h3>
            <button type="button" @click="closeModal" class="text-gray-400 hover:text-gray-500">
              <i class="fas fa-times"></i>
            </button>
          </div>

          <!-- Body -->
          <div class="p-6 max-h-[calc(100vh-200px)] overflow-y-auto">
            <!-- Basic Information Section -->
            <div class="mb-6">
              <h4 class="text-lg font-medium text-primary-600 mb-4">
                {{ formTranslation.common.basic_details }}
              </h4>

              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- Unique ID -->
                <div v-if="u_id_enabled">
                  <label class="block text-sm font-medium text-gray-700">
                    {{ formTranslation.patient.lbl_patient_unique_id }}
                    <span class="text-red-500">*</span>
                  </label>
                  <input v-model="patientData.u_id" type="text" required
                    class="w-full rounded-lg px-4 py-2.5 border border-gray-300 shadow-sm focus:ring-black focus:border-black" />
                </div>

                <!-- First Name -->
                <div>
                  <label class="block text-sm font-medium text-gray-700">
                    {{ formTranslation.common.fname }}
                    <span class="text-red-500">*</span>
                  </label>
                  <input v-model="patientData.first_name" :class="{
                    'border-red-500':
                      submitted && $v.patientData.first_name.$error,
                  }" type="text" required :placeholder="formTranslation.patient.fname_plh"
                    class="w-full rounded-lg px-4 py-2.5 border border-gray-300 shadow-sm focus:ring-black focus:border-black" />
                  <div v-if="submitted && !$v.patientData.first_name.required" class="mt-1 text-sm text-red-600">
                    {{ formTranslation.common.fname_required }}
                  </div>
                </div>

                <!-- Last Name -->
                <div>
                  <label class="block text-sm font-medium text-gray-700">
                    {{ formTranslation.common.lname }}
                    <span class="text-red-500">*</span>
                  </label>
                  <input v-model="patientData.last_name" :class="{
                    'border-red-500':
                      submitted && $v.patientData.last_name.$error,
                  }" type="text" required :placeholder="formTranslation.patient.lname_placeholder"
                    class="w-full rounded-lg px-4 py-2.5 border border-gray-300 shadow-sm focus:ring-black focus:border-black" />
                  <div v-if="submitted && !$v.patientData.last_name.required" class="mt-1 text-sm text-red-600">
                    {{ formTranslation.common.lname_required }}
                  </div>
                </div>

                <!-- Email -->
                <div>
                  <label class="block text-sm font-medium text-gray-700">
                    {{ formTranslation.common.email }}
                    <span class="text-red-500">*</span>
                  </label>
                  <input v-model="patientData.user_email" :class="{
                    'border-red-500':
                      submitted && $v.patientData.user_email.$error,
                  }" type="email" required :placeholder="formTranslation.patient.email_placeholder"
                    class="w-full rounded-lg px-4 py-2.5 border border-gray-300 shadow-sm focus:ring-black focus:border-black" />
                  <div v-if="submitted && !$v.patientData.user_email.required" class="mt-1 text-sm text-red-600">
                    {{ formTranslation.common.email_required }}
                  </div>
                </div>

                <!-- Contact Number -->
                <div>
                  <label class="block text-sm font-medium text-gray-700">
                    {{ formTranslation.common.contact_no }}
                    <span class="text-red-500">*</span>
                  </label>
                  <VuePhoneNumberInput v-model="patientData.mobile_number" :default-country-code="defaultCountryCode"
                    @update="contactUpdateHandaler" :class="{
                      'border-red-500':
                        submitted && $v.patientData.mobile_number.$error,
                    }" class="mt-1" clearable no-example />
                  <div v-if="submitted && !$v.patientData.mobile_number.required" class="mt-1 text-sm text-red-600">
                    {{ formTranslation.common.contact_num_required }}
                  </div>
                </div>

                <!-- Date of Birth -->
                <div v-if="!hideFields.includes('dob')">
                  <label class="block text-sm font-medium text-gray-700">
                    {{ formTranslation.common.dob }}
                    <span class="text-red-500">*</span>
                  </label>
                  <input v-model="patientData.dob" type="date" :max="new Date().toISOString().split('T')[0]" :class="{
                    'border-red-500': submitted && $v.patientData.dob.$error,
                  }"
                    class="w-full rounded-lg px-4 py-2.5 border border-gray-300 shadow-sm focus:ring-black focus:border-black" />
                  <div v-if="submitted && !$v.patientData.dob.required" class="mt-1 text-sm text-red-600">
                    {{ formTranslation.common.dob_required }}
                  </div>
                </div>

                <!-- NHS No. -->
                <div v-if="!hideFields.includes('nhs')">
                  <label class="block text-sm font-medium text-gray-700">
                    {{ formTranslation.common.nhs }}
                  </label>
                  <input v-model="patientData.nhs" type="text"
                    class="w-full rounded-lg px-4 py-2.5 border border-gray-300 shadow-sm focus:ring-black focus:border-black" />
                </div>

                <!-- registered_gp_name No. -->
                <div v-if="!hideFields.includes('registered_gp_name')">
                  <label class="block text-sm font-medium text-gray-700">
                    {{ formTranslation.common.registered_gp_name }}
                  </label>
                  <input v-model="patientData.registered_gp_name" type="text"
                    class="w-full rounded-lg px-4 py-2.5 border border-gray-300 shadow-sm focus:ring-black focus:border-black" />
                </div>

                <!-- registered_gp_address No. -->
                <div v-if="!hideFields.includes('registered_gp_address')">
                  <label class="block text-sm font-medium text-gray-700">
                    {{ formTranslation.common.registered_gp_address }}
                  </label>
                  <input v-model="patientData.registered_gp_address" type="text"
                    class="w-full rounded-lg px-4 py-2.5 border border-gray-300 shadow-sm focus:ring-black focus:border-black" />
                </div>

                <!-- Gender -->
                <div>
                  <label class="block text-sm font-medium text-gray-700">
                    {{ formTranslation.common.gender }}
                    <span class="text-red-500">*</span>
                  </label>
                  <div class="mt-2 space-x-4">
                    <label class="inline-flex items-center">
                      <input type="radio" v-model="patientData.gender" value="male"
                        class="form-radio text-purple-600" />
                      <span class="ml-2">{{
                        formTranslation.common.male
                      }}</span>
                    </label>
                    <label class="inline-flex items-center">
                      <input type="radio" v-model="patientData.gender" value="female"
                        class="form-radio text-purple-600" />
                      <span class="ml-2">{{
                        formTranslation.common.female
                      }}</span>
                    </label>
                    <label v-if="defaultUserRegistrationFormSettingData === 'on'" class="inline-flex items-center">
                      <input type="radio" v-model="patientData.gender" value="other"
                        class="form-radio text-purple-600" />
                      <span class="ml-2">{{
                        formTranslation.common.other
                      }}</span>
                    </label>
                  </div>
                  <div v-if="submitted && !$v.patientData.gender.required" class="mt-1 text-sm text-red-600">
                    {{ formTranslation.common.gender_required }}
                  </div>
                </div>

                <!-- Clinic Selection for Pro Users -->
                <div v-if="
                  userData.addOns.kiviPro &&
                  (getUserRole() === 'administrator' ||
                    getUserRole() === 'doctor')
                ">
                  <label class="block text-sm font-medium text-gray-700">
                    {{ formTranslation.patient.select_clinic }}
                    <span class="text-red-500">*</span>
                  </label>
                  <multi-select v-model="patientData.clinic_id" :options="clinics" :multiple="true"
                    :loading="clinicMultiselectLoader" track-by="id" label="label"
                    :placeholder="formTranslation.patient.search_placeholder" class="mt-1" />
                  <div v-if="submitted && !$v.patientData.clinic_id.required" class="mt-1 text-sm text-red-600">
                    {{ formTranslation.common.clinic_is_required }}
                  </div>
                </div>
              </div>
            </div>

            <!-- Address Section -->
            <div v-if="!hideFields.includes('address')" class="mb-6">
              <h4 class="text-lg font-medium text-primary-600 mb-4">
                {{ formTranslation.doctor.other_details }}
              </h4>

              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700">
                    {{ formTranslation.common.address }}
                    <span class="text-red-500">*</span>
                  </label>
                  <textarea v-model="patientData.address" :placeholder="formTranslation.patient.address_placeholder"
                    class="w-full rounded-lg px-4 py-2.5 border border-gray-300 shadow-sm focus:ring-black focus:border-black"
                    rows="3" :class="{
                      'border-red-500': submitted && $v.patientData.address.$error,
                    }"></textarea>
                  <div v-if="submitted && !$v.patientData.address.required" class="mt-1 text-sm text-red-600">
                    {{ formTranslation.common.address_required }}
                  </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <!-- City -->
                  <div v-if="!hideFields.includes('city')">
                    <label class="block text-sm font-medium text-gray-700">
                      {{ formTranslation.common.city }}
                    </label>
                    <input v-model="patientData.city" type="text"
                      :placeholder="formTranslation.patient.city_placeholder"
                      class="w-full rounded-lg px-4 py-2.5 border border-gray-300 shadow-sm focus:ring-black focus:border-black" />
                  </div>

                  <!-- Country -->
                  <div v-if="!hideFields.includes('country')">
                    <label class="block text-sm font-medium text-gray-700">
                      {{ formTranslation.common.country }}
                    </label>
                    <input v-model="patientData.country" type="text"
                      :placeholder="formTranslation.patient.country_placeholder"
                      class="w-full rounded-lg px-4 py-2.5 border border-gray-300 shadow-sm focus:ring-black focus:border-black" />
                  </div>

                  <!-- Postal Code -->
                  <div v-if="!hideFields.includes('postal_code')">
                    <label class="block text-sm font-medium text-gray-700">
                      {{ formTranslation.common.postal_code }}
                      <span class="text-red-500">*</span>
                    </label>
                    <input v-model="patientData.postal_code" type="text"
                      :placeholder="formTranslation.patient.pcode_placeholder"
                      class="w-full rounded-lg px-4 py-2.5 border border-gray-300 shadow-sm focus:ring-black focus:border-black"
                      :class="{
                        'border-red-500': submitted && $v.patientData.postal_code.$error,
                      }" />
                    <div v-if="submitted && !$v.patientData.postal_code.required" class="mt-1 text-sm text-red-600">
                      {{ formTranslation.common.postal_code_required }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Custom Fields Section -->
            <div v-if="isCustomeFieldExist" class="mb-6">
              <h4 class="text-lg font-medium text-primary-600 mb-4">
                {{ formTranslation.doctor.extra_detail }}
              </h4>
              <get-custom-fields v-if="!editMode" module_type="patient_module" :module_id="String(0)"
                @bindCustomField="getCustomFieldsValues" :fieldsValue="customFieldsData"
                @customFieldAvailable="isCustomeFieldExist = true" @requiredCustomField="getRequireFields" />
              <edit-custom-fields v-else module_type="patient_module" :module_id="editId"
                @bindCustomField="getCustomFieldsValues" :fieldsValue="customFieldsData"
                @requiredCustomField="getRequireFields" />
            </div>
          </div>

          <!-- Footer -->
          <div class="flex items-center justify-end gap-3 border-t p-4">
            <button type="button"
              class="px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 border border-gray-300 rounded-md"
              @click="closeModal">
              {{ formTranslation.common.cancel }}
            </button>
            <button type="submit"
              class="px-4 py-2 text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 rounded-md"
              :disabled="loading">
              <i v-if="loading" class="fas fa-spinner fa-spin mr-2"></i>
              {{ formTranslation.common.save }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import VuePhoneNumberInput from "vue-phone-number-input";
import "vue-phone-number-input/dist/vue-phone-number-input.css";
import {
  required,
  minLength,
  maxLength,
  requiredIf,
  email,
} from "vuelidate/lib/validators";
import { post, get } from "../../config/request";
import { validateForm } from "../../config/helper";

export default {
  name: "AddPatientModal",
  components: {
    VuePhoneNumberInput,
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    editMode: {
      type: Boolean,
      default: false,
    },
    editId: {
      type: String,
      default: "",
    },
  },

  data() {
    return {
      u_id_enabled: false,
      patientData: this.defaultPatientData(),
      loading: false,
      submitted: false,
      clinics: [],
      clinicMultiselectLoader: true,
      isCustomeFieldExist: false,
      defaultCountryCode: null,
      defaultUserRegistrationFormSettingData: "on",
      hideFields: [],
      requiredFields: [],
    };
  },

  validations: {
    patientData: {
      first_name: {
        required,
        minLength: minLength(2),
        maxLength: maxLength(50),
      },
      last_name: {
        required,
        minLength: minLength(2),
        maxLength: maxLength(50),
      },
      user_email: {
        required,
        email,
      },
      mobile_number: {
        required,
        minLength: minLength(4),
        maxLength: maxLength(15),
      },
      gender: {
        required,
      },
      dob: {
        required: requiredIf(function () {
          return !this.hideFields.includes('dob');
        }),
      },
      address: {
        required: requiredIf(function () {
          return !this.hideFields.includes('address');
        }),
      },
      postal_code: {
        required: requiredIf(function () {
          return !this.hideFields.includes('postal_code');
        }),
      },
      clinic_id: {
        required: requiredIf(function () {
          return (
            this.userData.addOns.kiviPro == true &&
            (this.getUserRole() == "administrator" ||
              this.getUserRole() == "doctor")
          );
        }),
      },
    },
  },

  created() {
    this.init();
  },

  methods: {
    init() {
      this.getCountryCodeData();
      this.getUserRegistrationFormData();
      this.getClinics();
      this.getHideFieldsArrayFromFilter();

      if (this.editMode && this.editId) {
        this.getPatientData();
      } else if (this.userData.unquie_id_status !== undefined) {
        this.getUniqueId();
      }

      if (
        this.userData.unquie_id_status !== undefined &&
        this.userData.unquie_id_status == true
      ) {
        this.u_id_enabled = this.userData.unquie_id_status;
      }
    },

    defaultPatientData() {
      return {
        first_name: "",
        last_name: "",
        username: "",
        user_email: "",
        user_pass: "",
        mobile_number: "",
        country_code: "",
        country_calling_code: "",
        gender: "",
        dob: "",
        nhs: "",
        registered_gp_name: "",
        registered_gp_address: "",
        address: "",
        city: "",
        country: "",
        postal_code: "",
        custom_fields: {},
        profile_image: "",
        u_id: "",
        clinic_id: [],
      };
    },

    async getPatientData() {
      try {
        const response = await get("patient_edit", {
          id: this.editId,
        });

        if (response.data.status && response.data.data) {
          this.patientData = response.data.data;

          if (response.data.data.country_calling_code) {
            this.defaultCountryCode = response.data.data.country_code;
          }

          this.patientData.custom_fields = response.data.custom_filed;
          this.isCustomeFieldExist =
            this.patientData.custom_fields &&
            this.patientData.custom_fields.length > 0;
        }
      } catch (error) {
        console.error(error);
        displayErrorMessage(this.formTranslation.widgets.record_not_found);
      }
    },

    async getUniqueId() {
      try {
        const response = await post("get_unique_id");
        if (response.data.data) {
          this.patientData.u_id = response.data.data;
        }
      } catch (error) {
        console.error(error);
      }
    },

    contactUpdateHandaler(val) {
      this.patientData.country_code = val.countryCode;
      this.patientData.country_calling_code = val.countryCallingCode;
    },

    async handleSubmit() {
      this.loading = true;
      this.submitted = true;

      this.$v.$touch();

      if (this.$v.patientData.$invalid) {
        this.loading = false;
        return false;
      }

      if (this.requiredFields.length > 0) {
        this.loading = false;
        displayErrorMessage(
          this.formTranslation.common.all_required_field_validation
        );
        return false;
      }

      try {
        console.log('Submitting patient data:', this.patientData);
        
        // Ensure the clinic_id is correctly formatted
        if (this.patientData.clinic_id && Array.isArray(this.patientData.clinic_id) && this.patientData.clinic_id.length === 0) {
          // If empty array, set a default value or null
          this.patientData.clinic_id = null;
        }
        
        const response = await post("patient_save", this.patientData);

        console.log('Patient save response:', response.data);
        
        if (response.data.status) {
          displayMessage(response.data.message);
          this.$emit("patient-saved");
          this.closeModal();
        } else {
          displayErrorMessage(response.data.message || 'Failed to save patient data');
        }
      } catch (error) {
        console.error('Patient save error:', error);
        displayErrorMessage(
          error.response?.data?.message || this.formTranslation.common.internal_server_error
        );
      } finally {
        this.loading = false;
      }
    },

    async getClinics() {
      this.clinicMultiselectLoader = true;
      try {
        const response = await get("get_static_data", {
          data_type: "clinic_list",
        });

        if (response.data.status && response.data.data) {
          this.clinics = response.data.data;
        }
      } catch (error) {
        console.error(error);
        displayErrorMessage(this.formTranslation.common.internal_server_error);
      } finally {
        this.clinicMultiselectLoader = false;
      }
    },

    async getHideFieldsArrayFromFilter() {
      try {
        const response = await get("get_hide_fields_array_from_filter");
        if (response.data.status && response.data.data) {
          this.hideFields = response.data.data;
        }
      } catch (error) {
        console.error(error);
        displayErrorMessage(this.formTranslation.common.internal_server_error);
      }
    },

    async getCountryCodeData() {
      try {
        const response = await get("get_country_code_settings_data");
        if (response.data.status && response.data.data) {
          this.defaultCountryCode = response.data.data.country_code;
        }
      } catch (error) {
        console.error(error);
        displayErrorMessage(this.formTranslation.common.internal_server_error);
      }
    },

    async getUserRegistrationFormData() {
      try {
        const response = await get("get_user_registration_form_settings_data");
        if (response.data.status && response.data.data) {
          this.defaultUserRegistrationFormSettingData =
            response.data.data.userRegistrationFormSettingData;
        }
      } catch (error) {
        console.error(error);
        displayErrorMessage(this.formTranslation.common.internal_server_error);
      }
    },

    getCustomFieldsValues(fieldsObj) {
      if (!fieldsObj) return false;
      this.patientData.custom_fields = fieldsObj;
    },

    getRequireFields(validateRequired) {
      this.requiredFields = validateRequired;
    },

    closeModal() {
      this.$emit("close");
      this.submitted = false;
      this.patientData = this.defaultPatientData();
    },
  },

  computed: {
    userData() {
      return this.$store.state.userDataModule.user;
    },
    customFieldsData() {
      return this.patientData.custom_fields || [];
    },
  },
};
</script>