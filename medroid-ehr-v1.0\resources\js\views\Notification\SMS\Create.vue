<template>
  <div class="container mx-auto p-4">
    <div class="w-full">
      <div v-if="userData.addOns.kiviPro != true" class="fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center">
        <overlay-message addon_type="pro"></overlay-message>
      </div>

      <form id="doctorDataForm" novalidate>
        <div class="bg-white shadow rounded-lg">
          <div class="flex justify-between items-center p-4 border-b">
            <h2 class="text-xl font-semibold">{{ formTranslation.settings.sms_template }}
              <a v-if="request_status == 'off'" href="https://apps.medroid.ai/docs/product/kivicare/pro-version/sms-whatsapp-template/" target="_blank">
                <i class="fa fa-question-circle text-gray-500"></i>
              </a>
            </h2>
            <div class="flex gap-2">
              <button class="btn-primary" type="button" @click="$refs.NotificationTestModal.modalOpen = true; $refs.NotificationTestModal.notificationType='sms'">
                <i class="fas fa-sms"></i> {{ formTranslation.widgets.send_test_sms }}
              </button>
              <button class="btn-primary" type="button" @click="$refs.NotificationTestModal.modalOpen = true; $refs.NotificationTestModal.notificationType='whatsapp'">
                <i class="fab fa-whatsapp"></i> {{ formTranslation.widgets.send_test_whatsapp }}
              </button>
            </div>
          </div>

          <div v-if="isLoading" class="flex justify-center items-center py-8">
            <loader-component-2></loader-component-2>
          </div>

          <div v-else class="p-4">
            <div v-for="(head, headIndex) in SMSTypeList" :key="headIndex" class="mb-4">
              <div @click="mainAccordian(headIndex)" :class="selectedMainAccrodionId === headIndex ? 'bg-gray-200' : 'bg-gray-100'" class="cursor-pointer p-3 rounded-md">
                {{ labels[headIndex] || headIndex }}
              </div>

              <div v-if="selectedMainAccrodionId === headIndex" class="pl-4">
                <div v-for="(item, index) in head" :key="index" class="mb-2">
                  <div class="flex items-center gap-2">
                    <input type="checkbox" :id="'checkbox-'+ item.ID" v-model="item.post_status" true-value="publish" false-value="draft" class="form-checkbox">
                    <button type="button" @click="selectedSmsTemplate(item.ID, headIndex, index)" :class="item.ID === activeSmsTemplateIndex ? 'font-bold' : ''" class="text-left w-full">
                      {{ item.post_status === 'publish' ? formTranslation.common['enabled_' + item.post_name] : formTranslation.common['disabled_' + item.post_name] }}
                    </button>
                  </div>

                  <div v-if="item.ID === activeSmsTemplateIndex" class="mt-2">
                    <input type="text" v-model="item.content_sid" :placeholder="formTranslation.settings.content_sid" class="form-input w-full mb-2">
                    <button type="button" @click="fetchTwillioTemplate(index, headIndex, item.content_sid)" class="btn-secondary">{{ formTranslation.settings.fetch_twilio_template }}</button>

                    <div v-if="smsDynamicKey[item.post_name]" class="mt-2">
                      <label class="font-medium">{{ formTranslation.settings.dynamic_keys_list }}</label>
                      <div class="flex flex-wrap gap-2 mt-1">
                        <button v-for="(key, keyIndex) in smsDynamicKey[item.post_name]" :key="keyIndex" @click.prevent="copyDynamicKey(key)" class="btn-sm-primary">
                          {{ key }}
                        </button>
                      </div>
                    </div>

                    <div class="mt-2">
                      <vue-editor :editorToolbar="customToolbar" v-model="item.post_content" disabled></vue-editor>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="flex justify-end p-4 border-t">
            <button v-if="!loading" @click="saveSMSTemplates" class="btn-primary">
              <i class="fa fa-save mr-1"></i> {{ formTranslation.common.save }}
            </button>
            <button v-else disabled class="btn-primary">
              <i class="fa fa-sync fa-spin"></i> {{ formTranslation.common.loading }}
            </button>
          </div>
        </div>
      </form>

      <NotificationTestModal ref="NotificationTestModal"></NotificationTestModal>
    </div>
  </div>
</template>

<script>
import {post,get} from "../../../config/request";
import NotificationTestModal from "../NotificationTestModal";
export default {
  components:{NotificationTestModal},
  data: () => {
    return {
      disabledEditors: [],
      SMSTypeList: [],
      activeSmsTemplateIndex: -1,
      smsTemplateTitle: '',
      request_status:'off',
      templateSaveRequest: {
        ID: 0,
        post_content: '',
      },
      loading: false,
      copyToolTipText:'',
      smsDynamicKey: [],
      labels:[],
      selectedMainAccrodionId:-1,
      isLoading:true,
      customToolbar: [[{
        header: [false, 1, 2, 3, 4, 5, 6]
      }], ["bold", "italic", "underline", "strike"], // toggled buttons
        [{
          align: ""
        }, {
          align: "center"
        }, {
          align: "right"
        }, {
          align: "justify"
        }], ["blockquote", "code-block"], [{
          list: "ordered"
        }, {
          list: "bullet"
        }, {
          list: "check"
        }], [{
          indent: "-1"
        }, {
          indent: "+1"
        }], // outdent/indent
        [{
          color: []
        }, {
          background: []
        }], // dropdown with defaults from theme
      ]
    }
  },
  mounted() {
    if(!['administrator'].includes(this.getUserRole())) {
      this.$router.push({ name: "403"})
    }
    this.init();
    this.copyToolTipText = this.formTranslation.settings.click_to_copy
    this.getModule();
  },
  methods: {
    init: function () {
      this.getSMSTemplate();
    },
    mainAccordian(value){
      this.selectedMainAccrodionId = this.selectedMainAccrodionId === value ? -1 : value;
    },
    fetchTwillioTemplate: function (id,headIndex,content_sid){
      get('get_twillio_sms_template',{content_sid:content_sid})
        .then((res)=>{
          this.SMSTypeList[headIndex][id].post_content = res.data.data['twilio/text']['body'];
        })
    },
    getSMSTemplate: function () {
      get('get_sms_template', {})
        .then((response) => {
          if (response.data.status !== undefined && response.data.status === true) {
            this.isLoading = false
            this.SMSTypeList = response.data.data
            if(response.data.dynamicKey !== undefined ){
              this.smsDynamicKey = response.data.dynamicKey;
            }
            this.labels = response.data.labels;
          }
        })
        .catch((error) => {
          console.log(error);
        })
    },
    selectedSmsTemplate: function (value,headerIndex,index) {
      this.activeSmsTemplateIndex = this.activeSmsTemplateIndex === value ? ' ' : value;
    },
    saveSMSTemplates: function () {
      if(this.userData.addOns.kiviPro != true){
        return;
      }
      this.loading = true;
      post('save_sms_template', { data : this.SMSTypeList } )
        .then((response) => {
          if (response.data.status !== undefined && response.data.status === true) {
            this.getSMSTemplate();
            displayMessage(response.data.message);
            displayMessage(response.data.message);
          } else {
            displayErrorMessage(response.data.message);
          }
          this.loading = false;
        })
        .catch((error) => {
          this.loading = false;
          displayErrorMessage(this.formTranslation.common.server_error+'.');
        })
    },
    copyDynamicKey: function(id) {
      const elem = document.createElement("input");
      document.querySelector("body").appendChild(elem);
      elem.value = id;
      elem.select();
      document.execCommand("copy");
      elem.remove();
      this.copyToolTipText =  id + ' ' + this.formTranslation.settings.copied
    },
    getModule:function (){
        if(window.request_data.link_show_hide !== undefined && window.request_data.link_show_hide !== ''){
        this.request_status = window.request_data.link_show_hide;
        }
    }
  },
  watch: {},
  computed:{
    userData () {
      return this.$store.state.userDataModule.user;
    }
  }
}

</script>
<style>
.accordion .card-header:has(button.not-collapsed):after {
  content: "\F077" !important;
}
</style>