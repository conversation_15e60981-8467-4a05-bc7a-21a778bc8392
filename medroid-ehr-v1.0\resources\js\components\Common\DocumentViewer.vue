<template>
    <div>
      <!-- View button -->
      <button
        title="View"
        class="p-1 text-gray-400 hover:text-gray-600"
        @click="openPdfViewer"
      >
        <i class="fas fa-eye"></i>
      </button>
  
      <!-- PDF viewer modal -->
      <div v-if="showPdfViewer" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
        <div class="relative w-full max-w-5xl h-5/6 bg-white rounded-lg shadow-lg overflow-hidden">
          <!-- Modal header -->
          <div class="flex items-center justify-between px-4 py-2 bg-gray-100 border-b">
            <h3 class="text-lg font-medium">Document Viewer</h3>
            <button @click="showPdfViewer = false" class="text-gray-500 hover:text-gray-700">
              <i class="fas fa-times"></i>
            </button>
          </div>
  
          <!-- Loading spinner -->
          <div v-if="isLoading" class="flex justify-center items-center h-full">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
          </div>
  
          <!-- PDF viewer container -->
          <div id="pdfViewerContainer" class="h-full overflow-auto p-4"></div>
        </div>
      </div>
    </div>
  </template>
  
  <script>
  import CryptoJS from 'crypto-js';
  import { get, post } from "../../config/request";
  
  export default {
    name: 'DocumentViewer',
    
    props: {
      document: {
        type: Object,
        required: true
      }
    },
    
    data() {
      return {
        showPdfViewer: false,
        isLoading: false,
        pdfPassword: ''
      };
    },
    
    methods: {
      async openPdfViewer() {
        this.showPdfViewer = true;
        this.isLoading = true;
        
        try {
          // First, make sure PDF.js is loaded
          if (!window.pdfjsLib) {
            await this.loadPdfJsLibrary();
          }
          
          // Then fetch the password from your API
          await this.fetchPdfPassword();
          
          // Finally, load the PDF with the retrieved password
          this.$nextTick(() => {
            this.loadPdfWithPassword();
          });
        } catch (error) {
          console.error("Error preparing PDF viewer:", error);
          this.isLoading = false;
        }
      },
  
      decryptPassword(encryptedPassword) {
        const encryptionKey = 'medroid-ehr'; // Must match the key used in PHP
        const decrypted = CryptoJS.AES.decrypt(
          encryptedPassword, 
          encryptionKey
        ).toString(CryptoJS.enc.Utf8);
        
        return decrypted;
      },
      
      async fetchPdfPassword() {
        try {
          // Replace with your actual API endpoint
          let response = await post('get_document_access_key', {
            document_id: this.document.id,
            system_generated: this.document.system_generated
          });
          console.log("response", response);
          console.log("response.data", response.data);
  
          this.pdfPassword = response.data.data.password;
          // this.pdfPassword = this.decryptPassword(encryptedPassword);
  
        } catch (error) {
          console.error("Error fetching PDF password:", error);
          this.pdfPassword = ""; // Clear password on error
        }
      },
      
      loadPdfJsLibrary() {
        return new Promise((resolve, reject) => {
          const script = document.createElement('script');
          script.src = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js';
          script.onload = () => {
            // Set worker source after library loads
            window.pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
            resolve();
          };
          script.onerror = reject;
          document.head.appendChild(script);
        });
      },
      
      loadPdfWithPassword() {
        const container = document.getElementById('pdfViewerContainer');
        if (!container) return;
        
        this.document.document_url = this.document.document_url.replace(/^http:/, "https:");

        // Get the PDF URL from the document
        const pdfUrl = encodeURI(this.document.document_url);
        
        // Create loading task with password
        const loadingTask = window.pdfjsLib.getDocument({
          url: pdfUrl,
          password: this.pdfPassword,
          withCredentials: true,
        });
        
        loadingTask.promise.then(pdf => {
          this.isLoading = false;
          container.innerHTML = '';
          const numPages = pdf.numPages;
          
          // Create viewer container
          const viewerContainer = document.createElement('div');
          viewerContainer.className = 'pdf-viewer';
          container.appendChild(viewerContainer);
          
          // Load each page
          for (let pageNum = 1; pageNum <= numPages; pageNum++) {
            this.renderPage(pdf, pageNum, viewerContainer);
          }
        }).catch(error => {
          this.isLoading = false;
          console.error('Error loading PDF:', error);
          
          if (error.name === 'PasswordException') {
            container.innerHTML = `
              <div class="flex flex-col items-center justify-center h-full">
                <div class="text-red-500 mb-4">Password required or incorrect password</div>
                <div>
                  <input type="password" id="manualPdfPassword" class="border p-2 rounded mr-2" placeholder="Enter PDF password">
                  <button id="tryPasswordBtn" class="bg-blue-500 text-white px-4 py-2 rounded">Open PDF</button>
                </div>
              </div>
            `;
            
            // Add event listener for manual password entry
            document.getElementById('tryPasswordBtn').addEventListener('click', () => {
              const manualPassword = document.getElementById('manualPdfPassword').value;
              if (manualPassword) {
                this.isLoading = true;
                container.innerHTML = '<div class="flex justify-center items-center h-full"><div class="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div></div>';
                
                // Try again with manual password
                const loadingTask = window.pdfjsLib.getDocument({
                  url: pdfUrl,
                  password: manualPassword
                });
                
                loadingTask.promise.then(pdf => {
                  this.isLoading = false;
                  container.innerHTML = '';
                  const viewerContainer = document.createElement('div');
                  viewerContainer.className = 'pdf-viewer';
                  container.appendChild(viewerContainer);
                  
                  for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
                    this.renderPage(pdf, pageNum, viewerContainer);
                  }
                }).catch(err => {
                  this.isLoading = false;
                  console.error('Error with manual password:', err);
                  container.innerHTML = '<div class="text-red-500 text-center my-4">Could not open PDF. Incorrect password.</div>';
                });
              }
            });
          } else {
            container.innerHTML = `<div class="text-red-500 text-center my-4">Error loading PDF: ${error.message}</div>`;
          }
        });
      },
      
      renderPage(pdf, pageNum, container) {
        pdf.getPage(pageNum).then(page => {
          const scale = 1.5;
          const viewport = page.getViewport({ scale });
          
          // Create page container
          const pageContainer = document.createElement('div');
          pageContainer.className = 'pdf-page mb-4';
          container.appendChild(pageContainer);
          
          // Add page number
          const pageLabel = document.createElement('div');
          pageLabel.className = 'text-sm text-gray-500 mb-2 text-center';
          pageLabel.textContent = `Page ${pageNum}`;
          pageContainer.appendChild(pageLabel);
          
          // Create canvas
          const canvas = document.createElement('canvas');
          canvas.className = 'mx-auto shadow-lg';
          pageContainer.appendChild(canvas);
          
          const context = canvas.getContext('2d');
          canvas.height = viewport.height;
          canvas.width = viewport.width;
          
          // Render the PDF page
          const renderContext = {
            canvasContext: context,
            viewport: viewport
          };
          
          page.render(renderContext);
        });
      }
    }
  };
  </script>
  
  <style scoped>
  .pdf-viewer {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .pdf-page {
    margin-bottom: 20px;
  }
  </style>