<template>
  <div>
    <div v-if="userData.addOns.kiviPro && exportData.length > 0" class="relative inline-block text-left">
      <!-- Main Export Button -->
      <button 
        @click="toggleExportDropdown" 
        class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 ease-in-out group"
      >
        <i class="fas fa-file-export text-gray-500 group-hover:text-gray-700 transition-colors mr-2"></i>
        <span>Export</span>
        <i :class="['fas ml-2', isExportDropdownOpen ? 'fa-chevron-up' : 'fa-chevron-down']"></i>
      </button>

      <!-- Dropdown Menu -->
      <div 
        v-if="isExportDropdownOpen" 
        class="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 divide-y divide-gray-100 focus:outline-none z-10"
      >
        <div class="py-1">
          <!-- CSV Option -->
          <download-csv :data="exportData" :name="fileExportName + '.csv'">
            <button 
              class="group flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 w-full text-left"
            >
              <i class="fas fa-file-csv text-gray-500 group-hover:text-gray-700 transition-colors mr-3"></i>
              <span>CSV</span>
            </button>
          </download-csv>

          <!-- Excel Option -->
          <vue-excel-xlsx 
            v-if="userData.addOns.kiviPro" 
            :data="exportData" 
            :columns="exportData.length > 0
              ? Object.keys(exportData[0]).map((elem) => ({
                  label: elem,
                  field: elem,
                }))
              : []"
            :sheet-name="moduleType" 
            :file-type="'xlsx'" 
            :file-name="fileExportName"

              class="group flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 w-full text-left"
            >
              <i class="fa fa-file-excel text-emerald-600 group-hover:text-emerald-700 transition-colors mr-3"></i>
              <span>Excel</span>
          </vue-excel-xlsx>

          <!-- PDF Option -->
          <!-- <button 
            class="group flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 w-full text-left"
            @click="printPdfFromArray(exportData, moduleName); closeExportDropdown();"
          >
            <i class="fa fa-file-pdf text-red-600 group-hover:text-red-700 transition-colors mr-3"></i>
            <span>PDF</span>
          </button> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment';

export default {
  name: "ModuleDataExport",
  props: {
    moduleName: {
      type: String,
      default: "",
    },
    moduleType: {
      type: String,
      default: "",
    },
    moduleData: {
      type: Array,
      default: () => [],
    },
    tableData: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      exportData: [],
      isExportDropdownOpen: false
    };
  },
  mounted() {
    this.formatData(this.moduleData);
  },
  methods: {
    formatPatientData(elem, tempValue, acc) {
      const _this = this;
      switch (elem) {
        case "u_id":
        case "ID":
          acc[
            _this.patientUniqueIdEnable
              ? _this.formTranslation.patient.unique_id
              : _this.formTranslation.common.id
          ] = tempValue;
          break;
        case "display_name":
          acc[_this.formTranslation.patient.dt_lbl_name] = tempValue;
          break;
        case "profile_image":
          acc[_this.formTranslation.clinic.profile_img] = tempValue;
          break;
        case "clinic_name":
          acc[_this.formTranslation.patient.clinic] = tempValue;
          break;
        case "user_email":
          acc[_this.formTranslation.patient.dt_lbl_email] = tempValue;
          break;
        case "mobile_number":
          acc[_this.formTranslation.patient.dt_lbl_mobile_number] = tempValue;
          break;
        case "gender":
          acc[_this.formTranslation.common.gender] = tempValue;
          break;
        case "dob":
          acc[_this.formTranslation.common.dob] = tempValue;
          break;
        case "full_address":
          acc[_this.formTranslation.common.address] = tempValue;
          break;
        case "blood_group":
          acc[_this.formTranslation.clinic.blood_group] = tempValue;
          break;
        case "user_registered":
          acc[_this.formTranslation.patient.dt_lbl_registered] = tempValue;
          break;
        case "user_status":
          acc[_this.formTranslation.common.status] =
            tempValue === "0"
              ? _this.formTranslation.common.active
              : _this.formTranslation.common.inactive;
          break;
        default:
          break;
      }
      return acc;
    },
    formatStaticData(elem, tempValue, acc) {
      const _this = this;
      switch (elem) {
        case "id":
        case "type":
          acc[elem] = tempValue;
          break;
        case "label":
          acc["name"] = tempValue;
          break;
        case "status":
          acc[elem] =
            tempValue === "1"
              ? _this.formTranslation.common.active
              : _this.formTranslation.common.inactive;
          break;
        default:
          break;
      }
      return acc;
    },
    formatCustomFieldData(elem, tempValue, acc) {
      const _this = this;
      switch (elem) {
        case "id":
        case "module_type":
          acc[elem] = tempValue;
          break;
        case "fields":
          let temp;
          try {
            temp = typeof tempValue === 'string' ? JSON.parse(tempValue) : tempValue;
            acc["label"] = temp.label || "";
            acc["placeholder"] = temp.placeholder || "";
            acc["options"] =
              temp.options !== undefined && Array.isArray(temp.options)
                ? temp.options
                  .map((ele) => {
                    return ele.text || "";
                  })
                  .join(",")
                : "";
            acc["type"] = temp.type || "";
            acc["required"] = temp.isRequired || false;
          } catch (e) {
            console.error("Error parsing fields JSON:", e);
            acc["label"] = "";
            acc["placeholder"] = "";
            acc["options"] = "";
            acc["type"] = "";
            acc["required"] = false;
          }
          break;
        case "status":
          acc[elem] =
            tempValue === "1"
              ? _this.formTranslation.common.active
              : _this.formTranslation.common.inactive;
          break;
        default:
          break;
      }
      return acc;
    },
    formatClinicDoctorHolidayData(elem, tempValue, acc) {
      const _this = this;
      switch (elem) {
        case "id":
        case "module_type":
        case "doctor_name":
        case "clinic_name":
          acc[elem] = tempValue;
          break;
        case "start_date":
        case "end_date":
          acc[elem] = tempValue;
          break;
        case "status":
          acc[elem] =
            tempValue === "1"
              ? _this.formTranslation.common.active
              : _this.formTranslation.common.inactive;
          break;
        default:
          break;
      }
      return acc;
    },
    formatBillingsData(elem, tempValue, acc) {
      const _this = this;
      switch (elem) {
        case "bill_id":
          acc["id"] = tempValue;
          break;
        case "id":
          acc["encounter_id"] = tempValue;
          break;
        case "total_amount":
        case "discount":
        case "actual_amount":
        case "clinic_id":
        case "doctor_id":
        case "patient_id":
        case "appointment_id":
        case "doctor_name":
        case "patient_name":
        case "clinic_name":
        case "service_name":
          acc[elem] = tempValue;
          break;
        case "encounter_date":
          acc[elem] = tempValue;
          break;
        case "status":
          acc[elem] =
            tempValue === "1"
              ? _this.formTranslation.patient_bill.unpaid
              : _this.formTranslation.patient_bill.paid;
          break;
        default:
          break;
      }
      return acc;
    },
    formatSessionData(elem, tempValue, acc, val) {
      const _this = this;

      switch (elem) {
        case "id":
        case "time_slot":
          acc[elem] = tempValue;
          break;
        case "days":
          acc[elem] =
            tempValue !== undefined && Array.isArray(tempValue)
              ? tempValue.join(",")
              : "";
          break;
        case "s_one_start_time":
          let temp = "";
          if (
            val.s_one_start_time && 
            val.s_one_end_time &&
            val.s_one_start_time.HH !== "" &&
            val.s_one_end_time.MM !== "" &&
            val.s_one_start_time.MM !== "" &&
            val.s_one_end_time.HH !== ""
          ) {
            temp =
              this.timeObjectToString(val.s_one_start_time) +
              " to " +
              this.timeObjectToString(val.s_one_end_time);
          }
          acc[_this.formTranslation.doctor_session.dt_lbl_morning_session] =
            temp;
          break;
        case "s_two_start_time":
          let temp2 = "";
          if (
            val.s_two_start_time && 
            val.s_two_end_time &&
            val.s_two_start_time.HH !== "" &&
            val.s_two_end_time.MM !== "" &&
            val.s_two_start_time.MM !== "" &&
            val.s_two_end_time.HH !== ""
          ) {
            temp2 =
              this.timeObjectToString(val.s_two_start_time) +
              " to " +
              this.timeObjectToString(val.s_two_end_time);
          }
          acc[_this.formTranslation.doctor_session.dt_lbl_evening_session] =
            temp2;
          break;
        case "clinic_id":
          if (tempValue && typeof tempValue === 'object') {
            acc["clinic_id"] = tempValue.id || "";
            acc["clinic_name"] = tempValue.label || "";
          } else {
            acc["clinic_id"] = tempValue;
            acc["clinic_name"] = "";
          }
          break;
        case "doctors":
          if (tempValue && typeof tempValue === 'object') {
            acc["doctor_id"] = tempValue.id || "";
            acc["doctor_name"] = tempValue.label || "";
          } else {
            acc["doctor_id"] = tempValue;
            acc["doctor_name"] = "";
          }
          break;
        default:
          break;
      }
      return acc;
    },
    formatServicesData(elem, tempValue, acc, val) {
      const _this = this;
      switch (elem) {
        case "id":
        case "doctor_id":
        case "charges":
        case "telemed_service":
        case "name":
        case "doctor_name":
          acc[elem] = tempValue;
          break;
        case "multiple":
          acc["allow_multiple"] =
            tempValue && tempValue !== "-" ? tempValue : "yes";
          break;
        case "service_type":
          acc["service_category"] = tempValue ? tempValue.replace(" ", "_") : "";
          break;
        case "service_id":
          acc["service_category_id"] = tempValue;
          break;
        case "service_name_alias":
          acc["service_category_alias"] = tempValue ? tempValue : "";
          break;
        case "image":
          acc["image"] =
            tempValue && tempValue !== "-"
              ? tempValue
              : window.pluginBASEURL + "assets/images/kc-demo-img.png";
          break;
        case "status":
          acc[elem] =
            tempValue === "1"
              ? _this.formTranslation.common.active
              : _this.formTranslation.common.inactive;
          break;
        default:
          break;
      }
      return acc;
    },
    formatReceptionistData(elem, tempValue, acc, val) {
      const _this = this;
      switch (elem) {
        case "ID":
        case "clinic_id":
        case "clinic_name":
        case "mobile_number":
        case "gender":
        case "dob":
        case "full_address":
          acc[elem] = tempValue;
          break;
        case "display_name":
          acc["name"] = tempValue && tempValue !== "-" ? tempValue : "yes";
          break;
        case "user_email":
          acc["email"] = tempValue;
          break;
        case "image":
          acc["profile_image"] = tempValue;
          break;
        case "user_status":
          acc[elem] =
            tempValue === "0"
              ? _this.formTranslation.common.active
              : _this.formTranslation.common.inactive;
          break;
        default:
          break;
      }
      return acc;
    },
    formatDoctorData(elem, tempValue, acc, val) {
      const _this = this;
      switch (elem) {
        case "ID":
        case "clinic_id":
        case "clinic_name":
        case "mobile_number":
        case "gender":
        case "dob":
        case "specialties":
        case "full_address":
          acc[elem] = tempValue;
          break;
        case "display_name":
          acc["name"] = tempValue && tempValue !== "-" ? tempValue : "yes";
          break;
        case "user_email":
          acc["email"] = tempValue;
          break;
        case "image":
          acc["profile_image"] = tempValue;
          break;
        case "user_status":
          acc[elem] =
            tempValue === "0"
              ? _this.formTranslation.common.active
              : _this.formTranslation.common.inactive;
          break;
        default:
          break;
      }
      return acc;
    },
    formatClinicData(elem, tempValue, acc, val) {
      const _this = this;
      switch (elem) {
        case "clinic_admin_email":
        case "clinic_admin_id":
        case "dob":
        case "specialties":
        case "clinic_full_address":
          acc[elem] = tempValue;
          break;
        case "id":
          acc["clinic_id"] = tempValue;
          break;
        case "name":
          acc["clinic_name"] =
            tempValue && tempValue !== "-" ? tempValue : "yes";
          break;
        case "email":
          acc["clinic_email"] = tempValue;
          break;
        case "telephone_no":
          acc["clinic_contact_no"] = tempValue;
          break;
        case "profile_image":
          acc["clinic_admin_profile_image"] = tempValue;
          break;
        case "clinic_image":
          acc["clinic_image"] = tempValue;
          break;
        case "status":
          acc[elem] =
            tempValue === "1"
              ? _this.formTranslation.common.active
              : _this.formTranslation.common.inactive;
          break;
        default:
          break;
      }
      return acc;
    },
    formatEncounterData(elem, tempValue, acc, val) {
      const _this = this;
      switch (elem) {
        case "id":
        case "clinic_id":
        case "clinic_name":
        case "doctor_id":
        case "doctor_name":
        case "patient_id":
        case "patient_name":
        case "appointment_id":
        case "description":
          acc[elem] = tempValue;
          break;
        case "encounter_date":
        case "created_at":
          acc["encounter_date"] = tempValue;
          break;
        case "status":
          acc[elem] =
            tempValue === "1"
              ? _this.formTranslation.common.active
              : _this.formTranslation.common.closed;
          break;
        default:
          break;
      }
      return acc;
    },
    formatPrescriptionData(elem, tempValue, acc, val) {
      acc[elem] = tempValue;
      return acc;
    },
    formatAppointmentData(elem, tempValue, acc, val) {
      acc[elem] = tempValue;
      return acc;
    },
    formatData(newVal) {
      if (!Array.isArray(newVal) || newVal.length <= 0) {
        this.exportData = [];
        return;
      }

      const _this = this;
      this.exportData = newVal.map(function (val) {
        if (!val || typeof val !== 'object') {
          return {};
        }
        
        return Object.keys(val).reduce((acc, elem) => {
          const tempValue = val[elem] !== undefined && val[elem] !== "" ? val[elem] : "-";
          
          switch (_this.moduleType) {
            case "patient":
              acc = _this.formatPatientData(elem, tempValue, acc);
              break;
            case "listing_data":
              acc = _this.formatStaticData(elem, tempValue, acc);
              break;
            case "custom_field":
              acc = _this.formatCustomFieldData(elem, tempValue, acc);
              break;
            case "clinic_doctor_holiday":
              acc = _this.formatClinicDoctorHolidayData(elem, tempValue, acc);
              break;
            case "billings":
              acc = _this.formatBillingsData(elem, tempValue, acc);
              break;
            case "session":
              acc = _this.formatSessionData(elem, tempValue, acc, val);
              break;
            case "services":
              acc = _this.formatServicesData(elem, tempValue, acc, val);
              break;
            case "receptionist":
              acc = _this.formatReceptionistData(elem, tempValue, acc, val);
              break;
            case "doctor":
              acc = _this.formatDoctorData(elem, tempValue, acc, val);
              break;
            case "clinic":
              acc = _this.formatClinicData(elem, tempValue, acc, val);
              break;
            case "encounter":
              acc = _this.formatEncounterData(elem, tempValue, acc, val);
              break;
            case "prescription":
              acc = _this.formatPrescriptionData(elem, tempValue, acc, val);
              break;
            case "appointment":
              acc = _this.formatAppointmentData(elem, tempValue, acc, val);
              break;
          }
          return acc;
        }, {});
      });
    },
    timeObjectToString(value) {
      if (!value || typeof value !== 'object' || !value.HH || !value.MM) {
        return "";
      }
      return value.HH + ":" + value.MM;
    },
    printPdfFromArray(data, title) {
      // This method needs to be implemented if not already defined elsewhere
      if (typeof this.$root.$emit === 'function') {
        this.$root.$emit('print-pdf', { data, title });
      } else {
        console.error('PDF printing functionality is not available');
      }
      this.closeExportDropdown();
    },
    toggleExportDropdown() {
      this.isExportDropdownOpen = !this.isExportDropdownOpen;
      if (this.isExportDropdownOpen) {
        // Add event listener to close dropdown when clicking outside
        document.addEventListener('click', this.closeExportDropdownOnClickOutside);
      }
    },
    closeExportDropdown() {
      this.isExportDropdownOpen = false;
      document.removeEventListener('click', this.closeExportDropdownOnClickOutside);
    },
    closeExportDropdownOnClickOutside(event) {
      const dropdown = this.$el.querySelector('.relative');
      if (dropdown && !dropdown.contains(event.target)) {
        this.closeExportDropdown();
      }
    },
  },
  computed: {
    userData() {
      return this.$store.state.userDataModule.user || { addOns: {} };
    },
    fileExportName() {
      return this.moduleName + " - " + moment().format("YYYY-MM-DD");
    },
    patientUniqueIdEnable() {
      if (
        this.userData.unquie_id_status !== undefined &&
        this.userData.unquie_id_status === true
      ) {
        return this.userData.unquie_id_status;
      } else {
        return false;
      }
    },
    module_type() {
      return this.moduleType;
    },
    module_name() {
      return this.moduleName;
    },
    module_data() {
      return this.moduleData;
    }
  },
  watch: {
    moduleData: {
      handler: function (newVal, oldVal) {
        this.formatData(newVal);
      },
      deep: true
    }
  },
  beforeDestroy() {
    // Clean up event listener when component is destroyed
    document.removeEventListener('click', this.closeExportDropdownOnClickOutside);
  }
};
</script>

<style scoped></style>