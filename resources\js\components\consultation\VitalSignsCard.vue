<template>
  <!-- Exact KiviCare VitalSigns Component -->
  <div class="bg-white border rounded-lg shadow-sm mb-3">
    <!-- Header Section -->
    <div class="bg-gradient-to-r from-pink-50 to-gray-50 p-4 border-b">
      <div class="flex justify-between items-center">
        <div class="flex items-center gap-3">
          <div class="bg-black rounded-lg p-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
              <path d="M22 12h-4l-3 9L9 3l-3 9H2"/>
            </svg>
          </div>
          <div class="flex items-center gap-2">
            <h2 class="font-medium text-black">Vital Signs</h2>

            <!-- AI populated badge - shows when content has been populated by AI -->
            <div
              v-if="isAIPopulated"
              class="px-2 py-0.5 rounded-full bg-blue-100 text-blue-600 text-xs font-medium flex items-center gap-1"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-sparkles">
                <path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z"/>
                <path d="M5 3v4"/>
                <path d="M19 17v4"/>
                <path d="M3 5h4"/>
                <path d="M17 19h4"/>
              </svg>
              AI populated
            </div>
          </div>
        </div>
        <div class="flex gap-2">
          <!-- Approve AI button - only shown when vitals have been AI populated -->
          <button
            v-if="isAIPopulated"
            @click="handleApproveAI"
            class="text-green-500 hover:text-green-600 flex items-center gap-1 text-sm p-2 hover:bg-green-50 rounded-lg transition-colors"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check-circle">
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
              <polyline points="22 4 12 14.01 9 11.01"/>
            </svg>
            Approve
          </button>
          <button
            @click="cloneVitals"
            class="p-2 text-pink-500 hover:bg-pink-50 rounded-lg transition-colors"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
              <path d="M5 12h14"/>
              <path d="M12 5v14"/>
            </svg>
          </button>
          <button
            v-if="!isFirstInstance"
            @click="removeVitals"
            class="p-2 text-pink-500 hover:bg-pink-50 rounded-lg transition-colors"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
              <path d="M18 6 6 18"/>
              <path d="m6 6 12 12"/>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Vitals Grid -->
    <div class="p-4">
      <div class="grid grid-cols-5 gap-4">
        <div
          v-for="(field, index) in vitalFields"
          :key="index"
          class="relative group"
        >
          <div class="p-4 bg-white border rounded-lg group-hover:border-pink-200 transition-colors">
            <div class="flex items-center justify-between mb-2">
              <label class="text-sm font-medium text-gray-600">{{ field.label }}</label>
              <div class="h-6 w-6 rounded-full bg-pink-50 flex items-center justify-center">
                <!-- Temperature Icon -->
                <svg v-if="field.key === 'temperature'" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-pink-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M14 14.76V3.5a2.5 2.5 0 0 0-5 0v11.26a4.5 4.5 0 1 0 5 0z"/>
                </svg>
                <!-- Pulse Icon -->
                <svg v-else-if="field.key === 'pulse'" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-pink-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M22 12h-4l-3 9L9 3l-3 9H2"/>
                </svg>
                <!-- Blood Pressure Icon -->
                <svg v-else-if="field.key === 'bloodPressure'" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-pink-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M12 2v8"/>
                  <circle cx="12" cy="14" r="4"/>
                  <path d="M12 18v4"/>
                </svg>
                <!-- Respiratory Rate Icon -->
                <svg v-else-if="field.key === 'respiratoryRate'" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-pink-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"/>
                  <circle cx="12" cy="12" r="3"/>
                </svg>
                <!-- Saturation Icon -->
                <svg v-else-if="field.key === 'saturation'" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-pink-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M12 2.69l5.66 5.66a8 8 0 1 1-11.31 0z"/>
                </svg>
              </div>
            </div>
            <div class="relative">
              <input
                v-model="localVitalSigns[field.key]"
                class="w-full bg-transparent border-b border-gray-200 focus:border-pink-300 focus:outline-none text-lg py-1 pr-12"
                :placeholder="loading ? 'Loading...' : '--'"
                :disabled="loading"
                type="text"
                @change="handleChange"
              />
              <span class="absolute right-0 bottom-2 text-sm text-gray-500">{{ field.unit }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Save Button -->
      <div class="flex justify-between items-center mt-3">
        <button
          @click="handleSaveVitals"
          class="flex items-center gap-1 text-sm text-blue-500 hover:text-blue-600"
          :disabled="loading"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="lucide lucide-save w-4 h-4"
          >
            <path d="M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z"></path>
            <path d="M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7"></path>
            <path d="M7 3v4a1 1 0 0 0 1 1h7"></path>
          </svg>
          Save
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "VitalSigns",

  props: {
    instanceId: {
      type: String,
      required: true,
    },
    isFirstInstance: {
      type: Boolean,
      default: false,
    },
    encounterId: {
      type: [String, Number],
      default: null,
      required: true,
      validator: function (value) {
        return value !== null && value !== undefined && value !== "";
      },
    },
    // New property to receive vital signs data from parent
    vitalSignsData: {
      type: Object,
      default: null
    },
  },

  data() {
    return {
      loading: false,
      isAIPopulated: false,
      localVitalSigns: {
        temperature: "",
        pulse: "",
        bloodPressure: "",
        respiratoryRate: "",
        saturation: "",
      },
      vitalFields: [
        {
          label: "Temperature",
          key: "temperature",
          unit: "°C",
        },
        {
          label: "Pulse",
          key: "pulse",
          unit: "BPM",
        },
        {
          label: "Blood Pressure",
          key: "bloodPressure",
          unit: "mmHg",
        },
        {
          label: "Respiratory Rate",
          key: "respiratoryRate",
          unit: "/min",
        },
        {
          label: "Saturation",
          key: "saturation",
          unit: "%",
        },
      ],
    };
  },

  watch: {
    vitalSignsData: {
      handler(newData) {
        if (newData) {
          this.localVitalSigns = { ...newData };
        }
      },
      immediate: true,
      deep: true
    }
  },

  methods: {
    handleChange() {
      this.$emit('update:vitals', this.instanceId, this.localVitalSigns);
    },

    handleSaveVitals() {
      this.$emit('save:success', this.instanceId, this.localVitalSigns);
    },

    handleApproveAI() {
      this.isAIPopulated = false;
      this.$emit('ai-approved', this.instanceId);
    },

    cloneVitals() {
      this.$emit('clone');
    },

    removeVitals() {
      this.$emit('remove', this.instanceId);
    },
  }
};
</script>

const addVitalSet = () => {
  // Could implement multiple vital sign sets here
  console.log('Add vital set functionality')
}

const clearAll = () => {
  const clearedVitalSigns = {}
  Object.keys(props.template).forEach(key => {
    clearedVitalSigns[key] = ''
  })
  emit('update', clearedVitalSigns)
}

const copyFromPrevious = () => {
  // Could implement copying from previous consultation
  console.log('Copy from previous functionality')
}
</script>
