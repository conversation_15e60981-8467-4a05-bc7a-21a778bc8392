<template>
  <div
    class="flex items-center justify-between px-6 py-4 relative border-b border-gray-100 bg-white backdrop-blur-lg shadow-sm transition-all duration-300"
    :class="{ 'py-2': isHeaderCollapsed }">
    <!-- Added dynamic padding -->

    <!-- Left Side with Greeting and DateTime -->
    <div class="flex items-center gap-4">
      <!-- Collapse Toggle Button -->
      <button class="hidden lg:block p-2 hover:bg-gray-50 rounded-lg transition-all duration-200" @click="toggleHeader">
        <i class="fas fa-chevron-up text-gray-600 transition-transform duration-300"
          :class="{ 'rotate-180': isHeaderCollapsed }"></i>
      </button>

      <!-- Mobile Sidebar Toggle -->
      <button class="lg:hidden p-2 hover:bg-gray-50 rounded-lg transition-all duration-200"
        @click="handleSideBarToggle">
        <div class="flex flex-col gap-1.5">
          <span class="w-5 h-0.5 bg-gray-600 transition-all"></span>
          <span class="w-5 h-0.5 bg-gray-600 transition-all"></span>
          <span class="w-5 h-0.5 bg-gray-600 transition-all"></span>
        </div>
      </button>

      <!-- Greeting and DateTime Section -->
      <div class="space-y-1.5 transition-all overflow-hidden duration-300" :class="{
        'max-h-0 opacity-0': isHeaderCollapsed,
        'max-h-20 opacity-100': !isHeaderCollapsed,
      }">
        <div class="flex items-center space-x-2">
          <h2 class="text-lg font-semibold">
            <span class="text-gray-700">{{ greeting }}, </span>
            <span
              class="bg-gradient-to-r from-pink-500 via-purple-500 to-purple-800 text-transparent bg-clip-text font-bold">{{
                displayUsername }}!</span>
          </h2>
        </div>
        <div class="flex items-center space-x-2">
          <span class="w-2.5 h-2.5 bg-green-500 rounded-full animate-pulse"></span>
          <p class="text-sm text-gray-500">{{ formattedDateTime }}</p>
        </div>
      </div>
    </div>

    <!-- Right Side with Search, Actions and Profile -->
    <div class="flex items-center gap-4">
      <!-- Search Bar -->
      <div class="relative">
        <input type="text" placeholder="Search..."
          class="w-64 pl-10 pr-4 py-2 bg-gray-50 border border-gray-100 rounded-lg focus:outline-none focus:border-gray-200 focus:ring-1 focus:ring-gray-200 transition-all duration-200" />
        <svg class="absolute left-3 top-2.5 h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="11" cy="11" r="8" />
          <path d="m21 21-4.3-4.3" />
        </svg>
      </div>

      <!-- Notification Bell with Chat Notifications -->
      <div class="relative" ref="notificationMenu">
        <button @click.stop="toggleNotifications"
          class="notification-button p-2 text-gray-600 hover:text-pink-600 hover:bg-pink-50 rounded-lg transition-all duration-200 relative">
          <i class="fas fa-bell text-lg"></i>
          <span v-if="totalUnreadCount > 0"
            class="absolute top-0 right-0 flex items-center justify-center px-1.5 py-0.5 text-xs text-white bg-pink-500 rounded-full min-w-[18px] min-h-[18px]">
            {{ totalUnreadCount > 99 ? '99+' : totalUnreadCount }}
          </span>
        </button>

        <!-- Notification Dropdown -->
        <div v-show="showNotifications"
          class="notification-dropdown absolute right-0 mt-2 w-80 bg-white shadow-lg rounded-lg py-1 border border-gray-100 z-50">
          <div class="border-b border-gray-100 px-4 py-2 flex justify-between items-center">
            <h3 class="font-medium text-gray-800">Notifications</h3>
            <div class="flex gap-2">
              <button @click="markAllAsRead" class="text-xs text-gray-500 hover:text-pink-600">
                Mark all as read
              </button>
            </div>
          </div>

          <!-- Notification Tabs -->
          <div class="flex border-b border-gray-100">
            <button @click="activeTab = 'chat'" class="flex-1 py-2 text-sm font-medium transition-colors duration-200 relative"
              :class="activeTab === 'chat' ? 'text-pink-600 border-b-2 border-pink-500' : 'text-gray-500'">
              Chat
              <span v-if="unreadCount > 0"
                class="absolute top-0.5 right-3 flex items-center justify-center px-1 py-0.5 text-[10px] text-white bg-pink-500 rounded-full min-w-[14px] min-h-[14px]">
                {{ unreadCount > 99 ? '99+' : unreadCount }}
              </span>
            </button>
            <button @click="activeTab = 'system'" class="flex-1 py-2 text-sm font-medium transition-colors duration-200 relative"
              :class="activeTab === 'system' ? 'text-pink-600 border-b-2 border-pink-500' : 'text-gray-500'">
              System
              <span v-if="systemUnreadCount > 0"
                class="absolute top-0.5 right-3 flex items-center justify-center px-1 py-0.5 text-[10px] text-white bg-pink-500 rounded-full min-w-[14px] min-h-[14px]">
                {{ systemUnreadCount > 99 ? '99+' : systemUnreadCount }}
              </span>
            </button>
          </div>

          <!-- Chat Notifications List -->
          <div v-if="activeTab === 'chat'" class="max-h-64 overflow-y-auto">
            <div v-if="chatNotifications.length === 0" class="px-4 py-8 text-center text-gray-500 text-sm">
              No unread messages
            </div>
            <a v-for="notification in chatNotifications" :key="notification.id" href="#"
              @click.prevent="navigateToConversation(notification.conversation_id)"
              class="block px-4 py-2 hover:bg-gray-50 transition-colors duration-200 border-b border-gray-50 last:border-0">
              <div class="flex items-start gap-3">
                <!-- Sender Avatar -->
                <div
                  class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-700 flex-shrink-0 mt-1 relative">
                  <i class="fas fa-user-circle"></i>

                  <!-- File Type Indicator (if applicable) -->
                  <div v-if="notification.has_file"
                    class="absolute -bottom-1 -right-1 w-4 h-4 rounded-full bg-pink-100 border border-white flex items-center justify-center">
                    <i v-if="notification.file_type && notification.file_type.startsWith('image/')"
                      class="fas fa-image text-[9px] text-pink-600"></i>
                    <i v-else-if="notification.file_type === 'application/pdf'"
                      class="fas fa-file-pdf text-[9px] text-pink-600"></i>
                    <i v-else class="fas fa-file text-[9px] text-pink-600"></i>
                  </div>
                </div>

                <!-- Message Content -->
                <div class="flex-grow min-w-0">
                  <!-- Header: Sender + Time + Unread Count -->
                  <div class="flex justify-between items-start mb-1">
                    <div class="flex items-center">
                      <p class="font-medium text-gray-800 truncate mr-2">{{ notification.sender_name }}</p>

                      <!-- Unread Count Badge -->
                      <span v-if="notification.unread_count > 1"
                        class="px-1.5 py-0.5 bg-pink-100 text-pink-700 text-[10px] rounded-full">
                        {{ notification.unread_count }}
                      </span>
                    </div>
                    <span class="text-xs text-gray-500 whitespace-nowrap">{{
                      formatNotificationTime(notification.created_at) }}</span>
                  </div>

                  <!-- Message Preview -->
                  <div class="flex items-center text-sm">
                    <!-- Show file icon inline if it's a file message -->
                    <i v-if="notification.has_file" :class="getFileIcon(notification.file_type)"
                      class="mr-1.5 text-gray-400"></i>

                    <!-- Message Text -->
                    <p class="text-gray-600 truncate">{{ notification.message }}</p>
                  </div>

                  <!-- Conversation Title (who you're chatting with) -->
                  <p class="text-xs text-gray-400 truncate mt-1">
                    {{ notification.title }}
                  </p>
                </div>
              </div>
            </a>
          </div>

          <!-- System Notifications List -->
          <div v-else class="max-h-64 overflow-y-auto">
            <div v-if="systemNotifications.length === 0" class="px-4 py-8 text-center text-gray-500 text-sm">
              No system notifications
            </div>
            <a v-for="notification in systemNotifications" :key="notification.id" href="#"
              @click.prevent="handleNotificationClick(notification)"
              class="block px-4 py-2 hover:bg-gray-50 transition-colors duration-200 border-b border-gray-50 last:border-0">
              <div class="flex items-start gap-3">
                <!-- Notification Icon -->
                <div
                  class="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 mt-1"
                  :class="getNotificationTypeClass(notification.type)">
                  <i :class="getNotificationTypeIcon(notification.type)"></i>
                </div>

                <!-- Notification Content -->
                <div class="flex-grow min-w-0">
                  <!-- Header: Title + Time -->
                  <div class="flex justify-between items-start mb-1">
                    <p class="font-medium text-gray-800 truncate mr-2">{{ notification.title }}</p>
                    <span class="text-xs text-gray-500 whitespace-nowrap">
                      {{ formatNotificationTime(notification.created_at) }}
                    </span>
                  </div>

                  <!-- Message Preview -->
                  <p class="text-sm text-gray-600 line-clamp-2">{{ notification.message }}</p>
                </div>
              </div>
            </a>
          </div>

          <!-- View All Link -->
          <div class="border-t border-gray-100 py-2 px-4 text-center">
            <router-link v-if="activeTab === 'chat'" :to="{ name: 'chat' }" class="text-sm text-pink-600 hover:text-pink-700 font-medium">
              View all messages
            </router-link>
            <router-link v-else :to="{ name: 'notifications' }" class="text-sm text-pink-600 hover:text-pink-700 font-medium">
              View all notifications
            </router-link>
          </div>
        </div>
      </div>

      <!-- QR Code Button (Admin Only) -->
      <button v-if="isAdmin && demoPluginActive" @click="showQrcode = true"
        class="p-2 text-gray-600 hover:text-pink-600 hover:bg-pink-50 rounded-lg transition-all duration-200">
        <i class="fa fa-qrcode text-lg"></i>
      </button>

      <!-- Fullscreen Button -->
      <button @click.prevent="handleFullScreen" type="button"
        class="p-2 text-gray-600 hover:text-pink-600 hover:bg-pink-50 rounded-lg transition-all duration-200">
        <i class="fas fa-expand text-lg"></i>
      </button>

      <!-- Profile Menu -->
      <div class="relative" ref="profileMenu">
        <button
          class="flex items-center gap-2 px-4 py-2 bg-gray-50 hover:bg-gray-100 rounded-lg transition-all duration-200 user-dropdown-btn"
          @click="handleProfileClick">
          <i class="fas fa-user text-gray-600"></i>
          <span class="text-sm font-medium text-gray-700">{{
            formattedUserRole
          }}</span>
        </button>

        <!-- Enhanced Profile Dropdown Menu -->
        <div v-if="showProfileMenu"
          class="absolute right-0 mt-2 w-56 bg-white shadow-lg rounded-lg py-1 border border-gray-100 user-dropdown z-50">
          <div class="px-4 py-3 border-b border-gray-100">
            <p class="text-sm font-medium text-gray-800">
              {{ displayUsername }}
            </p>
            <p class="text-xs text-gray-500">{{ userData.user_email }}</p>
          </div>

          <!-- Profile Link -->
          <router-link v-if="shouldShowProfile" :to="profileRoute"
            class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200">
            <i class="fas fa-user mr-3 text-gray-400 w-4"></i>
            {{ formTranslation.common.my_profile }}
          </router-link>

          <!-- Change Password -->
          <router-link v-if="kcCheckPermission('change_password')" :to="{ name: 'account-setting.password' }"
            class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200">
            <i class="fas fa-unlock-alt mr-3 text-gray-400 w-4"></i>
            {{ formTranslation.common.change_password }}
          </router-link>

          <div class="h-px bg-gray-100 my-1"></div>

          <!-- Logout -->
          <button
            class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200"
            @click="logout">
            <i class="fas fa-sign-out-alt mr-3 w-4"></i>
            {{ formTranslation.common.logout }}
          </button>
        </div>
      </div>
    </div>

    <!-- QR Code Modal -->
    <ModalPopup v-if="showQrcode" modalId="appointment-details-modal" modalSize="md" :openModal="showQrcode"
      :modalTitle="formTranslation.common.qrcode" @closeModal="showQrcode = false">
      <div class="flex justify-center p-4">
        <VueQRCodeComponent :text="qrUrl"></VueQRCodeComponent>
      </div>
      <div class="px-4 pb-4">
        <input
          class="w-full px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500 transition-all duration-200"
          v-model="qrUrl" />
      </div>
    </ModalPopup>

    <!-- Demo User Credentials Modal -->
    <ModalPopup v-if="showChatBot" modalId="credentials" modalSize="md" :openModal="showChatBot"
      modalTitle="Demo User Credentials" @closeModal="showChatBot = false">
      <div v-for="(item, key) in demoUser" :key="key" class="border-b border-gray-100 last:border-b-0">
        <div class="p-4">
          <h3 class="text-center font-medium text-gray-800 mb-3">{{ key }}</h3>
          <div class="space-y-3">
            <div class="flex items-center justify-between bg-gray-50 p-2 rounded-lg">
              <span class="text-gray-600">Email:</span>
              <span class="font-medium text-gray-800">{{
                item + splitEmail + "@kivicare.com"
              }}</span>
              <button class="text-gray-400 hover:text-pink-600 transition-colors duration-200" @click="
                copyParentText(item + splitEmail + '@kivicare.com', $event)
                ">
                <i class="fas fa-copy"></i>
              </button>
            </div>
            <div class="flex items-center justify-between bg-gray-50 p-2 rounded-lg">
              <span class="text-gray-600">Password:</span>
              <span class="font-medium text-gray-800">123456</span>
              <button class="text-gray-400 hover:text-pink-600 transition-colors duration-200"
                @click="copyParentText('123456', $event)">
                <i class="fas fa-copy"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
      <input type="text" id="modal-hidden-field" class="opacity-0 absolute" />
    </ModalPopup>
  </div>
</template>

<script>
import { post, get } from "../../config/request";
import VueQRCodeComponent from "vue-qrcode-component";
import ModalPopup from "../Modal/Index";

export default {
  name: "Header",
  components: {
    VueQRCodeComponent,
    ModalPopup,
  },
  data() {
    return {
      isFullscreen: false,
      isHeaderCollapsed: false,
      currentDateTime: new Date(),
      showProfileMenu: false,
      showNotifications: false,
      showQrcode: false,
      showChatBot: false,
      timer: null,
      notificationTimer: null,
      chatNotifications: [],
      systemNotifications: [],
      unreadCount: 0,
      systemUnreadCount: 0,
      totalUnreadCount: 0,
      activeTab: 'chat',
      demoPluginActive: window.request_data.demo_plugin_active,
      documentationLink: "off",
      demoUser: {
        doctor: "doctor_",
        Patient: "patient_",
        Receptionist: "receptionist_",
      },
      qrUrl: window.request_data.homePage + "?user=",
      splitEmail: "",
    };
  },
  mounted() {
    // Initialize datetime update
    this.timer = setInterval(() => {
      this.currentDateTime = new Date();
    }, 1000);

    // Initialize notification polling
    this.fetchChatNotifications();
    this.fetchSystemNotifications();
    this.notificationTimer = setInterval(() => {
      this.fetchChatNotifications();
      this.fetchSystemNotifications();
    }, 30000); // Poll every 30 seconds

    // Initialize header
    this.initializeHeader();

    // Add fullscreen change listeners
    document.addEventListener("fullscreenchange", this.handleFullscreenChange);
    document.addEventListener(
      "webkitfullscreenchange",
      this.handleFullscreenChange
    );
    document.addEventListener(
      "mozfullscreenchange",
      this.handleFullscreenChange
    );
    document.addEventListener(
      "MSFullscreenChange",
      this.handleFullscreenChange
    );

    // Add click event listeners for dropdowns
    document.addEventListener('click', (e) => {
      // Handle profile dropdown
      if (!e.target.closest('.user-dropdown') && !e.target.closest('.user-dropdown-btn')) {
        this.showProfileMenu = false;
      }

      // Handle notification dropdown
      if (!e.target.closest('.notification-dropdown') && !e.target.closest('button[class*="fa-bell"]')) {
        this.showNotifications = false;
      }
    });
  },
  beforeDestroy() {
    clearInterval(this.timer);
    clearInterval(this.notificationTimer);

    // Remove fullscreen change listeners
    document.removeEventListener(
      "fullscreenchange",
      this.handleFullscreenChange
    );
    document.removeEventListener(
      "webkitfullscreenchange",
      this.handleFullscreenChange
    );
    document.removeEventListener(
      "mozfullscreenchange",
      this.handleFullscreenChange
    );
    document.removeEventListener(
      "MSFullscreenChange",
      this.handleFullscreenChange
    );
  },
  methods: {
    toggleHeader() {
      this.isHeaderCollapsed = !this.isHeaderCollapsed;
    },
    toggleNotifications() {
      console.log('Toggle notifications. Before:', this.showNotifications);
      this.showNotifications = !this.showNotifications;
      console.log('Toggle notifications. After:', this.showNotifications);

      // If we're showing notifications, fetch them
      if (this.showNotifications) {
        this.fetchChatNotifications();
        
        // If system tab is active, also fetch system notifications
        if (this.activeTab === 'system') {
          this.fetchSystemNotificationDetails();
        }

        // Also add an event listener to close when clicking outside
        this.$nextTick(() => {
          document.addEventListener('click', this.closeNotificationsOnClickOutside);
        });
      } else {
        // Remove event listener when closing
        document.removeEventListener('click', this.closeNotificationsOnClickOutside);
      }
    },

    closeNotificationsOnClickOutside(event) {
      // Get the notification dropdown element
      const dropdown = this.$el.querySelector('.notification-dropdown');
      const button = this.$el.querySelector('.notification-button');

      // If we have a dropdown and the click is outside both the dropdown and the button
      if (dropdown && !dropdown.contains(event.target) && !button.contains(event.target)) {
        this.showNotifications = false;
        document.removeEventListener('click', this.closeNotificationsOnClickOutside);
      }
    },
    fetchChatNotifications() {
      console.log('Fetching chat notifications...');
      // Use the API to get unread message count
      get('get_unread_count', {})
        .then(response => {
          console.log('Unread count response:', response);
          if (response.data && response.data.status) {
            // Make sure it's a number
            const count = parseInt(response.data.data, 10) || 0;
            this.unreadCount = count;
            console.log('Unread count:', this.unreadCount);

            // If notification panel is open, fetch detailed notifications
            if (this.showNotifications) {
              this.fetchUnreadMessages();
            }
          } else {
            console.log('Invalid response format or status is false:', response);
            // Fall back to conversations to get unread messages count
            this.fetchUnreadFromConversations();
          }
        })
        .catch(error => {
          console.error('Error fetching unread count:', error);
          // Fall back to conversations to get unread messages count
          this.fetchUnreadFromConversations();
        });
    },

    // Fallback method to get unread count from conversations
    fetchUnreadFromConversations() {
      console.log('Falling back to get unread count from conversations');
      get('get_conversations', {})
        .then(response => {
          if (response.data && response.data.status) {
            const conversations = response.data.data || [];

            // Sum up all unread counts
            let totalUnread = 0;
            conversations.forEach(conv => {
              if (conv.unread_count) {
                totalUnread += parseInt(conv.unread_count, 10) || 0;
              }
            });

            console.log('Total unread from conversations:', totalUnread);
            this.unreadCount = totalUnread;

            // If notification panel is open, process conversations into notifications
            if (this.showNotifications && totalUnread > 0) {
              this.processConversationsIntoNotifications(conversations);
            }
          }
        })
        .catch(error => {
          console.error('Error fetching conversations for unread count:', error);
        });
    },
    fetchUnreadMessages() {
      console.log('Fetching unread messages details...');
      // Only fetch notifications if we have unread messages
      if (this.unreadCount > 0) {
        // Since get_unread_messages doesn't exist, we'll use get_conversations 
        // which returns unread count per conversation
        get('get_conversations', {})
          .then(response => {
            console.log('Conversations response:', response);
            if (response.data && response.data.status) {
              // Process the conversations into notifications
              this.processConversationsIntoNotifications(response.data.data || []);
            } else {
              console.log('Invalid response from get_conversations:', response);
              this.chatNotifications = [];
            }
          })
          .catch(error => {
            console.error('Error fetching unread messages:', error);
            this.chatNotifications = [];
          });
      } else {
        this.chatNotifications = [];
      }
    },

    // Process conversations into notification objects
    processConversationsIntoNotifications(conversations) {
      try {
        console.log('Processing conversations into notifications:', conversations);
        // Filter only conversations with unread messages
        const unreadConversations = conversations.filter(conv => (parseInt(conv.unread_count, 10) || 0) > 0);
        console.log('Unread conversations:', unreadConversations);

        if (unreadConversations.length === 0) {
          this.chatNotifications = [];
          return;
        }

        const notifications = unreadConversations.map(conv => {
          try {
            // Get more detailed info about the conversation
            const currentUserId = this.$store.state.userDataModule.user.ID;
            let otherMember = null;

            if (Array.isArray(conv.members)) {
              otherMember = conv.members.find(member =>
                member.user_id !== currentUserId ||
                member.id !== currentUserId
              );
            }

            // Extract message details safely
            const lastMessage = conv.last_message || {};
            const hasFile = !!(lastMessage.file_url && lastMessage.file_type);
            let messageText = lastMessage.message || 'New message';

            // Format message text to include file type info if there's a file
            if (hasFile) {
              const fileType = this.getFileTypeName(lastMessage.file_type);
              messageText = `[${fileType}] ${messageText || ''}`.trim();
            }

            // Create a rich notification object with safe fallbacks
            return {
              id: conv.id || 0,
              conversation_id: conv.id || 0,
              title: this.getConversationName(conv),
              message: messageText,
              sender_name: lastMessage.sender_name ||
                (otherMember ? otherMember.display_name : 'User'),
              sender_avatar: lastMessage.sender_avatar || '',
              unread_count: parseInt(conv.unread_count, 10) || 0,
              created_at: conv.last_activity || new Date().toISOString(),
              has_file: hasFile,
              file_type: lastMessage.file_type || '',
              type: 'chat'
            };
          } catch (err) {
            console.error('Error processing conversation into notification:', err, conv);
            // Return a minimal valid notification object if there's an error
            return {
              id: conv.id || 0,
              conversation_id: conv.id || 0,
              title: 'Chat',
              message: 'New message',
              sender_name: 'User',
              unread_count: 1,
              created_at: new Date().toISOString(),
              type: 'chat'
            };
          }
        });

        console.log('Transformed notifications:', notifications);
        this.chatNotifications = notifications;
      } catch (error) {
        console.error('Error processing conversations:', error);
        this.chatNotifications = [];
      }
    },

    navigateToConversation(conversationId) {
      // Close the notification panel
      this.showNotifications = false;

      // Navigate to the conversation
      this.$router.push({
        name: 'chat',
        query: { conversation: conversationId }
      });
    },

    // Helper method to get conversation name
    getConversationName(conversation) {
      // For direct chats, show the other person's name
      if (conversation.type === 'direct') {
        // Find the other member (not current user)
        const otherMember = conversation.members?.find(
          member => member.user_id !== this.$store.state.userDataModule.user.ID
        );
        return otherMember ? otherMember.display_name : 'Chat';
      }

      // For group chats, show the group name
      return conversation.name || 'Group Chat';
    },

    getFileTypeName(fileType) {
      if (!fileType) return 'File';

      if (fileType.startsWith('image/')) {
        return 'Image';
      } else if (fileType === 'application/pdf') {
        return 'PDF';
      } else if (fileType.startsWith('audio/')) {
        return 'Audio';
      } else if (fileType.startsWith('video/')) {
        return 'Video';
      } else if (fileType.includes('word') || fileType.includes('document')) {
        return 'Document';
      } else if (fileType.includes('excel') || fileType.includes('spreadsheet')) {
        return 'Spreadsheet';
      } else {
        return 'File';
      }
    },

    getFileIcon(fileType) {
      if (!fileType) return 'far fa-file text-gray-500';

      if (fileType.startsWith('image/')) {
        return 'far fa-file-image text-green-500';
      } else if (fileType === 'application/pdf') {
        return 'far fa-file-pdf text-red-500';
      } else if (fileType.startsWith('audio/')) {
        return 'far fa-file-audio text-blue-500';
      } else if (fileType.startsWith('video/')) {
        return 'far fa-file-video text-purple-500';
      } else if (fileType.includes('word') || fileType.includes('document')) {
        return 'far fa-file-word text-blue-500';
      } else if (fileType.includes('excel') || fileType.includes('spreadsheet')) {
        return 'far fa-file-excel text-green-500';
      } else {
        return 'far fa-file text-gray-500';
      }
    },
    markAllAsRead() {
      console.log('Marking all notifications as read...');
      
      if (this.activeTab === 'chat') {
        // Chat notifications - mark all conversations as read
        if (this.chatNotifications.length === 0) {
          console.log('No chat notifications to mark as read');
          return;
        }

        // Get all unique conversation IDs with unread messages
        const conversationIds = [...new Set(this.chatNotifications.map(n => n.conversation_id))];
        console.log('Conversation IDs to mark as read:', conversationIds);

        // Mark each conversation's messages as read
        const promises = conversationIds.map(convId => {
          console.log('Marking conversation as read:', convId);
          return post('mark_as_read', { conversation_id: convId });
        });

        Promise.all(promises)
          .then(responses => {
            console.log('Mark as read responses:', responses);
            // If all succeeded, clear notifications
            const allSucceeded = responses.every(response => response.data.status);
            if (allSucceeded) {
              console.log('All messages marked as read successfully');
              this.unreadCount = 0;
              this.chatNotifications = [];
              this.updateTotalUnreadCount();
            } else {
              console.log('Some messages could not be marked as read');
              // Refresh to get updated counts
              this.fetchChatNotifications();
            }
          })
          .catch(error => {
            console.error('Error marking messages as read:', error);
            // Refresh to get updated counts
            this.fetchChatNotifications();
          });
      } else {
        // System notifications - mark all notifications as read
        if (this.systemNotifications.length === 0) {
          console.log('No system notifications to mark as read');
          return;
        }

        post('notifications_mark_all_read', {})
          .then(response => {
            console.log('Mark all system notifications as read response:', response);
            if (response.data.status) {
              this.systemUnreadCount = 0;
              this.systemNotifications = [];
              this.updateTotalUnreadCount();
              displayMessage('All notifications marked as read');
            } else {
              console.log('Could not mark all system notifications as read');
              displayErrorMessage(response.data.message || 'Could not mark notifications as read');
              // Refresh to get updated counts
              this.fetchSystemNotifications();
            }
          })
          .catch(error => {
            console.error('Error marking all system notifications as read:', error);
            displayErrorMessage('Error marking notifications as read');
            // Refresh to get updated counts
            this.fetchSystemNotifications();
          });
      }
    },
    
    // Fetch system notifications
    fetchSystemNotifications() {
      console.log('Fetching system notifications...');
      
      // First get the unread count
      get('notifications_unread_count', {})
        .then(response => {
          console.log('System notifications unread count response:', response);
          if (response.data && response.data.status) {
            // Update the count
            const count = parseInt(response.data.data.count, 10) || 0;
            this.systemUnreadCount = count;
            
            // Update total count
            this.updateTotalUnreadCount();
            
            // If notification panel is open and system tab is active, fetch detailed notifications
            if (this.showNotifications && this.activeTab === 'system') {
              this.fetchSystemNotificationDetails();
            }
          } else {
            console.log('Invalid response format for system notifications count:', response);
            this.systemUnreadCount = 0;
            this.updateTotalUnreadCount();
          }
        })
        .catch(error => {
          console.error('Error fetching system notifications unread count:', error);
          this.systemUnreadCount = 0;
          this.updateTotalUnreadCount();
        });
    },
    
    // Fetch detailed system notifications
    fetchSystemNotificationDetails() {
      console.log('Fetching system notification details...');
      
      // Parameters for the request
      const params = {
        per_page: 10,
        page: 1,
        only_unread: true
      };
      
      get('notifications_list', params)
        .then(response => {
          console.log('System notifications details response:', response);
          if (response.data && response.data.status) {
            this.systemNotifications = response.data.data.notifications || [];
          } else {
            console.log('Invalid response format for system notifications details:', response);
            this.systemNotifications = [];
          }
        })
        .catch(error => {
          console.error('Error fetching system notification details:', error);
          this.systemNotifications = [];
        });
    },
    
    // Handle notification click
    handleNotificationClick(notification) {
      console.log('Notification clicked:', notification);
      
      // Mark this notification as read
      post('notifications_mark_read', {
        notification_ids: [notification.id]
      })
        .then(response => {
          console.log('Mark notification as read response:', response);
          if (response.data && response.data.status) {
            // Remove from unread list and update count
            this.systemNotifications = this.systemNotifications.filter(n => n.id !== notification.id);
            this.systemUnreadCount = response.data.data.unread_count || 0;
            this.updateTotalUnreadCount();
            
            // Navigate to the reference if applicable
            this.navigateToNotificationReference(notification);
          }
        })
        .catch(error => {
          console.error('Error marking notification as read:', error);
        });
    },
    
    // Navigate to the appropriate page based on notification type
    navigateToNotificationReference(notification) {
      // Close the notifications panel
      this.showNotifications = false;
      
      // Handle different reference types
      if (notification.reference_type && notification.reference_id) {
        switch (notification.reference_type) {
          case 'appointment':
            this.$router.push({
              name: 'appointment.list',
              query: { appointment_id: notification.reference_id }
            });
            break;
            
          case 'encounter':
            this.$router.push({
              name: 'patient.encounter',
              params: { id: notification.reference_id }
            });
            break;
            
          case 'patient':
            this.$router.push({
              name: 'patient.view',
              params: { id: notification.reference_id }
            });
            break;
            
          default:
            // For unknown reference types, just close the panel
            break;
        }
      }
    },
    
    // Update the total unread count
    updateTotalUnreadCount() {
      this.totalUnreadCount = this.unreadCount + this.systemUnreadCount;
    },
    
    // Get CSS class for notification type
    getNotificationTypeClass(type) {
      const classes = {
        info: 'bg-blue-100 text-blue-500',
        success: 'bg-green-100 text-green-500',
        warning: 'bg-yellow-100 text-yellow-600',
        error: 'bg-red-100 text-red-500',
        appointment: 'bg-purple-100 text-purple-500',
        encounter: 'bg-pink-100 text-pink-500',
        prescription: 'bg-indigo-100 text-indigo-500',
        billing: 'bg-amber-100 text-amber-500',
        task: 'bg-emerald-100 text-emerald-500'
      };
      
      return classes[type] || 'bg-gray-100 text-gray-500';
    },
    
    // Get icon for notification type
    getNotificationTypeIcon(type) {
      const icons = {
        info: 'fas fa-info-circle',
        success: 'fas fa-check-circle',
        warning: 'fas fa-exclamation-triangle',
        error: 'fas fa-times-circle',
        appointment: 'fas fa-calendar-check',
        encounter: 'fas fa-stethoscope',
        prescription: 'fas fa-prescription',
        billing: 'fas fa-file-invoice-dollar',
        task: 'fas fa-tasks'
      };
      
      return icons[type] || 'fas fa-bell';
    },
    formatNotificationTime(timestamp) {
      const date = new Date(timestamp);
      const now = new Date();
      const diffInHours = (now - date) / (1000 * 60 * 60);

      if (diffInHours < 24) {
        // Today - show time only
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
      } else if (diffInHours < 48) {
        // Yesterday
        return 'Yesterday';
      } else {
        // Show date
        return date.toLocaleDateString();
      }
    },
    handleFullScreen() {
      try {
        if (!this.isFullscreen) {
          if (document.documentElement.requestFullscreen) {
            document.documentElement.requestFullscreen();
          } else if (document.documentElement.webkitRequestFullscreen) {
            document.documentElement.webkitRequestFullscreen();
          } else if (document.documentElement.mozRequestFullScreen) {
            document.documentElement.mozRequestFullScreen();
          } else if (document.documentElement.msRequestFullscreen) {
            document.documentElement.msRequestFullscreen();
          }
        } else {
          if (document.exitFullscreen) {
            document.exitFullscreen();
          } else if (document.webkitExitFullscreen) {
            document.webkitExitFullscreen();
          } else if (document.mozCancelFullScreen) {
            document.mozCancelFullScreen();
          } else if (document.msExitFullscreen) {
            document.msExitFullscreen();
          }
        }
      } catch (error) {
        console.error("Fullscreen error:", error);
      }
    },
    handleFullscreenChange() {
      this.isFullscreen = !!(
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.mozFullScreenElement ||
        document.msFullscreenElement
      );
    },
    handleProfileClick() {
      this.showProfileMenu = !this.showProfileMenu;
    },
    initializeHeader() {
      if (this.isAdmin) {
        this.splitEmail = this.userData.user_email.split("@")[0];
        this.qrUrl += this.splitEmail;
      }
      this.getRequestHelper();
    },
    handleSideBarToggle() {
      this.$store.commit("TOGGLE_SIDEBAR", !this.$store.state.fullSideBar);
    },
    getColorForName(name) {
      if (!name) return "#f3e8ff";
      const colors = ["#f3e8ff", "#fae8ff", "#f5f3ff", "#ede9fe", "#ddd6fe"];
      let hash = 0;
      for (let i = 0; i < name.length; i++) {
        hash = name.charCodeAt(i) + ((hash << 5) - hash);
      }
      return colors[Math.abs(hash) % colors.length];
    },
    copyParentText(value, event) {
      const buttons = document.querySelectorAll("#credentials i");
      buttons.forEach((button) => {
        button.classList.remove("fa-clipboard");
        button.classList.add("fa-copy");
        button.parentElement.setAttribute("title", "Copy");
      });

      const targetIcon = event.target.closest("i");
      targetIcon.classList.remove("fa-copy");
      targetIcon.classList.add("fa-clipboard");
      targetIcon.parentElement.setAttribute(
        "title",
        "Value Copied to clipboard"
      );

      const elem = document.getElementById("modal-hidden-field");
      elem.value = value;
      elem.select();
      document.execCommand("copy");
    },
    getRequestHelper() {
      if (
        window.request_data.link_show_hide !== undefined &&
        window.request_data.link_show_hide !== ""
      ) {
        this.documentationLink = window.request_data.link_show_hide;
      }
    },
    logout() {
      this.$store.dispatch("logout", { self: this });
    },
  },
  computed: {
    formattedUserRole() {
      const role = this.getUserRole() || "";
      return role
        .split("_")
        .map(
          (word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
        )
        .join(" ");
    },
    userData() {
      return this.$store.state.userDataModule.user || {};
    },
    displayUsername() {
      return (
        this.userData.display_name ||
        this.userData.user_email?.split("@")[0] ||
        ""
      );
    },
    isAdmin() {
      return this.getUserRole() === "administrator";
    },
    greeting() {
      const hour = this.currentDateTime.getHours();
      if (hour < 12) return "Good Morning";
      if (hour < 18) return "Good Afternoon";
      return "Good Evening";
    },
    formattedDateTime() {
      const dateOptions = { weekday: "long", month: "short", day: "numeric" };
      const formattedDate = this.currentDateTime.toLocaleDateString(
        "en-US",
        dateOptions
      );

      const timeOptions = {
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        hour12: false,
      };
      const formattedTime = this.currentDateTime.toLocaleTimeString(
        "en-US",
        timeOptions
      );

      return `${formattedDate}, ${formattedTime}`;
    },
    shouldShowProfile() {
      const role = this.getUserRole();
      return (
        (role === "receptionist" &&
          this.kcCheckPermission("receptionist_profile")) ||
        (role === "doctor" && this.kcCheckPermission("doctor_profile")) ||
        (role === "patient" && this.kcCheckPermission("patient_profile")) ||
        ((role === "clinic_admin" || role === "administrator") &&
          this.kcCheckPermission("clinic_profile"))
      );
    },
    profileRoute() {
      const role = this.getUserRole();
      const routes = {
        receptionist: "receptionist.profile",
        doctor: "doctor.profile",
        patient: "patient.profile",
        clinic_admin: "clinic.profile",
        administrator: "clinic.profile",
      };
      return { name: routes[role] };
    },
  },
  watch: {
    "userData.user_email": {
      immediate: true,
      handler(newEmail) {
        if (newEmail && this.isAdmin) {
          this.splitEmail = newEmail.split("@")[0];
          this.qrUrl =
            window.request_data.homePage + "?user=" + this.splitEmail;
        }
      },
    },
    activeTab(newTab) {
      // If notification panel is open, fetch the appropriate notifications
      if (this.showNotifications) {
        if (newTab === 'system') {
          this.fetchSystemNotificationDetails();
        } else if (newTab === 'chat') {
          this.fetchUnreadMessages();
        }
      }
    }
  },
};
</script>

<style scoped>
.rotate-180 {
  transform: rotate(180deg);
}

/* Smooth transitions for height and opacity */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
</style>