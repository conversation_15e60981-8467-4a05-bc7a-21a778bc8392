<?php

namespace Database\Factories;

use App\Models\Provider;
use App\Models\User;
use App\Models\Clinic;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Provider>
 */
class ProviderFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Provider::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Allow factory usage in testing environment
        if (app()->environment('testing')) {
            return [
                'user_id' => User::factory(),
                'clinic_id' => Clinic::factory(),
                'specialization' => fake()->randomElement([
                    'General Practice',
                    'Cardiology',
                    'Dermatology',
                    'Pediatrics',
                    'Orthopedics',
                    'Neurology',
                    'Psychiatry',
                    'Oncology'
                ]),
                'license_number' => 'LIC-' . fake()->unique()->numerify('######'),
                'verification_status' => fake()->randomElement(['pending', 'verified', 'rejected']),
                'verified_at' => fake()->optional(0.7)->dateTimeBetween('-1 year', 'now'),
                'rejection_reason' => null,
                'practice_locations' => [
                    [
                        'name' => fake()->company() . ' Clinic',
                        'address' => fake()->address(),
                        'phone' => fake()->phoneNumber()
                    ]
                ],
                'rating' => fake()->randomFloat(1, 3.0, 5.0),
                'pricing' => [
                    'consultation_fee' => fake()->randomFloat(2, 50, 300),
                    'currency' => 'USD'
                ],
                'gender' => fake()->randomElement(['male', 'female', 'other']),
                'languages' => [
                    fake()->randomElement(['English', 'Spanish', 'French', 'German'])
                ],
                'bio' => fake()->paragraph(),
                'profile_image' => null,
                'education' => [
                    [
                        'degree' => 'MD',
                        'institution' => fake()->company() . ' Medical School',
                        'year' => fake()->year('-10 years')
                    ]
                ],
                'accepts_insurance' => fake()->boolean(80),
                'insurance_providers' => [
                    fake()->randomElement(['Blue Cross', 'Aetna', 'Cigna'])
                ],
                'weekly_availability' => [
                    ['day' => 'Monday', 'slots' => ['09:00-12:00', '14:00-17:00']],
                    ['day' => 'Tuesday', 'slots' => ['09:00-12:00', '14:00-17:00']],
                    ['day' => 'Wednesday', 'slots' => ['09:00-12:00', '14:00-17:00']],
                    ['day' => 'Thursday', 'slots' => ['09:00-12:00', '14:00-17:00']],
                    ['day' => 'Friday', 'slots' => ['09:00-12:00', '14:00-17:00']],
                    ['day' => 'Saturday', 'slots' => []],
                    ['day' => 'Sunday', 'slots' => []]
                ],
                'absences' => [],
                'certifications' => [
                    [
                        'name' => 'Board Certification',
                        'issuer' => fake()->company() . ' Medical Board',
                        'year' => fake()->year('-5 years')
                    ]
                ],
            ];
        }

        throw new \Exception('ProviderFactory is disabled for production use. Create providers manually through proper channels.');
    }

    /**
     * Indicate that the provider is verified.
     */
    public function verified(): static
    {
        return $this->state(fn (array $attributes) => [
            'verification_status' => 'verified',
            'verified_at' => now(),
            'rejection_reason' => null,
        ]);
    }

    /**
     * Indicate that the provider is pending verification.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'verification_status' => 'pending',
            'verified_at' => null,
            'rejection_reason' => null,
        ]);
    }

    /**
     * Indicate that the provider is rejected.
     */
    public function rejected(): static
    {
        return $this->state(fn (array $attributes) => [
            'verification_status' => 'rejected',
            'verified_at' => null,
            'rejection_reason' => fake()->sentence(),
        ]);
    }

    /**
     * Create a provider with specific specialization.
     */
    public function specialization(string $specialization): static
    {
        return $this->state(fn (array $attributes) => [
            'specialization' => $specialization,
        ]);
    }

    /**
     * Create a provider with high rating.
     */
    public function highRating(): static
    {
        return $this->state(fn (array $attributes) => [
            'rating' => fake()->randomFloat(1, 4.5, 5.0),
        ]);
    }

    /**
     * Create a provider with low rating.
     */
    public function lowRating(): static
    {
        return $this->state(fn (array $attributes) => [
            'rating' => fake()->randomFloat(1, 1.0, 3.0),
        ]);
    }

    /**
     * Create a provider that doesn't accept insurance.
     */
    public function noInsurance(): static
    {
        return $this->state(fn (array $attributes) => [
            'accepts_insurance' => false,
            'insurance_providers' => [],
        ]);
    }

    /**
     * Create a provider with minimal data.
     */
    public function minimal(): static
    {
        return $this->state(fn (array $attributes) => [
            'practice_locations' => [],
            'languages' => [],
            'bio' => null,
            'education' => [],
            'insurance_providers' => [],
            'absences' => [],
            'certifications' => [],
        ]);
    }
}
