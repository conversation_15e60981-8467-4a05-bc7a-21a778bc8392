<template>
  <div class="min-h-screen p-6 bg-gradient-to-br from-pink-50 to-purple-50">
    <div class="relative" v-if="!userData.addOns.kiviPro">
      <div
        class="absolute inset-0 bg-white/50 backdrop-blur-sm z-10 flex items-center justify-center"
      >
        <overlay-message addon_type="pro" />
      </div>
    </div>

    <!-- Header Section -->
    <div class="mb-8 flex justify-between items-center">
      <div class="flex items-center gap-4">
        <button
          class="flex items-center gap-2 px-4 py-2 bg-black text-sm text-white rounded-lg shadow-sm hover:bg-gray-800"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="w-4 h-4"
          >
            <path d="m12 19-7-7 7-7" />
            <path d="M19 12H5" />
          </svg>
          <span>Back</span>
        </button>
        <h1 class="text-2xl font-semibold text-gray-800">
          {{ formTranslation.common.custom_form_list }}
          <a
            v-if="request_status == 'off'"
            href="https://apps.medroid.ai/docs/product/kivicare/clinic-admin/settings/#custom-form"
            target="_blank"
            class="ml-2 text-gray-400 hover:text-gray-600"
          >
            <i class="fa fa-question-circle"></i>
          </a>
        </h1>
      </div>

      <router-link
        v-if="kcCheckPermission('custom_form_add')"
        :to="{ name: 'custom-form.create' }"
        class="px-4 py-2 bg-black text-sm text-white rounded-lg hover:bg-gray-800 flex items-center gap-2"
      >
        <span>{{ formTranslation.common.add_form }}</span>
      </router-link>
    </div>

    <!-- Search Bar -->
    <div class="relative mb-6">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"
      >
        <circle cx="11" cy="11" r="8" />
        <path d="m21 21-4.3-4.3" />
      </svg>
      <input
        type="text"
        v-model="serverParams.searchTerm"
        @input="globalFilter({ searchTerm: serverParams.searchTerm })"
        :placeholder="
          formTranslation.common.search_custom_form_data_global_placeholder
        "
        class="w-full pl-10 pr-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
      />
    </div>

    <!-- Filters Grid -->
    <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-6">
      <input
        v-model="serverParams.columnFilters.id"
        :placeholder="formTranslation.common.id"
        @input="onColumnFilter({ columnFilters: serverParams.columnFilters })"
        class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
      />

      <input
        v-model="serverParams.columnFilters.name"
        :placeholder="formTranslation.common.name"
        @input="onColumnFilter({ columnFilters: serverParams.columnFilters })"
        class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
      />

      <select
        v-model="serverParams.columnFilters.module_type"
        @change="onColumnFilter({ columnFilters: serverParams.columnFilters })"
        class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
      >
        <option
          v-for="type in moduleType"
          :key="type.value"
          :value="type.value"
        >
          {{ type.text }}
        </option>
      </select>

      <select
        v-model="serverParams.columnFilters.status"
        @change="onColumnFilter({ columnFilters: serverParams.columnFilters })"
        class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
      >
        <option value="">All Status</option>
        <option value="1">Active</option>
        <option value="0">Inactive</option>
      </select>
    </div>

    <!-- Loading State -->
    <div v-if="pageLoader" class="flex justify-center my-8">
      <loader-component-2></loader-component-2>
    </div>

    <!-- Table Section -->
    <div
      v-else
      class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-x-auto"
    >
      <table class="w-full">
        <thead class="bg-gray-50">
          <tr>
            <th
              v-for="column in customFormList.column"
              :key="column.field"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              {{ column.label }}
            </th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
          <tr
            v-for="(row, index) in customFormList.data"
            :key="row.id"
            class="hover:bg-gray-50"
          >
            <td class="px-6 py-4 whitespace-nowrap">{{ row.id }}</td>
            <td class="px-6 py-4 whitespace-nowrap">
              {{ row.name | typeFiled }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              {{ row.module_type | typeFiled }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center gap-2">
                <!-- Toggle button -->
                <toggle-switch
                  :value="row.status === '1' ? 'on' : 'off'"
                  @input="
                    (value) => {
                      // Update the local state immediately for the toggle animation
                      row.status = value === 'on' ? '1' : '0';
                      // Then call the status change function
                      changeModuleValueStatus({
                        module_type: 'custom_form',
                        id: row.id,
                        value: value === 'on' ? '1' : '0',
                      });
                    }
                  "
                  on-value="on"
                  off-value="off"
                />
                <span
                  v-if="row.status === '1'"
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
                >
                  {{ formTranslation.common.active }}
                </span>
                <span
                  v-else
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"
                >
                  {{ formTranslation.common.inactive }}
                </span>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex gap-2">
                <router-link
                  v-if="kcCheckPermission('custom_form_edit')"
                  :to="{ name: 'custom-form.edit', params: { id: row.id } }"
                  class="p-1 hover:bg-gray-100 rounded"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="w-4 h-4 text-gray-600"
                  >
                    <path
                      d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"
                    />
                  </svg>
                </router-link>
                <button
                  v-if="kcCheckPermission('custom_form_delete')"
                  @click="deleteCustomFormData(index + 1)"
                  class="p-1 hover:bg-gray-100 rounded"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="w-4 h-4 text-red-500"
                  >
                    <path d="M3 6h18" />
                    <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
                    <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
                  </svg>
                </button>
              </div>
            </td>
          </tr>
          <tr v-if="customFormList.data.length === 0">
            <td colspan="5" class="px-6 py-4 text-center text-red-500">
              {{ formTranslation.common.no_data_found }}
            </td>
          </tr>
        </tbody>
      </table>

      <!-- Pagination -->
      <div
        class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6"
      >
        <div class="flex items-center gap-2">
          <span class="text-sm text-gray-700">Rows per page:</span>
          <select
            v-model="serverParams.perPage"
            @change="onPerPageChange({ currentPerPage: serverParams.perPage })"
            class="border border-gray-300 rounded-md text-sm p-1"
          >
            <option>10</option>
            <option>25</option>
            <option>50</option>
          </select>
        </div>
        <div class="flex items-center gap-4">
          <span class="text-sm text-gray-700">
            Page {{ serverParams.page }} of
            {{ Math.ceil(totalRows / serverParams.perPage) }}
          </span>
          <div class="flex gap-2">
            <button
              @click="onPageChange({ currentPage: serverParams.page - 1 })"
              :disabled="serverParams.page === 1"
              class="p-1 rounded hover:bg-gray-100 disabled:opacity-50"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="w-5 h-5 text-gray-600"
              >
                <path d="m15 18-6-6 6-6" />
              </svg>
            </button>
            <button
              @click="onPageChange({ currentPage: serverParams.page + 1 })"
              :disabled="
                serverParams.page >= Math.ceil(totalRows / serverParams.perPage)
              "
              class="p-1 rounded hover:bg-gray-100 disabled:opacity-50"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="w-5 h-5 text-gray-600"
              >
                <path d="m9 18 6-6-6-6" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { post, get } from "../../config/request";

export default {
  data: () => {
    return {
      pageLoader: true,
      customFormList: {
        column: [],
        data: [],
      },
      customFormRequest: {},
      serverParams: {
        columnFilters: {},
        sort: [
          {
            field: "",
            type: "",
          },
        ],
        page: 1,
        perPage: 10,
        searchTerm: "",
      },
      oldServerParams: {
        columnFilters: {},
        searchTerm: "",
        perPage: 10,
      },
      totalRows: 0,
      moduleType: [
        { value: "doctor_module", text: "Doctor module" },
        { value: "patient_module", text: "Patient module" },
        { value: "patient_encounter_module", text: "Patient encounter module" },
        { value: "appointment_module", text: "Appointment module" },
      ],
      request_status: "off",
    };
  },
  mounted() {
    if (["patient", "receptionist", "doctor"].includes(this.getUserRole())) {
      this.$router.push({ name: "403" });
    }
    this.init();
    this.moduleType.unshift({
      value: "",
      text: this.formTranslation.common.all,
    });
    this.getModule();
  },
  methods: {
    init: function () {
      this.customFormList = this.defaultCustomFormData();
      this.getCustomFormList();
    },
    getCustomFormList() {
      get("custom_form_list", this.serverParams)
        .then((data) => {
          this.pageLoader = false;
          if (data.data.status !== undefined && data.data.status === true) {
            this.customFormList.data = data.data.data;
            this.totalRows = data.data.total;
          } else {
            this.customFormList.data = data.data.data;
            this.totalRows = 0;
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    updateParams: function (newProps) {
      this.serverParams = Object.assign({}, this.serverParams, newProps);
      this.getCustomFormList();
    },

    onPageChange(params) {
      this.updateParams({ page: params.currentPage });
    },

    onPerPageChange(params) {
      if (this.oldServerParams.perPage === params.currentPerPage) {
        return;
      }
      this.oldServerParams.perPage = params.currentPerPage;
      this.updateParams({
        perPage: params.currentPerPage,
        page: params.currentPage,
      });
    },

    onSortChange(params) {
      this.updateParams({
        sort: params,
      });
    },
    globalFilter: _.debounce(function (params) {
      if (this.oldServerParams.searchTerm === params.searchTerm) {
        return;
      }
      this.oldServerParams.searchTerm = params.searchTerm;
      this.updateParams({
        searchTerm: params.searchTerm,
        perPage: this.serverParams.perPage,
        page: 1,
      });
    }, 300),
    onColumnFilter: _.debounce(function (params) {
      var emptyValue = true;
      var emptyValue2 = true;
      Object.values(params.columnFilters).map(function (value, index, array) {
        if (value) {
          emptyValue = false;
        }
      });
      Object.values(this.oldServerParams.columnFilters).map(function (
        value,
        index,
        array
      ) {
        if (value) {
          emptyValue2 = false;
        }
      });
      if (!emptyValue || !emptyValue2) {
        this.oldServerParams.columnFilters = Object.assign(
          {},
          params.columnFilters
        );
        this.updateParams({
          columnFilters: params.columnFilters,
          perPage: this.serverParams.perPage,
          page: 1,
        });
      }
    }, 300),
    defaultCustomFormData() {
      return {
        data: [],
        column: [
          {
            field: "id",
            label: this.formTranslation.common.id,
          },
          {
            field: "name",
            label: this.formTranslation.common.name,
          },
          {
            field: "module_type",
            label: this.formTranslation.custom_field.dt_lbl_type,
          },
          {
            field: "status",
            label: this.formTranslation.service.dt_lbl_status,
          },
          {
            field: "actions",
            label: this.formTranslation.custom_field.dt_lbl_action,
          },
        ],
      };
    },
    deleteCustomFormData: function (index) {
      if (this.customFormList.data[index - 1] !== undefined) {
        this.$swal
          .fire({
            title: this.formTranslation.clinic_schedule.dt_are_you_sure,
            text: this.formTranslation.common.py_delete_field,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#d33",
            cancelButtonColor: "#3085d6",
            confirmButtonText: "Yes",
            cancelButtonText: "Cancel",
          })
          .then((result) => {
            if (result.isConfirmed) {
              // Show loading state
              this.$swal.fire({
                title: "Deleting...",
                text: "Please wait while we process your request",
                allowOutsideClick: false,
                didOpen: () => {
                  this.$swal.showLoading();
                },
              });

              post("custom_form_delete", {
                id: this.customFormList.data[index - 1].id,
              })
                .then((data) => {
                  if (
                    data.data.status !== undefined &&
                    data.data.status === true
                  ) {
                    // Remove the item from the list
                    this.customFormList.data.splice(index - 1, 1);

                    // Show success message
                    this.$swal.fire({
                      icon: "success",
                      title: "Deleted",
                      text: data.data.message,
                      showConfirmButton: false,
                      timer: 1500,
                    });
                  } else {
                    // Show error if status is not true
                    this.$swal.fire({
                      icon: "error",
                      title: "Error",
                      text: data.data.message || "Something went wrong",
                      showConfirmButton: true,
                    });
                  }
                })
                .catch((error) => {
                  // Handle error scenarios
                  const errorMessage =
                    error.response?.data?.message ||
                    this.formTranslation.common.internal_server_error;

                  this.$swal.fire({
                    icon: "error",
                    title: "Error",
                    text: errorMessage,
                    showConfirmButton: true,
                  });
                });
            }
          });
      }
    },
    getModule: function () {
      if (
        window.request_data.link_show_hide !== undefined &&
        window.request_data.link_show_hide !== ""
      ) {
        this.request_status = window.request_data.link_show_hide;
      }
    },
  },
  computed: {
    userData() {
      return this.$store.state.userDataModule.user;
    },
  },
  filters: {
    typeFiled: function (value) {
      let label = value;
      let lableValue = "";
      if (label !== "" && label !== null) {
        if (label !== undefined && label !== "") {
          lableValue = label;
          lableValue = lableValue.replace(/_/gi, " ");
        }
      }
      return lableValue;
    },
  },
};
</script>
