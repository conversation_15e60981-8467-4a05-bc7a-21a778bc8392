.kivicare-avatar-upload {
  position: relative;
  max-width: 205px;
  margin: 5px auto;
  .kivicare-avatar-edit {
      position: absolute;
      right: 22px;
      z-index: 1;
      top: 11px;
      input {
          display: none;
          + label {
              display: inline-block;
              width: 34px;
              height: 34px;
              margin-bottom: 0;
              border-radius: 100%;
              background: #FFFFFF;
              border: 1px solid transparent;
              box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.12);
              cursor: pointer;
              font-weight: normal;
              transition: all .2s ease-in-out;
              &:hover {
                  background: #f1f1f1;
                  border-color: #d6d6d6;
              }
              .fa-2x {
                font-size: 20px;
              }
              .fa-pencil-alt:before {
                content: "\f303";
                // font-family: 'FontAwesome';
                color: #757575;
                position: absolute;
                top: 7px;
                left: 0;
                right: 0;
                text-align: center;
                margin: auto;
              }
          }
      }
  }
  .kivicare-avatar-preview {
      width: 192px;
      height: 192px;
      position: relative;
      border-radius: 100%;
      border: 6px solid #F8F8F8;
      box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
      > div {
          width: 100%;
          height: 100%;
          border-radius: 100%;
          background-size: cover;
          background-repeat: no-repeat;
          background-position: center;
      }
  }
}