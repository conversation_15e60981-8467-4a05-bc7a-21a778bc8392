# BillingView.vue
<template>
  <div class="min-h-screen p-6 bg-gradient-to-br from-pink-50 to-purple-50">
    <!-- Header Section -->
    <div class="mb-8 flex justify-between items-center">
      <div class="flex items-center gap-4">
        <button
          class="flex items-center gap-2 px-4 py-2 bg-black text-white rounded-lg shadow-sm hover:bg-gray-800"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="lucide lucide-arrow-left w-4 h-4"
          >
            <path d="m12 19-7-7 7-7"></path>
            <path d="M19 12H5"></path>
          </svg>
          <span>Back</span>
        </button>
        <h1 class="text-2xl font-semibold text-gray-800">
          {{ formTranslation.patient_bill.bills }}
        </h1>
      </div>
      <button
        v-if="kcCheckPermission('patient_bill_add') && !billCreateModel"
        @click="billCreateModel = !billCreateModel"
        class="px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 flex items-center gap-2"
      >
        <span>{{ formTranslation.patient_bill.add_bill }}</span>
      </button>
    </div>

    <!-- Global Search -->
    <div class="relative mb-6">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
        class="lucide lucide-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"
      >
        <circle cx="11" cy="11" r="8"></circle>
        <path d="m21 21-4.3-4.3"></path>
      </svg>
      <input
        type="text"
        v-model="serverParams.searchTerm"
        @input="globalFilter"
        :placeholder="
          formTranslation.common.search_bills_data_global_placeholder
        "
        class="w-full pl-10 pr-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
      />
    </div>

    <!-- Advanced Filters -->
    <div class="grid grid-cols-6 gap-4 mb-6">
      <!-- ID Filter -->
      <div class="relative">
        <input
          type="text"
          v-model="serverParams.columnFilters.bill_id"
          @input="onColumnFilter"
          :placeholder="formTranslation.common.id"
          class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
        />
        <button
          v-if="serverParams.columnFilters.bill_id"
          @click="clearFilter('bill_id')"
          class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clip-rule="evenodd"
            />
          </svg>
        </button>
      </div>

      <!-- Doctor Filter -->
      <div class="relative">
        <input
          type="text"
          v-model="serverParams.columnFilters.doctor_name"
          @input="onColumnFilter"
          placeholder="Filter By Doctor"
          class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
        />
        <button
          v-if="serverParams.columnFilters.doctor_name"
          @click="clearFilter('doctor_name')"
          class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clip-rule="evenodd"
            />
          </svg>
        </button>
      </div>

      <!-- Clinic Filter -->
      <div class="relative">
        <input
          type="text"
          v-model="serverParams.columnFilters.clinic_name"
          @input="onColumnFilter"
          placeholder="Filter By Clinic"
          class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
        />
        <button
          v-if="serverParams.columnFilters.clinic_name"
          @click="clearFilter('clinic_name')"
          class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clip-rule="evenodd"
            />
          </svg>
        </button>
      </div>

      <!-- Patient Filter -->
      <div class="relative">
        <input
          type="text"
          v-model="serverParams.columnFilters.patient_name"
          @input="onColumnFilter"
          placeholder="Filter By Patient"
          class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
        />
        <button
          v-if="serverParams.columnFilters.patient_name"
          @click="clearFilter('patient_name')"
          class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clip-rule="evenodd"
            />
          </svg>
        </button>
      </div>

      <!-- Date Filter -->
      <div class="relative">
        <vc-date-picker
          v-model="serverParams.columnFilters.created_at"
          @input="onColumnFilter"
          mode="range"
          :popover="{ visibility: 'click' }"
          class="w-full"
        >
          <template v-slot="{ inputValue, inputEvents }">
            <input
              :value="inputValue"
              v-on="inputEvents"
              placeholder="Filter By Date"
              class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
            />
          </template>
        </vc-date-picker>
        <button
          v-if="serverParams.columnFilters.created_at"
          @click="clearFilter('created_at')"
          class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clip-rule="evenodd"
            />
          </svg>
        </button>
      </div>

      <!-- Status Filter -->
      <div class="relative">
        <select
          v-model="serverParams.columnFilters.status"
          @change="onColumnFilter"
          class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
        >
          <option value="">
            {{ formTranslation.static_data.dt_lbl_plh_sr_fltr_status }}
          </option>
          <option value="0">{{ formTranslation.patient_bill.paid }}</option>
          <option value="1">{{ formTranslation.patient_bill.unpaid }}</option>
        </select>
        <button
          v-if="serverParams.columnFilters.status"
          @click="clearFilter('status')"
          class="absolute right-8 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-4 w-4"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clip-rule="evenodd"
            />
          </svg>
        </button>
      </div>
    </div>

    <!-- Table Section -->
    <div
      class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-x-auto"
    >
      <!-- Loader -->
      <div v-show="pageLoader" class="flex justify-center items-center py-8">
        <loader-component-2 />
      </div>

      <!-- Table -->
      <!-- Replace just the table section in your BillingView.vue -->
      <table class="w-full" v-show="!pageLoader">
        <thead class="bg-gray-50">
          <tr>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              <input
                type="checkbox"
                class="rounded border-gray-300"
                @change="selectAllRows"
              />
            </th>
            <th
              v-for="column in [
                'ID',
                'Doctor/Clinic',
                'Patient/Services',
                'Amount Details',
                'Date',
                'Status',
                'Actions',
              ]"
              :key="column"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              {{ column }}
            </th>
          </tr>
        </thead>

        <tbody class="divide-y divide-gray-200">
          <tr
            v-for="(row, rowIndex) in billingList.data"
            :key="`row-${row.id}-${rowIndex}`"
            class="hover:bg-gray-50"
          >
            <!-- Checkbox -->
            <td class="px-6 py-4 whitespace-nowrap">
              <input
                type="checkbox"
                class="rounded border-gray-300"
                v-model="selectedRows"
                :value="row.id"
              />
            </td>

            <!-- ID -->
            <td class="px-6 py-4 whitespace-nowrap">
              {{ row.bill_id }}
            </td>

            <!-- Doctor/Clinic -->
            <td class="px-6 py-4">
              <div class="text-sm font-medium text-gray-900">
                {{ row.doctor_name }}
              </div>
              <div class="text-xs text-gray-500">{{ row.clinic_name }}</div>
            </td>

            <!-- Patient/Services -->
            <td class="px-6 py-4">
              <div class="text-sm font-medium text-gray-900">
                {{ row.patient_name }}
              </div>
              <div class="text-xs text-gray-500">{{ row.service_name }}</div>
            </td>

            <!-- Amount Details -->
            <td class="px-6 py-4">
              <div class="space-y-1">
                <div class="flex justify-between text-sm">
                  <span class="text-gray-500">Total:</span>
                  <span class="font-medium">{{ row.total_amount }}</span>
                </div>
                <div class="flex justify-between text-sm font-medium">
                  <span class="text-gray-900">Due:</span>
                  <span class="text-gray-900">{{ row.actual_amount }}</span>
                </div>
              </div>
            </td>

            <!-- Date -->
            <td class="px-6 py-4 whitespace-nowrap">
              {{ row.created_at }}
            </td>

            <!-- Status -->
            <td class="px-6 py-4 whitespace-nowrap">
              <span
                :class="[
                  'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                  row.status === '0'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800',
                ]"
              >
                {{
                  row.status === "0"
                    ? formTranslation.patient_bill.paid
                    : formTranslation.patient_bill.unpaid
                }}
              </span>
            </td>

            <!-- Actions -->
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex gap-2">
                <button
                  v-if="
                    kcCheckPermission('patient_bill_edit') && row.status == '1'
                  "
                  @click="billModelOpen(row, 'edit')"
                  class="p-1 hover:bg-gray-100 rounded"
                  :title="formTranslation.common.edit"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="w-4 h-4 text-gray-600"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
                    />
                  </svg>
                </button>
                <button
                  v-if="kcCheckPermission('patient_bill_view')"
                  @click="billModelOpen(row, 'detail')"
                  class="p-1 hover:bg-gray-100 rounded"
                  :title="formTranslation.patient_bill.bill_details"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="w-4 h-4 text-gray-600"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                </button>
                <router-link
                  v-if="kcCheckPermission('patient_encounter_view')"
                  :to="{
                    name: 'patient-encounter.dashboard',
                    params: { encounter_id: row.id },
                  }"
                  class="p-1 hover:bg-gray-100 rounded"
                  :title="formTranslation.patient_encounter.encounter_details"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="w-4 h-4 text-blue-600"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                    />
                  </svg>
                </router-link>
              </div>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- Pagination -->
      <div
        class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6"
      >
        <div class="flex items-center gap-2">
          <span class="text-sm text-gray-700">Rows per page:</span>
          <select
            v-model="serverParams.perPage"
            @change="onPerPageChange"
            class="border border-gray-300 rounded-md text-sm p-1"
          >
            <option>10</option>
            <option>25</option>
            <option>50</option>
          </select>
        </div>
        <div class="flex items-center gap-4">
          <span class="text-sm text-gray-700">
            Page {{ serverParams.page }} of
            {{ Math.ceil(totalRows / serverParams.perPage) }}
          </span>
          <div class="flex gap-2">
            <button
              @click="onPageChange({ currentPage: serverParams.page - 1 })"
              :disabled="serverParams.page === 1"
              class="p-1 rounded hover:bg-gray-100 disabled:opacity-50"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="lucide lucide-chevron-left w-5 h-5 text-gray-600"
              >
                <path d="m15 18-6-6 6-6"></path>
              </svg>
            </button>
            <button
              @click="onPageChange({ currentPage: serverParams.page + 1 })"
              :disabled="
                serverParams.page >= Math.ceil(totalRows / serverParams.perPage)
              "
              class="p-1 rounded hover:bg-gray-100 disabled:opacity-50"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="lucide lucide-chevron-right w-5 h-5 text-gray-600"
              >
                <path d="m9 18 6-6-6-6"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Create Bill Modal -->
    <template v-if="billCreateModel">
      <div
        class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center"
      >
        <div
          class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] flex flex-col"
        >
          <!-- Modal Header -->
          <div class="flex items-center justify-between px-6 py-4 border-b">
            <h3 class="text-xl font-semibold text-gray-800">
              {{ formTranslation.patient_bill.add_new_bill }}
            </h3>
            <button
              @click="billCreateModel = false"
              class="p-1 hover:bg-gray-100 rounded-full"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6 text-gray-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          <!-- Modal Content -->
          <div class="overflow-y-auto flex-grow">
            <div class="p-6">
              <div v-if="withoutBillEncounterData.length > 0">
                <div class="flex flex-col md:flex-row gap-4 mb-6">
                  <div class="flex-grow">
                    <select
                      v-model="selected_encounter_id"
                      @change="encounterSelect"
                      class="w-full rounded-lg border-gray-300 shadow-sm focus:border-purple-400 focus:ring focus:ring-purple-200"
                    >
                      <option value="">
                        {{
                          formTranslation.reports.plh_select +
                          " " +
                          formTranslation.patient_encounter.encounters +
                          " " +
                          formTranslation.common.id
                        }}
                      </option>
                      <option
                        v-for="(index, key) in withoutBillEncounterData"
                        :key="key"
                        :value="index.id"
                      >
                        {{
                          formTranslation.patient_encounter.encounters +
                          "#" +
                          index.id
                        }}
                      </option>
                    </select>
                    <p
                      v-if="generateBillSubmittedFalse"
                      class="mt-2 text-sm text-red-600"
                    >
                      {{
                        formTranslation.patient_encounter.encounters +
                        " " +
                        formTranslation.common.id +
                        " " +
                        formTranslation.common.required
                      }}
                    </p>
                  </div>
                  <button
                    @click="generateBill"
                    :disabled="generateBillLoader"
                    class="px-6 py-2 bg-black text-white rounded-lg hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                  >
                    <svg
                      v-if="generateBillLoader"
                      class="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        class="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        stroke-width="4"
                      ></circle>
                      <path
                        class="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    {{
                      generateBillLoader
                        ? formTranslation.common.loading
                        : formTranslation.patient_bill.generate_bill
                    }}
                  </button>
                </div>

                <!-- Consultation Details -->
                <div v-if="selected_encounter_id != ''" class="mt-6">
                  <div class="border-t border-gray-200 pt-6">
                    <h4
                      class="text-lg font-semibold text-gray-800 mb-4 text-center"
                    >
                      {{ formTranslation.patient_encounter.encounter_details }}
                    </h4>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div
                        v-if="
                          userData.addOns.kiviPro == true &&
                          (getUserRole() == 'administrator' ||
                            getUserRole() == 'doctor')
                        "
                        class="bg-gray-50 p-4 rounded-lg"
                      >
                        <p class="text-sm text-gray-500">
                          {{ formTranslation.clinic.clinic }}
                        </p>
                        <p class="font-medium text-gray-900">
                          {{ encounterData.clinic_name }}
                        </p>
                      </div>
                      <div
                        v-if="getUserRole() != 'doctor'"
                        class="bg-gray-50 p-4 rounded-lg"
                      >
                        <p class="text-sm text-gray-500">
                          {{ formTranslation.common.doctor }}
                        </p>
                        <p class="font-medium text-gray-900">
                          {{ encounterData.doctor_name }}
                        </p>
                      </div>
                      <div class="bg-gray-50 p-4 rounded-lg">
                        <p class="text-sm text-gray-500">
                          {{ formTranslation.common.patient }}
                        </p>
                        <p class="font-medium text-gray-900">
                          {{ encounterData.patient_name }}
                        </p>
                      </div>
                      <div class="bg-gray-50 p-4 rounded-lg">
                        <p class="text-sm text-gray-500">
                          {{ formTranslation.common.date }}
                        </p>
                        <p class="font-medium text-gray-900">
                          {{ encounterData.encounter_date }}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div v-else class="flex items-center justify-center py-12">
                <div class="text-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="mx-auto h-12 w-12 text-gray-400"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  <h3 class="mt-2 text-lg font-medium text-gray-900">
                    {{
                      formTranslation.patient_bill
                        .no_encounter_found_for_billing
                    }}
                  </h3>
                </div>
              </div>
            </div>
          </div>

          <!-- Modal Footer -->
          <div class="px-6 py-4 border-t bg-gray-50 flex justify-end gap-3">
            <button
              @click="billCreateModel = false"
              class="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              {{ formTranslation.common.cancel }}
            </button>
          </div>
        </div>
      </div>
    </template>

    <!-- Edit Bill Modal -->
    <template v-if="billEditDetailsModel">
      <div
        class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center"
      >
        <div
          class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] flex flex-col"
        >
          <!-- Modal Header -->
          <div class="flex items-center justify-between px-6 py-4 border-b">
            <h3 class="text-xl font-semibold text-gray-800">
              {{ formTranslation.patient_bill.invoice_detail }}
            </h3>
            <button
              @click="billEditDetailsModel = false"
              class="p-1 hover:bg-gray-100 rounded-full"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6 text-gray-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          <!-- Modal Content -->
          <div class="overflow-y-auto flex-grow">
            <div class="p-6">
              <bill-form
                v-if="getUserRole() !== 'patient'"
                :encounterId="encounterId"
                :checkOutVal="checkOutVal"
                @onBillSaved="handleBillSave"
                @onBillCancel="handleBillCancel"
                :appointmentData="encounterData"
                :clinic_extra="clinic_extra"
                :doctorId="doctorId"
              />
            </div>
          </div>
        </div>
      </div>
    </template>

    <!-- View Bill Modal -->
    <template v-if="billDetailsModel">
      <div
        class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center"
      >
        <div
          class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] flex flex-col"
        >
          <!-- Modal Header -->
          <div class="flex items-center justify-between px-6 py-4 border-b">
            <h3 class="text-xl font-semibold text-gray-800">
              {{ formTranslation.patient_bill.invoice_detail }}
            </h3>
            <button
              @click="billDetailsModel = false"
              class="p-1 hover:bg-gray-100 rounded-full"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6 text-gray-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          <!-- Modal Content -->
          <div class="overflow-y-auto flex-grow">
            <div class="p-6">
              <bill-details
                :encounterId="encounterId"
                :clinic_extra="clinic_extra"
                @onBillCancel="handleBillCancel"
              />
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import { post, get } from "../../config/request";
import BillDetails from "../../components/PatientBill/BillDetails";
import BillForm from "../../components/PatientBill/BillForm";
import ModalPopup from "../../components/Modal/Index";

export default {
  name: "BillingView",
  components: {
    BillDetails,
    BillForm,
    ModalPopup,
  },
  data: () => {
    return {
      pageLoader: true,
      billingList: {
        data: [],
        column: [],
      },
      selectedRows: [],
      allRowsSelected: false,
      serverParams: {
        columnFilters: {
          bill_id: "",
          doctor_name: "",
          clinic_name: "",
          patient_name: "",
          created_at: "",
          status: "",
        },
        sort: [
          {
            field: "",
            type: "",
          },
        ],
        page: 1,
        perPage: 10,
        searchTerm: "",
      },
      oldServerParams: {
        columnFilters: {},
        searchTerm: "",
        perPage: 10,
      },
      totalRows: 0,
      clinic_extra: {
        prefix: "",
        postfix: "",
      },
      billDetailsModel: false,
      billEditDetailsModel: false,
      encounterId: 0,
      checkOutVal: 0,
      encounterData: {},
      doctorId: 0,
      billCreateModel: false,
      selected_encounter_id: "",
      withoutBillEncounterData: [],
      generateBillSubmittedFalse: false,
      generateBillLoader: false,
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      if (this.kcCheckPermission("patient_bill_add")) {
        this.getWithoutBillEncounterList();
      }
      this.billingList = this.defaultbillingList();
      this.getBillingDataList();
    },
    defaultbillingList() {
      return {
        column: [
          {
            field: "bill_id",
            label: this.formTranslation.common.id,
            sortable: true,
          },
          {
            field: "doctor_name",
            label: "Doctor/Clinic",
            sortable: true,
          },
          {
            field: "patient_name",
            label: "Patient/Services",
            sortable: true,
          },
          {
            field: "amount",
            label: "Amount Details",
            sortable: true,
          },
          {
            field: "created_at",
            label: this.formTranslation.patient_encounter.dt_lbl_date,
            sortable: true,
          },
          {
            field: "status",
            label: this.formTranslation.common.status,
            sortable: true,
          },
          {
            field: "actions",
            label: this.formTranslation.patient_encounter.dt_lbl_action,
            sortable: false,
          },
        ],
        data: [],
      };
    },
    getWithoutBillEncounterList() {
      get("get_without_bill_encounter_list", {})
        .then((data) => {
          if (data.data.status !== undefined && data.data.status === true) {
            this.withoutBillEncounterData = data.data.data;
          } else {
            this.withoutBillEncounterData = [];
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    getBillingDataList() {
      this.pageLoader = true;

      let params = {
        ...this.serverParams,
        columnFilters: { ...this.serverParams.columnFilters },
      };

      // Format date range if exists
      if (
        params.columnFilters.created_at &&
        typeof params.columnFilters.created_at === "object"
      ) {
        params.columnFilters.created_at = {
          start: moment(params.columnFilters.created_at.start).format(
            "YYYY-MM-DD"
          ),
          end: moment(params.columnFilters.created_at.end).format("YYYY-MM-DD"),
        };
      }

      get("billing_record_list", params)
        .then((response) => {
          this.pageLoader = false;
          if (response.data.status === true) {
            this.billingList.data = response.data.data;
            this.totalRows = response.data.total_rows;

            if (response.data.clinic_extra) {
              this.clinic_extra = response.data.clinic_extra;
            }
          } else {
            this.billingList.data = [];
            this.totalRows = 0;
          }
        })
        .catch((error) => {
          this.pageLoader = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    updateParams(newProps) {
      this.serverParams = Object.assign({}, this.serverParams, newProps);
      this.getBillingDataList();
    },
    onPageChange(params) {
      this.updateParams({ page: params.currentPage });
    },
    onPerPageChange() {
      this.updateParams({
        page: 1,
      });
    },
    onSortChange(params) {
      this.updateParams({
        sort: params,
      });
    },
    globalFilter: _.debounce(function () {
      this.updateParams({
        page: 1,
      });
    }, 300),
    onColumnFilter: _.debounce(function () {
      // Remove empty filters
      let filters = {};
      Object.keys(this.serverParams.columnFilters).forEach((key) => {
        if (this.serverParams.columnFilters[key]) {
          filters[key] = this.serverParams.columnFilters[key];
        }
      });

      // Handle date range specially
      if (filters.created_at && typeof filters.created_at === "object") {
        filters.created_at = {
          start: moment(filters.created_at.start).format("YYYY-MM-DD"),
          end: moment(filters.created_at.end).format("YYYY-MM-DD"),
        };
      }

      this.updateParams({
        columnFilters: filters,
        page: 1,
      });
    }, 300),
    clearFilter(filterName) {
      this.serverParams.columnFilters[filterName] = "";
      this.onColumnFilter();
    },
    billModelOpen(data, type) {
      this.encounterId = data.id;
      this.encounterData = data;
      this.doctorId = data.doctor_id;
      if (data.appointment_id !== null && data.appointment_id !== "") {
        this.checkOutVal = 1;
      }
      if (type === "edit") {
        this.billEditDetailsModel = true;
      } else {
        this.billDetailsModel = true;
      }
    },
    handleBillSave(data) {
      this.billDetailsModel = false;
      this.billEditDetailsModel = false;
      this.encounterId = 0;
      this.encounterData = {};
      this.doctorId = 0;
      this.checkOutVal = 0;
      this.getBillingDataList();
      if (this.kcCheckPermission("patient_bill_add")) {
        this.getWithoutBillEncounterList();
      }
    },
    handleBillCancel() {
      this.billDetailsModel = false;
      this.billEditDetailsModel = false;
      this.encounterId = 0;
      this.encounterData = {};
      this.doctorId = 0;
      this.checkOutVal = 0;
    },
    handleSort(column) {
      if (!column.sortable) return;

      let sort = {
        field: column.field,
        type: "desc",
      };

      if (this.serverParams.sort.field === column.field) {
        sort.type = this.serverParams.sort.type === "desc" ? "asc" : "desc";
      }

      this.updateParams({
        sort: sort,
        page: 1,
      });
    },

    selectAllRows(event) {
      this.allRowsSelected = event.target.checked;
      if (this.allRowsSelected) {
        this.selectedRows = this.billingList.data.map((row) => row.id);
      } else {
        this.selectedRows = [];
      }
    },

    formatCurrency(value) {
      return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
      }).format(value);
    },

    formatDate(date) {
      return moment(date).format("MMMM D, YYYY");
    },
  },
  computed: {
    userData() {
      return this.$store.state.userDataModule.user;
    },
  },
};
</script>
