<?php

namespace App\Console\Commands;

use App\Models\BotPersona;
use App\Models\AutomatedPost;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class RobustBotManager extends Command
{
    protected $signature = 'bot:robust-manager {action=run}
                           {--force : Force execution even if conditions not met}
                           {--debug : Enable debug output}';
    
    protected $description = 'Robust bot manager ensuring 76 posts per day with failure recovery';

    public function handle()
    {
        $action = $this->argument('action');
        $debug = $this->option('debug');

        if ($debug) {
            $this->info("🔍 Debug mode enabled");
        }

        switch ($action) {
            case 'run':
                return $this->runRobustManager();
            case 'status':
                return $this->showDetailedStatus();
            case 'fix-failed':
                return $this->fixFailedPosts();
            case 'force-daily-quota':
                return $this->forceDailyQuota();
            default:
                $this->error("Unknown action: {$action}");
                return 1;
        }
    }

    private function runRobustManager()
    {
        $this->info("🚀 Starting Robust Bot Manager");
        
        // Step 1: Check system health
        $this->checkSystemHealth();
        
        // Step 2: Fix any failed posts
        $this->fixFailedPosts();
        
        // Step 3: Ensure daily quota is met
        $this->ensureDailyQuota();
        
        // Step 4: Process queue
        $this->processQueue();
        
        // Step 5: Publish ready posts
        $this->publishReadyPosts();
        
        // Step 6: Final status check
        $this->showBriefStatus();
        
        $this->info("✅ Robust Bot Manager completed");
        return 0;
    }

    private function checkSystemHealth()
    {
        $this->info("🔍 Checking system health...");
        
        // Check queue worker (without pgrep for shared hosting)
        $pidFile = storage_path('logs/queue-worker.pid');
        $queueRunning = false;
        
        if (file_exists($pidFile)) {
            $pid = trim(file_get_contents($pidFile));
            if ($pid && is_dir("/proc/$pid")) {
                $queueRunning = true;
                $this->info("✅ Queue worker running (PID: $pid)");
            } else {
                unlink($pidFile);
            }
        }
        
        if (!$queueRunning) {
            $this->warn("⚠️ No queue workers running - starting one");
            $process = shell_exec('nohup php artisan queue:work --daemon --tries=3 > /dev/null 2>&1 & echo $!');
            if ($process) {
                file_put_contents($pidFile, trim($process));
                $this->info("✅ Started queue worker (PID: " . trim($process) . ")");
            }
        }

        // Check database connection
        try {
            DB::connection()->getPdo();
            $this->info("✅ Database connection healthy");
        } catch (\Exception $e) {
            $this->error("❌ Database connection failed: " . $e->getMessage());
            return false;
        }

        // Check bot personas
        $activePersonas = BotPersona::where('is_active', true)->count();
        if ($activePersonas < 19) {
            $this->warn("⚠️ Only {$activePersonas} active personas (expected 19)");
        } else {
            $this->info("✅ All 19 personas active");
        }

        return true;
    }

    private function fixFailedPosts()
    {
        $this->info("🔧 Fixing failed posts...");
        
        $failedPosts = AutomatedPost::where('status', 'failed')
            ->where('retry_count', '<', 3)
            ->where('created_at', '>=', now()->subDays(2))
            ->get();

        if ($failedPosts->count() > 0) {
            $this->info("Found {$failedPosts->count()} failed posts to retry");
            
            foreach ($failedPosts as $post) {
                $post->status = 'draft';
                $post->retry_count += 1;
                $post->error_message = null;
                $post->save();
                
                $this->line("  ↻ Retrying post ID: {$post->id} (attempt {$post->retry_count})");
            }
        } else {
            $this->info("✅ No failed posts to fix");
        }

        // Delete old failed posts (older than 7 days)
        $deletedOldFailed = AutomatedPost::where('status', 'failed')
            ->where('created_at', '<', now()->subDays(7))
            ->delete();

        if ($deletedOldFailed > 0) {
            $this->info("🗑️ Deleted {$deletedOldFailed} old failed posts");
        }
    }

    private function ensureDailyQuota()
    {
        $this->info("📊 Ensuring daily quota (76 posts)...");
        
        $today = Carbon::today();
        $activePersonas = BotPersona::where('is_active', true)->get();
        
        $totalNeeded = $activePersonas->count() * 4; // 4 posts per persona
        $totalToday = AutomatedPost::whereDate('created_at', $today)
            ->whereIn('status', ['draft', 'generating_image', 'ready', 'posted', 'published'])
            ->count();

        $this->info("Today's progress: {$totalToday}/{$totalNeeded} posts");

        if ($totalToday < $totalNeeded) {
            $needed = $totalNeeded - $totalToday;
            $this->warn("⚠️ Need {$needed} more posts today");
            
            // Check posts per persona
            foreach ($activePersonas as $persona) {
                $personaToday = AutomatedPost::where('bot_persona_id', $persona->id)
                    ->whereDate('created_at', $today)
                    ->whereIn('status', ['draft', 'generating_image', 'ready', 'posted', 'published'])
                    ->count();

                $personaNeeded = 4 - $personaToday;
                
                if ($personaNeeded > 0) {
                    $this->line("  📝 {$persona->first_name} {$persona->last_name}: {$personaToday}/4 posts (need {$personaNeeded})");
                    
                    // Generate missing posts for this persona
                    for ($i = 0; $i < $personaNeeded; $i++) {
                        Artisan::call('bot:manage', [
                            'action' => 'generate-posts',
                            '--persona' => $persona->id,
                            '--force' => true
                        ]);
                    }
                }
            }
        } else {
            $this->info("✅ Daily quota met or exceeded");
        }
    }

    private function processQueue()
    {
        $this->info("⚙️ Processing queue jobs...");
        
        $queuedJobs = DB::table('jobs')->count();
        
        if ($queuedJobs > 0) {
            $this->info("Processing {$queuedJobs} queued jobs...");
            
            // Process jobs in batches
            $processed = 0;
            $maxProcess = min($queuedJobs, 10); // Process max 10 at a time
            
            for ($i = 0; $i < $maxProcess; $i++) {
                try {
                    Artisan::call('queue:work', ['--once' => true, '--timeout' => 60]);
                    $processed++;
                } catch (\Exception $e) {
                    $this->warn("⚠️ Queue processing error: " . $e->getMessage());
                    break;
                }
            }
            
            $this->info("✅ Processed {$processed} jobs");
        } else {
            $this->info("✅ No jobs in queue");
        }
    }

    private function publishReadyPosts()
    {
        $this->info("📤 Publishing ready posts...");
        
        $readyPosts = AutomatedPost::where('status', 'ready')->count();
        
        if ($readyPosts > 0) {
            $this->info("Publishing {$readyPosts} ready posts...");
            Artisan::call('bot:force-publish');
            $output = Artisan::output();
            $this->line($output);
        } else {
            $this->info("✅ No posts ready for publishing");
        }
    }

    private function showBriefStatus()
    {
        $today = Carbon::today();
        
        $todayStats = [
            'total' => AutomatedPost::whereDate('created_at', $today)->count(),
            'published' => AutomatedPost::whereDate('created_at', $today)->where('status', 'published')->count(),
            'ready' => AutomatedPost::where('status', 'ready')->count(),
            'processing' => AutomatedPost::whereIn('status', ['draft', 'generating_image'])->count(),
            'failed' => AutomatedPost::where('status', 'failed')->count(),
        ];

        $this->info("📊 Quick Status:");
        $this->line("  Today: {$todayStats['total']}/76 posts created");
        $this->line("  Published: {$todayStats['published']}");
        $this->line("  Ready: {$todayStats['ready']}");
        $this->line("  Processing: {$todayStats['processing']}");
        $this->line("  Failed: {$todayStats['failed']}");
    }

    private function showDetailedStatus()
    {
        $this->info("📊 DETAILED BOT SYSTEM STATUS");
        $this->info(str_repeat("=", 50));
        
        // Daily stats
        $today = Carbon::today();
        $yesterday = Carbon::yesterday();
        
        $todayTotal = AutomatedPost::whereDate('created_at', $today)->count();
        $todayPublished = AutomatedPost::whereDate('created_at', $today)->where('status', 'published')->count();
        $yesterdayTotal = AutomatedPost::whereDate('created_at', $yesterday)->count();
        
        $this->info("📅 DAILY PROGRESS:");
        $this->line("  Today: {$todayTotal}/76 created, {$todayPublished} published");
        $this->line("  Yesterday: {$yesterdayTotal}/76 created");
        
        // Per-persona breakdown
        $this->info("\n👥 PER-PERSONA STATUS:");
        $personas = BotPersona::where('is_active', true)->get();
        
        foreach ($personas as $persona) {
            $personaToday = AutomatedPost::where('bot_persona_id', $persona->id)
                ->whereDate('created_at', $today)
                ->count();
            
            $personaPublished = AutomatedPost::where('bot_persona_id', $persona->id)
                ->whereDate('created_at', $today)
                ->where('status', 'published')
                ->count();
                
            $status = $personaToday >= 4 ? "✅" : "⚠️";
            $this->line("  {$status} {$persona->first_name} {$persona->last_name}: {$personaToday}/4 ({$personaPublished} published)");
        }
        
        // System health
        $this->info("\n🔧 SYSTEM HEALTH:");
        $queueJobs = DB::table('jobs')->count();
        $failedJobs = DB::table('failed_jobs')->count();
        $readyPosts = AutomatedPost::where('status', 'ready')->count();
        $processingPosts = AutomatedPost::whereIn('status', ['draft', 'generating_image'])->count();
        
        $this->line("  Queue jobs: {$queueJobs}");
        $this->line("  Failed jobs: {$failedJobs}");
        $this->line("  Ready posts: {$readyPosts}");
        $this->line("  Processing posts: {$processingPosts}");
        
        return 0;
    }

    private function forceDailyQuota()
    {
        $this->info("🎯 FORCING DAILY QUOTA COMPLETION");
        
        $personas = BotPersona::where('is_active', true)->get();
        $today = Carbon::today();
        
        foreach ($personas as $persona) {
            $personaToday = AutomatedPost::where('bot_persona_id', $persona->id)
                ->whereDate('created_at', $today)
                ->whereIn('status', ['draft', 'generating_image', 'ready', 'posted', 'published'])
                ->count();

            $needed = 4 - $personaToday;
            
            if ($needed > 0) {
                $this->info("Generating {$needed} posts for {$persona->first_name} {$persona->last_name}");
                
                for ($i = 0; $i < $needed; $i++) {
                    Artisan::call('bot:manage', [
                        'action' => 'generate-posts',
                        '--persona' => $persona->id,
                        '--force' => true
                    ]);
                }
            }
        }
        
        $this->info("✅ Daily quota generation complete");
        return 0;
    }
}