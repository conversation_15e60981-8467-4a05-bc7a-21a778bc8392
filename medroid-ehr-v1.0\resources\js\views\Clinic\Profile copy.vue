<template>
  <div>
    <!-- Loading State -->
    <div
      v-if="formLoader"
      class="flex items-center justify-center min-h-screen"
    >
      <loader-component-2></loader-component-2>
    </div>

    <!-- Main Content -->
    <div
      v-if="formTranslation.common !== undefined && !formLoader"
      class="flex flex-col lg:flex-row gap-6"
    >
      <!-- Form Section -->
      <div class="lg:w-3/4">
        <form
          id="clinicDataForm"
          @submit.prevent="handleSubmit"
          :novalidate="true"
        >
          <div class="bg-white rounded-lg shadow">
            <!-- Header -->
            <div class="px-6 py-4 border-b border-gray-200">
              <h3 class="text-xl font-semibold">
                {{ formTranslation.doctor.edit_profile }}
              </h3>
            </div>

            <!-- Form Content -->
            <div class="p-6">
              <!-- Clinic Info Section -->
              <h6 class="text-sm font-semibold text-gray-500 mb-4">
                {{ formTranslation.clinic.clinic_info }}
              </h6>
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Clinic Name -->
                <div>
                  <label
                    for="name"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    {{ formTranslation.clinic.clinic_name }}
                    <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="name"
                    v-model="clinicData.name"
                    :class="{
                      'border-red-500': submitted && $v.clinicData.name.$error,
                    }"
                    class="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    :placeholder="formTranslation.clinic.plh_clinic_name"
                    type="text"
                  />
                  <p
                    v-if="submitted && !$v.clinicData.name.required"
                    class="mt-1 text-sm text-red-500"
                  >
                    {{ formTranslation.common.name_required }}
                  </p>
                </div>

                <!-- Email -->
                <div>
                  <label
                    for="email"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    {{ formTranslation.common.email_address }}
                    <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="email"
                    v-model="clinicData.email"
                    :class="{
                      'border-red-500': submitted && $v.clinicData.email.$error,
                    }"
                    class="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    :placeholder="formTranslation.clinic.plh_email"
                    type="email"
                  />
                  <p
                    v-if="submitted && !$v.clinicData.email.required"
                    class="mt-1 text-sm text-red-500"
                  >
                    {{ formTranslation.common.email_required }}
                  </p>
                  <p
                    v-else-if="submitted && !$v.clinicData.email.emailValidate"
                    class="mt-1 text-sm text-red-500"
                  >
                    {{ formTranslation.common.invalid_email }}
                  </p>
                </div>

                <!-- Phone Number -->
                <div>
                  <label
                    for="telephone_no"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    {{ formTranslation.common.contact_no }}
                    <span class="text-red-500">*</span>
                  </label>
                  <VuePhoneNumberInput
                    v-model="clinicData.telephone_no"
                    id="telephone_no"
                    clearable
                    :default-country-code="defaultCountryCode"
                    @update="contactUpdateHandaler"
                    no-example
                    class="phone-input"
                  />
                  <p
                    v-if="submitted && !$v.clinicData.telephone_no.required"
                    class="mt-1 text-sm text-red-500"
                  >
                    {{ formTranslation.common.contact_num_required }}
                  </p>
                </div>

                <!-- Specialties -->
                <div>
                  <label
                    for="specialties"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    {{ formTranslation.clinic.specialities }}
                    <span class="text-red-500">*</span>
                  </label>
                  <div class="relative">
                    <multi-select
                      v-model="clinicData.specialties"
                      id="specialties"
                      :options="formattedSpecialization"
                      :multiple="true"
                      :loading="specializationMultiselectLoader"
                      @tag="addNewSpecialization"
                      :taggable="true"
                      label="label"
                      track-by="label"
                      class="multiselect-custom"
                    />
                    <button
                      type="button"
                      class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                      @click="clinicData.specialties = ''"
                    >
                      ×
                    </button>
                  </div>
                  <span class="text-sm text-blue-600 mt-1">{{
                    formTranslation.clinic.note_specialization
                  }}</span>
                  <p
                    v-if="submitted && !$v.clinicData.specialties.required"
                    class="mt-1 text-sm text-red-500"
                  >
                    {{ formTranslation.clinic.clinic_specialities_required }}
                  </p>
                </div>
              </div>

              <!-- Additional sections like Address, Admin Details, etc. follow the same pattern -->
              <!-- Contact Information Section -->
              <hr class="my-6 border-gray-200" />
              <h6 class="text-sm font-semibold text-gray-500 mb-4">
                {{ formTranslation.common.contact_info }}
              </h6>
              <div class="space-y-6">
                <!-- Address -->
                <div>
                  <label
                    for="address"
                    class="block text-sm font-medium text-gray-700"
                  >
                    {{ formTranslation.common.address }}
                    <span class="text-red-500">*</span>
                  </label>
                  <textarea
                    id="address"
                    v-model="clinicData.address"
                    rows="3"
                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    :placeholder="formTranslation.clinic.plh_address"
                  ></textarea>
                  <p
                    v-if="submitted && !$v.clinicData.address.required"
                    class="mt-1 text-sm text-red-500"
                  >
                    {{ formTranslation.common.address_required }}
                  </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <!-- Country -->
                  <div>
                    <label
                      for="country"
                      class="block text-sm font-medium text-gray-700"
                    >
                      {{ formTranslation.common.country }}
                      <span class="text-red-500">*</span>
                    </label>
                    <input
                      id="country"
                      v-model="clinicData.country"
                      type="text"
                      :placeholder="formTranslation.clinic.plh_country"
                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      :class="{
                        'border-red-500':
                          submitted && $v.clinicData.country.$error,
                      }"
                    />
                    <p
                      v-if="submitted && !$v.clinicData.country.required"
                      class="mt-1 text-sm text-red-500"
                    >
                      {{ formTranslation.common.country_required }}
                    </p>
                  </div>

                  <!-- City -->
                  <div>
                    <label
                      for="city"
                      class="block text-sm font-medium text-gray-700"
                    >
                      {{ formTranslation.common.city }}
                      <span class="text-red-500">*</span>
                    </label>
                    <input
                      id="city"
                      v-model="clinicData.city"
                      type="text"
                      :placeholder="formTranslation.clinic.plh_city"
                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      :class="{
                        'border-red-500':
                          submitted && $v.clinicData.city.$error,
                      }"
                    />
                    <p
                      v-if="submitted && !$v.clinicData.city.required"
                      class="mt-1 text-sm text-red-500"
                    >
                      {{ formTranslation.common.city_required }}
                    </p>
                  </div>

                  <!-- Postal Code -->
                  <div>
                    <label
                      for="postal_code"
                      class="block text-sm font-medium text-gray-700"
                    >
                      {{ formTranslation.common.postal_code }}
                      <span class="text-red-500">*</span>
                    </label>
                    <input
                      id="postal_code"
                      v-model="clinicData.postal_code"
                      type="text"
                      :placeholder="formTranslation.clinic.plh_pcode"
                      class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      :class="{
                        'border-red-500':
                          submitted && $v.clinicData.postal_code.$error,
                      }"
                    />
                    <p
                      v-if="submitted && !$v.clinicData.postal_code.required"
                      class="mt-1 text-sm text-red-500"
                    >
                      {{ formTranslation.common.postal_code_required }}
                    </p>
                  </div>
                </div>
              </div>

              <!-- Clinic Admin Details Section -->
              <hr class="my-6 border-gray-200" />
              <h6 class="text-sm font-semibold text-gray-500 mb-4">
                {{ formTranslation.clinic.clinic_admin_detail }}
              </h6>
              <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div class="lg:col-span-2">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- First Name -->
                    <div>
                      <label
                        for="first_name"
                        class="block text-sm font-medium text-gray-700"
                      >
                        {{ formTranslation.common.fname }}
                        <span class="text-red-500">*</span>
                      </label>
                      <input
                        id="first_name"
                        v-model="clinicData.first_name"
                        type="text"
                        :placeholder="formTranslation.clinic.fname_plh"
                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        :class="{
                          'border-red-500':
                            submitted && $v.clinicData.first_name.$error,
                        }"
                      />
                      <p
                        v-if="submitted && !$v.clinicData.first_name.required"
                        class="mt-1 text-sm text-red-500"
                      >
                        {{ formTranslation.common.fname_required }}
                      </p>
                    </div>

                    <!-- Last Name -->
                    <div>
                      <label
                        for="last_name"
                        class="block text-sm font-medium text-gray-700"
                      >
                        {{ formTranslation.common.lname }}
                        <span class="text-red-500">*</span>
                      </label>
                      <input
                        id="last_name"
                        v-model="clinicData.last_name"
                        type="text"
                        :placeholder="formTranslation.receptionist.lname_plh"
                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        :class="{
                          'border-red-500':
                            submitted && $v.clinicData.last_name.$error,
                        }"
                      />
                      <p
                        v-if="submitted && !$v.clinicData.last_name.required"
                        class="mt-1 text-sm text-red-500"
                      >
                        {{ formTranslation.common.lname_required }}
                      </p>
                    </div>

                    <!-- Admin Email -->
                    <div>
                      <label
                        for="user_email"
                        class="block text-sm font-medium text-gray-700"
                      >
                        {{ formTranslation.common.email }}
                        <span class="text-red-500">*</span>
                      </label>
                      <input
                        id="user_email"
                        v-model="clinicData.user_email"
                        type="email"
                        :placeholder="formTranslation.clinic.email_plh"
                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        :class="{
                          'border-red-500':
                            submitted && $v.clinicData.user_email.$error,
                        }"
                      />
                      <p
                        v-if="submitted && !$v.clinicData.user_email.required"
                        class="mt-1 text-sm text-red-500"
                      >
                        {{ formTranslation.common.email_required }}
                      </p>
                    </div>

                    <!-- Admin Phone -->
                    <div>
                      <label
                        for="mobile_number"
                        class="block text-sm font-medium text-gray-700"
                      >
                        {{ formTranslation.common.contact_no }}
                        <span class="text-red-500">*</span>
                      </label>
                      <VuePhoneNumberInput
                        v-model="clinicData.mobile_number"
                        id="mobile_number"
                        clearable
                        :default-country-code="defaultCountryCode_admin"
                        @update="contactUpdateHandaler_admin"
                        class="phone-input"
                        no-example
                      />
                      <p
                        v-if="
                          submitted && !$v.clinicData.mobile_number.required
                        "
                        class="mt-1 text-sm text-red-500"
                      >
                        {{ formTranslation.common.contact_num_required }}
                      </p>
                    </div>

                    <!-- Date of Birth -->
                    <div>
                      <label
                        for="dob"
                        class="block text-sm font-medium text-gray-700"
                      >
                        {{ formTranslation.common.dob }}
                      </label>
                      <input
                        type="date"
                        id="doc_birthdate"
                        v-model="clinicData.dob"
                        :max="new Date().toISOString().slice(0, 10)"
                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <!-- Gender -->
                    <div>
                      <label
                        class="block text-sm font-medium text-gray-700 mb-2"
                      >
                        {{ formTranslation.common.gender }}
                        <span class="text-red-500">*</span>
                      </label>
                      <div class="space-y-2">
                        <label class="inline-flex items-center mr-4">
                          <input
                            type="radio"
                            v-model="clinicData.gender"
                            value="male"
                            class="form-radio text-blue-600"
                          />
                          <span class="ml-2">{{
                            formTranslation.common.male
                          }}</span>
                        </label>
                        <label class="inline-flex items-center mr-4">
                          <input
                            type="radio"
                            v-model="clinicData.gender"
                            value="female"
                            class="form-radio text-blue-600"
                          />
                          <span class="ml-2">{{
                            formTranslation.common.female
                          }}</span>
                        </label>
                        <label
                          v-if="defaultUserRegistrationFormSettingData === 'on'"
                          class="inline-flex items-center"
                        >
                          <input
                            type="radio"
                            v-model="clinicData.gender"
                            value="other"
                            class="form-radio text-blue-600"
                          />
                          <span class="ml-2">{{
                            formTranslation.common.other
                          }}</span>
                        </label>
                      </div>
                      <p
                        v-if="submitted && !$v.clinicData.gender.required"
                        class="mt-1 text-sm text-red-500"
                      >
                        {{ formTranslation.common.gender_required }}
                      </p>
                    </div>
                  </div>
                </div>

                <!-- Admin Profile Image -->
                <div class="lg:col-span-1">
                  <div class="flex justify-center">
                    <div class="relative">
                      <div
                        class="w-32 h-32 rounded-full bg-cover bg-center border-4 border-white shadow"
                        :style="'background-image: url(' + adminPreview + ');'"
                      ></div>
                      <button
                        @click="uploadAdmin"
                        class="absolute bottom-0 right-0 bg-white rounded-full p-2 shadow-lg hover:bg-gray-50"
                      >
                        <i class="fas fa-pencil-alt"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Submit Button -->
              <div class="mt-6 flex justify-end">
                <button
                  type="submit"
                  class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  :disabled="loading"
                >
                  <template v-if="!loading">
                    <i class="fa fa-save mr-2"></i>
                    {{ formTranslation.clinic.save_btn }}
                  </template>
                  <template v-else>
                    <i class="fa fa-sync fa-spin mr-2"></i>
                    {{ formTranslation.common.loading }}
                  </template>
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>

      <!-- Profile Card -->
      <div class="lg:w-1/4">
        <div class="bg-white rounded-lg shadow">
          <!-- Profile Image Upload -->
          <div class="flex justify-center pt-6">
            <div class="relative">
              <!-- Image Preview -->
              <div
                class="w-32 h-32 rounded-full bg-cover bg-center"
                :style="'background-image: url(' + profileImage + ');'"
              ></div>

              <!-- Edit Button -->
              <button
                @click="uploadProfile"
                class="absolute bottom-0 right-0 bg-white rounded-full p-2 shadow-lg hover:bg-gray-50"
              >
                <i class="fas fa-pencil-alt"></i>
              </button>
            </div>
          </div>

          <!-- Profile Info -->
          <div class="p-6 text-center">
            <h5 class="text-xl font-semibold">{{ clinicData.name }}</h5>
            <div class="text-gray-600 mt-2">{{ clinicData.email }}</div>
            <div class="mt-4" v-if="clinicData.address">
              {{ clinicData.address }}
            </div>
            <div class="mt-2 text-gray-600">
              {{ clinicData.specialties | clinicSpecialityFormat }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import VuePhoneNumberInput from "vue-phone-number-input";
import "vue-phone-number-input/dist/vue-phone-number-input.css";
import { maxLength, minLength, required } from "vuelidate/lib/validators";
import { post, get } from "../../config/request";
import {
  alphaSpace,
  maxTime,
  minTime,
  objToTime,
  phoneNumber,
  postalCode,
  validateForm,
  validateTimeSlot,
  emailValidate,
} from "../../config/helper";

export default {
  components: {
    VuePhoneNumberInput,
  },
  data: () => {
    return {
      cardTitle: "Edit clinic profile",
      clinicData: {},
      loading: false,
      submitted: false,
      editProfileText: '<i class="fa fa-pen-fancy"></i> Edit Profile',
      buttonText: '<i class="fa fa-plus"></i> Add',
      profileImage: "",
      adminPreview:
        window.request_data.kiviCarePluginURL + "assets/images/kc-demo-img.png",
      formLoader: true,
      defaultCountryCode: null,
      defaultCountryCode_admin: null,
      defaultUserRegistrationFormSettingData: "on",
      subscription: {
        membership_level: "",
        status: "",
        created: "",
        next_payment_date: "",
        fee: "",
      },
    };
  },
  validations: {
    clinicData: {
      name: {
        required,
      },
      email: {
        required,
        emailValidate,
      },
      telephone_no: {
        required,
        // phoneNumber,
        minLength: minLength(4),
        maxLength: maxLength(15),
      },
      address: {
        required,
      },
      first_name: {
        required,
      },
      last_name: {
        required,
      },
      mobile_number: {
        required,
        // phoneNumber,
        minLength: minLength(4),
        maxLength: maxLength(15),
      },
      user_email: {
        required,
        emailValidate,
      },
      // dob: {required},
      specialties: {
        required,
      },
      gender: {
        required,
      },
      city: {
        required,
        alphaSpace,
        maxLength: maxLength(30),
      },
      country: {
        required,
        alphaSpace,
        maxLength: maxLength(30),
      },
      postal_code: {
        required,
        postalCode,
        maxLength: maxLength(12),
      },
      status: { required },
    },
  },
  mounted() {
    this.getCountryCodeData();
    this.getUserRegistrationFormData();
    this.state = this.defaultClinicData();
    this.profileImage =
      window.request_data.kiviCarePluginURL + "assets/images/kc-demo-img.png";
    this.init();
  },
  filters: {
    clinicSpecialityFormat: function (Speciality) {
      let result = [];
      let typeOfData = typeof Speciality;
      if (
        (typeOfData === "object" || typeOfData === "array") &&
        Speciality !== undefined &&
        Speciality !== null &&
        Speciality.length > 0
      ) {
        let i = 0;
        Speciality.map((speciality) => {
          result.push(speciality.label);
        });
        return result.join(" ,");
      } else {
        return "-";
      }
    },
  },
  methods: {
    contactUpdateHandaler: function (val) {
      this.clinicData.country_code = val.countryCode;
      this.clinicData.country_calling_code = val.countryCallingCode;
    },
    contactUpdateHandaler_admin: function (val) {
      this.clinicData.country_code_admin = val.countryCode;
      this.clinicData.country_calling_code_admin = val.countryCallingCode;
    },
    init: function () {
      this.editProfile();
    },
    cancleSubscriptionModel() {
      this.$swal
        .fire({
          title: "Are you sure you want to cancel?",
          text: "You'll still have access until the end of your current billing period",
          icon: "warning",
          showCancelButton: true,
          confirmButtonColor: "#d33",
          cancelButtonColor: "#3085d6",
          confirmButtonText: "Yes, Cancel Subscription",
          cancelButtonText: "No, Keep Subscription",
        })
        .then((result) => {
          if (result.isConfirmed) {
            // Show loading state
            this.$swal.fire({
              title: "Cancelling...",
              html: "Please wait while we process your request",
              allowOutsideClick: false,
              didOpen: () => {
                this.$swal.showLoading();
              },
            });

            // Make API call
            get("cancel_subscription", {})
              .then((response) => {
                if (response.data.success) {
                  // Success scenario
                  this.$swal.fire({
                    icon: "success",
                    title: "Cancelled",
                    text: "Your subscription has been cancelled successfully",
                    showConfirmButton: false,
                    timer: 2000,
                  });

                  // Optional: Update UI to reflect cancelled status
                  // For example, update status badge
                  this.$nextTick(() => {
                    const statusBadge = document.querySelector(".status-badge");
                    if (statusBadge) {
                      statusBadge.classList.remove("badge-success");
                      statusBadge.classList.add("badge-danger");
                      statusBadge.textContent = "Cancelled";
                    }
                  });
                } else {
                  // Handle API error
                  this.$swal.fire({
                    icon: "error",
                    title: "Error",
                    text:
                      response.data.message || "Failed to cancel subscription",
                    showConfirmButton: true,
                  });
                }
              })
              .catch((error) => {
                // Handle network/other errors
                this.$swal.fire({
                  icon: "error",
                  title: "Error",
                  text: "Something went wrong. Please try again.",
                  showConfirmButton: true,
                });
              });
          }
        });
    },
    uploadProfile() {
      let _this = this;

      var custom_uploader = kivicareCustomImageUploader(this.formTranslation);

      custom_uploader.on("select", function () {
        var attachment = custom_uploader
          .state()
          .get("selection")
          .first()
          .toJSON();
        _this.profileImage = attachment.url;
        _this.clinicData.clinic_profile = attachment.id;
      });

      //Open the uploader dialog
      custom_uploader.open();
    },
    uploadAdmin() {
      let _this = this;

      var custom_uploader = kivicareCustomImageUploader(this.formTranslation);

      custom_uploader.on("select", function () {
        var attachment = custom_uploader
          .state()
          .get("selection")
          .first()
          .toJSON();
        _this.adminPreview = attachment.url;
        _this.clinicData.profile_image = attachment.id;
      });

      //Open the uploader dialog
      custom_uploader.open();
    },
    handleSubmit: function () {
      this.loading = true;

      this.submitted = true;

      // stop here if form is invalid
      this.$v.$touch();
      this.$nextTick(() => {
        if (
          document.querySelector(".is-invalid") !== null &&
          document.querySelector(".is-invalid") !== undefined
        ) {
          document
            .querySelector(".is-invalid")
            .scrollIntoView({ block: "center", behavior: "smooth" });
        } else if (
          document.querySelector(".invalid-feedback") !== null &&
          document.querySelector(".invalid-feedback") !== undefined
        ) {
          document
            .querySelector(".invalid-feedback")
            .scrollIntoView({ block: "center", behavior: "smooth" });
        }
      });
      if (this.$v.clinicData.$invalid) {
        this.loading = false;
        return;
      }

      if (validateForm("clinicDataForm")) {
        post("clinic_save", this.clinicData)
          .then((response) => {
            this.loading = false;
            if (
              response.data.status !== undefined &&
              response.data.status === true
            ) {
              displayMessage(response.data.message);
              if (response.data.choose_language_updated) {
                this.$store.dispatch(
                  "staticDataModule/refreshDashboardLocale",
                  { self: this }
                );
              }
              this.$store.dispatch("fetchAllClinic", { self: this });
            } else {
              displayErrorMessage(response.data.message);
            }
          })
          .catch((error) => {
            console.log(error);
            this.loading = false;
            displayErrorMessage(
              this.formTranslation.common.internal_server_error
            );
          });
      }
    },
    defaultClinicData: function () {
      return {
        id: "",
        name: "",
        email: "",
        country_code: "",
        country_calling_code: "",
        telephone_no: "",
        address: "",
        city: "",
        country: "",
        postal_code: "",
        currency_prefix: "$",
        currency_postfix: "",
        // decimal_point: {},
        status: 1,
        specialties: [],
        profile_image: "",
        country_code_admin: "",
        country_calling_code_admin: "",
        choose_language: "",
      };
    },
    editProfile: function () {
      this.formLoader = true;
      this.cardTitle = this.formTranslation.clinic.edit_clinic_Profile;
      this.buttonText =
        '<i class="fa fa-save"></i> ' + this.formTranslation.common.save;
      get("clinic_edit", {})
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.clinicData = response.data.data;
            if (
              response.data.data.country_calling_code !== "" &&
              response.data.data.country_calling_code !== undefined
            ) {
              this.defaultCountryCode = response.data.data.country_code;
            }
            if (
              response.data.data.country_calling_code_admin !== "" &&
              response.data.data.country_calling_code_admin !== undefined
            ) {
              this.defaultCountryCode_admin =
                response.data.data.country_code_admin;
            }
            if (this.clinicData.profile_image) {
              this.adminPreview = this.clinicData.profile_image;
            }
            if (this.clinicData.clinic_profile) {
              this.profileImage = this.clinicData.clinic_profile;
            }
            this.formLoader = false;
            this.clinicData.choose_language =
              this.kc_available_translations.find(
                (el) => el.lang === response.data.data.choose_language
              );
          }
        })
        .catch((error) => {
          this.formLoader = false;
          console.log(error);
          displayErrorMessage(this.formTranslation.clinic.plh_record_not_found);
        });
    },
    addNewSpecialization: function (value) {
      let specialitiesObj = {
        label: value,
        type: "specialization",
        value: value.replace(" ", "_"),
        status: 1,
      };
      let _this = this;
      post("static_data_save", specialitiesObj)
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            _this.clinicData.specialties.push({
              id: response.data.insert_id,
              label: value,
            });
            _this.$store.commit("staticDataModule/ADD_OPTION_STATIC_DATA", {
              dataType: "specialization",
              option: { id: response.data.insert_id, label: value },
            });
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    getCountryCodeData: function () {
      get("get_country_code_settings_data", {})
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.defaultCountryCode = response.data.data.country_code;
            this.defaultCountryCode_admin = response.data.data.country_code;
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    getUserRegistrationFormData: function () {
      get("get_user_registration_form_settings_data", {})
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.defaultUserRegistrationFormSettingData =
              response.data.data.userRegistrationFormSettingData;
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
  },
  computed: {
    specialization() {
      return this.$store.state.staticDataModule.static_data.specialization;
    },
    specializationMultiselectLoader() {
      return this.$store.state.staticDataModule.static_data_loader;
    },
    kc_available_translations() {
      return this.$store.state.userDataModule.user.kc_available_translations;
    },
  },
  watch: {},
};
</script>
<style scoped>
[type="date"] {
  background: #fff
    url(https://cdn1.iconfinder.com/data/icons/cc_mono_icon_set/blacks/16x16/calendar_2.png)
    97% 50% no-repeat;
}
[type="date"]::-webkit-inner-spin-button {
  display: none;
}
[type="date"]::-webkit-calendar-picker-indicator {
  opacity: 0;
}
label {
  display: block;
}
#doc_birthdate {
  border: 1px solid #c4c4c4;
  border-radius: 5px;
  background-color: #fff;
  padding: 3px 5px;
  box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.1);
  /* width: 190px; */
  width: 100%;
  height: 45px;
  color: #8c9cad;
}
#doc_birthdate ::placeholder {
  color: #8c9cad;
}
</style>
