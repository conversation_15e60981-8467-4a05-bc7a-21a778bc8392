<template>
  <div
    class="bg-white text-card-foreground rounded-xl border shadow overflow-hidden hover:shadow-md transition-shadow mb-4"
  >
    <div class="p-6">
      <div class="p-4">
        <!-- Header Section -->
        <div
          class="flex flex-col md:flex-row md:items-center md:justify-between"
        >
          <div class="flex items-start space-x-4">
            <div
              class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="w-6 h-6 text-purple-600"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path
                  d="m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5"
                ></path>
                <rect x="2" y="6" width="14" height="12" rx="2"></rect>
              </svg>
            </div>
            <div>
              <h3 class="font-semibold text-lg">
                Prescription #{{ prescriptions[0].encounter_id }}
              </h3>
              <div class="flex items-center space-x-2 mt-1">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="w-4 h-4 text-gray-400"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path d="M8 2v4"></path>
                  <path d="M16 2v4"></path>
                  <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                  <path d="M3 10h18"></path>
                </svg>
                <span class="text-sm text-gray-600">
                  {{ formatDate(prescriptions[0].created_at) }}
                </span>
              </div>
            </div>
          </div>
          <div class="mt-4 md:mt-0 flex items-center space-x-3">
            <button
              @click="toggleDetails"
              class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              {{ showDetails ? "Hide Details" : "View Details" }}
            </button>
          </div>
        </div>

        <!-- Details Section -->
        <div v-if="showDetails" class="mt-4 pt-4 border-t">
          <div class="grid grid-cols-1 md:grid-cols-1 gap-2">
            <!-- Prescriptions -->
            <div>
              <h4 class="font-semibold mb-2">Prescribed Medications</h4>
              <div
                v-for="prescription in prescriptions"
                :key="prescription.id"
                class="bg-gray-50 rounded-lg p-4 mb-3 shadow-sm hover:shadow-md transition-all duration-300 ease-in-out"
              >
                <div class="flex justify-between items-center mb-3">
                  <h4 class="text-lg font-semibold text-gray-800">
                    {{ prescription.name?.label || "Unknown Medication" }}
                  </h4>
                  <div
                    class="text-sm text-gray-500 flex items-center space-x-2"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5 text-purple-500"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path
                        d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"
                      ></path>
                    </svg>
                    <span>Prescription</span>
                  </div>
                </div>

                <div class="grid grid-cols-2 gap-3 text-sm text-gray-600">
                  <div
                    v-if="prescription.dose"
                    class="flex items-center space-x-2"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-4 w-4 text-blue-500"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path
                        d="M10 13v5a3 3 0 0 0 3 3v0a3 3 0 0 0 3-3v-5h2v-3h-2v-2a3 3 0 0 0-3-3v0a3 3 0 0 0-3 3v2H8v3h2z"
                      ></path>
                      <path d="M16 8V5a2 2 0 0 0-2-2h0a2 2 0 0 0-2 2v3"></path>
                    </svg>
                    <div>
                      <span class="font-medium">Dose</span>
                      <p class="text-xs text-gray-500">
                        {{ prescription.dose.trim() }}
                      </p>
                    </div>
                  </div>

                  <div
                    v-if="prescription.route"
                    class="flex items-center space-x-2"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-4 w-4 text-green-500"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
                      <path d="M2 17l10 5 10-5"></path>
                      <path d="M2 12l10 5 10-5"></path>
                    </svg>
                    <div>
                      <span class="font-medium">Route</span>
                      <p class="text-xs text-gray-500">
                        {{ prescription.route }}
                      </p>
                    </div>
                  </div>

                  <div
                    v-if="prescription.frequency"
                    class="flex items-center space-x-2"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-4 w-4 text-red-500"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <circle cx="12" cy="12" r="10"></circle>
                      <polyline points="12 6 12 12 16 14"></polyline>
                    </svg>
                    <div>
                      <span class="font-medium">Frequency</span>
                      <p class="text-xs text-gray-500">
                        {{ prescription.frequency }}
                      </p>
                    </div>
                  </div>

                  <div
                    v-if="prescription.duration"
                    class="flex items-center space-x-2"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-4 w-4 text-yellow-500"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <rect
                        x="3"
                        y="4"
                        width="18"
                        height="18"
                        rx="2"
                        ry="2"
                      ></rect>
                      <line x1="16" y1="2" x2="16" y2="6"></line>
                      <line x1="8" y1="2" x2="8" y2="6"></line>
                      <line x1="3" y1="10" x2="21" y2="10"></line>
                    </svg>
                    <div>
                      <span class="font-medium">Duration</span>
                      <p class="text-xs text-gray-500">
                        {{ prescription.duration }} days
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "DashboardPrescription",
  props: {
    prescriptionGroup: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      showDetails: false,
    };
  },
  computed: {
    prescriptions() {
      return this.prescriptionGroup;
    },
  },
  methods: {
    formatDate(dateString) {
      if (!dateString) return "N/A";
      const date = new Date(dateString);
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    },
    toggleDetails() {
      this.showDetails = !this.showDetails;
    },
  },
};
</script>
