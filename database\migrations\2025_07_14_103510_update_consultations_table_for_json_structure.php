<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('consultations', function (Blueprint $table) {
            // Add new JSON columns
            $table->json('main_tabs')->nullable()->after('vital_signs');
            $table->json('additional_tabs')->nullable()->after('main_tabs');

            // Drop old individual text columns (if they exist)
            $columns_to_drop = [
                'chief_complaint',
                'history_of_present_illness',
                'past_medical_history',
                'medications',
                'allergies',
                'social_history',
                'family_history',
                'review_of_systems',
                'physical_examination',
                'assessment',
                'diagnosis',
                'differential_diagnosis',
                'treatment_plan',
                'follow_up_instructions',
                'patient_education',
                'additional_notes',
                'attachments'
            ];

            foreach ($columns_to_drop as $column) {
                if (Schema::hasColumn('consultations', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('consultations', function (Blueprint $table) {
            // Drop JSON columns
            $table->dropColumn(['main_tabs', 'additional_tabs']);

            // Re-add old columns
            $table->text('chief_complaint')->nullable();
            $table->text('history_of_present_illness')->nullable();
            $table->text('past_medical_history')->nullable();
            $table->text('medications')->nullable();
            $table->text('allergies')->nullable();
            $table->text('social_history')->nullable();
            $table->text('family_history')->nullable();
            $table->text('review_of_systems')->nullable();
            $table->text('physical_examination')->nullable();
            $table->text('assessment')->nullable();
            $table->text('diagnosis')->nullable();
            $table->text('differential_diagnosis')->nullable();
            $table->text('treatment_plan')->nullable();
            $table->text('follow_up_instructions')->nullable();
            $table->text('patient_education')->nullable();
            $table->text('additional_notes')->nullable();
            $table->json('attachments')->nullable();
        });
    }
};
