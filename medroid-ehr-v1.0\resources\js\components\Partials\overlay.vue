<template>
  <div class="text-center">
    <a :href="selected_addon.href" target="_blank">
      <i class="fas fa-lock text-primary" style="font-size: 30px;"></i>
      <p id="cancel-label" v-html="kivicareProFeatureIcon(addon_type, selected_addon.message)"> </p>
    </a>
  </div>
</template>

<script>
import { AddonsList, DefaultAddons } from '../../const/addons';
export default {
  props: {
    addon_type: {
      type: [String],
      default() {
        return 'pro';
      }
    }
  },
  name: "overlay",
  data: () => {
    return {
      selected_addon: DefaultAddons
    }
  },
  mounted() {
    this.selected_addon = AddonsList?.[this.addon_type] ? AddonsList[this.addon_type] : this.selected_addon;
  }
}
</script>