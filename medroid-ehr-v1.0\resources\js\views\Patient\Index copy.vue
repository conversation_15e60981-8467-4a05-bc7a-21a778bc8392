<template>
  <div>
    <div class="min-h-screen p-6 bg-gradient-to-br from-pink-50 to-purple-50">
      <div class="mb-8 flex justify-between items-center">
        <div class="flex items-center gap-4">
          <button
            class="flex items-center gap-2 px-4 py-2 bg-black text-white rounded-lg shadow-sm hover:bg-gray-800"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="lucide lucide-arrow-left w-4 h-4"
            >
              <path d="m12 19-7-7 7-7"></path>
              <path d="M19 12H5"></path></svg
            ><span>Back</span>
          </button>
          <h1 class="text-2xl font-semibold text-gray-800">
            Patient Episodes List
          </h1>
        </div>
        <button
          class="px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 flex items-center gap-2"
        >
          <span>Add Episode</span>
        </button>
      </div>
      <div class="relative mb-6">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="lucide lucide-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"
        >
          <circle cx="11" cy="11" r="8"></circle>
          <path d="m21 21-4.3-4.3"></path></svg
        ><input
          placeholder="Search episode data by id, doctor, clinic, patient, date and status(0 or 1)"
          class="w-full pl-10 pr-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
          type="text"
          value=""
        />
      </div>
      <div class="grid grid-cols-6 gap-4 mb-6">
        <input
          placeholder="ID"
          class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
          type="text"
          value=""
        /><input
          placeholder="Filter Episode by doctor"
          class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
          type="text"
          value=""
        /><input
          placeholder="Filter Episode by clinic"
          class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
          type="text"
          value=""
        /><input
          placeholder="Filter Episode by patient"
          class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
          type="text"
          value=""
        /><input
          class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
          type="date"
          value=""
        /><select
          class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
        >
          <option value="">Filter by status</option>
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
        </select>
      </div>
      <div
        class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-x-auto"
      >
        <table class="w-full">
          <thead class="bg-gray-50">
            <tr>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                <input class="rounded border-gray-300" type="checkbox" />
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                ID
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Doctor Name
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Clinic Name
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Patient Name
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Date
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Status
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Action
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200">
            <tr class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <input class="rounded border-gray-300" type="checkbox" />
              </td>
              <td class="px-6 py-4 whitespace-nowrap">3</td>
              <td class="px-6 py-4 whitespace-nowrap">Anupam Doctor doctor</td>
              <td class="px-6 py-4 whitespace-nowrap">Anupam Majety</td>
              <td class="px-6 py-4 whitespace-nowrap">Anupam Patient</td>
              <td class="px-6 py-4 whitespace-nowrap">November 19, 2024</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800"
                  >ACTIVE</span
                >
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex gap-2">
                  <button class="p-1 hover:bg-gray-100 rounded">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      class="lucide lucide-pen w-4 h-4 text-gray-600"
                    >
                      <path
                        d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"
                      ></path>
                    </svg></button
                  ><button class="p-1 hover:bg-gray-100 rounded">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      class="lucide lucide-file-text w-4 h-4 text-gray-600"
                    >
                      <path
                        d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"
                      ></path>
                      <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                      <path d="M10 9H8"></path>
                      <path d="M16 13H8"></path>
                      <path d="M16 17H8"></path>
                    </svg></button
                  ><button class="p-1 hover:bg-gray-100 rounded">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      class="lucide lucide-trash2 w-4 h-4 text-red-500"
                    >
                      <path d="M3 6h18"></path>
                      <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                      <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                      <line x1="10" x2="10" y1="11" y2="17"></line>
                      <line x1="14" x2="14" y1="11" y2="17"></line>
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
            <tr class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <input class="rounded border-gray-300" type="checkbox" />
              </td>
              <td class="px-6 py-4 whitespace-nowrap">2</td>
              <td class="px-6 py-4 whitespace-nowrap">Anupam Doctor doctor</td>
              <td class="px-6 py-4 whitespace-nowrap">Anupam Majety</td>
              <td class="px-6 py-4 whitespace-nowrap">Anupam Patient</td>
              <td class="px-6 py-4 whitespace-nowrap">November 22, 2024</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800"
                  >ACTIVE</span
                >
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex gap-2">
                  <button class="p-1 hover:bg-gray-100 rounded">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      class="lucide lucide-pen w-4 h-4 text-gray-600"
                    >
                      <path
                        d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"
                      ></path>
                    </svg></button
                  ><button class="p-1 hover:bg-gray-100 rounded">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      class="lucide lucide-file-text w-4 h-4 text-gray-600"
                    >
                      <path
                        d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"
                      ></path>
                      <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                      <path d="M10 9H8"></path>
                      <path d="M16 13H8"></path>
                      <path d="M16 17H8"></path>
                    </svg></button
                  ><button class="p-1 hover:bg-gray-100 rounded">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      class="lucide lucide-trash2 w-4 h-4 text-red-500"
                    >
                      <path d="M3 6h18"></path>
                      <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                      <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                      <line x1="10" x2="10" y1="11" y2="17"></line>
                      <line x1="14" x2="14" y1="11" y2="17"></line>
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
            <tr class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <input class="rounded border-gray-300" type="checkbox" />
              </td>
              <td class="px-6 py-4 whitespace-nowrap">1</td>
              <td class="px-6 py-4 whitespace-nowrap">Anupam Doctor doctor</td>
              <td class="px-6 py-4 whitespace-nowrap">Anupam Majety</td>
              <td class="px-6 py-4 whitespace-nowrap">Anupam Patient</td>
              <td class="px-6 py-4 whitespace-nowrap">November 14, 2024</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800"
                  >ACTIVE</span
                >
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex gap-2">
                  <button class="p-1 hover:bg-gray-100 rounded">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      class="lucide lucide-pen w-4 h-4 text-gray-600"
                    >
                      <path
                        d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"
                      ></path>
                    </svg></button
                  ><button class="p-1 hover:bg-gray-100 rounded">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      class="lucide lucide-file-text w-4 h-4 text-gray-600"
                    >
                      <path
                        d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"
                      ></path>
                      <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                      <path d="M10 9H8"></path>
                      <path d="M16 13H8"></path>
                      <path d="M16 17H8"></path>
                    </svg></button
                  ><button class="p-1 hover:bg-gray-100 rounded">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      class="lucide lucide-trash2 w-4 h-4 text-red-500"
                    >
                      <path d="M3 6h18"></path>
                      <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                      <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                      <line x1="10" x2="10" y1="11" y2="17"></line>
                      <line x1="14" x2="14" y1="11" y2="17"></line>
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
        <div
          class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6"
        >
          <div class="flex items-center gap-2">
            <span class="text-sm text-gray-700">Rows per page:</span
            ><select class="border border-gray-300 rounded-md text-sm p-1">
              <option>10</option>
              <option>25</option>
              <option>50</option>
            </select>
          </div>
          <div class="flex items-center gap-4">
            <span class="text-sm text-gray-700">Page 1 of 1</span>
            <div class="flex gap-2">
              <button
                class="p-1 rounded hover:bg-gray-100 disabled:opacity-50"
                disabled=""
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="lucide lucide-chevron-left w-5 h-5 text-gray-600"
                >
                  <path d="m15 18-6-6 6-6"></path>
                </svg></button
              ><button
                class="p-1 rounded hover:bg-gray-100 disabled:opacity-50"
                disabled=""
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="lucide lucide-chevron-right w-5 h-5 text-gray-600"
                >
                  <path d="m9 18 6-6-6-6"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { post, get } from "../../config/request";
import CustomForm from "../CustomForm/Form.vue";
export default {
  components: { CustomForm },
  data: () => {
    return {
      pageLoader: true,
      patientList: {
        column: [],
        data: [],
      },
      totalRows: 0,
      clinic: [],
      filterClinic: [],
      p_uid: false,
      serverParams: {
        columnFilters: {},
        sort: [
          {
            field: "",
            type: "",
          },
        ],
        page: 1,
        perPage: 10,
        searchTerm: "",
      },
      oldServerParams: {
        columnFilters: {},
        searchTerm: "",
        perPage: 10,
      },
      globalCheckboxApplyData: {},
      globalCheckboxApplyDataActions: [],
      patientCustomFormData: {},
      patientCustomFormModal: false,
      patientCustomFormViewMode: false,
    };
  },
  mounted() {
    this.init();
    // this.enabled_u_id = this.$route.params.pid != undefined ? this.$route.params.pid : ''
  },
  methods: {
    init: function () {
      this.getUniqueSetting();
      this.patientList = this.defaultPatientList(this.formTranslation);
      this.globalCheckboxApplyData = this.defaultGlobalCheckboxApplyData();
      this.globalCheckboxApplyDataActions =
        this.defaultGlobalCheckboxApplyDataActions();

      this.getPatientList();
      setTimeout(() => {
        this.clinic = this.clinics;
        this.clinic.forEach((element) => {
          this.filterClinic.push({ value: element.id, text: element.label });
        });
      }, 1000);
      this.getDynamicTranslation();
    },
    defaultPatientList: function () {
      return {
        column: [
          {
            field: "uid",
            label: this.p_uid
              ? this.formTranslation.patient.unique_id
              : this.formTranslation.common.id,
            sortable: !this.p_uid,
            width: "100px",
            filterOptions: {
              enabled: true,
              placeholder: this.p_uid
                ? this.formTranslation.patient.unique_id
                : this.formTranslation.common.id,
              filterValue: "",
            },
          },
          {
            label: this.formTranslation.patient.dt_lbl_name,
            field: "display_name",
            filterOptions: {
              enabled: true,
              placeholder: this.formTranslation.patient.dt_plh_name_filter,
              filterValue: "",
            },
          },
          {
            field: "clinic_name",
            label: this.formTranslation.patient.clinic,
            sortable: false,
            filterOptions: {
              enabled:
                this.userData.addOns.kiviPro &&
                ["administrator", "doctor"].includes(this.getUserRole()),
              filterValue: "",
              filterDropdownItems: this.filterClinic,
            },
          },
          {
            label: this.formTranslation.patient.dt_lbl_email,
            field: "user_email",
            filterOptions: {
              enabled: true,
              placeholder: this.formTranslation.patient.dt_plh_email_fltr,
              filterValue: "",
            },
          },
          // {
          //   label: this.formTranslation.patient.dt_lbl_mobile_number,
          //   field: 'mobile_number',
          //   sortable: false,
          //   width: '200px',
          //   filterOptions: {
          //     enabled: true,
          //     placeholder: this.formTranslation.patient.dt_plh_mobile_fltr_number,
          //     filterValue: '',
          //   },
          // },
          // {
          //   label: this.formTranslation.patient.dt_lbl_registered,
          //   field: 'user_registered',
          //   filterOptions: {
          //     enabled: false,
          //     placeholder: this.formTranslation.patient.dt_plh_date,
          //     filterValue: '',
          //   },
          // },
          // {
          //   field: 'user_status',
          //   label: this.formTranslation.service.dt_lbl_status,
          //   filterOptions: {
          //     enabled: true, // enable filter for this column
          //     placeholder: this.formTranslation.static_data.dt_lbl_plh_sr_fltr_status,
          //     filterDropdownItems: [
          //       { value: '0', text: this.formTranslation.common.active },
          //       { value: '1', text: this.formTranslation.common.inactive }
          //     ],
          //     filterValue: '',
          //   },
          //   html: true
          // },
          {
            label: this.formTranslation.patient.dt_lbl_action,
            field: "action",
            sortable: false,
            html: true,
          },
        ],
        data: [],
      };
    },
    getPatientList: function () {
      get("patient_list", this.serverParams)
        .then((data) => {
          this.pageLoader = false;
          if (data.data.status !== undefined && data.data.status === true) {
            this.patientList.data = data.data.data;
            this.totalRows = data.data.total_rows;
          } else {
            this.patientList.data = [];
            this.totalRows = 0;
          }
        })
        .catch((error) => {
          this.pageLoader = false;
          console.log(error);
        });
    },
    defaultGlobalCheckboxApplyData() {
      return {
        action_perform: "delete",
        module: "patient",
        data: [],
      };
    },
    defaultGlobalCheckboxApplyDataActions: function () {
      return [
        {
          value: "active",
          label: this.formTranslation.service.dt_active,
        },
        {
          value: "inactive",
          label: this.formTranslation.service.dt_inactive,
        },
        {
          value: "resend_credential",
          label: this.formTranslation.receptionist.resend_credential,
        },
        {
          value: "delete",
          label: this.formTranslation.clinic_schedule.dt_lbl_dlt,
        },
      ];
    },

    confirmDelete() {
      let content = "";
      if (this.globalCheckboxApplyData.action_perform === "delete") {
        content = this.formTranslation.common.py_delete;
      } else if (
        this.globalCheckboxApplyData.action_perform === "resend_credential"
      ) {
        content = this.formTranslation.common.py_resend_credential;
      } else if (
        this.globalCheckboxApplyData.action_perform === "active" ||
        this.globalCheckboxApplyData.action_perform === "inactive"
      ) {
        content = this.formTranslation.common.py_status;
      }
      $.confirm({
        title: this.formTranslation.clinic_schedule.dt_are_you_sure,
        content: content,
        type: "red",
        buttons: {
          ok: {
            text: this.formTranslation.common.yes,
            btnClass: "btn-danger",
            keys: ["enter"],
            action: () => {
              this.globalCheckboxApply();
            },
          },
          cancel: {
            text: this.formTranslation.common.cancel,
          },
        },
      });
    },

    globalCheckboxApply() {
      this.pageLoader = true;
      post("module_wise_multiple_data_update", this.globalCheckboxApplyData)
        .then((data) => {
          this.pageLoader = false;
          if (data.data.status !== undefined && data.data.status === true) {
            displayMessage(data.data.message);
            this.getPatientList();
          } else {
            displayErrorMessage(data.data.message);
          }
        })
        .catch((error) => {
          this.pageLoader = true;
          console.log(error);
        });
    },
    deletePatientData: function (index) {
      if (this.patientList.data[index - 1] !== undefined) {
        let ele = $("#user_delete_" + index);
        $.confirm({
          title: this.formTranslation.clinic_schedule.dt_are_you_sure,
          content:
            this.formTranslation.common.action_delete_appointment_patient,
          type: "red",
          buttons: {
            ok: {
              text: this.formTranslation.common.yes,
              btnClass: "btn-danger",
              keys: ["enter"],
              action: () => {
                ele.prop("disabled", true);
                $(ele).find("i").removeClass("fa fa-trash");
                $(ele).find("i").addClass("fa fa-sync fa-spin");
                get("patient_delete", {
                  id: this.patientList.data[index - 1].ID,
                })
                  .then((data) => {
                    ele.prop("disabled", false);
                    $(ele).find("i").removeClass("fa fa-sync fa-spin");
                    $(ele).find("i").addClass("fa fa-trash");
                    if (
                      data.data.status !== undefined &&
                      data.data.status === true
                    ) {
                      this.patientList.data.splice(index - 1, 1);
                      displayMessage(data.data.message);
                    }
                  })
                  .catch((error) => {
                    ele.prop("disabled", false);
                    $(ele).find("i").removeClass("fa fa-sync fa-spin");
                    $(ele).find("i").addClass("fa fa-trash");
                    console.log(error);
                    displayErrorMessage(
                      this.formTranslation.common.internal_server_error
                    );
                  });
              },
            },
            cancel: {
              text: this.formTranslation.common.cancel,
            },
          },
        });
      }
    },
    resendRequest: function (id) {
      var element = $("#resend_" + id).find("i");
      element.removeClass("fa fa-paper-plane ");
      element.addClass("fa fa-spinner fa-spin");
      post("resend_credential", { id: id })
        .then((data) => {
          element.removeClass("fa fa-spinner fa-spin");
          element.addClass("fa fa-paper-plane");
          if (data.data.status !== undefined && data.data.status === true) {
            displayMessage(data.data.message);
          } else {
            displayErrorMessage(data.data.message);
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    getDynamicTranslation: function () {
      this.$store.state.staticDataModule.langTranslateData;
    },
    getUniqueSetting: function () {
      if (
        this.userData.unquie_id_status !== undefined &&
        this.userData.unquie_id_status == true
      ) {
        this.p_uid = this.userData.unquie_id_status;
      } else {
        this.p_uid = false;
      }
    },
    updateParams(newProps) {
      this.serverParams = Object.assign({}, this.serverParams, newProps);
      this.getPatientList();
    },

    onPageChange(params) {
      this.updateParams({ page: params.currentPage });
    },

    onPerPageChange(params) {
      if (this.oldServerParams.perPage === params.currentPerPage) {
        return;
      }
      this.oldServerParams.perPage = params.currentPerPage;
      this.updateParams({
        perPage: params.currentPerPage,
        page: params.currentPage,
      });
    },

    onSortChange(params) {
      this.updateParams({
        sort: params,
      });
    },
    globalFilter: _.debounce(function (params) {
      if (this.oldServerParams.searchTerm === params.searchTerm) {
        return;
      }
      this.oldServerParams.searchTerm = params.searchTerm;
      this.updateParams({
        searchTerm: params.searchTerm,
        perPage: this.serverParams.perPage,
        page: 1,
      });
    }, 300),

    onColumnFilter: _.debounce(function (params) {
      var emptyValue = true;
      var emptyValue2 = true;
      Object.values(params.columnFilters).map(function (value, index, array) {
        if (value) {
          emptyValue = false;
        }
      });
      Object.values(this.oldServerParams.columnFilters).map(function (
        value,
        index,
        array
      ) {
        if (value) {
          emptyValue2 = false;
        }
      });
      if (!emptyValue || !emptyValue2) {
        this.oldServerParams.columnFilters = Object.assign(
          {},
          params.columnFilters
        );
        this.updateParams({
          columnFilters: params.columnFilters,
          perPage: this.serverParams.perPage,
          page: 1,
        });
      }
    }, 300),

    getInitials(name) {
      if (name !== undefined && name !== "" && name !== null) {
        const patient_name = name.split(" ");
        const initials = patient_name.map((patient_name) =>
          patient_name.charAt(0).toUpperCase()
        );
        return initials.join("");
      } else {
        return " - ";
      }
    },
    customFormOpen(props, custom_form_data) {
      this.patientCustomFormData = custom_form_data;
      this.patientCustomFormData.module_id = props.ID;
      this.patientCustomFormViewMode = false;
      this.patientCustomFormModal = true;
    },
    customFormCondition(props, custom_form_data) {
      return (
        props.custom_forms &&
        props.custom_forms.length &&
        (custom_form_data.clinic_ids.length === 0 ||
          custom_form_data.clinic_ids.some((value) =>
            props.clinic_id.includes(value)
          ))
      );
    },
  },
  computed: {
    patientListExport() {
      return "Patient List - " + moment().format("YYYY-MM-DD");
    },
    getSpeciality: function () {
      return (salut) => {
        if (salut !== undefined && salut.length > 0) {
          let specialties = "";
          salut.map(function (spec, index) {
            specialties +=
              salut.length === index + 1 ? spec.label : spec.label + ", ";
            return spec;
          });
          return specialties;
        }
        return " - ";
      };
    },
    userData() {
      return this.$store.state.userDataModule.user;
    },
    clinics() {
      return this.$store.state.clinic;
    },
    // formTranslation: function () {
    //   return this.$store.state.staticDataModule.langTranslateData ;
    // }
  },
  watch: {},
};
</script>
<style>
#printPatientData .vgt-table thead th {
  vertical-align: middle;
}

@media (max-width: 576px) {
  #printPatientData .vgt-compact td:before {
    width: 42%;
    padding-left: 0;
  }
}
</style>
