<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Upload Patient Documents</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
        }
        body {
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.5;
            padding: 0;
            margin: 0;
        }
        .kc-mobile-upload-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .kc-mobile-upload-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .kc-mobile-upload-header h1 {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        .kc-mobile-upload-header p {
            color: #666;
            margin: 0;
        }
        .kc-upload-tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        .kc-upload-tab {
            flex: 1;
            text-align: center;
            padding: 12px;
            cursor: pointer;
            color: #666;
            transition: all 0.3s;
        }
        .kc-upload-tab.active {
            color: #000;
            border-bottom: 2px solid #000;
            font-weight: 500;
        }
        .kc-upload-content {
            display: none;
        }
        .kc-upload-content.active {
            display: block;
        }
        .kc-form-group {
            margin-bottom: 20px;
        }
        .kc-form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        .kc-form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        .kc-form-control:focus {
            border-color: #000;
            outline: none;
        }
        .kc-btn {
            display: inline-block;
            background-color: #000;
            color: #fff;
            border: none;
            border-radius: 5px;
            padding: 12px 20px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .kc-btn:hover {
            background-color: #333;
        }
        .kc-btn-outline {
            background-color: transparent;
            color: #000;
            border: 1px solid #000;
        }
        .kc-btn-outline:hover {
            background-color: #f5f5f5;
        }
        .kc-camera-container {
            position: relative;
            overflow: hidden;
            width: 100%;
            border-radius: 8px;
            background-color: #f5f5f5;
            margin-bottom: 20px;
        }
        .kc-camera-container video, 
        .kc-camera-container canvas {
            width: 100%;
            display: block;
        }
        .kc-camera-actions {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 10px;
        }
        .kc-alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        .kc-alert-error {
            background-color: #fff5f5;
            color: #e53e3e;
            border: 1px solid #feb2b2;
        }
        .kc-alert-success {
            background-color: #f0fff4;
            color: #38a169;
            border: 1px solid #c6f6d5;
        }
        .kc-upload-preview {
            margin-top: 20px;
            text-align: center;
        }
        .kc-upload-preview img {
            max-width: 100%;
            max-height: 300px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        .kc-file-input {
            padding: 30px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        .kc-file-input:hover {
            border-color: #000;
        }
        .kc-file-input input {
            display: none;
        }
        .kc-file-input-icon {
            font-size: 24px;
            margin-bottom: 10px;
            color: #666;
        }
        .kc-file-input-text {
            color: #666;
        }
        .kc-footer {
            text-align: center;
            margin-top: 30px;
            font-size: 14px;
            color: #666;
        }
        .kc-session-info {
            text-align: center;
            margin-top: 20px;
            font-size: 14px;
            color: #666;
        }
        @media (max-width: 767px) {
            .kc-mobile-upload-container {
                margin: 0;
                width: 100%;
                min-height: 100vh;
                box-shadow: none;
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="kc-mobile-upload-container">
        <div class="kc-mobile-upload-header">
            <h1>Upload Document</h1>
            <p>Use your mobile device to upload files or take photos for patient records</p>
        </div>
        
        <?php if (isset($error_message) && $error_message): ?>
            <div class="kc-alert kc-alert-error">
                <?php echo esc_html($error_message); ?>
            </div>
        <?php endif; ?>
        
        <?php if (isset($success_message) && $success_message): ?>
            <div class="kc-alert kc-alert-success">
                <?php echo esc_html($success_message); ?>
            </div>
        <?php endif; ?>
        
        <?php 
        global $wpdb;
        $document_types = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}kc_static_data WHERE type = 'document_type'");
        if (empty($document_types)) {
            // Default document types if none exist in database
            $document_types = [
                (object)['value' => 'medical_report', 'label' => 'Medical Report'],
                (object)['value' => 'lab_result', 'label' => 'Lab Result'],
                (object)['value' => 'prescription', 'label' => 'Prescription'],
                (object)['value' => 'other', 'label' => 'Other']
            ];
        }
        ?>
        
        <div class="kc-upload-tabs">
            <div class="kc-upload-tab active" data-tab="camera">Camera</div>
            <div class="kc-upload-tab" data-tab="files">Files</div>
        </div>
        
        <!-- Camera Tab -->
        <div class="kc-upload-content active" id="camera-tab">
            <div class="kc-camera-container">
                <video id="camera-preview" autoplay playsinline></video>
                <canvas id="camera-canvas" style="display: none;"></canvas>
            </div>
            
            <div class="kc-camera-actions">
                <button id="capture-btn" class="kc-btn">Take Photo</button>
                <button id="retake-btn" class="kc-btn kc-btn-outline" style="display: none;">Retake</button>
            </div>
            
            <form id="camera-form" method="post" enctype="multipart/form-data" style="display: none;">
                <input type="hidden" name="image_data" id="image-data">
                <input type="hidden" name="session_id" value="<?php echo esc_attr($session_id); ?>">
                
                <div class="kc-form-group">
                    <label for="camera-document-name">Document Name</label>
                    <input type="text" id="camera-document-name" name="document_name" class="kc-form-control" required placeholder="e.g., X-Ray Result">
                </div>
                
                <div class="kc-form-group">
                    <label for="camera-document-type">Document Type</label>
                    <select id="camera-document-type" name="document_type" class="kc-form-control" required>
                        <?php foreach ($document_types as $type): ?>
                            <option value="<?php echo esc_attr($type->value); ?>"><?php echo esc_html($type->label); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="kc-form-group">
                    <label for="camera-document-description">Description (Optional)</label>
                    <textarea id="camera-document-description" name="document_description" class="kc-form-control" rows="3" placeholder="Add a brief description of this document"></textarea>
                </div>
                
                <button type="submit" class="kc-btn" style="width: 100%;">Upload Photo</button>
            </form>
        </div>
        
        <!-- File Upload Tab -->
        <div class="kc-upload-content" id="files-tab">
            <form id="file-form" method="post" enctype="multipart/form-data">
                <input type="hidden" name="session_id" value="<?php echo esc_attr($session_id); ?>">
                
                <div class="kc-file-input">
                    <div class="kc-file-input-icon">📄</div>
                    <div class="kc-file-input-text">Click to select a file or drag and drop</div>
                    <input type="file" name="document" id="file-input" accept="image/*,application/pdf">
                </div>
                
                <div id="file-preview" class="kc-upload-preview" style="display: none;">
                    <img id="preview-image" src="" alt="Preview">
                </div>
                
                <div class="kc-form-group">
                    <label for="document-name">Document Name</label>
                    <input type="text" id="document-name" name="document_name" class="kc-form-control" required placeholder="e.g., Blood Test Results">
                </div>
                
                <div class="kc-form-group">
                    <label for="document-type">Document Type</label>
                    <select id="document-type" name="document_type" class="kc-form-control" required>
                        <?php foreach ($document_types as $type): ?>
                            <option value="<?php echo esc_attr($type->value); ?>"><?php echo esc_html($type->label); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="kc-form-group">
                    <label for="document-description">Description (Optional)</label>
                    <textarea id="document-description" name="document_description" class="kc-form-control" rows="3" placeholder="Add a brief description of this document"></textarea>
                </div>
                
                <button type="submit" class="kc-btn" style="width: 100%;">Upload Document</button>
            </form>
        </div>
        
        <div class="kc-session-info">
            This upload session will expire in 
            <span id="session-timer">
                <?php 
                $minutes_left = floor(($session_data['expires_at'] - time()) / 60);
                echo esc_html($minutes_left . ' minute' . ($minutes_left != 1 ? 's' : '')); 
                ?>
            </span>
        </div>
        
        <div class="kc-footer">
            Powered by Medroid EHR
        </div>
    </div>

    <script>
        // Tab switching
        document.querySelectorAll('.kc-upload-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                // Remove active class from all tabs and content
                document.querySelectorAll('.kc-upload-tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.kc-upload-content').forEach(c => c.classList.remove('active'));
                
                // Add active class to clicked tab and corresponding content
                this.classList.add('active');
                document.getElementById(this.dataset.tab + '-tab').classList.add('active');
            });
        });
        
        // File input preview
        const fileInput = document.getElementById('file-input');
        const filePreview = document.getElementById('file-preview');
        const previewImage = document.getElementById('preview-image');
        
        if (fileInput) {
            fileInput.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const file = this.files[0];
                    
                    // Set document name from file name if empty
                    const documentNameInput = document.getElementById('document-name');
                    if (documentNameInput && !documentNameInput.value) {
                        documentNameInput.value = file.name.split('.').slice(0, -1).join('.');
                    }
                    
                    // Only show preview for images
                    if (file.type.startsWith('image/')) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            previewImage.src = e.target.result;
                            filePreview.style.display = 'block';
                        }
                        reader.readAsDataURL(file);
                    } else {
                        filePreview.style.display = 'none';
                    }
                }
            });
            
            // Drag and drop
            const dropArea = document.querySelector('.kc-file-input');
            
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                dropArea.addEventListener(eventName, preventDefaults, false);
            });
            
            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }
            
            ['dragenter', 'dragover'].forEach(eventName => {
                dropArea.addEventListener(eventName, highlight, false);
            });
            
            ['dragleave', 'drop'].forEach(eventName => {
                dropArea.addEventListener(eventName, unhighlight, false);
            });
            
            function highlight() {
                dropArea.style.borderColor = '#000';
            }
            
            function unhighlight() {
                dropArea.style.borderColor = '#ddd';
            }
            
            dropArea.addEventListener('drop', handleDrop, false);
            
            function handleDrop(e) {
                const dt = e.dataTransfer;
                const files = dt.files;
                fileInput.files = files;
                
                // Trigger change event
                const event = new Event('change');
                fileInput.dispatchEvent(event);
            }
        }
        
        // Camera functionality
        const cameraPreview = document.getElementById('camera-preview');
        const cameraCanvas = document.getElementById('camera-canvas');
        const captureBtn = document.getElementById('capture-btn');
        const retakeBtn = document.getElementById('retake-btn');
        const cameraForm = document.getElementById('camera-form');
        const imageDataInput = document.getElementById('image-data');
        
        let stream;
        
        async function initCamera() {
            try {
                stream = await navigator.mediaDevices.getUserMedia({ 
                    video: { 
                        facingMode: 'environment',
                        width: { ideal: 1920 },
                        height: { ideal: 1080 }
                    } 
                });
                cameraPreview.srcObject = stream;
            } catch (err) {
                console.error('Camera error:', err);
                alert('Error accessing camera: ' + err.message);
            }
        }
        
        if (cameraPreview && captureBtn) {
            // Initialize camera when tab is shown
            initCamera();
            
            // Capture button
            captureBtn.addEventListener('click', function() {
                // Set canvas dimensions to match video
                cameraCanvas.width = cameraPreview.videoWidth;
                cameraCanvas.height = cameraPreview.videoHeight;
                
                // Draw video frame to canvas
                const context = cameraCanvas.getContext('2d');
                context.drawImage(cameraPreview, 0, 0, cameraCanvas.width, cameraCanvas.height);
                
                // Get data URL
                const dataURL = cameraCanvas.toDataURL('image/jpeg');
                imageDataInput.value = dataURL;
                
                // Show canvas, hide video
                cameraPreview.style.display = 'none';
                cameraCanvas.style.display = 'block';
                
                // Show retake button and form
                captureBtn.style.display = 'none';
                retakeBtn.style.display = 'inline-block';
                cameraForm.style.display = 'block';
                
                // Set document name
                const documentNameInput = document.getElementById('camera-document-name');
                if (documentNameInput && !documentNameInput.value) {
                    documentNameInput.value = 'Camera Capture - ' + new Date().toLocaleString();
                }
            });
            
            // Retake button
            retakeBtn.addEventListener('click', function() {
                // Show video, hide canvas
                cameraPreview.style.display = 'block';
                cameraCanvas.style.display = 'none';
                
                // Show capture button, hide retake button and form
                captureBtn.style.display = 'inline-block';
                retakeBtn.style.display = 'none';
                cameraForm.style.display = 'none';
            });
            
            // Clean up on page unload
            window.addEventListener('beforeunload', function() {
                if (stream) {
                    stream.getTracks().forEach(track => track.stop());
                }
            });
        }
        
        // Handle form submissions via API
        document.getElementById('camera-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            try {
                const response = await fetch('/wp-json/kivicare/api/v1/patient-mobile-upload/upload', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    // Show success message
                    alert('Image uploaded successfully!');
                    
                    // Reset the camera
                    retakeBtn.click();
                } else {
                    alert('Error: ' + result.message);
                }
            } catch (error) {
                console.error('Upload error:', error);
                alert('Error uploading image. Please try again.');
            }
        });
        
        document.getElementById('file-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            if (!fileInput.files || !fileInput.files[0]) {
                alert('Please select a file to upload');
                return;
            }
            
            const formData = new FormData(this);
            try {
                const response = await fetch('/wp-json/kivicare/api/v1/patient-mobile-upload/upload', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    // Show success message
                    alert('File uploaded successfully!');
                    
                    // Reset the form
                    this.reset();
                    filePreview.style.display = 'none';
                } else {
                    alert('Error: ' + result.message);
                }
            } catch (error) {
                console.error('Upload error:', error);
                alert('Error uploading file. Please try again.');
            }
        });
        
        // Session timer
        const sessionTimer = document.getElementById('session-timer');
        if (sessionTimer) {
            const expiresAt = <?php echo json_encode($session_data['expires_at']); ?>;
            
            function updateTimer() {
                const now = Math.floor(Date.now() / 1000);
                const secondsLeft = expiresAt - now;
                
                if (secondsLeft <= 0) {
                    sessionTimer.textContent = 'Expired';
                    return;
                }
                
                const minutesLeft = Math.floor(secondsLeft / 60);
                const seconds = secondsLeft % 60;
                
                sessionTimer.textContent = minutesLeft + ' minute' + (minutesLeft !== 1 ? 's' : '') + 
                                          ' and ' + seconds + ' second' + (seconds !== 1 ? 's' : '');
                
                setTimeout(updateTimer, 1000);
            }
            
            updateTimer();
        }
    </script>
</body>
</html>