<template>
  <div class="w-full">
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
      <!-- Header Section -->
      <div class="px-6 py-4 border-b border-gray-200 bg-white flex flex-col md:flex-row justify-between items-start md:items-center">
        <div class="mb-4 md:mb-0">
          <h1 class="text-xl font-bold text-gray-800">SignatureRX Prescriptions</h1>
          <p class="text-sm text-gray-500">Track all prescriptions sent to SignatureRX</p>
        </div>

        <!-- Search and Filter Controls -->
        <div class="flex flex-col sm:flex-row w-full md:w-auto gap-2">
          <select 
            v-model="filters.status" 
            class="form-select rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 text-sm"
          >
            <option value="">All Statuses</option>
            <option v-for="option in statusOptions" :key="option.value" :value="option.value">
              {{ option.text }}
            </option>
          </select>
          
          <div class="relative flex-grow sm:max-w-xs">
            <input 
              type="text" 
              v-model="filters.search" 
              placeholder="Search..."
              class="form-input w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 text-sm"
            />
            <button 
              @click="getPrescriptions()" 
              class="absolute inset-y-0 right-0 px-3 flex items-center bg-blue-500 text-white rounded-r-md hover:bg-blue-600 transition-colors"
            >
              <i class="fa fa-search"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="isBusy" class="flex flex-col items-center justify-center py-12">
        <div class="w-12 h-12 border-4 border-blue-400 border-t-transparent rounded-full animate-spin"></div>
        <p class="mt-4 text-gray-600">Loading prescriptions...</p>
      </div>

      <!-- Empty State -->
      <div v-else-if="prescriptionList.data.length === 0" class="flex flex-col items-center justify-center py-16">
        <svg class="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
        <p class="mt-4 text-gray-600">No prescriptions found</p>
      </div>

      <!-- Table Content -->
      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Patient</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Doctor</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Clinic</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reference ID</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
              <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Details</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <template v-for="(item, index) in prescriptionList.data">
              <tr :key="item.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ index + 1 }}</td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900">{{ item.patient_name }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ item.doctor_name }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ item.clinic_name }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="getStatusClass(item.status)">
                    {{ formatStatus(item.status) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ item.reference_id || '—' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ item.created_at | dateFormat }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                  <button 
                    @click="toggleDetails(item)" 
                    class="text-blue-600 hover:text-blue-900 focus:outline-none focus:underline"
                  >
                    {{ expandedRows[item.id] ? 'Hide' : 'Show' }}
                  </button>
                </td>
              </tr>
              
              <!-- Expanded Details Row -->
              <tr v-if="expandedRows[item.id]" :key="`${item.id}-details`">
                <td colspan="8" class="px-6 py-4 bg-gray-50">
                  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h5 class="text-base font-semibold text-gray-800 mb-2">Prescription Details</h5>
                        <div class="space-y-2">
                          <p class="text-sm"><span class="font-medium">Reference ID:</span> {{ item.reference_id || 'Not available' }}</p>
                          <p class="text-sm"><span class="font-medium">Action:</span> {{ formatAction(item.prescription_action) }}</p>
                          <p class="text-sm"><span class="font-medium">Affiliate Tag:</span> {{ item.aff_tag || 'Medroid EHR' }}</p>
                        </div>
                      </div>
                      
                      <div v-if="item.response_data">
                        <h5 class="text-base font-semibold text-gray-800 mb-2">Response Details</h5>
                        <div class="bg-gray-100 rounded p-3 overflow-auto max-h-40">
                          <pre class="text-xs text-gray-700">{{ JSON.stringify(item.response_data, null, 2) }}</pre>
                        </div>
                      </div>
                    </div>
                  </div>
                </td>
              </tr>
            </template>
          </tbody>
        </table>
      </div>

      <!-- Pagination Controls -->
      <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p class="text-sm text-gray-700">
              Showing
              <span class="font-medium">{{ prescriptionList.data.length ? (paginate.currentPage - 1) * paginate.perPage + 1 : 0 }}</span>
              to
              <span class="font-medium">{{ Math.min(paginate.currentPage * paginate.perPage, paginate.totalRows) }}</span>
              of
              <span class="font-medium">{{ paginate.totalRows }}</span>
              results
            </p>
          </div>
          <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <button
                @click="paginate.currentPage > 1 && getPrescriptions(paginate.currentPage - 1)"
                :disabled="paginate.currentPage <= 1"
                class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                :class="{ 'opacity-50 cursor-not-allowed': paginate.currentPage <= 1 }"
              >
                <span class="sr-only">Previous</span>
                <i class="fa fa-chevron-left"></i>
              </button>
              
              <button
                v-for="page in paginationRange"
                :key="page"
                @click="getPrescriptions(page)"
                :class="[
                  paginate.currentPage === page ? 'z-10 bg-blue-50 border-blue-500 text-blue-600' : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50',
                  'relative inline-flex items-center px-4 py-2 border text-sm font-medium'
                ]"
              >
                {{ page }}
              </button>
              
              <button
                @click="paginate.currentPage < Math.ceil(paginate.totalRows / paginate.perPage) && getPrescriptions(paginate.currentPage + 1)"
                :disabled="paginate.currentPage >= Math.ceil(paginate.totalRows / paginate.perPage)"
                class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                :class="{ 'opacity-50 cursor-not-allowed': paginate.currentPage >= Math.ceil(paginate.totalRows / paginate.perPage) }"
              >
                <span class="sr-only">Next</span>
                <i class="fa fa-chevron-right"></i>
              </button>
            </nav>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { get, post } from '../../config/request';

export default {
  name: "SignatureRXList",
  data() {
    return {
      isBusy: false,
      expandedRows: {},
      prescriptionList: {
        field: [
          {
            key: "index",
            label: "#",
            sortable: true
          },
          {
            key: "patient_name",
            label: "Patient",
            sortable: true
          },
          {
            key: "doctor_name",
            label: "Doctor",
            sortable: true
          },
          {
            key: "clinic_name",
            label: "Clinic",
            sortable: true
          },
          {
            key: "status",
            label: "Status",
            sortable: true
          },
          {
            key: "reference_id",
            label: "Reference ID",
            sortable: true
          },
          {
            key: "created_at",
            label: "Date",
            sortable: true
          },
          {
            key: "details",
            label: "Details"
          }
        ],
        data: []
      },
      filters: {
        status: "",
        search: ""
      },
      statusOptions: [
        { value: "draft", text: "Draft" },
        { value: "issued", text: "Issued" },
        { value: "completed", text: "Completed" },
        { value: "failed", text: "Failed" }
      ],
      paginate: {
        currentPage: 1,
        perPage: 10,
        totalRows: 0
      }
    };
  },
  computed: {
    // Calculate pagination range to show in the UI
    paginationRange() {
      const totalPages = Math.ceil(this.paginate.totalRows / this.paginate.perPage);
      
      // Show maximum 5 page buttons
      const maxVisiblePages = 5;
      const halfVisiblePages = Math.floor(maxVisiblePages / 2);
      
      let startPage = Math.max(1, this.paginate.currentPage - halfVisiblePages);
      let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
      
      // Adjust if we're near the end
      if (endPage - startPage + 1 < maxVisiblePages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
      }
      
      return Array.from({ length: endPage - startPage + 1 }, (_, i) => startPage + i);
    }
  },
  created() {
    this.getPrescriptions();
  },
  methods: {
    // Toggle row details visibility
    toggleDetails(item) {
      this.$set(this.expandedRows, item.id, !this.expandedRows[item.id]);
    },
    
    // Fetch prescriptions data
    getPrescriptions(page) {
      if (page) {
        this.paginate.currentPage = page;
      }
      
      this.isBusy = true;
      
      const requestParams = {
        page: this.paginate.currentPage,
        limit: this.paginate.perPage
      };
      
      // Add filters if they're set
      if (this.filters.status) {
        requestParams.status = this.filters.status;
      }
      
      if (this.filters.search) {
        requestParams.search = this.filters.search;
      }
      
      get("signaturerx_all_prescriptions", requestParams)
        .then(response => {
          if (response.data.status) {
            this.prescriptionList.data = response.data.data.prescriptions;
            this.paginate.totalRows = response.data.data.total;
            
            // Reset expanded rows when data changes
            this.expandedRows = {};
          } else {
            this.prescriptionList.data = [];
            this.paginate.totalRows = 0;
            // Use toast or notification utility from global mixin
            if (this.$toast) {
              this.$toast.error(response.data.message);
            } else if (this.$notify) {
              this.$notify({
                group: "notification",
                text: response.data.message,
                type: "error"
              });
            } else {
              console.error(response.data.message);
            }
          }
        })
        .catch(error => {
          this.prescriptionList.data = [];
          this.paginate.totalRows = 0;
          // Use toast or notification utility from global mixin
          const errorMsg = error.response?.data?.message || error.message || "Failed to load prescriptions";
          if (this.$toast) {
            this.$toast.error(errorMsg);
          } else if (this.$notify) {
            this.$notify({
              group: "notification",
              text: errorMsg,
              type: "error"
            });
          } else {
            console.error(errorMsg);
          }
          console.error("SignatureRX error:", error);
        })
        .finally(() => {
          this.isBusy = false;
        });
    },
    
    // Get CSS classes for status badges
    getStatusClass(status) {
      const baseClasses = "px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full";
      
      switch (status) {
        case "draft":
          return `${baseClasses} bg-yellow-100 text-yellow-800`;
        case "issued":
          return `${baseClasses} bg-green-100 text-green-800`;
        case "completed":
          return `${baseClasses} bg-blue-100 text-blue-800`;
        case "failed":
          return `${baseClasses} bg-red-100 text-red-800`;
        default:
          return `${baseClasses} bg-gray-100 text-gray-800`;
      }
    },
    
    // Format status text
    formatStatus(status) {
      if (!status) return "Unknown";
      return status.charAt(0).toUpperCase() + status.slice(1);
    },
    
    // Format action text
    formatAction(action) {
      if (!action) return "Unknown";
      
      switch(action) {
        case "draft":
          return "Draft";
        case "issueOnly":
          return "Issue Only";
        case "issueForCollection":
          return "Issue For Collection";
        case "issueToContact":
          return "Issue To Contact";
        case "issueForDelivery":
          return "Issue For Delivery";
        default:
          return action;
      }
    }
  }
};
</script>