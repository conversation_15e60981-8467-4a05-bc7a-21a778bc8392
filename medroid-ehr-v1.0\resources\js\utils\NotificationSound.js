/**
 * Notification Sound Utility
 * 
 * This utility handles playing notification sounds in a robust way
 * that works even if the sound file doesn't exist.
 */

class NotificationSound {
  constructor() {
    this.audio = null;
    this.isSupported = false;
    this.initialized = false;
    
    // Initialize the sound manager
    this.initialize();
  }
  
  /**
   * Initialize the notification sound
   */
  initialize() {
    try {
      // Try to create audio context for maximum compatibility
      if (typeof AudioContext !== 'undefined' || typeof webkitAudioContext !== 'undefined') {
        this.isSupported = true;
        
        // Don't actually load the sound yet - we'll do that on the first play
        // This reduces unnecessary network requests
      } else {
        // Fallback to the basic Audio API
        this.audio = new Audio();
        this.audio.src = '/wp-content/plugins/kivicare-clinic-management-system/assets/sounds/notification.mp3';
        this.audio.load();
        this.isSupported = true;
      }
    } catch (e) {
      console.warn('Audio notifications not supported in this browser:', e);
      this.isSupported = false;
    }
    
    this.initialized = true;
  }
  
  /**
   * Play the notification sound
   * 
   * @returns {Promise} A promise that resolves when the sound has played
   */
  play() {
    return new Promise((resolve, reject) => {
      if (!this.isSupported) {
        resolve(); // Silently succeed if not supported
        return;
      }
      
      try {
        // For modern browsers with AudioContext
        if (typeof AudioContext !== 'undefined' || typeof webkitAudioContext !== 'undefined') {
          // Create a simple beep sound programmatically
          const AudioCtx = window.AudioContext || window.webkitAudioContext;
          const audioCtx = new AudioCtx();
          const oscillator = audioCtx.createOscillator();
          const gainNode = audioCtx.createGain();
          
          oscillator.connect(gainNode);
          gainNode.connect(audioCtx.destination);
          
          oscillator.type = 'sine';
          oscillator.frequency.value = 850; // Set frequency for notification tone
          gainNode.gain.value = 0.1; // Set low volume
          
          oscillator.start();
          
          // Stop after a short duration
          setTimeout(() => {
            oscillator.stop();
            resolve();
          }, 200);
        } else if (this.audio) {
          // Fallback to basic Audio API
          this.audio.play().then(resolve).catch(() => {
            // If it fails to play the file, create a beep with an oscillator
            try {
              const AudioCtx = window.AudioContext || window.webkitAudioContext;
              const audioCtx = new AudioCtx();
              const oscillator = audioCtx.createOscillator();
              const gainNode = audioCtx.createGain();
              
              oscillator.connect(gainNode);
              gainNode.connect(audioCtx.destination);
              
              oscillator.type = 'sine';
              oscillator.frequency.value = 850;
              gainNode.gain.value = 0.1;
              
              oscillator.start();
              setTimeout(() => {
                oscillator.stop();
                resolve();
              }, 200);
            } catch (e) {
              // Give up - audio not supported on this browser
              console.warn('Failed to play notification sound:', e);
              resolve();
            }
          });
        } else {
          resolve(); // No audio available
        }
      } catch (e) {
        console.warn('Error playing notification sound:', e);
        resolve(); // Always resolve, even on error
      }
    });
  }
}

// Export a singleton instance
export default new NotificationSound();