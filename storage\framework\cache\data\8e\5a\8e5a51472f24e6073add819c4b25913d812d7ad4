1752493211a:40:{s:11:"total_users";i:99;s:20:"monthly_active_users";i:21;s:18:"daily_active_users";i:1;s:16:"user_growth_rate";d:130.2;s:13:"patient_count";i:93;s:14:"provider_count";i:4;s:11:"admin_count";i:1;s:14:"verified_users";i:42;s:17:"verification_rate";d:42.4;s:18:"total_appointments";i:18;s:20:"monthly_appointments";i:4;s:18:"daily_appointments";i:0;s:27:"appointment_completion_rate";d:0;s:29:"appointment_cancellation_rate";d:0;s:22:"completed_appointments";i:0;s:22:"cancelled_appointments";i:0;s:14:"total_consults";i:162;s:16:"monthly_consults";i:85;s:14:"daily_consults";i:0;s:28:"consult_to_appointment_ratio";d:900;s:20:"repeat_consult_users";i:11;s:24:"appointments_by_provider";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:2:{i:0;a:3:{s:11:"provider_id";i:1;s:13:"provider_name";s:10:"Verena Chu";s:5:"count";i:15;}i:1;a:3:{s:11:"provider_id";i:2;s:13:"provider_name";s:15:"Muntazir Jivraj";s:5:"count";i:3;}}s:28:" * escapeWhenCastingToString";b:0;}s:14:"user_locations";a:0:{}s:19:"diagnostic_accuracy";i:0;s:25:"diagnostic_feedback_count";i:0;s:22:"top_accurate_diagnoses";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}s:24:"top_inaccurate_diagnoses";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}s:13:"total_revenue";d:0;s:15:"monthly_revenue";d:0;s:25:"average_appointment_value";d:0;s:19:"revenue_by_provider";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}s:13:"total_clinics";i:2;s:14:"active_clinics";i:2;s:26:"clinics_accepting_patients";i:2;s:20:"telemedicine_clinics";i:2;s:22:"clinic_activation_rate";d:100;s:26:"telemedicine_adoption_rate";d:100;s:16:"clinics_by_state";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:17:"App\Models\Clinic":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:7:"clinics";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:2:{s:5:"state";s:5:"Essex";s:5:"count";i:2;}s:11:" * original";a:2:{s:5:"state";s:5:"Essex";s:5:"count";i:2;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:8:{s:15:"operating_hours";s:5:"array";s:16:"services_offered";s:5:"array";s:18:"insurance_accepted";s:5:"array";s:9:"is_active";s:7:"boolean";s:20:"accepts_new_patients";s:7:"boolean";s:20:"telemedicine_enabled";s:7:"boolean";s:10:"created_at";s:8:"datetime";s:10:"updated_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:22:{i:0;s:4:"name";i:1;s:4:"slug";i:2;s:11:"description";i:3;s:5:"email";i:4;s:5:"phone";i:5;s:7:"website";i:6;s:7:"address";i:7;s:4:"city";i:8;s:5:"state";i:9;s:11:"postal_code";i:10;s:7:"country";i:11;s:15:"operating_hours";i:12;s:16:"services_offered";i:13;s:14:"license_number";i:14;s:6:"tax_id";i:15;s:9:"is_active";i:16;s:20:"accepts_new_patients";i:17;s:20:"telemedicine_enabled";i:18;s:18:"insurance_accepted";i:19;s:4:"logo";i:20;s:13:"primary_color";i:21;s:15:"secondary_color";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:24:"top_clinics_by_providers";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:2:{i:0;O:17:"App\Models\Clinic":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:7:"clinics";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:26:{s:2:"id";i:1;s:4:"name";s:24:"Chelmsford Health Centre";s:4:"slug";N;s:11:"description";s:52:"Default healthcare clinic for providers and patients";s:5:"email";s:16:"<EMAIL>";s:5:"phone";s:11:"01245823923";s:7:"website";s:34:"https://chelmsfordhealthcentre.com";s:7:"address";s:13:"Dickens Place";s:4:"city";s:10:"Chelmsford";s:5:"state";s:5:"Essex";s:11:"postal_code";s:7:"CM1 4UU";s:7:"country";s:2:"US";s:15:"operating_hours";N;s:16:"services_offered";N;s:14:"license_number";N;s:6:"tax_id";N;s:9:"is_active";i:1;s:20:"accepts_new_patients";i:1;s:20:"telemedicine_enabled";i:1;s:18:"insurance_accepted";N;s:4:"logo";N;s:13:"primary_color";s:7:"#3B82F6";s:15:"secondary_color";s:7:"#EF4444";s:10:"created_at";s:19:"2025-06-06 09:20:14";s:10:"updated_at";s:19:"2025-07-11 12:36:55";s:15:"providers_count";i:4;}s:11:" * original";a:26:{s:2:"id";i:1;s:4:"name";s:24:"Chelmsford Health Centre";s:4:"slug";N;s:11:"description";s:52:"Default healthcare clinic for providers and patients";s:5:"email";s:16:"<EMAIL>";s:5:"phone";s:11:"01245823923";s:7:"website";s:34:"https://chelmsfordhealthcentre.com";s:7:"address";s:13:"Dickens Place";s:4:"city";s:10:"Chelmsford";s:5:"state";s:5:"Essex";s:11:"postal_code";s:7:"CM1 4UU";s:7:"country";s:2:"US";s:15:"operating_hours";N;s:16:"services_offered";N;s:14:"license_number";N;s:6:"tax_id";N;s:9:"is_active";i:1;s:20:"accepts_new_patients";i:1;s:20:"telemedicine_enabled";i:1;s:18:"insurance_accepted";N;s:4:"logo";N;s:13:"primary_color";s:7:"#3B82F6";s:15:"secondary_color";s:7:"#EF4444";s:10:"created_at";s:19:"2025-06-06 09:20:14";s:10:"updated_at";s:19:"2025-07-11 12:36:55";s:15:"providers_count";i:4;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:8:{s:15:"operating_hours";s:5:"array";s:16:"services_offered";s:5:"array";s:18:"insurance_accepted";s:5:"array";s:9:"is_active";s:7:"boolean";s:20:"accepts_new_patients";s:7:"boolean";s:20:"telemedicine_enabled";s:7:"boolean";s:10:"created_at";s:8:"datetime";s:10:"updated_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:22:{i:0;s:4:"name";i:1;s:4:"slug";i:2;s:11:"description";i:3;s:5:"email";i:4;s:5:"phone";i:5;s:7:"website";i:6;s:7:"address";i:7;s:4:"city";i:8;s:5:"state";i:9;s:11:"postal_code";i:10;s:7:"country";i:11;s:15:"operating_hours";i:12;s:16:"services_offered";i:13;s:14:"license_number";i:14;s:6:"tax_id";i:15;s:9:"is_active";i:16;s:20:"accepts_new_patients";i:17;s:20:"telemedicine_enabled";i:18;s:18:"insurance_accepted";i:19;s:4:"logo";i:20;s:13:"primary_color";i:21;s:15:"secondary_color";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:1;O:17:"App\Models\Clinic":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:7:"clinics";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:26:{s:2:"id";i:2;s:4:"name";s:12:"Rainbow Labs";s:4:"slug";N;s:11:"description";N;s:5:"email";s:23:"<EMAIL>";s:5:"phone";s:11:"01245823923";s:7:"website";s:25:"https://rainbowlabs.co.uk";s:7:"address";s:13:"Dickens Place";s:4:"city";s:10:"Chelmsford";s:5:"state";s:5:"Essex";s:11:"postal_code";s:7:"CM1 4UU";s:7:"country";s:2:"US";s:15:"operating_hours";N;s:16:"services_offered";N;s:14:"license_number";N;s:6:"tax_id";N;s:9:"is_active";i:1;s:20:"accepts_new_patients";i:1;s:20:"telemedicine_enabled";i:1;s:18:"insurance_accepted";N;s:4:"logo";N;s:13:"primary_color";s:7:"#3B82F6";s:15:"secondary_color";s:7:"#EF4444";s:10:"created_at";s:19:"2025-06-18 18:20:23";s:10:"updated_at";s:19:"2025-06-18 18:20:23";s:15:"providers_count";i:1;}s:11:" * original";a:26:{s:2:"id";i:2;s:4:"name";s:12:"Rainbow Labs";s:4:"slug";N;s:11:"description";N;s:5:"email";s:23:"<EMAIL>";s:5:"phone";s:11:"01245823923";s:7:"website";s:25:"https://rainbowlabs.co.uk";s:7:"address";s:13:"Dickens Place";s:4:"city";s:10:"Chelmsford";s:5:"state";s:5:"Essex";s:11:"postal_code";s:7:"CM1 4UU";s:7:"country";s:2:"US";s:15:"operating_hours";N;s:16:"services_offered";N;s:14:"license_number";N;s:6:"tax_id";N;s:9:"is_active";i:1;s:20:"accepts_new_patients";i:1;s:20:"telemedicine_enabled";i:1;s:18:"insurance_accepted";N;s:4:"logo";N;s:13:"primary_color";s:7:"#3B82F6";s:15:"secondary_color";s:7:"#EF4444";s:10:"created_at";s:19:"2025-06-18 18:20:23";s:10:"updated_at";s:19:"2025-06-18 18:20:23";s:15:"providers_count";i:1;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:8:{s:15:"operating_hours";s:5:"array";s:16:"services_offered";s:5:"array";s:18:"insurance_accepted";s:5:"array";s:9:"is_active";s:7:"boolean";s:20:"accepts_new_patients";s:7:"boolean";s:20:"telemedicine_enabled";s:7:"boolean";s:10:"created_at";s:8:"datetime";s:10:"updated_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:22:{i:0;s:4:"name";i:1;s:4:"slug";i:2;s:11:"description";i:3;s:5:"email";i:4;s:5:"phone";i:5;s:7:"website";i:6;s:7:"address";i:7;s:4:"city";i:8;s:5:"state";i:9;s:11:"postal_code";i:10;s:7:"country";i:11;s:15:"operating_hours";i:12;s:16:"services_offered";i:13;s:14:"license_number";i:14;s:6:"tax_id";i:15;s:9:"is_active";i:16;s:20:"accepts_new_patients";i:17;s:20:"telemedicine_enabled";i:18;s:18:"insurance_accepted";i:19;s:4:"logo";i:20;s:13:"primary_color";i:21;s:15:"secondary_color";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:23:"top_clinics_by_patients";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:2:{i:0;O:17:"App\Models\Clinic":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:7:"clinics";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:26:{s:2:"id";i:1;s:4:"name";s:24:"Chelmsford Health Centre";s:4:"slug";N;s:11:"description";s:52:"Default healthcare clinic for providers and patients";s:5:"email";s:16:"<EMAIL>";s:5:"phone";s:11:"01245823923";s:7:"website";s:34:"https://chelmsfordhealthcentre.com";s:7:"address";s:13:"Dickens Place";s:4:"city";s:10:"Chelmsford";s:5:"state";s:5:"Essex";s:11:"postal_code";s:7:"CM1 4UU";s:7:"country";s:2:"US";s:15:"operating_hours";N;s:16:"services_offered";N;s:14:"license_number";N;s:6:"tax_id";N;s:9:"is_active";i:1;s:20:"accepts_new_patients";i:1;s:20:"telemedicine_enabled";i:1;s:18:"insurance_accepted";N;s:4:"logo";N;s:13:"primary_color";s:7:"#3B82F6";s:15:"secondary_color";s:7:"#EF4444";s:10:"created_at";s:19:"2025-06-06 09:20:14";s:10:"updated_at";s:19:"2025-07-11 12:36:55";s:14:"patients_count";i:64;}s:11:" * original";a:26:{s:2:"id";i:1;s:4:"name";s:24:"Chelmsford Health Centre";s:4:"slug";N;s:11:"description";s:52:"Default healthcare clinic for providers and patients";s:5:"email";s:16:"<EMAIL>";s:5:"phone";s:11:"01245823923";s:7:"website";s:34:"https://chelmsfordhealthcentre.com";s:7:"address";s:13:"Dickens Place";s:4:"city";s:10:"Chelmsford";s:5:"state";s:5:"Essex";s:11:"postal_code";s:7:"CM1 4UU";s:7:"country";s:2:"US";s:15:"operating_hours";N;s:16:"services_offered";N;s:14:"license_number";N;s:6:"tax_id";N;s:9:"is_active";i:1;s:20:"accepts_new_patients";i:1;s:20:"telemedicine_enabled";i:1;s:18:"insurance_accepted";N;s:4:"logo";N;s:13:"primary_color";s:7:"#3B82F6";s:15:"secondary_color";s:7:"#EF4444";s:10:"created_at";s:19:"2025-06-06 09:20:14";s:10:"updated_at";s:19:"2025-07-11 12:36:55";s:14:"patients_count";i:64;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:8:{s:15:"operating_hours";s:5:"array";s:16:"services_offered";s:5:"array";s:18:"insurance_accepted";s:5:"array";s:9:"is_active";s:7:"boolean";s:20:"accepts_new_patients";s:7:"boolean";s:20:"telemedicine_enabled";s:7:"boolean";s:10:"created_at";s:8:"datetime";s:10:"updated_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:22:{i:0;s:4:"name";i:1;s:4:"slug";i:2;s:11:"description";i:3;s:5:"email";i:4;s:5:"phone";i:5;s:7:"website";i:6;s:7:"address";i:7;s:4:"city";i:8;s:5:"state";i:9;s:11:"postal_code";i:10;s:7:"country";i:11;s:15:"operating_hours";i:12;s:16:"services_offered";i:13;s:14:"license_number";i:14;s:6:"tax_id";i:15;s:9:"is_active";i:16;s:20:"accepts_new_patients";i:17;s:20:"telemedicine_enabled";i:18;s:18:"insurance_accepted";i:19;s:4:"logo";i:20;s:13:"primary_color";i:21;s:15:"secondary_color";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:1;O:17:"App\Models\Clinic":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:7:"clinics";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:26:{s:2:"id";i:2;s:4:"name";s:12:"Rainbow Labs";s:4:"slug";N;s:11:"description";N;s:5:"email";s:23:"<EMAIL>";s:5:"phone";s:11:"01245823923";s:7:"website";s:25:"https://rainbowlabs.co.uk";s:7:"address";s:13:"Dickens Place";s:4:"city";s:10:"Chelmsford";s:5:"state";s:5:"Essex";s:11:"postal_code";s:7:"CM1 4UU";s:7:"country";s:2:"US";s:15:"operating_hours";N;s:16:"services_offered";N;s:14:"license_number";N;s:6:"tax_id";N;s:9:"is_active";i:1;s:20:"accepts_new_patients";i:1;s:20:"telemedicine_enabled";i:1;s:18:"insurance_accepted";N;s:4:"logo";N;s:13:"primary_color";s:7:"#3B82F6";s:15:"secondary_color";s:7:"#EF4444";s:10:"created_at";s:19:"2025-06-18 18:20:23";s:10:"updated_at";s:19:"2025-06-18 18:20:23";s:14:"patients_count";i:0;}s:11:" * original";a:26:{s:2:"id";i:2;s:4:"name";s:12:"Rainbow Labs";s:4:"slug";N;s:11:"description";N;s:5:"email";s:23:"<EMAIL>";s:5:"phone";s:11:"01245823923";s:7:"website";s:25:"https://rainbowlabs.co.uk";s:7:"address";s:13:"Dickens Place";s:4:"city";s:10:"Chelmsford";s:5:"state";s:5:"Essex";s:11:"postal_code";s:7:"CM1 4UU";s:7:"country";s:2:"US";s:15:"operating_hours";N;s:16:"services_offered";N;s:14:"license_number";N;s:6:"tax_id";N;s:9:"is_active";i:1;s:20:"accepts_new_patients";i:1;s:20:"telemedicine_enabled";i:1;s:18:"insurance_accepted";N;s:4:"logo";N;s:13:"primary_color";s:7:"#3B82F6";s:15:"secondary_color";s:7:"#EF4444";s:10:"created_at";s:19:"2025-06-18 18:20:23";s:10:"updated_at";s:19:"2025-06-18 18:20:23";s:14:"patients_count";i:0;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:8:{s:15:"operating_hours";s:5:"array";s:16:"services_offered";s:5:"array";s:18:"insurance_accepted";s:5:"array";s:9:"is_active";s:7:"boolean";s:20:"accepts_new_patients";s:7:"boolean";s:20:"telemedicine_enabled";s:7:"boolean";s:10:"created_at";s:8:"datetime";s:10:"updated_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:22:{i:0;s:4:"name";i:1;s:4:"slug";i:2;s:11:"description";i:3;s:5:"email";i:4;s:5:"phone";i:5;s:7:"website";i:6;s:7:"address";i:7;s:4:"city";i:8;s:5:"state";i:9;s:11:"postal_code";i:10;s:7:"country";i:11;s:15:"operating_hours";i:12;s:16:"services_offered";i:13;s:14:"license_number";i:14;s:6:"tax_id";i:15;s:9:"is_active";i:16;s:20:"accepts_new_patients";i:17;s:20:"telemedicine_enabled";i:18;s:18:"insurance_accepted";i:19;s:4:"logo";i:20;s:13:"primary_color";i:21;s:15:"secondary_color";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}}