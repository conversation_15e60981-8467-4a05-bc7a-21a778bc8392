<?php

namespace App\controllers;

use App\baseClasses\KCBase;
use App\models\MDTemplateManager;
use App\models\KCClinic;
use App\models\KCDoctorClinicMapping;
use Exception;
use WP_User;
use WP_REST_Response;
use WP_REST_Request;

class MDTemplateManagerController extends KCBase {

    public $db;
    private $template_manager;

    /**
     * Constructor
     */
    public function __construct() {
        global $wpdb;
        $this->db = $wpdb;
        $this->template_manager = new MDTemplateManager();
    }

    /**
     * Get templates based on user role and permissions
     */
    public function index($request) {
        error_log('MD Template Manager: get_templates called');
        
        // Debug info
        $params = $request->get_params();
        error_log('Request params: ' . print_r($params, true));
        
        try {
            $user_id = get_current_user_id();
            error_log('MD Template Manager: Current user ID: ' . $user_id);
            
            // If user is not logged in, still try to get all templates
            // This ensures the app doesn't break for non-authenticated users
            if (!$user_id) {
                error_log('MD Template Manager: No user ID, returning all templates');
                $all_templates = $this->db->get_results("SELECT * FROM {$this->db->prefix}md_template_manager WHERE status = 1 ORDER BY created_at DESC");
                
                if ($all_templates) {
                    error_log('MD Template Manager: Found ' . count($all_templates) . ' templates');
                    $response = [
                        'status' => true,
                        'message' => 'Templates retrieved successfully',
                        'data' => $all_templates,
                        'debug' => ['userID' => 0, 'tableExists' => $this->tableExists()]
                    ];
                    return new WP_REST_Response($response, 200);
                } else {
                    error_log('MD Template Manager: No templates found');
                    return new WP_REST_Response([
                        'status' => true,
                        'message' => 'No user templates available',
                        'data' => [],
                        'debug' => ['userID' => 0, 'tableExists' => $this->tableExists()]
                    ], 200);
                }
            }
            
            $user = new WP_User($user_id);
            error_log('MD Template Manager: User roles: ' . implode(', ', $user->roles));
            
            $response = [];

            // Get the templates based on user role
            if (in_array('administrator', $user->roles)) {
                // Admin can see all templates
                $response['data'] = $this->db->get_results("SELECT * FROM {$this->db->prefix}md_template_manager WHERE status = 1 ORDER BY created_at DESC");
            } elseif (in_array('kiviCare_clinic_admin', $user->roles)) {
                // Clinic admin can see clinic templates and their personal templates
                $clinic_id = kcGetClinicIdOfClinicAdmin();
                if (empty($clinic_id)) {
                    $response['data'] = [];
                } else {
                    $response['data'] = $this->template_manager->get_templates_by_clinic($clinic_id);
                }
            } elseif (in_array('kiviCare_doctor', $user->roles)) {
                // Doctors can see their personal templates, their clinics' templates, and public templates
                $response['data'] = $this->template_manager->get_visible_templates($user_id);
            } else {
                // Other users don't see templates
                $response['data'] = [];
            }

            // Format data for display
            if (!empty($response['data'])) {
                foreach ($response['data'] as $key => $template) {
                    // Set user-friendly labels for share status
                    $share_status_labels = [
                        'private' => __('Private', 'kc-lang'),
                        'clinic' => __('Clinic', 'kc-lang'),
                        'public' => __('Public', 'kc-lang')
                    ];
                    
                    // Set the display category name
                    $category_labels = [
                        'general' => __('General', 'kc-lang'),
                        'referral' => __('Referral', 'kc-lang'),
                        'sick_note' => __('Sick Note', 'kc-lang'),
                        'consultation' => __('Consultation', 'kc-lang'),
                        'procedure' => __('Procedure', 'kc-lang')
                    ];
                    
                    $response['data'][$key]->share_status_label = $share_status_labels[$template->share_status] ?? $template->share_status;
                    $response['data'][$key]->category_label = $category_labels[$template->category] ?? $template->category;
                    
                    // Check ownership
                    $response['data'][$key]->is_owner = ($template->doctor_id == $user_id);
                    $response['data'][$key]->is_editable = ($template->doctor_id == $user_id) || 
                                                          (in_array('administrator', $user->roles)) || 
                                                          (in_array('kiviCare_clinic_admin', $user->roles) && $template->clinic_id == kcGetClinicIdOfClinicAdmin());
                    
                    // Get doctor name if exists
                    if (!empty($template->doctor_id)) {
                        $doctor = get_userdata($template->doctor_id);
                        $response['data'][$key]->doctor_name = $doctor ? $doctor->display_name : __('Unknown', 'kc-lang');
                    } else {
                        $response['data'][$key]->doctor_name = $template->is_system ? __('System', 'kc-lang') : '';
                    }
                    
                    // Get clinic name if exists
                    if (!empty($template->clinic_id)) {
                        $clinic = (new KCClinic())->get_by(['id' => $template->clinic_id]);
                        $response['data'][$key]->clinic_name = $clinic ? $clinic->name : __('Unknown', 'kc-lang');
                    } else {
                        $response['data'][$key]->clinic_name = '';
                    }
                }
            }
            
            $response['status'] = true;
            $response['message'] = __('Templates retrieved successfully', 'kc-lang');
            $response['debug'] = [
                'userID' => $user_id,
                'roles' => $user->roles,
                'tableExists' => $this->tableExists(),
                'templateCount' => isset($response['data']) ? count($response['data']) : 0
            ];
            error_log('MD Template Manager: Sending response: ' . print_r($response, true));
            return new WP_REST_Response($response, 200);
            
        } catch (Exception $e) {
            error_log('MD Template Manager: Error: ' . $e->getMessage());
            error_log('MD Template Manager: Trace: ' . $e->getTraceAsString());
            return new WP_REST_Response([
                'status' => false,
                'message' => $e->getMessage(),
                'debug' => [
                    'trace' => $e->getTraceAsString(),
                    'tableExists' => $this->tableExists()
                ]
            ], 500);
        }
    }

    /**
     * Save a new template
     */
    public function save_template($request) {
        try {
            error_log('MDTemplateManagerController: save_template called');
            $params = $request->get_params();
            error_log('Request params: ' . print_r($params, true));
            
            // Check for nonce if provided, but don't block if nonce is missing
            if (isset($params['_ajax_nonce']) && !wp_verify_nonce($params['_ajax_nonce'], 'ajax_nonce')) {
                error_log('MDTemplateManagerController: Invalid nonce provided');
                // Still continue - we're now using permission_callback for authorization
            }
            
            // Use params directly or fall back to the old way
            $request_data = isset($params) && !empty($params) ? $params : $this->request->getInputs();
            $user_id = get_current_user_id();
            $user = new WP_User($user_id);
            
            // Log all available request data for debugging
            error_log('Request data: ' . print_r($request_data, true));
            
            // Validate required fields
            if (empty($request_data['name']) || empty($request_data['category']) || empty($request_data['content'])) {
                error_log('Missing required fields in template save request');
                throw new Exception(__('Name, category and content are required fields', 'kc-lang'));
            }
            
            // Prepare template data
            $template_data = [
                'name' => sanitize_text_field($request_data['name']),
                'category' => sanitize_text_field($request_data['category']),
                'content' => $request_data['content'], // Content may contain HTML
                'doctor_id' => in_array('kiviCare_doctor', $user->roles) ? $user_id : null,
                'clinic_id' => null,
                'is_system' => 0,
                'share_status' => sanitize_text_field($request_data['share_status'] ?? 'private'),
                'status' => 1,
                'created_at' => current_time('Y-m-d H:i:s')
            ];
            
            error_log('Template data to save: ' . print_r($template_data, true));
            
            // If clinic admin, assign to clinic
            if (in_array('kiviCare_clinic_admin', $user->roles)) {
                $template_data['clinic_id'] = kcGetClinicIdOfClinicAdmin();
                
                // If clinic admin is also a doctor, assign doctor_id
                if (in_array('kiviCare_doctor', $user->roles)) {
                    $template_data['doctor_id'] = $user_id;
                }
            }
            
            // Save the template
            $id = $this->template_manager->insert($template_data);
            error_log('Template save result: ' . ($id ? 'Success (ID: '.$id.')' : 'Failed'));
            
            if ($id) {
                $response_data = [
                    'status' => true,
                    'message' => __('Template saved successfully', 'kc-lang'),
                    'id' => $id
                ];
                error_log('Sending successful response: ' . print_r($response_data, true));
                return new WP_REST_Response($response_data, 200);
            } else {
                throw new Exception(__('Failed to save template', 'kc-lang'));
            }
            
        } catch (Exception $e) {
            error_log('Template save error: ' . $e->getMessage());
            error_log('Error trace: ' . $e->getTraceAsString());
            
            $error_response = [
                'status' => false,
                'message' => $e->getMessage(),
                'debug' => [
                    'trace' => $e->getTraceAsString(),
                    'request_data' => isset($request_data) ? $request_data : null
                ]
            ];
            error_log('Sending error response: ' . print_r($error_response, true));
            return new WP_REST_Response($error_response, 500);
        }
    }

    /**
     * Update an existing template
     */
    public function update_template($request) {
        try {
            $params = $request->get_params();
            
            // Check for nonce if provided
            if (isset($params['_ajax_nonce']) && !wp_verify_nonce($params['_ajax_nonce'], 'ajax_nonce')) {
                return new WP_REST_Response([
                    'status' => false,
                    'message' => esc_html__('Unauthorized access', 'kc-lang')
                ], 403);
            }
            
            // Use params directly or fall back to the old way
            $request_data = isset($params) && !empty($params) ? $params : $this->request->getInputs();
            $user_id = get_current_user_id();
            $user = new WP_User($user_id);
            
            // Validate required fields
            if (empty($request_data['id']) || empty($request_data['name']) || empty($request_data['category']) || empty($request_data['content'])) {
                throw new Exception(__('ID, name, category and content are required fields', 'kc-lang'));
            }
            
            // Get the existing template
            $template = $this->template_manager->get_template($request_data['id']);
            
            if (!$template) {
                throw new Exception(__('Template not found', 'kc-lang'));
            }
            
            // Check if user has permission to edit this template
            $has_permission = false;
            
            // Admin can edit any template
            if (in_array('administrator', $user->roles)) {
                $has_permission = true;
            }
            // Clinic admin can edit clinic templates
            else if (in_array('kiviCare_clinic_admin', $user->roles) && $template->clinic_id == kcGetClinicIdOfClinicAdmin()) {
                $has_permission = true;
            }
            // Doctor can edit their own templates
            else if ($template->doctor_id == $user_id) {
                $has_permission = true;
            }
            
            if (!$has_permission) {
                throw new Exception(__('You do not have permission to edit this template', 'kc-lang'));
            }
            
            // System templates cannot be edited
            if ($template->is_system) {
                throw new Exception(__('System templates cannot be edited. Please clone it to make your own version.', 'kc-lang'));
            }
            
            // Prepare updated data
            $template_data = [
                'name' => sanitize_text_field($request_data['name']),
                'category' => sanitize_text_field($request_data['category']),
                'content' => $request_data['content'], // Content may contain HTML
                'share_status' => sanitize_text_field($request_data['share_status'] ?? $template->share_status),
                'updated_at' => current_time('Y-m-d H:i:s')
            ];
            
            // Update the template
            $result = $this->template_manager->update($template_data, ['id' => $request_data['id']]);
            
            if ($result) {
                return new WP_REST_Response([
                    'status' => true,
                    'message' => __('Template updated successfully', 'kc-lang')
                ], 200);
            } else {
                throw new Exception(__('Failed to update template', 'kc-lang'));
            }
            
        } catch (Exception $e) {
            error_log('Template update error: ' . $e->getMessage());
            return new WP_REST_Response([
                'status' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a template (soft delete by setting status = 0)
     */
    public function delete_template($request) {
        try {
            $params = $request->get_params();
            
            // Check for nonce if provided
            if (isset($params['_ajax_nonce']) && !wp_verify_nonce($params['_ajax_nonce'], 'ajax_nonce')) {
                return new WP_REST_Response([
                    'status' => false,
                    'message' => esc_html__('Unauthorized access', 'kc-lang')
                ], 403);
            }
            
            // Use params directly or fall back to the old way
            $request_data = isset($params) && !empty($params) ? $params : $this->request->getInputs();
            $user_id = get_current_user_id();
            $user = new WP_User($user_id);
            
            // Validate required fields
            if (empty($request_data['id'])) {
                throw new Exception(__('Template ID is required', 'kc-lang'));
            }
            
            // Get the existing template
            $template = $this->template_manager->get_template($request_data['id']);
            
            if (!$template) {
                throw new Exception(__('Template not found', 'kc-lang'));
            }
            
            // Check if user has permission to delete this template
            $has_permission = false;
            
            // Admin can delete any template except system templates
            if (in_array('administrator', $user->roles) && !$template->is_system) {
                $has_permission = true;
            }
            // Clinic admin can delete clinic templates
            else if (in_array('kiviCare_clinic_admin', $user->roles) && $template->clinic_id == kcGetClinicIdOfClinicAdmin() && !$template->is_system) {
                $has_permission = true;
            }
            // Doctor can delete their own templates
            else if ($template->doctor_id == $user_id && !$template->is_system) {
                $has_permission = true;
            }
            
            if (!$has_permission) {
                throw new Exception(__('You do not have permission to delete this template', 'kc-lang'));
            }
            
            // Soft delete by updating status
            $result = $this->template_manager->update(['status' => 0], ['id' => $request_data['id']]);
            
            if ($result) {
                return new WP_REST_Response([
                    'status' => true,
                    'message' => __('Template deleted successfully', 'kc-lang')
                ], 200);
            } else {
                throw new Exception(__('Failed to delete template', 'kc-lang'));
            }
            
        } catch (Exception $e) {
            error_log('Template delete error: ' . $e->getMessage());
            return new WP_REST_Response([
                'status' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get details of a single template
     */
    public function get_template_details($request) {
        try {
            $params = $request->get_params();
            
            // Check for nonce if provided
            if (isset($params['_ajax_nonce']) && !wp_verify_nonce($params['_ajax_nonce'], 'ajax_nonce')) {
                return new WP_REST_Response([
                    'status' => false,
                    'message' => esc_html__('Unauthorized access', 'kc-lang')
                ], 403);
            }
            
            // Use params directly or fall back to the old way
            $request_data = isset($params) && !empty($params) ? $params : $this->request->getInputs();
            
            // Validate required fields
            if (empty($request_data['id'])) {
                throw new Exception(__('Template ID is required', 'kc-lang'));
            }
            
            // Get the template
            $template = $this->template_manager->get_template($request_data['id']);
            
            if (!$template) {
                throw new Exception(__('Template not found', 'kc-lang'));
            }
            
            // Check if user has access to this template
            $user_id = get_current_user_id();
            $user = new WP_User($user_id);
            $has_access = false;
            
            // Admin can access any template
            if (in_array('administrator', $user->roles)) {
                $has_access = true;
            }
            // Clinic admin can access clinic templates
            else if (in_array('kiviCare_clinic_admin', $user->roles) && $template->clinic_id == kcGetClinicIdOfClinicAdmin()) {
                $has_access = true;
            }
            // Doctor can access their templates, their clinic's templates and public templates
            else if (in_array('kiviCare_doctor', $user->roles)) {
                if ($template->doctor_id == $user_id || $template->share_status == 'public' || $template->is_system) {
                    $has_access = true;
                } else if ($template->share_status == 'clinic') {
                    // Check if doctor belongs to the clinic
                    $doctor_clinics = (new KCDoctorClinicMapping())->get_by(['doctor_id' => $user_id]);
                    foreach($doctor_clinics as $clinic) {
                        if ($clinic->clinic_id == $template->clinic_id) {
                            $has_access = true;
                            break;
                        }
                    }
                }
            }
            
            if (!$has_access) {
                throw new Exception(__('You do not have access to this template', 'kc-lang'));
            }
            
            // Add additional info to the template
            if ($template->doctor_id) {
                $doctor = get_userdata($template->doctor_id);
                $template->doctor_name = $doctor ? $doctor->display_name : __('Unknown', 'kc-lang');
            } else {
                $template->doctor_name = $template->is_system ? __('System', 'kc-lang') : '';
            }
            
            if ($template->clinic_id) {
                $clinic = (new KCClinic())->get_by(['id' => $template->clinic_id]);
                $template->clinic_name = $clinic ? $clinic->name : __('Unknown', 'kc-lang');
            } else {
                $template->clinic_name = '';
            }
            
            // Get category label
            $category_labels = [
                'general' => __('General', 'kc-lang'),
                'referral' => __('Referral', 'kc-lang'),
                'sick_note' => __('Sick Note', 'kc-lang'),
                'consultation' => __('Consultation', 'kc-lang'),
                'procedure' => __('Procedure', 'kc-lang')
            ];
            $template->category_label = $category_labels[$template->category] ?? $template->category;
            
            // Check if user can edit this template
            $template->is_editable = ($template->doctor_id == $user_id) || 
                                      (in_array('administrator', $user->roles)) || 
                                      (in_array('kiviCare_clinic_admin', $user->roles) && $template->clinic_id == kcGetClinicIdOfClinicAdmin());
            
            // Return the template
            return new WP_REST_Response([
                'status' => true,
                'data' => $template,
                'message' => __('Template details retrieved successfully', 'kc-lang')
            ], 200);
            
        } catch (Exception $e) {
            error_log('Template details error: ' . $e->getMessage());
            return new WP_REST_Response([
                'status' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clone a template (create a copy for the current user)
     */
    public function clone_template($request) {
        try {
            $params = $request->get_params();
            
            // Check for nonce if provided
            if (isset($params['_ajax_nonce']) && !wp_verify_nonce($params['_ajax_nonce'], 'ajax_nonce')) {
                return new WP_REST_Response([
                    'status' => false,
                    'message' => esc_html__('Unauthorized access', 'kc-lang')
                ], 403);
            }
            
            // Use params directly or fall back to the old way
            $request_data = isset($params) && !empty($params) ? $params : $this->request->getInputs();
            $user_id = get_current_user_id();
            $user = new WP_User($user_id);
            
            // Validate required fields
            if (empty($request_data['id'])) {
                throw new Exception(__('Template ID is required', 'kc-lang'));
            }
            
            // Get the source template
            $source = $this->template_manager->get_template($request_data['id']);
            
            if (!$source) {
                throw new Exception(__('Template not found', 'kc-lang'));
            }
            
            // Check if user can access this template
            $has_access = false;
            
            // Admin can clone any template
            if (in_array('administrator', $user->roles)) {
                $has_access = true;
            }
            // Clinic admin can clone clinic templates
            else if (in_array('kiviCare_clinic_admin', $user->roles) && $source->clinic_id == kcGetClinicIdOfClinicAdmin()) {
                $has_access = true;
            }
            // Doctor can clone their templates, their clinic's templates and public templates
            else if (in_array('kiviCare_doctor', $user->roles)) {
                if ($source->doctor_id == $user_id || $source->share_status == 'public' || $source->is_system) {
                    $has_access = true;
                } else if ($source->share_status == 'clinic') {
                    // Check if doctor belongs to the clinic
                    $doctor_clinics = (new KCDoctorClinicMapping())->get_by(['doctor_id' => $user_id]);
                    foreach($doctor_clinics as $clinic) {
                        if ($clinic->clinic_id == $source->clinic_id) {
                            $has_access = true;
                            break;
                        }
                    }
                }
            }
            
            if (!$has_access) {
                throw new Exception(__('You do not have access to clone this template', 'kc-lang'));
            }
            
            // Prepare new template data
            $clone_data = [
                'name' => sprintf(__('Copy of %s', 'kc-lang'), $source->name),
                'category' => $source->category,
                'content' => $source->content,
                'doctor_id' => in_array('kiviCare_doctor', $user->roles) ? $user_id : null,
                'clinic_id' => null,
                'is_system' => 0,
                'share_status' => 'private', // Default to private for cloned templates
                'status' => 1,
                'created_at' => current_time('Y-m-d H:i:s')
            ];
            
            // If clinic admin, assign to clinic
            if (in_array('kiviCare_clinic_admin', $user->roles)) {
                $clone_data['clinic_id'] = kcGetClinicIdOfClinicAdmin();
                
                // If clinic admin is also a doctor, assign doctor_id
                if (in_array('kiviCare_doctor', $user->roles)) {
                    $clone_data['doctor_id'] = $user_id;
                }
            }
            
            // Create the cloned template
            $id = $this->template_manager->insert($clone_data);
            
            if ($id) {
                return new WP_REST_Response([
                    'status' => true,
                    'message' => __('Template cloned successfully', 'kc-lang'),
                    'id' => $id
                ], 200);
            } else {
                throw new Exception(__('Failed to clone template', 'kc-lang'));
            }
            
        } catch (Exception $e) {
            error_log('Template clone error: ' . $e->getMessage());
            return new WP_REST_Response([
                'status' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Import default templates from the templates.json file
     */
    public function import_default_templates($request) {
        try {
            $params = $request->get_params();
            
            // Check for nonce if provided
            if (isset($params['_ajax_nonce']) && !wp_verify_nonce($params['_ajax_nonce'], 'ajax_nonce')) {
                return new WP_REST_Response([
                    'status' => false,
                    'message' => esc_html__('Unauthorized access', 'kc-lang')
                ], 403);
            }
            // Check if templates already exist
            $templates_count = $this->db->get_var("SELECT COUNT(*) FROM {$this->db->prefix}md_template_manager WHERE is_system = 1");
            
            if ($templates_count > 0) {
                throw new Exception(__('Default templates have already been imported', 'kc-lang'));
            }
            
            // Get templates from JSON file
            $templates_file = KIVI_CARE_DIR . 'resources/js/lib/templates.json';
            
            if (!file_exists($templates_file)) {
                throw new Exception(__('Templates file not found', 'kc-lang'));
            }
            
            $templates_content = file_get_contents($templates_file);
            $templates = json_decode($templates_content, true);
            
            if (empty($templates)) {
                throw new Exception(__('No templates found in the templates file', 'kc-lang'));
            }
            
            // Import templates
            $this->template_manager->insert_system_templates($templates);
            
            // Return success
            return new WP_REST_Response([
                'status' => true,
                'message' => __('Default templates imported successfully', 'kc-lang')
            ], 200);
            
        } catch (Exception $e) {
            error_log('Import templates error: ' . $e->getMessage());
            return new WP_REST_Response([
                'status' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Register the templates page in the admin menu
     */
    public function register_menu() {
        // This will be handled in Vue router
    }

    /**
     * Initialize the template manager
     */
    public function init() {
        // Add hook to register the database table in KCActivate
        add_action('kcpro_init_db_tables', [$this, 'register_table']);
        
        // Add filter for encounter summarize
        add_filter('kivicare_encounter_summarize', [$this, 'process_user_template'], 10, 2);
        
        // Load the controller
        new self();
    }

    /**
     * Register the database table
     */
    public function register_table() {
        require_once KIVI_CARE_DIR . 'app/database/md-template-manager-db.php';
    }
    
    /**
     * Check if the template manager table exists
     * 
     * @return bool
     */
    private function tableExists() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'md_template_manager';
        return $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
    }
    
    /**
     * Process user templates for encounter summarize
     * 
     * @param array $data The data to be processed
     * @param array $request_data The original request data
     * @return array The processed data
     */
    public function process_user_template($data, $request_data) {
        error_log('Process user template called with request data: ' . print_r($request_data, true));
        
        // Check if this is a user template
        if (!empty($request_data['isUserTemplate']) && !empty($request_data['templateContent'])) {
            error_log('Processing user template with content length: ' . strlen($request_data['templateContent']));
            
            // Get the template content
            $template_content = $request_data['templateContent'];
            
            // Replace variables in the template content with actual data
            $template_content = $this->replace_template_variables($template_content, $request_data);
            
            error_log('Template content after variable replacement: ' . substr($template_content, 0, 100) . '...');
            
            // Replace the template content in the data
            $data['template_content'] = $template_content;
            
            error_log('Template processing completed successfully');
        } else {
            error_log('Invalid template data: isUserTemplate=' . 
                     (isset($request_data['isUserTemplate']) ? var_export($request_data['isUserTemplate'], true) : 'undefined') . 
                     ', templateContent=' . 
                     (isset($request_data['templateContent']) ? (strlen($request_data['templateContent']) > 0 ? 'present' : 'empty') : 'undefined'));
        }
        
        return $data;
    }
    
    /**
     * Replace template variables with actual data
     * 
     * @param string $content The template content
     * @param array $request_data The request data with patient, doctor, clinic and encounter information
     * @return string The processed content with variables replaced
     */
    private function replace_template_variables($content, $request_data) {
        // Regular expression to find all ${variable.name} patterns
        $variable_regex = '/\${([^}]+)}/';
        
        // Find all variables in the content
        preg_match_all($variable_regex, $content, $matches);
        
        // If no variables found, return the content as is
        if (empty($matches[1])) {
            return $content;
        }
        
        // Get variables to replace
        $variables = $matches[1];
        
        // Create a mapping of variables to their values
        $replacements = [];
        
        // Loop through each variable and prepare replacement
        foreach ($variables as $variable) {
            $value = $this->get_variable_value($variable, $request_data);
            $replacements['${' . $variable . '}'] = $value;
        }
        
        // Replace all variables in the content
        return str_replace(array_keys($replacements), array_values($replacements), $content);
    }
    
    /**
     * Get the value of a variable based on its name and request data
     * 
     * @param string $variable The variable name in format "section.field" (e.g. "patient.name")
     * @param array $request_data The request data
     * @return string The variable value or empty string if not found
     */
    private function get_variable_value($variable, $request_data) {
        // Parse variable name (format: section.field)
        $parts = explode('.', $variable);
        
        if (count($parts) !== 2) {
            return '';
        }
        
        $section = $parts[0];
        $field = $parts[1];
        
        // Handle dynamic date/time variables
        if ($section === 'date' && $field === '') {
            return date('d/m/Y');
        } elseif ($section === 'time' && $field === '') {
            return date('H:i');
        }
        
        // Handle patient variables
        if ($section === 'patient') {
            $patient_data = !empty($request_data['patient_details']) ? $request_data['patient_details'] : [];
            
            switch ($field) {
                case 'name':
                    return $patient_data['patient_name'] ?? '';
                case 'unique_id':
                    return $patient_data['patient_unique_id'] ?? '';
                case 'dob':
                    return $patient_data['dob'] ?? '';
                case 'age':
                    // Calculate age if DOB is available
                    if (!empty($patient_data['dob'])) {
                        $dob = new \DateTime($patient_data['dob']);
                        $now = new \DateTime();
                        $interval = $now->diff($dob);
                        return $interval->y;
                    }
                    return '';
                case 'gender':
                    return $patient_data['gender'] ?? '';
                case 'email':
                    return $patient_data['patient_email'] ?? '';
                case 'mobile_number':
                    return $patient_data['mobile_number'] ?? '';
                case 'address':
                    return $patient_data['address'] ?? '';
                case 'city':
                    return $patient_data['city'] ?? '';
                case 'country':
                    return $patient_data['country'] ?? '';
                case 'postal_code':
                    return $patient_data['postal_code'] ?? '';
                default:
                    return '';
            }
        }
        
        // Handle doctor variables
        if ($section === 'doctor') {
            $doctor_id = !empty($request_data['doctor_id']) ? $request_data['doctor_id'] : 0;
            
            if ($doctor_id) {
                $doctor = get_userdata($doctor_id);
                
                switch ($field) {
                    case 'name':
                        return $doctor ? $doctor->display_name : '';
                    case 'speciality':
                        return get_user_meta($doctor_id, 'specialties', true) ?? '';
                    case 'qualification':
                        return get_user_meta($doctor_id, 'qualifications', true) ?? '';
                    default:
                        return '';
                }
            }
        }
        
        // Handle clinic variables
        if ($section === 'clinic') {
            $clinic_id = !empty($request_data['clinic_id']) ? $request_data['clinic_id'] : 0;
            
            if ($clinic_id) {
                global $wpdb;
                $clinic = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}kc_clinics WHERE id = {$clinic_id}");
                
                switch ($field) {
                    case 'name':
                        return $clinic ? $clinic->name : '';
                    case 'address':
                        return $clinic ? $clinic->address : '';
                    case 'contact':
                        return $clinic ? $clinic->telephone_no : '';
                    case 'email':
                        return $clinic ? $clinic->email : '';
                    default:
                        return '';
                }
            }
        }
        
        // Handle encounter variables
        if ($section === 'encounter') {
            switch ($field) {
                case 'id':
                    return $request_data['encounter_id'] ?? '';
                case 'concerns':
                    return isset($request_data['concerns']) ? $request_data['concerns'] : '';
                case 'history':
                    return isset($request_data['history']) ? $request_data['history'] : '';
                case 'examination':
                    return isset($request_data['examination']) ? $request_data['examination'] : '';
                case 'diagnosis':
                    return isset($request_data['diagnosis']) ? $request_data['diagnosis'] : '';
                case 'plan':
                    return isset($request_data['plan']) ? $request_data['plan'] : '';
                case 'medical_history':
                    return isset($request_data['medical_history']) ? $request_data['medical_history'] : '';
                case 'allergies':
                    return isset($request_data['allergies']) ? $request_data['allergies'] : '';
                case 'medications':
                    return isset($request_data['medications']) ? $request_data['medications'] : '';
                case 'date':
                    $encounter_id = $request_data['encounter_id'] ?? 0;
                    if ($encounter_id) {
                        global $wpdb;
                        $date = $wpdb->get_var("SELECT created_at FROM {$wpdb->prefix}kc_patient_encounters WHERE id = {$encounter_id}");
                        return $date ? date('d/m/Y', strtotime($date)) : '';
                    }
                    return '';
                default:
                    return '';
            }
        }
        
        // Handle appointment variables
        if ($section === 'appointment') {
            $appointment_id = !empty($request_data['appointment_id']) ? $request_data['appointment_id'] : 0;
            
            if ($appointment_id) {
                global $wpdb;
                $appointment = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}kc_appointments WHERE id = {$appointment_id}");
                
                switch ($field) {
                    case 'date':
                        return $appointment ? date('d/m/Y', strtotime($appointment->appointment_start_date)) : '';
                    case 'time':
                        return $appointment ? date('H:i', strtotime($appointment->appointment_start_time)) : '';
                    default:
                        return '';
                }
            }
        }
        
        return '';
    }
}