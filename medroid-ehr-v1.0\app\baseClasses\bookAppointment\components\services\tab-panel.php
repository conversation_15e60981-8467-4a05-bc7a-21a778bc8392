<div class="p-6">
    <!-- Header with title and search -->
    <div class="flex justify-between items-center mb-6 flex-wrap gap-4">
        <h3 class="text-xl font-semibold text-gray-900">Select Service</h3>
        <div class="relative w-full">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search w-4 h-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2">
                <circle cx="11" cy="11" r="8"></circle>
                <path d="m21 21-4.3-4.3"></path>
            </svg>
            <input type="text" id="serviceSearch" placeholder="Search..."
             style="padding-left: 2.5rem;"
                class="pl-10 py-2 rounded-md border border-gray-300 focus:ring-2 focus:ring-indigo-500 focus:outline-none text-sm w-full" />
        </div>
    </div>

    <!-- Service type filters -->
    <div class="md:flex space-x-2 mb-6">
        <button type="button" class="filter-button px-4 py-2 rounded-md text-sm font-medium bg-gray-100 hover:bg-gray-200 bg-indigo-600 text-white"
            data-filter="all">All</button>
        <button type="button"
            class="filter-button px-4 py-2 rounded-md text-sm font-medium transition-colors inline-flex items-center bg-gray-100 text-gray-600 hover:bg-gray-200"
            data-filter="virtual">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-video w-4 h-4 mr-2">
                <path d="m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5"></path>
                <rect x="2" y="6" width="14" height="12" rx="2"></rect>
            </svg>
            Virtual
        </button>
        <button type="button"
            class="filter-button px-4 py-2 rounded-md text-sm font-medium transition-colors inline-flex items-center bg-gray-100 text-gray-600 hover:bg-gray-200"
            data-filter="clinic">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-users w-4 h-4 mr-2">
                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                <circle cx="9" cy="7" r="4"></circle>
                <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
            </svg>
            In-Clinic
        </button>
    </div>

    <!-- Service list -->
    <div id="serviceLists" class="space-y-4">
        <span class="loader-class hidden">
            <!-- Loader placeholder -->
            <div class="flex justify-center items-center py-6">
                <div class="double-lines-spinner"></div>
            </div>
        </span>

        <!-- Category cards can be loaded dynamically -->
    </div>
</div>

<script>
    // Get the filter buttons and service list container
    const filterButtons = document.querySelectorAll('.filter-button');
    const serviceList = document.getElementById('serviceLists');

    // Add click event listeners to the filter buttons
    filterButtons.forEach(button => {
        button.addEventListener('click', () => {
            const filterType = button.getAttribute('data-filter');

            // Remove 'active' class from all buttons
            filterButtons.forEach(btn => btn.classList.remove('bg-indigo-600', 'text-white'));

            // Add 'active' class to the clicked button
            button.classList.remove('text-gray-600');
            button.classList.add('bg-indigo-600', 'text-white');

            // Filter the service items based on the selected type
            filterServices(filterType);
        });
    });

    // Function to filter the service items
    function filterServices(type) {
        const serviceItems = serviceList.querySelectorAll('.service-item');

        serviceItems.forEach(item => {
            if (type === 'all') {
                item.style.display = 'block';
            } else if (type === 'virtual' && item.classList.contains('virtual')) {
                item.style.display = 'block';
            } else if (type === 'clinic' && item.classList.contains('clinic')) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    }
</script>