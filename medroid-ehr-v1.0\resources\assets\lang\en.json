{"activity_log": {"activity_logs": "Activity Logs", "activity_type": "Activity Type", "all_types": "All Types", "from_date": "From Date", "to_date": "To Date", "apply_filter": "Apply Filter", "reset": "Reset", "id": "ID", "user": "User", "role": "Role", "description": "Description", "date_time": "Date & Time", "loading": "Loading...", "no_records": "No activity logs found", "showing": "Showing", "of": "of", "entries": "entries", "previous": "Previous", "next": "Next", "error": "Error", "error_fetching_logs": "Error fetching activity logs", "ok": "OK"}, "contacts": {"contacts_directory": "Contacts", "add_contact": "Add Contact", "edit_contact": "Edit Contact", "search_contacts": "Search contacts...", "all_types": "All Types", "type_general": "General", "type_clinic": "Clinic", "type_doctor": "Doctor", "type_pharmacy": "Pharmacy", "name": "Name", "type": "Type", "email": "Email", "phone": "Phone", "address": "Address", "city": "City", "state": "State", "postal_code": "Postal Code", "country": "Country", "clinic": "Clinic", "notes": "Notes", "actions": "Actions", "no_contacts_found": "No contacts found", "contact_details": "Contact Details", "confirm_delete": "Are you sure you want to delete this contact?", "delete_success": "Contact deleted successfully", "add_success": "Contact added successfully", "update_success": "Contact updated successfully", "name_required": "Name is required", "type_required": "Type is required", "invalid_email": "Invalid email address", "email_or_phone_required": "Email or phone is required", "global_contact": "Global Contact (visible to all clinics)", "global_contact_help": "Global contacts are visible to all clinics and cannot be edited by clinic users", "associate_with_clinic": "Associate with Clinic", "status_active": "Active", "status_inactive": "Inactive"}, "common": {"patient_check_in_link": "Patient Check-In Link", "share_check_in_link": "Share this link with patients to allow them to check in for their appointments. This link will expire in 20 days for security.", "open": "Open", "generating_link": "Generating secure check-in link...", "refresh_link": "Refresh link", "loading": "Loading...", "clinic_not_found": "Clinic information not found", "link_expires_notice": "Note: Check-in links expire after 20 days for security reasons. You can generate a new link anytime.", "kivicare_Management_system": "Medroid - Clinic & Patient Management System (EHR)", "stripe_payment_currency_match": "Note: The Stripe payment currency must be the same as the service price currency", "encounter_body_chart": "Consultation body chart", "theme_mode": "Theme mode", "menu_bar_position": "Menu bar position", "menu_items": "Menu items", "select_menu_items": "Select menu items", "image_template": "Image template", "image": "Image", "default": "<PERSON><PERSON><PERSON>", "add_new_image": "Add new image", "replace": "Replace", "new": "New", "file": "File", "body_chart": "Body chart", "clone": "<PERSON><PERSON>", "image_handle_preference": "Image Handling Preference (when image save after editing)", "body_chart_delete_confirm_message": "Press yes to delete body chart details.", "image_delete_confirm_message": "Press yes to delete image details", "create_new_image": "Create New image", "replace_orginal_image": "Replace original image", "same_image_exist_in_template": "The selected image is already set as the template. Please choose a different image.", "body_chart_addons": "Consultation body chart", "image_title": "Image title", "image_description": "Image description", "enter_image_title": "Enter image title", "enter_image_description": "Enter image description", "select_template": "Select Template", "upload_new_image": "Upload new image", "save_image": "Save image", "view_report": "View report", "reset": "Reset", "download": "Download", "filter_by_description": "Filter by description", "last_updated_by": "Last updated by", "filter_by_last_updated_by": "Filter by Last updated", "search_body_chart_data_global_placeholder": "Search body chart data by Id, name, updated user name....", "stripe_payment": "Stripe payment", "publishable_key": "Publishable key", "enter_publishable_key": "Enter publishable key", "publishable_key_required": "Stripe Publishable key is required", "stripe_currency_required": "Stripe currency is required", "tax_rate": "Tax rate", "add_tax": "Add tax", "new_tax": "New tax", "custom_notification": "Custom notification", "get": "GET", "post": "POST", "headers": "Headers", "enter_key": "Enter key", "enter_value": "Enter value", "add_header": "Add header", "add_query_parameter": "Add query parameter", "add_dynamic_key": "Add dynamic key", "query_parameters": "Query Parameters", "send_request": "Send Request", "save_custom_notification": "Save custom notification", "enter_name": "Enter name", "notification_type": "Notification Type", "sms": "SMS", "whatsapp": "Whatsapp", "status_code": "Status Code", "form": "Form", "response": "Response", "dynamic_keys": "Dynamic keys", "collections": "Collections", "tax": "Tax", "value": "Value", "custom_form": "Custom Form", "please_fill_all_required_fields": "Please fill all required fields", "custom_form_list": "Custom form list", "add_form": "Add form", "search_custom_form_data_global_placeholder": "Search by name,module type and id", "form_title": "Form title", "form_title_color": "Form title color", "form_title_alignment": "Form title alignment", "add_field_classes": "Add field classes", "class": "Class", "heading_title": "Heading title", "heading_tag": "Heading tag", "module_type": "Module type", "form_icon": "Form icon", "form_icon_placeholder": "Enter font icon class", "show_if_appointment_status": "Show if Appointment status", "show_in": "Show in", "custom_form_appointment_note": "Note: If encounter is selected, form will only be displayed if there has been an appointment encounter", "custom_form_clinic_note": "Note: To show form to all clinics, please leave the selection blank.", "please_add_form_fields": "Please add form fields", "please_select_form_status": "Please select form status", "please_select_module_type": "Please select module type", "please_enter_field_label": "Please enter field label", "please_select_field_type": "Please select field type", "please_select_file_types": "Please select file types", "please_enter_options": "Please enter options", "enabling_twilio_whatsApp_will_disable_custom_notification_WhatsApp": "Enabling <PERSON><PERSON><PERSON>sApp will disable custom notification WhatsApp", "enabling_twilio_sms_will_disable_custom_notification_sms": "Enabling Twilio SMS will disable custom notification SMS", "enabling_custom_notification_whatsApp_will_disable_twillo_WhatsApp": "Enabling custom notification WhatsApp will disable Twilio WhatsApp", "enabling_custom_notification_sms_will_disable_twillo_sms": "Enabling custom notification SMS will disable <PERSON><PERSON><PERSON> SMS", "enable_sms": "Enable SMS", "enable_whatsapp": "Enable WhatsApp", "no_tax_found": "No tax found", "tax_doctor_note": "Note: To apply tax to all doctors, please leave the selection blank.", "tax_service_note": "Note: To apply tax to all services, please leave the selection blank.", "tax_clinic_note": "Note: To apply tax to all clinics, please leave the selection blank.", "search_tax_data_global_placeholder": "Search by name,status....", "no_appointments": "No Appointments Found", "cancel": "Cancel", "clear": "Clear", "undo": "Undo", "signature": "Signature", "encounter_id": "Consultation ID", "total_rows": "Total Rows", "rows_selected": "Rows selected", "apply": "Apply", "total_rows_inserted": "Total Rows Inserted", "import_more_file": "Import More File", "appointment_module": "Appointment Module", "encounter_module": "Consultation Module", "clinical_detail_module": "Clinical Detail Module", "prescription_module": "Prescription Module", "clinic_module": "Clinic Module", "patient_module": "<PERSON><PERSON>", "doctor_module": "Doctor <PERSON><PERSON><PERSON>", "receptionist_module": "Receptionist <PERSON><PERSON><PERSON>", "service_module": "Service Module", "session_module": "Session Module", "billing_module": "Billing <PERSON>", "holiday_module": "Absence Module", "dashboard_module": "Dashboard Module", "custom_field_module": "Custom field Module", "static_data_module": "Static/Listing data Module", "other_module": "Other Module", "patient_report_module": "Patient report Module", "import": "Import", "file_type": "File type", "upload_file": "Upload File", "clinic_admin": "Clinic admin", "removed_clinics_doctor_msg": "Removed clinics will delete service and session data of this doctor and removed clinics.", "razorpay": "Razorpay", "razorpay_currency_notice": "Note: The Razorpay currency must be the same as the service price currency", "send_notification_when_user_register": "Send Notification when user register", "import_data": "Import data", "registration_shortcode_setting": "Registration Shortcode  Setting", "default_status_when_doctor_register": "Default status when doctor register", "default_status_when_receptionist_register": "Default status when receptionist register", "default_status_when_patient_register": "Default status when patient register", "encounter_not_close": "Consultation is not close,please first close encounter  then checkout", "select_allowed_file_type": "Select allowed file type", "accept_all_file_type": "To accept all type of file select all option from dropdown option", "clinic_appointment_count": "Clinic Appointment Count", "doctor_appointment_count": "Doctor Appointment Count", "message": "SMS/Whatsapp", "note_notification": "Note : If notification is on demo import will take time", "add_session": "Add Doctors Session", "detail": "Detail", "please_share_your_experience": "Please Share Your Experience", "date": "Date", "not_verified": "Not Verified", "verified": "Verified", "demo_user": "Create Selected Demo User", "close": "Close", "closed": "Closed", "test": "Test", "select_option": "-- Select option --", "all": "All", "required": "Required.", "back_to_wordpress": "Back To Wordpress", "update": "Update", "my_profile": "My Profile", "change_password": "Change Password", "logout": "Logout", "choose_file": "Choose file", "no_file_chosen": "No file Chosen", "full_screen": "Full Screen", "warning_zoom_configuration": "Warning: Please save you zoom configuration", "zoom_configuration_link": "Zoom configuration link", "dob": "DOB", "dob_required": "Date of birth is required", "gender": "Gender", "gender_required": "Gender is required", "male": "Male", "female": "Female", "other": "Other", "service": "Service", "services": "Services", "service_add": "Add Service", "sr_no": "Sr no", "item_name": "Item name", "price": "Price", "quantity": "Quantity", "total": "Total", "no_records_found": "No records found", "_note": "Note", "note": "Note: Type and press enter to create new service", "status": "Status", "action": "Action", "title": "Title", "name": "Name", "doctor": "Doctor", "receptionist": "Receptionist", "doctors": "Doctors", "patient": "Patient", "fname": "First Name", "fname_required": "First name is required.", "lname": "Last Name", "lname_required": "Last name is required.", "email": "Email", "email_required": "Email is required.", "password": "Password", "pwd_required": "Password is required.", "repeat_pwd": "Repeat Password", "repeat_password_required": "Repeat Password is required.", "pwd_not_match": "New password and confirm password does not match.", "login_btn": "<PERSON><PERSON>", "sign_up": "Sign Up", "no": "No:#", "dr": "Dr.", "filters": "Filters", "add_filter": "Apply filters", "close_filter": "Close filter", "back": "Back", "save": "Save", "url": "url", "icon": "Icon", "clinic_admin_email": "Clinic Admin Email", "invalid_email": "Invalid email format", "active": "Active", "inactive": "Inactive", "name_required": "Name is required", "email_address": "Email address", "contact_info": "Contact information", "settings": "Settings", "en_dis_module": "Enable/Disable Modules", "fname_validation_1": "First name allows only alphabetic value (not allowed space)", "fname_validation_2": "First name length should be between 2 to 15 character", "lname_validation_1": "Last name allows only alphabetic value (not allowed space)", "lname_validation_2": "Last name length should be between 2 to 15 character", "contact": "Contact", "contact_required": "Contact is required.", "contact_validation_1": "Contact number length should be between 6 to 15 digits", "contact_validation_2": "Invalid contact number format", "telemed": "Telemed", "to": "To", "time": "Time", "contact_no": "Contact no", "contact_num_required": "Contact number is required", "city": "City", "city_required": "City is required", "city_validation_1": "City name allows only alphabetic value", "city_validation_2": "City maximum length should be 30 character", "state": "State", "state_validation_1": "State name allows only alphabetic value", "state_validation_2": "State maximum length should be 30 character", "country": "Country", "country_required": "Country is required", "country_validation_1": "Country name allows only alphabetic value", "country_validation_2": "Country maximum length should be 30 character", "address": "Address", "address_required": "Address is required", "postal_code": "Postal code", "postal_code_required": "Postal code is required", "postal_code_validation_1": "Invalid postal code format", "postal_code_validation_2": "Postal code should be maximum 12 digits", "profile": "Profile", "static_data": "Static Data", "handle_request": "Handle Request", "email_to_get_help": "Other than this many more fine-tunings and tweaks are done. Please <NAME_EMAIL> if you face any issues with the update.", "note_options": "Note: Type option name and press enter to add new option", "note_1": "1) If you face any issue then try deactivating and activating the plugin or contact us.", "note_2": "2) If you want to revert old version. PLease install", "wp_rollback": "wp-rollback", "plugin": "plugin.", "keep_improving": "We will keep improving with your support! Thank You!", "currency_setting": "<PERSON><PERSON><PERSON><PERSON>", "module": " <PERSON><PERSON><PERSON>", "i_understand": "I Understand", "version": "Important! Major Version update!! (V2.0.0)", "microsoft": "Microsoft", "google": "Google", "outlook": "Outlook", "yahoo": "Yahoo", "read_notice": "Please read this below log before moving forward :", "faced_issue": "Faced issues ?", "if_use_older_version": "If you face problems with this version and you want to continue with old version then please install & use", "check_video": "For smooth migration to new version check following Video guide:", "kivicare_v2": "Medroid Upgrade V2.0.0", "appointment_flow": "Appointment Flow", "basic_details": "Basic details", "close_form_btn": "Close form", "add": "Add", "edit": "Edit", "questions": "Health Question", "enabled_kivicare_patient_report": "Enabled Patient Report Template", "disabled_kivicare_patient_report": "Disabled Patient Report Template", "enabled_kivicare_user_verified": "Enabled User Verified Template", "disabled_kivicare_user_verified": "Disabled User Verified Template", "enabled_kivicare_admin_new_user_register": "Enabled Admin New User Register", "disabled_kivicare_admin_new_user_register": "Disabled Admin New User Register", "enabled_kivicare_book_prescription": "Enabled Patient Prescription Template", "disabled_kivicare_book_prescription": "Disabled Patient Prescription Template", "enabled_kivicare_book_appointment_reminder ": "Enabled - Patient  Appointment ReminderTemplate", "disabled_kivicare_book_appointment_reminder": "Disabled - Patient  Appointment ReminderTemplate", "enabled_kivicare_clinic_book_appointment": "Enabled - Clinic Booked Appointment Template", "disabled_kivicare_clinic_book_appointment": "Disabled - Clinic Booked Appointment Template", "enabled_kivicare_add_appointment": "Enabled - Appointment Add SMS Template", "disabled_kivicare_add_appointment": "Disabled - Appointment Add SMS Template", "enabled_kivicare_encounter_close": "Enabled - Consultation Close Add SMS Template", "disabled_kivicare_encounter_close": "Disabled - Consultation Close Add SMS Template", "enabled_kivicare_receptionist_register": "Enabled Medroid Receptionist Register", "disabled_kivicare_receptionist_register": "Disabled Medroid Receptionist Register", "enabled_kivicare_doctor_registration": "Enabled Medroid Doctor Registration", "disabled_kivicare_doctor_registration": "Disabled Medroid Doctor Registration", "enabled_kivicare_book_appointment": "Enabled Medroid Book Appointment", "disabled_kivicare_book_appointment": "Disabled Medroid Book Appointment", "enabled_kivicare_resend_user_credential": "Enabled Resend user credentials", "disabled_kivicare_resend_user_credential": "Disabled Resend user credentials", "enabled_kivicare_cancel_appointment": "Enabled Cancel appointment", "disabled_kivicare_cancel_appointment": "Disabled <PERSON><PERSON> appointment", "enabled_kivicare_patient_register": "Enabled Patient Registration Template", "disabled_kivicare_patient_register": "Disabled Patient Registration Template", "enabled_kivicare_zoom_link": "Enabled Video Conference appointment Template", "disabled_kivicare_zoom_link": "Disabled Video Conference appointment Template", "enabled_kivicare_doctor_book_appointment": "Enabled Doctor Booked Appointment Template", "disabled_kivicare_doctor_book_appointment": "Disabled Doctor Booked Appointment Template", "enabled_kivicare_add_doctor_zoom_link": "Enabled Doctor Zoom Video Conference appointment Template", "disabled_kivicare_add_doctor_zoom_link": "Disabled Doctor Zoom Video Conference appointment Template", "enabled_kivicare_add_doctor_meet_link": "Enabled Google Meet Video Conference appointment Template", "disabled_kivicare_add_doctor_meet_link": "Disabled Google Meet Video Conference appointment Template", "enabled_kivicare_meet_link": "Enabled Google Meet Video Conference appointment Template", "enabled_medroid_episode_mail": "Patient Consultation Mail Template", "disabled_medroid_episode_mail": "Patient Consultation Mail Template", "disabled_kivicare_meet_link": "Disabled Google Meet Video Conference appointment Template", "enabled_kivicare_clinic_admin_registration": "Enabled Clinic Admin Registration", "disabled_kivicare_clinic_admin_registration": "Disabled Clinic Admin Registration", "enabled_kivicare_payment_pending": "Enabled Payment Pending Template", "disabled_kivicare_payment_pending": "Disabled Payment Pending Template", "enabled_kivicare_patient_clinic_check_in_check_out": "Enabled Patient Clinic Check In  Template", "disabled_kivicare_patient_clinic_check_in_check_out": "Disabled Patient Clinic Check In  Template", "google_event_template": "Google Event Template", "upcoming": "Upcoming", "completed": "Completed(check-out)", "cancelled": "Cancelled", "accepted": "accepted", "action_delete_appointment_doctor": "This action may delete your doctor's appointments, sessions and holidays.", "action_delete_appointment_patient": "This action may delete patient's appointment, history, encounters.", "actual_amount": "Actual AMOUNT", "add_service": "Add service", "add_static_data": "Add Static Data", "add_to_calender": "Add to Calendar", "add_to_cart": "Add To <PERSON>", "address_info": "Address info", "booking_successful": "Booking successful", "cancel_date": "All the appointment on selected date will cancel.", "clinic": "Clinic", "color_green": "green", "color_red": "red", "confirm_booking": "Confirm booking", "connected_with_google_calender": "You are connected with the google calender.", "connected_with_google_meet": "You are connected with the google calender.", "create_add": "Create Add", "dates": "DATE", "deleting_services": "Deleting service may affects your existing appointment data.", "desc": "desc", "disconnected": "Disconnected", "edit_bill_items": "Edit bill item", "edit_clinic": "Edit Clinic", "edit_encounter": "Edit encounter", "edit_receptionist": "Edit receptionist", "edit_service": "Edit service", "edit_staic_data": "Edit Static Data", "english": "English", "folder_permission_msg": "Please give permission to your language folder", "google_calendar": "Google Calendar", "google_calendar_client_id": "Google Calendar Client ID", "google_calendar_client_secret": "Google Calendar Client Secret", "google_calendar_configuration": "Google Calendar Configuration", "google_calendar_intergration": "Google Calendar Intergration", "guide_to_setup_google_calender": "Guide to setup google calender.", "important_note": "Important! Note", "in_resources": "in resources.", "internal_server_error": "Internal server error", "key": "Key", "KiviCare_lang_folder": "KiviCare_lang folder", "lang_folder": "lang folder", "media_uploads_folder": "in media uploads folder and", "monthly": "Monthly", "yearly": "Yearly", "no_data_found": "No Data Found", "notes": "NOTES", "patient_setting": "Patient Setting", "please_connect_google_calendar_automatically": "Please connect with your Google Account to get appointments in Google Calendar automatically.", "please_connect_google_meet_automatically": "Please connect your Google Account for Google Meet.", "press_yes_delete_billitems": "Press yes to delete bill item", "press_yes_to_delete_static_data": "Press yes to delete static data", "py_delete": "Press yes to delete", "py_delete_appointment": "Press yes to delete appointment", "py_delete_clinic": "Press yes to delete clinic", "py_delete_report": "Press yes to delete Report", "py_delete_clinic_session": "Press yes to delete clinic session", "py_delete_field": "Press yes to delete Field", "py_delete_prescription": "Press yes to delete prescription", "py_delete_receptionist": "Press yes to delete Receptionist", "receptionist_list": "Receptionist List", "remove_from_calender": "Remove from Calender", "reset_appointment_slot": "Following action reset your current appointment session slot.", "save_and_close_encounters": "Save & Close Consultation", "save_item": "Save item", "save_profile": "Save profile", "server_error": "Server Error", "setting_for_add_event_in_calendar_for_patient": "Setting for add event in calendar for patient.", "start_date": "START DATE", "static_data_list": "Static Data List", "terms_condition": "Terms & Condition", "total_amount": "Total AMOUNT", "total_appointment": "Total Appointment", "update_appointment_status": "Press yes to update appointment status", "update_system": "Press yes to update payment status", "video_consultation": "Video Consultation", "weekly": "Weekly", "yes": "Yes", "checkin": "CheckIn", "loco_translate": "Loco Translate", "view": "View", "doctor_not_available_for_this_clinic": "Select Doctor or Clinic is not Available", "request_features": "Request Features", "hide_request_features": "Hide all hard links : Request features | Get Support | Documentation.", "remove_request_features": "Remove request features | Get Support | Documentation Hard Links.", "remove_links": "Remove Hard Links", "age": "Age", "clinic_admin_info": "Clinic Admin Information", "clinic_currency": "Clinic currency prefix,postfix", "enable_wordpress_logo_status": "Enable Wordpress Logo", "clinic_is_required": "Clinic is required", "google_recaptcha": "Google Recaptcha V3", "enable_google_recaptcha": "Enable Google Recaptcha V3", "site_key": "Google Recaptcha Site Key", "secret_key": "Google Recaptcha Secret Key", "google_recaptcha_refer_link": "Please Click Here To Create New Site and Secret Key", "fullcalendar_setting": "Dashboard Calendar Setting", "fullcalendar_license_key": "License Key", "logout_redirect": "Logout Redirect To", "include_in_multiservice": "Allow multi selection while booking?", "payment_transaction_failed": "Payment Transaction Failed.Please Try Again", "payment_transaction_saved": "Appointment successfully booked, Please check your email for Confirmation Mail", "more": "More", "telemed_link": "Telemed Meeting Link", "date_format_refer_link": "Please Click Here To Know more about date Format", "date_format_setting": "Date Format Setting", "custom_date_format": "Enter Custom Date Format ", "export_pdf": "Export pdf", "select_doctor_or_clinic_is_not_available": "Select Doctor or Clinic is not Available", "test_and_save": "Test & Save", "search_patient_global_placeholder": "Search patient by name, email and status(0 or 1)", "search_doctor_global_placeholder": "Search doctor by name, email and status(0 or 1)", "search_receptionist_global_placeholder": "Search receptionist by name, email and status(0 or 1)", "search_listing_data_global_placeholder": "Search listing-data by name, type and status(0 or 1)", "search_custom_field_data_global_placeholder": "Search custom field data by id,field,type and status(0 or 1)", "search_holiday_data_global_placeholder": "Search holiday data by id,schedule of,name", "search_service_field_data_global_placeholder": "Search services data by id, doctor, name, category, price  and status(0 or 1)", "search_bills_data_global_placeholder": "Search bills data by id,encounter_id, clinic, doctor, patient, total amount, discount ,amount due  and status(0 or 1)", "search_encounter_field_data_global_placeholder": "Search encounter data by id, doctor, clinic, patient, date  and status(0 or 1)", "search_clinic_field_data_global_placeholder": "Search clinic data by id, name, email, admin-email, speciality,address and status(0 or 1)", "customField": "Custom Field", "label": " Label", "input_type": " Input Type", "options": " Options", "id": "ID", "prescription": "Prescription", "prescription_name": "name", "prescription_frequency": "frequency", "prescription_duration": "duration", "copied": "copied!", "failed_to_copy": "Receptionist Role"}, "paypal": {"paypal_setting": "Paypal Payment Gateway", "paypal_account_setting": "<PERSON><PERSON> Account <PERSON>s", "paypal_configration": "Paypal Configration", "client_id": "Client ID", "client_secret": "Client Secret", "mode": "Mode", "plh_enter_client_id": "Enter Your Client Id", "plh_enter_client_secret": "Enter Your Client Secret", "sandbox": "Sandbox", "live": "Live", "paypal_mode_required": "Payment mode is required", "paypal_client_id_required": "Client id is required", "paypal_client_secret_required": "Client secret is required"}, "dashboard": {"dashboard": "Dashboard", "total_patients": "Total Patients", "total_visited_patients": "Total visited patients", "total_doctors": "Total Doctors", "total_clinic_doctors": "Total clinic doctors", "total_appointments": "Total Appointments", "total_clinic_appointments": "Total clinic appointments", "latest_appointments": "Latest appointments", "reload": "Reload", "view_all": "View All", "weekly_appointments": "Weekly appointments", "weekly_total_appointments": "Weekly total appointments", "monthly_appointments": "Monthly appointments", "monthly_total_appointments": "Monthly total appointments", "today_appointment_list": "today's appointment list", "total_revenue": "Total revenue", "total_clinic_revenue": "Total clinic revenue", "total_generated_revenue": "Total generated revenue", "filter": "Filter", "reset": "Reset", "total_today_appointments": "Total Today Appointments", "total_service": "Total service", "patients": "Patients", "medical_dashboard": "Medical dashboard"}, "doctor": {"doctor_name": "Doctor name", "first_name": " first name", "last_name": " last name", "email": " email", "doctor_contact": " contact", "gender": " gender", "specilization": " specilization", "doctor_specialization_required": "Doctor specialization is required", "experience_year": "Experience(In Year)", "address_details": "Address details", "degree": "Degree", "degree_required": "Degree is required", "degree_validation_1": "Degree allow only character value", "university": "University", "university_required": "University is required", "university_validation": "University allow only character value", "year": "Year", "select_year": "-- Select year --", "year_required": "Year is required", "college_university": "College/University", "api_key": "API key", "api_secret": "API secret", "api_secret_required": "API secret is required", "api_key_required": "API Key is required", "zoom_configuration_guide": "Zoom Configuration guide", "zoom_step1": "Step 1 : Sign up or Sign in here :", "zoom_step2": "Step 2 : Click/Hover on Develop button at the right in navigation bar and click on build app", "zoom_step3": "Step 3 : Choose your app type to JWT", "zoom_step4": "Step 4 : Fill the mandatory information and In the App credentials tag you can see API key and API Secret.", "zoom_step5": "Step 5 : Copy and Paste API key and API secret here and click on save button and you are ready to go.", "other_detail": "Other detail", "consultation_fees": "Consultation Fees", "video_consultation_fees": "Video consultation fees", "doctor_fees_required": "Doctor's fees is required", "zoom_market_place_portal": "Zoom market Place portal", "create_app": "Create app", "doctors_list": "Doctors List", "other_details": "Other details", "extra_detail": "Extra detail", "add_doctor": "Add doctor", "edit_profile": "Edit profile", "basic_information": "Basic information", "basic_settings": "Basic Settings", "type": "Type", "type_required": "Type is required", "fees_type": "Fees type", "range": "Range", "fixed": "Fixed", "fees": "Fees", "fees_type_required": "Fees type is required", "doc_fee_required": " Doctor's fees is required", "doc_fee_validation_1": "Doctor's fees must be greater then zero", "doc_fee_validation_2": "Doctor's fees must be between 0 to ***********00000000", "doc_fee_validation_3": "Doctor's fees Minimum fees and maximum fees are required", "doc_fee_validation_4": "Doctor's fees Minimum fees and maximum fees must be greater then zero", "doc_fee_validation_5": "Doctor's to fees minimum value must be greater then from fees value.", "doc_fee_validation_6": "Doctor's fees Maximum fees and minimum fees must be between 0 to ***********00000000", "qualification_information": "Qualification Information", "qualification_speciality_details": "Qualification/Speciality details", "doctor_working_days_sessions": "Doctor's working days & sessions", "doctor_working_days": "Working days", "charge_n_doc_selection": "Added charges and doctor selection", "doc_field_customization": "Individual doctor field customization", "save_btn": "Save", "fname_plh": "Enter first name", "lname_placeholder": "Enter last name", "email_placeholder": "Enter email", "address_placeholder": "Enter address", "country_placeholder": "Enter country", "search_placeholder": "Search", "tag_select_clinic_plh": "Select clinic", "contact_placeholder": "Enter contact", "welcome_date_plh": "welcome date", "tag_doc_sp_plh": "Doctor specialization", "add_sp_plh": "Add Specialization", "experience_plh": "Enter experience", "pcode_placeholder": "Enter pin code", "degree_plh": "Enter degree", "university_plh": "Enter university name", "API_key_plh": "Enter your API key", "API_secret_plh": "Enter your API secret", "dt_lbl_sr": "SR.", "dt_lbl_name": "Name", "dt_name_filter_plh": "Filter doctor by name", "dt_lbl_clinic_name": "Clinic", "dt_lbl_email": "Email", "dt_email_fltr_plh": "Filter doctor by email", "dt_lbl_mobile_number": "Mobile", "dt_mobile_fltr_number": "Filter doctor by mobile no", "dt_lbl_specialties": "Specialization", "dt_specialities_filter_plh": "Filter doctor by specialties", "dt_lbl_actions": "Action", "add_qualification": "Add Qualification", "plh_clinic_address": "Clinic address", "plh_enter_city": "Enter city", "plh_enter_state": "Enter state", "plh_enter_country_name": "Enter country name", "plh_enter_postal_code": "Enter postal code", "plh_enter_fees": "Enter fees", "plh_min_price_range": "Min price range", "plh_max_price_range": "Max price range", "plh_enter_degree": "Enter degree", "plh_enter_university": "Enter university name", "city_placeholder": "Enter city", "add_qualifiaction": "Add qualifiaction", "edit_qualification": "Edit qualification", "lbl_calender_not_connected": "Google Calender not connected please", "lbl_important_note": "Important! Note", "lbl_sign_in": "Sign in", "lbl_sign_in_google": "Sign in with google.", "save_qualification": "Save qualification", "no_doctor_found": "No Doctor Found"}, "patient": {"patient_name": " Patient name", "first_name": " first name", "last_name": " last name", "email": "Email", "contact": "Mobile No.", "gender": " gender", "add_patient": "Add patient", "patients_lists": "Patients List", "medical_report": "Medical Report", "add_medical_report": "Add Medical Report", "upload_report": "Upload Report", "select_clinic": "Select Clinic", "fname_plh": "Enter first name", "lname_placeholder": "Enter last name", "email_placeholder": "Enter email", "contact_placeholder": "Enter contact", "address_placeholder": "Enter address", "city_placeholder": "Enter city", "state_plh": "Enter state", "country_placeholder": "Enter country", "pcode_placeholder": "Enter pin code", "sr": "SR.", "name": "Name", "name_placeholder": "Filter patient by name", "clinic": "Clinic", "clinic_placeholder": "Filter clinic name", "filter_email_placeholder": "Filter patient by email", "filter_contact_placeholder": "Filter patient by contact", "blood": "Blood Group", "blood_placeholder": "ARALL", "reg_date": "Registered NO.", "reg_date_placeholder": "Filter patient by Reg. date", "action": "Action", "search_placeholder": "Search", "save_btn": "save", "tag_select_clinic_plh": "Select clinic", "welcome_date_plh": "welcome date", "tag_doc_sp_plh": "Doctor specialization", "add_sp_plh": "Add Specialization", "experience_plh": "Enter experience", "degree_plh": "Enter degree", "university_plh": "Enter university name", "API_key_plh": "Enter your API key", "API_secret_plh": "Enter your API secret", "edit_bill": "Edit Bill", "edit_patient": "Edit patient", "edit_profile": "Edit profile", "exports_CSV": "Export CSV", "exports_excel": "Export Excel", "lbl_patient_unique_id": "Patient Unique ID", "unique_id": "Unique ID", "lbl_postfix": "Postfix", "lbl_prefix": "Prefix", "patient_unique_setting": "Patient unique Id Setting", "print": "Print", "dt_plh_all": "All", "dt_lbl_sr": "SR.", "dt_lbl_name": "Name", "dt_lbl_clinic_name": "Clinic Name", "dt_lbl_action": "Action", "dt_lbl_email": "Email", "dt_lbl_mobile_number": "Mobile", "dt_lbl_specialties": "Specialization", "dt_lbl_registered": "Registered ON", "dt_lbl_blood_group": "Blood Group", "dt_plh_name_filter": "Filter patient by name", "dt_plh_email_fltr": "Filter patient by email", "dt_plh_mobile_fltr_number": "Filter patient by mobile no", "dt_plh_date": "Fi<PERSON> Patient by Date", "dt_plh_specialities_filter": "Filter doctor by specialties", "plh_username": "<PERSON><PERSON> Username", "plh_pwd": "Enter Password", "plh_repeat_pwd": "Repeat Password", "plh_enter_report": "Enter report name", "plh_enter_title": "Enter title", "plh_enter_notes": "Enter notes", "clinic_check_out_in": "Check Out or Check In Clinic"}, "receptionist": {"fname_plh": "Enter first name", "name_plh": "Name", "save_btn": "Save", "lname_plh": "Enter last name", "email_plh": "Enter email", "first_name": " first name", "last_name": " last name", "email": " email", "receptionist_contact": " contact", "gender": " gender", "contact_plh": "Enter contact number", "welcome_date_plh": "welcome date", "search_placeholder": "Search", "select_clinic": "Select Clinic", "address_plh": "Enter address", "country_plh": "Enter country name", "city_plh": "Enter city", "plh_enter_state": "Enter state", "pcode_plh": "Enter postal code", "dt_lbl_sr": "SR.", "dt_lbl_name": "Name", "dt_lbl_clinic_name": "Clinic Name", "dt_lbl_email": "Email", "dt_lbl_mobile": "Mobile No", "dt_lbl_status": "Status", "dt_lbl_action": "Action", "dt_plh_name_fltr": "Filter receptionist by name", "dt_plh_email_fltr": "Filter receptionist by email", "dt_plh_mobilr_fltr": "Filter receptionist by mobile no", "dt_all": "All", "dt_active": "Active", "dt_inactive": "InActive", "plh_clinic_add": "Clinic address", "login_user_not_found": "Login user not found", "press_yes_to_delete_receptionist": "Press yes to delete receptionist", "resend_credential": "Resend credential", "upload_profile": "Upload Proifle"}, "clinic": {"clinic": "Clinic", "first_name": " first name", "last_name": " last name", "email": " email", "clinic_contact": " contact", "gender": " gender", "receptionist": "Receptionist", "receptionists_list": "Receptionists List", "add_receptionist": "Add receptionist", "clinic_name": "Clinic name", "clinic_info": "Clinic information", "clinic_profile": "Clinic Profile", "add_clinic": "Add clinic", "admin_profile": "Admin Profile", "clinic_admin_detail": "Clinic Admin Detail", "clinic_name_validation_1": "invalid clinic name format only allow alphabetic value", "clinic_name_validation_2": "Clinic name length must be between 2 to 35 characters", "select_clinic": "Select Clinic", "speciality": "Speciality", "specialties": "Specialties", "specialities": "Specialities", "note_specialization": "Note: Type and press enter to add new specialization", "clinic_specialities_required": "Clinic specialities is required", "currency_prefix": "Currency prefix", "currency_postfix": "Currency postfix", "currency_decimals": "Currency decimals", "profile_img": "Profile image", "edit_profile_img": "Edit profile image", "doctor_record_not_found": "Doctor record not found ", "blood_group": "Blood group", "select_blood_group": "-- Select blood group --", "update_profile": "Update Profile", "clinic_list": "Clinic List", "clinic_name_plh": "Enter clinic name", "email_plh": "Enter email address", "telephone_plh": "Enter telephone number", "search_placeholder": "Search", "clinic_sp_plh": "Clinic specialization", "currency_prefix_plh": "Enter currency prefix", "currency_postfix_plh": "Enter currency postfix", "address_plh": "Enter address", "city_plh": "Enter city", "country_plh": "Enter country name", "pcode_plh": "Enter postal code", "fname_plh": "Enter first name", "welcome_date": "welcome date", "clinic_logo": "Clinic logo", "save_btn": "Save", "dt_lbl_contect": "Contact No", "dt_lbl_sr": "SR.", "dt_lbl_name": "Name", "dt_lbl_status": "Status", "dt_plh_name_filter": "Filter clinic by name", "dt_lbl_clinic_name": "Clinic", "dt_lbl_email": "Email", "dt_plh_fltr_name": "Filter doctor by name", "dt_plh_fltr_contact": "Filter clinic by contact number", "dt_plh_fltr_specialitiy": "Filter clinic by specialties", "dt_lbl_registered_on": "Registered ON", "dt_lbl_mobile_number": "Mobile", "dt_lbl_specialties": "Specialization", "dt_lbl_action": "Action", "plh_email": "Enter email address", "plh_contact": "Enter contact number", "plh_clinic_specialization": "Clinic specialization", "plh_address": "Clinic address", "plh_country": "Enter country", "plh_city": "Enter city", "plh_pcode": "Enter postal code", "plh_currency_prefix": "Enter currency prefix", "plh_currency_postfix": "Enter currency postfix", "plh_currency_decimal": "currency decimals", "plh_select_decimal": "Select decimal", "plh_clinic_name": "Enter clinic name", "add_session_detail": "Add session details", "editholiday": "Edit holiday", "no_doctor_found": "No Doctor Found", "edit_session_detail": "Edit Session Detail", "save_session_detail": "Save Session Detail", "edit_clinic_Profile": "Edit Clinic Profile", "plh_record_not_found": "Record Not Found", "plh_clinic_not_found": "Clinic Not Found", "no_speciality_found": "No Speciality Found", "clinic_profile_updated_successfully": "Clinic profile updated successfully", "clinic_profile_not_updated_successfully": "Clinic profile not updated successfully"}, "appointments": {"paid": "Paid", "unpaid": "Unpaid", "pending": "Pending", "appointment": "Appointment", "appointments": "Appointments", "description": "Description", "booked": "Booked", "cancelled": "Cancelled", "arrived": "Arrived", "check_in": "Check in", "check_out": "Check out", "start": "Start", "join": "Join", "doc_required": "Doctor is required", "visit_type_required": "Visit type is required", "appointment_date": "Appointment Date", "appointment_date_required": "Appointment date is required", "select_status": "Select status", "status_required": "Status is required", "available_slot": "Available Slot", "session": "Session", "no_time_slots_found": "No time slots found", "time_slot_required": "Time Slot Required", "appointment_details": "Appointment details", "appointment_type": "Appointment type", "completed": "Completed", "appointment_time": "Appointment Time", "appointment_time_required": " Appointment time is required.", "book_appointment": "Book Appointment", "today_appointment": "Today's Appointment", "tomorrow_appointment": "Tomorrow's Appointment", "appointment_booking": "Appointment Booking", "available_appointments_on": "Available Appointments On", "appointment_visit_type_required": "Appointment visit type is required.", "appointment_detail": "Appointment Detail", "save_appointment": "Save Appointment", "add_review": "Add Review to doctor", "patient_review": "Patient rating", "ratings": "Ratings", "appointment_list": "Appointment List", "save_btn": "Save", "search_plh": "Search", "doctor_plh": "Select doctor", "tag_visit_type_plh": "Visit type", "tag_patient_type_plh": "Patient type", "patient_requires": "Patient is required", "appointment_desc_plh": "Enter appointment description", "tag_patient_plh": "Patient", "all": "all", "upcoming": "upcoming", "tag_plh_session_doc": "Select session doctors", "tag_plh_appointment_type": "Select appointment type", "plh_enter_something": "Enter something", "plh_doc_name": "DOCTOR NAME", "plh_patient_name": "PATIENT NAME", "plh_date": "Date", "plh_patient": "Patient", "plh_status": "Status", "dt_lbl_sr": "SR. NO", "dt_lbl_data": "Date", "dt_lbl_time": "Time", "dt_lbl_paient_name": "Patient Name", "dt_lbl_doc_name": "Doctor Name", "dt_lbl_status": "Status", "dt_lbl_visi_type": "Visit Type", "dt_lbl_description": "Description", "dt_lbl_action": "Action", "add_appointment_btn": "Add appointment", "close_form_btn": "Close form", "appointment_date_plh": "Appointment Date", "select_date": "select Date", "select_clinic": "Select Clinic", "select_patient": "Select Patient", "restrict_appointment": "Appointment Setting", "booking_restriction": "Booking Restriction", "restrict_appointment_detail": "Restrict Pre or Post Booking of Appointment", "pre_appointment": "Restriction Pre Booking(in Days)", "post_appointment": "Restriction Post Booking(in Days)", "pre_appointment_required": "Pre Appointment Restriction Days is Required", "pre_book_are_you_sure": "Are you sure want save settings ?", "post_appointment_required": "Post Appointment Restriction Days is Required", "pre_appointment_length": "Pre Appointment Restriction Days must be greater then zero and less then 365 days", "post_appointment_length": "Post Appointment Restriction Days must be greater then zero and less then 365 days", "multi_file_upload": "Appointment File Upload Setting", "appointment_multi_file_upload": "Appointment Multi File Upload", "appointment_daily_reminder": "Appointment Daily Reminder", "appointment_email_reminder": "Appointment <PERSON><PERSON>", "notice_of_appointment_reminder": "Select Hours", "appointment_sms_reminder": "Appointment <PERSON><PERSON> Reminder (Twilio)", "appointment_whatsapp_reminder": "Appointment <PERSON><PERSON><PERSON> (Twilio)", "pre_post_note": "For example, Booking Open Before: 60 days, Booking Close Before: 7 days, As consideration for the current date, The appointment booking opens 60 days ago and closed 7 days ago. ", "file_uploading": "Medical Report Uploading", "appointment_reminder_info": "cron job will run in every 2 minutes and select the appointment in next select hours (Example if you select/save 06:00  cron will job collect all appointment of current date + 6 hours   ) and send email/sms/whatsapps accordings to setting  notification to patient only once a day.", "post_day_must_be_greater_then_pre_day": "Appointment Restriction Post Days Must Be Greater Then Pre Day", "appointment_time_format": "Appointment Time Format", "appointment_time_24_format": "Format Appointment Time in 24 Hours Format", "disableDelete": "Disable multiple delete", "enableDelete": "Enable multiple delete", "deleteSelectedAppointment": "Delete selected appointment", "appointment_description_notes": "Appointment Setting", "appointment_description": "Appointment Description", "show_patient_information": "Show Patient Info While Save Appointment."}, "clinic_schedule": {"schedule": "Schedule", "holiday_of": "Absence of", "module_type_required": "Module type is required", "schedule_date": "Schedule date", "schedule_date_required": "Schedule date is required", "holiday_list": "Absence List", "tag_module_type_plh": "Select module type", "select_modulr_plh": "Select module", "select_schedule_date_plh": "Select Schedule date", "tag_doctors_plh": "doctors", "select_doc_plh": "Select Doctor", "dt_lbl_sr": "SR.", "dt_lbl_schedul_of": "Schedule Of", "dt_plh_fltr_by_schedule": "Filter Absence by Schedule", "dt_lbl_name": "Name", "dt_plh_fltr_by_doc": "Filter Absence by doctor", "dt_lbl_from_date": "From Date", "dt_plh_fltr_by_date": "Filter Absence by start date", "dt_lbl_to_date": "To DATE", "dt_plh_fltr_by_end_date": "Filter Absence by end date", "dt_lbl_action": "Action", "dt_srvr_err": "Internal server error", "dt_are_you_sure": "Are you sure ?", "dt_press_dlt": "Press yes to delete holiday", "dt_holiday_list": "Absence List", "dt_lbl_dlt": "Delete", "dt_lbl_edit": "Edit", "dt_lbl_print": "Print", "dt_export_excel": "Export Excel", "dt_export_csv": "Export CSV", "clinic": "Clinic", "add_holiday_btn": "Add holiday", "close_form_btn": "close form", "dt_current_appointment_session": "Delete Current Appointmet Session", "schedule_not_found": "Schedule not found", "dt_delete_doctor_appointment": "This action may delete your doctor\"s appointments, sessions and holidays."}, "doctor_session": {"doc_sessions": "Availablity", "session_doc_required": "Session doctor is required", "doc_already_added": "Selected Doctor is already added in another session", "week_days": "Week days", "days_required": "Days is required", "days_already_exist": "Selected days is already exist in the other session", "morning_session": "Morning session", "start_time_required": "Start time is required", "start_time_smaller_then_end": "Start time must be smaller then end time", "end_time_required": "End time is required", "end_time_bigger_then_start": "End time must be bigger then start time", "evening_session": "Evening session", "start_time_smaller_then_first_session_end_time": "Start time must be smaller then first session's end time", "set_session_for_doc": "This tab helps you to set sessions for individual Doctors", "plh_search": "Search", "plh_tag_session_doc": "Select session doctors", "plh_tag_clinic": "Select clinic", "plh_start_time": "Start time", "plh_end_time": "End time", "dt_plh_fltr_by_doc": "Filter doctor session by name", "dt_lbl_sr": "SR.", "dt_lbl_doc": "Doctor", "dt_lbl_clinic": "Clinic Name", "dt_lbl_days": "Days", "dt_lbl_morning_session": "Morning Session", "dt_lbl_evening_session": "Evening Session", "dt_lblaction": "Action", "add_session_btn": "Add Availablity", "close_form_btn": "close form", "save_btn": "Save Session", "clinic_session_list": "Clinic Session List", "doctor_session_not_saved_successfully": "Doctor session not saved successfully", "doctor_session_saved_successfully": "Doctor session saved successfully", "edit_session": "Edit session", "export_CSV": "Export CSV", "export_excel": "Export Excel", "no_speciality_found": "No speciality Found", "save_session": "Save session", "clinic_profile_data_not_found": "Clinic Profile Data Not Found"}, "patient_encounter": {"patient_extra_details": "Patient extra details", "send_pre_mail": "Email To Patient", "encounters": "Consultations", "encounter_dashboard": "Consultation dashboard", "is_required": "is required", "note_prescription": "Note: Type and press enter to create new prescription", "frequency": "Frequency", "frequency_required": "Frequency is required", "duration_Days": "Duration (In Days)", "duration_required": "Duration is required", "instruction": "Instruction", "duration": "Duration", "no_prescription_found": "No prescription found", "no_patient_report_found": "No patient report found", "add_prescription": "Add prescription", "encounter_date": "Consultation Date", "encounter_date_required": "Consultation date is required", "encounter_module": "Consultation Module", "prescription": "Prescription", "encounter_details": "Consultation details", "detail_placeholder": "placeholder", "save_btn": "Save", "proceed_btn": "Proceed", "tag_select_clinic": "Select clinic", "search_plh": "Search", "tag_select_doctor": "Select doctor", "tag_patient_type_plh": "Patient type", "tag_name_plh": "Name", "dt_lbl_sr": "SR.", "dt_lbl_doc_name": "Doctor Name", "dt_plh_fltr_by_doc": "Filter Consultation by doctor", "dt_lbl_clinic": "Clinic Name", "dt_plh_fltr_by_clinic": "Filter Consultation by clinic name", "dt_lbl_patient": "Patient Name", "dt_plh_fltr_patient": "Filter Consultation by patient name", "dt_lbl_name": "Date", "dt_plh_fltr_date": "Filter Consultation by date", "dt_lbl_action": "Action", "edit_prescription": "Edit prescription", "lname_plh": "Enter last name"}, "medical_records": {"problem_type": "Problem type", "problem_start_date_required": "Problem start date is required", "problem_start_date": "Problem start date", "problem_end_date": "Problem end date", "problem_outcome": "Problem outcome", "medical_records": "Medical records", "add_medical_problems": "Add medical problems", "plh_problem_type": "Select problem type", "plh_search": "Search", "plh_problem_outcome": "Select problem outcome", "edit_medical_record": "Edit medical record", "lbl_action": "START DATE", "lbl_date": "DATE", "lbl_desc": "DESCRIPTION", "lbl_end_date": "END DATE", "lbl_outcome": "OUTCOME", "lbl_problem_type": "PROBLEM TYPE", "lbl_sr_no": "SR. NO", "lbl_start_date": "START DATE", "medical_record_not_found": "Medical record not found", "plh_medical_desc": "Enter Medical Record description"}, "reports": {"reports": "Reports", "filter_by": "Filter <PERSON>", "clinic_revenue_overall": "Clinic Revenue (Overall)", "clinic_revenue_detail": "Clinic Revenue (Detail)", "clinic_doctor_revenue": "Clinic Doctor Revenue", "prescription_module": "Prescription Module", "report_required": "Report is required.", "tag_plh_select_clinic": "Select clinic", "plh_search": "Search", "plh_flter_by": "Select Filter by", "plh_select": "select"}, "patient_front_widget": {"specialization": "Specialization", "username_email": "Username or Email", "fill_form": "Please fill in this form to create an account."}, "service": {"service_list": "Service List", "service_category": "Service category", "service_category_required": "Service category is required", "note_category": "Note: Type and press enter to add new category", "service_name": "Service Name", "service_name_required": "Service name is required", "service_validation": "Service name length should be between 2 to 100 character", "charges": "charges", "category": "category", "name": "name", "doctor": "doctor", "service_charge": "service charge from service module. ", "service_charges_required": "Service charges is required", "service_charge_length": "Service charges should be between 0 to ***********", "select_all": "Select all", "save_btn": "Save", "tag_select_service_plh": "Select service category", "select_service_plh": "Select service category", "service_name_plh": "Enter service name", "charges_plh": "Enter charges", "tag_select_doc_plh": "Select doctor", "select_doc_plh": "Select doctor", "select_status_plh": " Select status", "dt_lbl_sr": " SR. NO", "dt_lbl_name": " Name", "dt_lbl_clinic_name": " Clinic Name", "dt_lbl_email": "Email", "dt_lbl_mobile": "Mobile No", "dt_lbl_status": "Status", "dt_lbl_action": "Action", "dt_plh_name_fltr": "Filter service by name", "dt_plh_fltr_by_doc": "Filter service by doctor", "dt_plh_fltr_by_price": "Filter Service by price", "dt_all": "All", "dt_active": "Active", "dt_inactive": "InActive", "dt_lbl_charges": "Charges", "dt_lbl_doctor": "Doctor", "dt_lbl_category": "Category", "add_service_btn": "Add Service", "close_form_btn": "close form"}, "patient_bill": {"invoice_id": "Invoice id :", "created_at": "Created at :", "payment_status": "Payment Status :", "paid": "Paid", "unpaid": "Unpaid", "patient_details": "Patient details", "amount_due": "Amount due", "print": "Print", "service_required": "Service is required", "price_required": "Price is required", "prize_greater_then_0": "Price must be greater then zero", "prize_between_number": "Price must be between 0 to ***********00000000", "quantity_required": "Quantity is required", "please_add_bill_items": "Please add bill items", "payment_link": "payment link", "bill_total_required": "Bill total is required", "discount": "Discount", "discount_amount": "Discount in amount", "discount_required": "Discount is required", "discount_greater_then_0": "Discount must be greater then zero", "discount_less_then_total_bill_amount": "Discount must be less then total bill amount", "payable_amount": "Payable Amount", "bill_title": "Bill title", "bill_title_required": "Bill title is required", "bill_items": "Bill items", "grand_total": "Grand total", "grand_total_required": "Grand total is required", "print_bill": "Print bill", "billing_records": "Billing records", "add_bill": "Add bill", "generate_bill": "Generate new bill", "add_new_bill": "Add new bill", "no_encounter_found_for_billing": "No encounters found for billing", "patient_required": "Patient is required", "encounter_close": "Consultation close", "bill_details": "Bill details", "other_info": "Other information", "patients_encounter_list": "Patients Consultation List", "patients_encounter_list_is_Empty": "Patients Consultation List Is Empty", "bills": "Bills", "payment_setting": "Payment Setting", "woocommerce_payment_notice": "Note: If Woocommerce payment enable then Appointments Email, Sms and Zoom Link will we send after payment completed and google calendar event will we  added after payment complete.when patient book appointment Service will added as woocommerce product(if already not exists) after patient is redirect to cart page.", "woocommerce_payment_gateway": "Woocommerce Payment Gateway", "local_payment_gateway": "Local Payment Gateway", "amount": "Amount", "items": "Items", "notes": "Notes", "invoice_n_payment": "Services will be used for invoicing and other future payment related implementations", "currency": "Set currency prefix,postfix and decimals points.", "tag_plh_service": "Select service", "plh_service": "Services", "plh_price": "Price", "plh_quality": "Quantity", "plh_select_service": "Select service", "plh_services": "Services", "tag_plh_status": "Select status", "plh_status": "Status", "plh_total_amount": "Enter total_amount", "plh_discount": "Enter discount", "plh_enter_title": "Enter title", "add_item": "Add item", "bill_add_item": "Add <PERSON>", "bill_close": "Close Form", "lbl_action": "Action", "lbl_actual_amount": "ACTUAL AMOUNT", "lbl_date": "DATE", "lbl_discount": "DISCOUNT", "lbl_sr_no": "SR. NO", "lbl_status": "Status", "lbl_title": "Title", "lbl_total_amount": "Total AMOUNT", "plh_search": "Search", "payment_or_bill_item_error": "Please Enable Payment Gateway And Add Items in Bills", "generate_invoice": "Generate invoice", "invoice_detail": "Invoice detail"}, "settings": {"general": "General", "holidays": "Holidays", "configurations": "Configurations", "email_template": "<PERSON>ail Te<PERSON>late", "sms_template": "SMS/WhatsApp Template", "listings": "Listings", "custom_field": "Custom Field", "payment": "Payment", "new_setting": "New refined settings with various settings like email, invoice, currency, etc.", "pro_settings": " Pro Settings ", "language_settings": "Language Settings", "tag_plh_option": "Select Option", "plh_search": "Search", "plh_enter_acc_sid": "Enter your ACCOUNT SID", "plh_auth_token": "Enter your AUTH TOKEN", "plh_enter_number": "Enter your to number", "patient_setting": "Patient Setting", "dynamic_keys_list": "Template Dynamic Keys List(click on button to copy)", "click_to_copy": "Click To Copy", "copied": "Key Copied"}, "pro_setting": {"theme_setting": "Theme Settings", "set_site_loader": "Site Loader", "set_site_logo": "Site Logo", "set_language": "Language", "set_theme_color": "Theme color", "rtl_mode": "RTL Mode", "on": "on", "twilo_sms_configration": "Twilo SMS Configration", "account_sid": "ACCOUNT SID", "auth_token": "AUTH TOKEN", "phone_number": "PHONE NUMBER", "twilio_account_setting": "<PERSON><PERSON><PERSON> A<PERSON>unt <PERSON>", "twilo_sms_guide": "Twilo SMS guide", "twilo_whatsapp_guide": "<PERSON><PERSON><PERSON> Whatsapp guide", "twilio_step_1": "Step 1 :  You can sign up for a free Twilio trial account here", "twilo_sms_portal": "Twilo SMS portal", "twilio_step_2": "Step 2 : To get the Twilio CLI connected to your account. Visit", "get_console": "get console", "unique_sid": "and you’ll find your unique Account SID and <PERSON><PERSON> to provide to the CLI.", "twilio_step_3": "Step 3 : <PERSON><PERSON> and <PERSON><PERSON> ACCOUNT SID  and AUTH TOKEN and click on save button and here you go.", "twilio_step_4": "Step 4 : To get your first Twi<PERSON> phone number for sending sms. Visit", "twillo_imp_note": "Important Note: <PERSON><PERSON><PERSON>(doctor/patient) Phone/contact No must be in twillo specific format ([+] [country code] [mobile number] )", "twillo_help_note": "Please Refer here for more details", "head_on_console": "head on over to the console", "phone_msg_sid": "and you will get phone number to send SMS if you dont want any particular number to send message use your SID", "add_new_langauge": "Add New Langauge", "translate": "Translate", "custom_langauge_translation": "Custom Langauge Translation", "translating": "translating...", "select_color": "Select Color", "google_account_setting": "Google Account Settings", "connect_with_google": "Connect with google", "please_refer_link": "Please refer the following link for the setup.", "plh_select_lang": "Select Language", "clinical_detail": "Include Consultation Clinical Details in Prescription print", "clinical_detail_patient_hide": "Hide Consultation Clinical Details To Patient", "include_encounter_custom_fields_in_print": "Include Consultation custom fields in Prescription print", "copy_right_text": "Copy Right Text", "change_copy_right_text": "Change Copy Right Text"}, "custom_field": {"label_name_required": "Label name is required", "label_name_validation": "Label name allow only alphabetic value", "where_it_look_like": "Where it look like", "shows_in_doc_creation_form": "It shows in doctor creation form", "shows_in_patient_encounter_dashboard": "It shows in patient encounter dashboard", "shows_in_patient_creation_form": "It shows in Appointment dashboard", "filed_name": "Filed name :", "invalid_label_name": "Invalid label name", "label_required": "Label is required", "field_name_used": "Field name is already used.", "input_type": "Input Type", "input_type_required": "Input type is required", "placeholder": "Placeholder", "options": "Options", "validation": "Validation", "mandatory_field": "Mandatory field", "custom_field_list": "Custom Field List", "add_custom_field": "Add custom field", "tag_module_plh": "<PERSON><PERSON><PERSON>", "select_module_plh": "Select module", "doctors_plh": "Doctors", "tag_doctors_plh": "Select Doctor", "field_label_plh": "Enter field label", "input_type_plh": "Select input type", "placeholder_plh": "Enter placeholder", "tag_add_new_option_plh": "Add this as new option", "serach_plh": "Search or add a option", "status_plh": "Select status", "dt_lbl_sr": "SR.", "dt_lbl_field": "Field", "dt_plh_fltr_by_name": "Filter custom field by name", "dt_lbl_type": "Type", "dt_plh_fltr_by_type": "Filter custom field by type", "dt_lbl_action": "Action", "add_field": "Add field", "add_new_field": "Add new field", "are_you_sure": "Are you sure ?", "doctor_profile_data_not_found": "Doctor profile data not found", "edit_custom_field": "Edit custom field", "edit_field": "Edit field", "exports_CSV": "Export CSV", "exports_excel": "Export Excel", "press_yes_to_delete": "Press yes to delete", "prints": "Print", "record_not_found": "Record not found", "save_field": "Save field", "label": "Label"}, "encounter_dashboard": {"problems": "Problems", "observation": "Observations", "notes": "Notes", "add_btn": "Add", "add_prescription_btn": "Add Prescription", "presciption_save_btn": "Save", "title": "Clinical Detail", "close_form": "Close Form", "add_encounter": "Add encounter"}, "setup_wizard": {"previous": "Previous", "add_session_details": "Add session details", "session_doctors": "Session doctors", "days": "Days", "no_sessions_found": "No sessions found", "time_slot_minute": "Time slot (in minute)", "open_time": "Open time", "close_time": "Close time", "session_demo": "Session demo", "invalid_time_slot": "Invalid time slot found. invalid slot time is ", "doctor_list": "Doctor list", "kivicare_ehr": "Medroid - Clinic & Patient Management System (EHR)", "prev": "Prev", "plh_enter_current_password": "Enter your current password", "choose_image": "Choose Image", "plh_enter_fname": "Enter first name", "plh_enter_lame": "Enter last name", "plh_enter_email": "Enter email", "plh_enter_contct": "Enter telephone number", "plh_welcome_date": "welcome date", "plh_enter_clinic": "Enter clinic name", "plh_clinic_specialization": "Clinic specialization", "plh_specialization": "Specialization", "plh_currency_prefix": "Enter currency prefix", "plh_currency_postfix": "Enter currency postfix", "plh_enter_address": "Enter address", "plh_enter_city": "Enter city", "plh_enter_country": "Enter country", "plh_enter_pcode": "Enter postal code", "plh_enter_pwd": "Enter password", "plh_doc_specialization": "Doctor specialization", "plh_search": "Search", "plh_enter_fees": "Enter fees", "plh_min_price_range": "Min price range", "plh_max_price_range": "Max price range", "plh_enter_degree": "Enter degree", "plh_enter_university": "Enter university name", "plh_select_session_doc": "Select session doctors", "plh_start_time": "Select start time", "plh_end_time": "Select end time"}, "notification": {"notification": "Send test email", "test_sender_email_required": "Test sender email is required", "test_content": "Test content", "test_content_required": "Test content is required", "email_notification": "Enable/Disable email notification.", "forbidden_403": "403 | forbidden", "plh_enter_email": "Enter emailId"}, "static_data": {"listing_data": "Listing Data", "terms_n_condition": "Terms and Condition", "new_filters_n_view": "New Enhanced Filters and view", "booking_widget_updated": "The booking widget is updated", "visit_type_replaced": "Visiting Type is replaced with services (please check service tab)", "appointment_flow_update": "Appointment check-in and check-out flow updated", "label": "Label", "add_list_data_btn": "Add List Data", "data_label_plh": "Enter data label", "tag_select_type_plh": "Select type", "select_type_plh": "Select type", "tag_select_status_plh": "Select status", "select_status_plh": "Select status", "dt_lbl_sr": "SR.", "dt_lbl_name": "Name", "dt_lbl_plh_fltr_name": "Filter by name", "dt_lbl_type": "Type", "dt_lbl_plh_fltr_type": "Filter by type", "dt_lbl_status": "Status", "dt_lbl_plh_sr_fltr_status": "Filter by status", "dt_lbl_action": "Action"}, "widgets": {"doc_not_found": "Doctor not found", "zoom_config": "Zoom configuration", "terms_condition": "Terms and Condition", "date_required": "Date is required", "current_pwd": "Current password", "current_pwd_required": "Current password is required", "new_pwd": "New password", "appointment_info": "Appointment info", "available_slots": "Available slots", "service_detail": "Service Detail", "no_service_detail_found": "No service detail found.", "book_now": "Book Now", "registration_success": "Registration successful please check your email", "more_detail": "more detail ...", "username_email_required": "Username or email is required.", "new_pwd_required": "New password is required", "confirm_pwd": "Confirm password", "confirm_pwd_required": "Confirm password is required", "pwd_validation": "New password and Confirm password does't match", "home": "Home", "change_pwd": "Change Password ", "logging_out": "Logging out ....", "total_visits": "Total Visits", "upcoming_visits": "Upcoming Visits", "example_component": "Example Component", "email_to_get_help_1": "Other than this many more fine-tunings and tweaks are done. Please email at", "email_to_get_help_2": "<EMAIL>", "email_to_get_help_3": "if you face any issues with the update.", "feedback_note": "After great user feedback, We have some major changes released in this update.", "imp_version_update": "Important! Major Version update!! (V2.0.1)", "replace_appointment": "Replace appointment", "option_as": "option as", "service_type": "service type", "add_charges": "you have to add charges for", "manage_doctor": "Can Manage individual doctor's", "send_test_email": "Send test Email", "send_test_sms": "Send test Sms", "send_test_whatsapp": "Send test Whatsapp", "morning": "Morning", "evening": "Evening", "plh_search": "Search", "plh_enter_desc": "Enter description", "clinics": "Clinics", "plh_select_doc": "Select doctor", "plh_select_service": "Select service", "plh_enter_usrname_email": "Enter username or email", "plh_enter_fnmae": "Enter firstname", "plh_enter_pwd": "Enter password", "plh_enter_lname": "Enter lastname", "plh_enter_email": "Enter email", "plh_enter_contact": "Enter contact", "plh_enter_crrent_pwd": "Enter current password", "plh_enter_new_pwd": "Enter new password", "plh_confirm_pwd": "Enter confirm password", "patient_info": "Patient Info", "select_doctor_msg": "Select doctor to get appointments slots.", "slot_not_available_msg": "Sorry, No available slots for this doctor on this day.", "session": "Session", "Session": "Session", "login_user_not_found": "Login user not found", "record_not_found": "Record not found", "onlyForPatient": "Admin can not view the widget. Only patients can view the widget. Please open this page in incognito mode or use another browser without an admin login.", "summary": "Summary", "doctors": "Doctors", "dr_prefix": "Dr.", "back_to_home": "Back To Home Page"}, "widget_setting": {"widget_setting": "Widget Setting", "clinic_setting": "Clinic", "doctor_setting": "Doctor", "show_clinic": "Show clinic", "show_clinic_image": "Show clinic image", "show_clinic_address": "Show clinic address", "contact_details": "Contact Details", "show_doctor_image": "Show doctor image", "show_doctor_experience": "Show doctor experience", "contact_details_required": "Contact Detail is required", "service_setting": "Service", "show_service_type": "Show service type", "show_service_price": "Show service price", "choose_your_doctor": "Choose Your Doctor", "choose_your_doctor_text": "pick a specific Doctor to perform your service", "services_from_category": "Services from Category", "services_from_category_text": "Please select a service from below options", "select_date_and_time": "Select Date and Time", "select_date_and_time_text": "Select date to see a timeline of available slots", "user_detail_information": "User Detail Information", "user_detail_information_text": "Please provide you contact details", "confirmation": "Confirmation", "confirmation_detail": "Confirmation Detail", "confirmation_text": "Confirm your booking", "choose_clinic": "Choose a Clinic", "select_clinic": "Select Clinic", "choose_clinic_text": "Please select a Clinic you want to visit", "clinic_contact": "Contact", "clinic_email": "Email", "available_time_slots": "Available time slots", "enter_details": "Enter Details", "signup": "Signup", "appointment_summary": "Appointment summary", "date_time": "Date And Time", "at": "at", "total_price": "Total Price", "number": "Number", "confirm": "Confirm", "payment_method": "Payment method", "payPal": "PayPal", "pay_later": "Pay Later", "visa": "VISA", "pay": "Pay", "select_category": "Select Category", "widget_order": "Widget Order", "widget_color": "Widget Color", "set_widget_primary_color": "Primary Color", "set_widget_primary_hover_color": "Primary Hover Color", "set_widget_secondary_color": "Secondary Color", "set_widget_secondary_hover_color": "Secondary Hover Color", "widget_print_setting": "Print Detail setting", "print": "Print Detail", "phone_email": "Show Phone & Email", "show_phone_number": "Show phone number", "show_email_address": "Show email address", "hide_contact_details": "Hide contact details", "loader_setting": "Loader", "loader_select": "Select Loader", "redirect_after_woocommerce": "Redirect to Print after payment", "redirect_after_woocommerce_notice": "Redirect to Print appointment after woocommerce payment complete.", "enable_woocommerce": "Enable Woocommerce", "enable_local_payment": "Enable Local Payment Gateway"}, "patient_dashboard_widget": {"blood_group": "Blood Group", "fname_plh": "Enter first name", "lname_plh": "Enter last name", "email_plh": "Enter email", "search_plh": "Search", "select_blood_grp": "Search", "contact_plh": "Enter contact", "address_plh": "Enter address", "city_plh": "Enter city", "state_plh": "Enter state", "country_plh": "Enter country", "pcode_plh": "Enter pin code", "save_profile_btn": "save", "change_pass_btn": "Change Password", "latest_appointments": "Latest appointments", "no_appointment_msg": "No appointment found.", "appointment_list": "Appointment List", "select_doctor_msg": "Select doctor to get appointments slots.", "slot_not_available_msg": "Sorry, No available slots for this doctor on this day.", "book_now_btn": "Book Appointment", "profile_head": "Profile", "change_password_head": "Change Password", "personal_info": "Personal Info"}, "change_password": {"plh_confirm_password": "Confirm password", "plh_old_pwd": "Enter your old password", "plh_new_pwd": "Enter your new password", "plh_confirm_pwd": "Enter your confirm password", "plh_enter_confirm_password": "Enter confirm password", "plh_new_password": "New password"}, "zoom_config": {"plh_api_key": "Enter api key", "plh_api_secret": "Enter api secret"}, "question": {"question_list": "Question List", "close_form_btn": "Close form", "add_question_btn": "Add Question", "question": "Question", "quest_flt_plh": "Filter Health Question by Question", "clinic": "Clinic ID", "clinic_flt_plh": "Filter Health Question by Clinic ID", "question_flt_plh": "Enter Question ", "question_required": "Question is Required"}, "datatable": {"next_text": "Next", "prev_text": "Prev", "rows_per_page": "Rows per page", "of_text": "of", "all_text": "ALL", "search_placeholder": "Search Table", "page_text": "Page"}, "google_event": {"google_event_title": "Google Event title", "google_event_desc": "Google Event Description", "template": "Google Event Template"}, "auth": {"login": "<PERSON><PERSON>", "register": "Register", "forget_password": "Forget Password ?"}, "googlemeet": {"googlemeet": "Google Meet", "google_meet_configuration": "Google Meet Configuration", "google_meet_client_id": "Google Meet Client ID", "google_meet_client_secret": "Google Meet Client Secret", "guide_to_setup_google_meet": "Guide to setup google GoogleMeet.", "google_event_template": "Google Meet Event Template", "google_meet_intergration": "Google Meet Intergration"}, "fullcalendar": {"today": "Today", "day": "Day", "month": "Month", "week": "Week"}, "days": {"mon": "Mon", "tue": "<PERSON><PERSON>", "wed": "Wed", "thu": "<PERSON>hu", "fri": "<PERSON><PERSON>", "sat": "Sat", "sun": "Sun"}, "months": {"January": "January", "February": "February", "March": "March", "April": "April", "May": "May", "June": "June", "July": "July", "August": "August", "September": "September", "October": "October", "November": "November", "December": "December"}, "months_short": {"Jan": "Jan", "Feb": "Feb", "Mar": "Mar", "Apr": "Apr", "May": "May", "Jun": "Jun", "Jul": "Jul", "Aug": "Aug", "Sep": "Sep", "Oct": "Oct", "Nov": "Nov", "Dec": "Dec"}, "webhooks": {"webhooks": "Webhooks"}}