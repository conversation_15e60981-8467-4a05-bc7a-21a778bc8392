export const AddonsList = {
  pro: {
    href: "https://codecanyon.net/item/kivicare-pro-clinic-patient-management-system-ehr-addon/30690654?s_rank=3",
    message: "Available In Pro",
    text: "Pro",
  },
  telemed: {
    href: "https://codecanyon.net/item/kivicare-telemed-addon/29363726?s_rank=9",
    message: "Available In Telemed Addon",
    text: "Addons",
  },
  googlemeet: {
    href: "https://codecanyon.net/item/kivicare-google-meet-telemed-addon/35301137?s_rank=21",
    message: "Available In Googlemeet Addon",
    text: "Addons",
  },
  googlemeetAndtelemed: {
    href: "https://codecanyon.net/user/medroiddesign",
    message: "Available In Googlemeet Addon or Telemed Addon",
    text: "Addons",
  },
  app: {
    href: "https://codecanyon.net/item/kivicare-flutter-app-clinic-patient-management-system/30970616",
    message: "Available For Kivicare Flutter App",
    text: "Addons",
  },
  bodyChart: {
    href: "https://codecanyon.net/item/kivicare-encounter-body-chart-addon/51585503",
    message: "Available In Encounter Body Chart Addon",
    text: "Addons",
  },
  webhooks: {
    href: "https://codecanyon.net/user/medroiddesign",
    message: "Available In Webhooks Addon",
    text: "Addons",
  },
};

export const DefaultAddons = {
  href: "https://codecanyon.net/item/kivicare-pro-clinic-patient-management-system-ehr-addon/30690654?s_rank=3",
  message: "Available In Pro",
  text: "Pro",
};
