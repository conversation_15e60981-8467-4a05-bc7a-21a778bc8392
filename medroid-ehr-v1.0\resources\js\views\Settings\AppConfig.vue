<template>
  <div class="grid grid-cols-12 gap-4">
    <div class="col-span-12">
      <div class="relative">
        <!-- Overlay -->
        <div
          v-if="userData.addOns.api != true"
          class="absolute inset-0 bg-white bg-opacity-40 flex items-center justify-center"
        >
          <overlay-message addon_type="app" />
        </div>

        <!-- Main Card -->
        <div class="bg-white rounded-lg shadow-lg">
          <!-- Header -->
          <div class="p-4 border-b">
            <div class="grid grid-cols-12 gap-4">
              <div class="col-span-12 md:col-span-8">
                <h2 class="text-xl font-bold m-0">
                  {{ formTranslation.settings.app_config }}
                </h2>
              </div>
            </div>
          </div>

          <!-- One Signal App Section -->
          <div class="p-4 border-b">
            <div class="space-y-4">
              <div class="mb-4">
                <h3 class="text-lg font-semibold">
                  {{ formTranslation.settings.one_signal_app_notification }}
                </h3>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- App ID Input -->
                <div>
                  <label
                    for="app_id"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    {{ formTranslation.settings.app_id }}
                    <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="app_id"
                    v-model="app.app_id"
                    disabled
                    :placeholder="formTranslation.settings.app_id_placeholder"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100"
                  />
                </div>

                <!-- API Key Input -->
                <div>
                  <label
                    for="api_key"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    {{ formTranslation.settings.api_key }}
                    <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="api_key"
                    v-model="app.api_key"
                    disabled
                    :placeholder="formTranslation.settings.api_key_placeholder"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100"
                  />
                </div>
              </div>

              <!-- Save Button -->
              <div class="flex justify-end">
                <button
                  disabled
                  class="px-4 py-2 bg-blue-600 text-white rounded-md disabled:opacity-50 flex items-center space-x-2"
                >
                  <i class="fa fa-save"></i>
                  <span>{{ formTranslation.common.save }}</span>
                </button>
              </div>
            </div>
          </div>

          <!-- Firebase Config Section -->
          <div class="p-4">
            <div class="space-y-4">
              <div class="mb-4">
                <h3 class="text-lg font-semibold">
                  {{ formTranslation.settings.firebase_app_config }}
                </h3>
              </div>

              <!-- Firebase Settings Grid -->
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div v-for="settings in firebaseConfigSetting" :key="settings">
                  <label
                    :for="'firebase_' + settings"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    {{ formTranslation.settings.app[settings] }}
                    <span class="text-red-500">*</span>
                  </label>
                  <input
                    :id="'firebase_' + settings"
                    v-model="firebaseConfig[settings]"
                    :placeholder="formTranslation.settings.app[settings]"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md"
                  />
                </div>
              </div>

              <!-- Client Email -->
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label
                    for="firebase_client_emaail"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    {{ formTranslation.settings.app["client_emaail"] }}
                    <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="firebase_client_emaail"
                    v-model="firebaseConfig['client_emaail']"
                    :placeholder="formTranslation.settings.app['client_emaail']"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md"
                  />
                </div>
              </div>

              <!-- Private Key -->
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label
                    for="firebase_privat_key"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    {{ formTranslation.settings.app["privat_key"] }}
                    <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="firebase_privat_key"
                    v-model="firebaseConfig.privat_key"
                    :placeholder="formTranslation.settings.app['privat_key']"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md"
                  />
                </div>
              </div>

              <!-- Project ID -->
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label
                    for="firebase_project_id"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    {{ formTranslation.settings.app["project_id"] }}
                    <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="firebase_project_id"
                    v-model="firebaseConfig.project_id"
                    :placeholder="formTranslation.settings.app['project_id']"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md"
                  />
                </div>
              </div>

              <!-- Save Button -->
              <div class="flex justify-end">
                <button
                  @click="saveCommonSettings"
                  class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2"
                >
                  <i
                    :class="isLoading ? 'fa fa-spinner fa-spin' : 'fa fa-save'"
                  ></i>
                  <span>{{ formTranslation.common.save }}</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { post, get } from "../../config/request";
export default {
  data: () => {
    return {
      app: {
        app_id: "",
        api_key: "",
      },
      firebaseConfig: {
        serverKey: "",
        client_emaail: "",
        privat_key: "",
        project_id: "",
      },
      isLoading: false,
      firebaseConfigSetting: ["serverKey"],
    };
  },
  mounted() {
    if (!["administrator"].includes(this.getUserRole())) {
      this.$router.push({ name: "403" });
    }
    this.init();
    this.getModule();
  },
  methods: {
    init: function () {},
    saveCommonSettings: function () {
      if (this.isLoading) {
        return true;
      }

      this.isLoading = true;
      post("save_app_config", { config: this.firebaseConfig })
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
          }
          this.isLoading = false;
        })
        .catch((error) => {
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
          this.isLoading = false;
        });
    },
    getModule: function () {
      get("get_app_config").then((response) => {
        if (
          response.data.success !== undefined &&
          response.data.success === true
        ) {
          this.firebaseConfig = response.data.data;
        }
      });
    },
  },
  computed: {
    userData() {
      return this.$store.state.userDataModule.user;
    },
  },
};
</script>
