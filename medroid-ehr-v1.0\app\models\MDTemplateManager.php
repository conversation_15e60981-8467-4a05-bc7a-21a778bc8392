<?php

namespace App\models;
use TenQuality\WP\Database\Abstracts\DataModel;
use TenQuality\WP\Database\Traits\DataModelTrait;

class MDTemplateManager extends DataModel
{
    use DataModelTrait;
    
    /**
     * Data table name in database (without prefix).
     * @var string
     */
    const TABLE = 'md_template_manager';
    
    /**
     * Data table name in database (without prefix).
     * @var string
     */
    protected $table = self::TABLE;
    protected $primary_key = 'id';
    
    /**
     * Get templates by doctor ID
     * @param int $doctor_id
     * @return array
     */
    public function get_templates_by_doctor($doctor_id)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . $this->table;
        
        $query = "SELECT * FROM {$table_name} 
                  WHERE (doctor_id = %d OR share_status = 'public' OR is_system = 1)
                  AND status = 1
                  ORDER BY created_at DESC";
        
        return $wpdb->get_results($wpdb->prepare($query, $doctor_id));
    }
    
    /**
     * Get templates by clinic ID
     * @param int $clinic_id
     * @return array
     */
    public function get_templates_by_clinic($clinic_id)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . $this->table;
        
        $query = "SELECT * FROM {$table_name} 
                  WHERE (clinic_id = %d OR share_status = 'public' OR is_system = 1)
                  AND status = 1
                  ORDER BY created_at DESC";
        
        return $wpdb->get_results($wpdb->prepare($query, $clinic_id));
    }
    
    /**
     * Get templates visible to a specific doctor
     * This includes: personal templates, clinic templates for their clinics, and public templates
     * @param int $doctor_id
     * @return array
     */
    public function get_visible_templates($doctor_id)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . $this->table;
        $clinic_mapping_table = $wpdb->prefix . 'kc_doctor_clinic_mapping';
        
        $query = "SELECT t.* FROM {$table_name} t
                  LEFT JOIN {$clinic_mapping_table} cm ON t.clinic_id = cm.clinic_id
                  WHERE (t.doctor_id = %d 
                     OR (cm.doctor_id = %d AND t.share_status IN ('clinic', 'public'))
                     OR t.share_status = 'public'
                     OR t.is_system = 1)
                  AND t.status = 1
                  GROUP BY t.id
                  ORDER BY t.created_at DESC";
        
        return $wpdb->get_results($wpdb->prepare($query, $doctor_id, $doctor_id));
    }
    
    /**
     * Get a single template by ID
     * @param int $id
     * @return object
     */
    public function get_template($id)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . $this->table;
        
        $query = "SELECT * FROM {$table_name} WHERE id = %d AND status = 1";
        
        return $wpdb->get_row($wpdb->prepare($query, $id));
    }
    
    /**
     * Insert system templates from the provided templates array
     * @param array $templates_data
     * @return void
     */
    public function insert_system_templates($templates_data)
    {
        foreach ($templates_data as $key => $content) {
            // Format the name for display
            $name = str_replace('_template', '', $key);
            $name = str_replace('_', ' ', $name);
            $name = ucwords($name);
            
            // Determine the category based on key
            $category = 'general';
            if (strpos($key, 'referral') !== false) {
                $category = 'referral';
            } elseif (strpos($key, 'leave') !== false || strpos($key, 'sick') !== false) {
                $category = 'sick_note';
            } elseif (strpos($key, 'consultation') !== false) {
                $category = 'consultation';
            } elseif (strpos($key, 'operation') !== false || strpos($key, 'procedure') !== false) {
                $category = 'procedure';
            }
            
            $template_data = [
                'name' => $name,
                'category' => $category,
                'content' => $content,
                'doctor_id' => null,
                'clinic_id' => null,
                'is_system' => 1,
                'share_status' => 'public',
                'status' => 1,
                'created_at' => current_time('Y-m-d H:i:s')
            ];
            
            $this->insert($template_data);
        }
    }
}