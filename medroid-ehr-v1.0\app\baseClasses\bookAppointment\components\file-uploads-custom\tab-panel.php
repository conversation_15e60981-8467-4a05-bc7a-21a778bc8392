<div class="">
    <div class="space-y-4">
        <!-- Header Section -->
        <div class="mb-6 text-left">
            <h1 class="text-2xl font-semibold text-gray-900">
                <?php echo esc_html__("More About Appointment", "kc-lang"); ?>
            </h1>
            <p class="text-sm text-gray-500 mt-1">Please provide your medical information</p>
        </div>

        <div class="widget-content space-y-4">

        <div class="space-y-4">
            <!-- Appointment Description -->
            <?php if (kcCheckExtraTabConditionInAppointmentWidget('description')) { ?>
                <div class="space-y-1">
                    <label class="block text-sm font-medium text-gray-700 px-1 text-left">
                        Appointment Description
                    </label>
                    <textarea
                        name="appointment_description"
                        class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-fuchsia-500 focus:border-fuchsia-500 text-black"
                        placeholder="Enter Appointment Descriptions"></textarea>
                </div>
            <?php } ?>
            <!-- Medical Report Upload -->
            <?php if (kcAppointmentMultiFileUploadEnable()) { ?>
                <div class="space-y-2">
                    <label class="block text-sm font-medium text-gray-700 px-1 text-left">
                        Add Medical Report
                    </label>
                    <div class="file-upload-div border-2 border-dashed rounded-lg p-4">
                        <div class="flex flex-col items-center justify-center space-y-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round"
                                class="lucide lucide-upload w-8 h-8 text-gray-400">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                <polyline points="17 8 12 3 7 8"></polyline>
                                <line x1="12" x2="12" y1="3" y2="15"></line>
                            </svg>
                            <span class="text-sm text-gray-600">Drop files here or click to upload</span>
                        </div>
                        <input class="hidden" type="file" name="medical_report" <?php echo esc_html(isKiviCareProActive() ? 'multiple' : ''); ?>>
                    </div>
                    <div id="kivicare_file_upload_review"></div>
                </div>
            <?php } ?>

        </div>
           

            <!-- Custom Fields -->
            <div id="customFieldsListAppointment"></div>

        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', () => {
    // Delegate click events on the body for dynamically loaded elements
    document.body.addEventListener('click', (e) => {
        // Check if the clicked element is a button with data-toggle attribute
        if (e.target.matches('button[data-toggle]')) {
            const button = e.target;
            const section = button.nextElementSibling; // The corresponding toggle-section
            const icon = button.querySelector('svg.lucide-chevron-right'); // Chevron icon

            // Toggle visibility and animation
            if (section && section.classList.contains('hidden')) {
                section.classList.remove('hidden');
                section.classList.add('block');
                icon.classList.add('rotate-90'); // Rotate icon
            } else if (section) {
                section.classList.remove('block');
                section.classList.add('hidden');
                icon.classList.remove('rotate-90'); // Reset icon rotation
            }
        }

        // Check if the clicked element or its parent has the .file-upload-div class
        const file_upload_container = e.target.closest('.file-upload-div');
        if (file_upload_container) {
            // Prevent click propagation if the user clicks directly on the input
            if (e.target.tagName.toLowerCase() === 'input') return;

            const fileInput = file_upload_container.querySelector('input[type="file"]');
            if (fileInput) {
                fileInput.click(); // Trigger the file input click
            }
        }
    });
});

</script>