<template>
  <div class="main-content" id="panel">
    <!-- Keep your existing modals unchanged -->
    <ModalPopup v-if="showQrcode && getUserRole() === 'administrator' && demoPluginActive" 
      modalId="appointment-details-modal" 
      modalSize="md" 
      :openModal="showQrcode"
      :modalTitle="formTranslation.common.qrcode" 
      @closeModal="showQrcode = false"
    >
      <!-- Your existing QR code modal content -->
    </ModalPopup>

    <ModalPopup v-if="showChatBot && getUserRole() === 'administrator' && demoPluginActive" 
      modalId="credentials" 
      modalSize="md" 
      :openModal="showChatBot"
      modalTitle="Demo User Credentials" 
      @closeModal="showChatBot = false"
    >
      <!-- Your existing demo credentials modal content -->
    </ModalPopup>

    <!-- Updated Modern Navigation -->
    <nav 
      class="fixed top-0 left-0 right-0 z-50 transition-all duration-300 bg-white"
      :class="[
        isScrolled ? 'transform -translate-y-full' : 'transform translate-y-0',
        isHovered ? 'transform translate-y-0' : '',
      ]"
      @mouseenter="handleNavHover(true)"
      @mouseleave="handleNavHover(false)"
    >
      <div class="w-full px-4 shadow-sm border-b border-gray-100 bg-white/80 backdrop-blur-lg">
        <div class="max-w-7xl mx-auto">
          <div class="flex justify-between h-16">
            <!-- Left Section -->
            <div class="flex items-center space-x-4">
              <!-- Mobile menu button -->
              <button 
                class="lg:hidden p-2 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200"
                @click="handleSideBarToggle"
              >
                <div class="space-y-1">
                  <div class="w-5 h-0.5 bg-gray-600"></div>
                  <div class="w-5 h-0.5 bg-gray-600"></div>
                  <div class="w-5 h-0.5 bg-gray-600"></div>
                </div>
              </button>

              <!-- Greeting Section -->
              <div class="flex items-center space-x-3">
                <div class="space-y-1">
                  <div class="flex items-center space-x-2">
                    <h1 class="text-lg font-semibold text-gray-800">{{ greeting }},</h1>
                    <span class="bg-gradient-to-r from-pink-500 to-purple-600 text-transparent bg-clip-text font-semibold">
                      {{ displayUsername }}!
                    </span>
                  </div>
                  <div class="flex items-center space-x-2">
                    <span class="flex items-center">
                      <span class="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2"></span>
                      <span class="text-xs text-gray-500">{{ formattedDateTime }}</span>
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Right Section -->
            <div class="flex items-center space-x-4">
              <!-- QR Code Button -->
              <button 
                v-if="getUserRole() === 'administrator' && demoPluginActive"
                @click="showQrcode = true"
                class="p-2 text-gray-600 hover:text-pink-600 hover:bg-pink-50 rounded-full transition-colors"
              >
                <i class="fa fa-qrcode text-lg"></i>
              </button>

              <!-- Demo Credentials Button -->
              <button 
                v-if="getUserRole() === 'administrator' && demoPluginActive"
                @click="showChatBot = true"
                class="p-2 text-gray-600 hover:text-pink-600 hover:bg-pink-50 rounded-full transition-colors"
              >
                <i class="fas fa-id-card text-lg"></i>
              </button>

              <!-- Documentation Link -->
              <a 
                v-if="documentationLink === 'off' && getUserRole() === 'administrator'"
                href="https://apps.medroid.ai/docs/product/kivicare/what-is-kivicare/"
                target="_blank"
                rel="noopener noreferrer"
                class="p-2 text-gray-600 hover:text-pink-600 hover:bg-pink-50 rounded-full transition-colors"
              >
                <i class="fas fa-external-link-alt text-lg"></i>
              </a>

              <!-- Fullscreen Button -->
              <button 
                id="btnFullscreen"
                class="p-2 text-gray-600 hover:text-pink-600 hover:bg-pink-50 rounded-full transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M15 3h6v6M9 21H3v-6M21 3l-7 7M3 21l7-7" />
                </svg>
              </button>

              <!-- Profile Dropdown -->
              <div class="relative">
                <b-dropdown variant="link" class="profile-dropdown" no-caret right>
                  <template v-slot:button-content>
                    <div class="flex items-center space-x-3">
                      <div class="relative">
                        <div 
                          class="h-10 w-10 rounded-full flex items-center justify-center text-gray-700 text-lg font-semibold shadow-sm border border-gray-100"
                          :style="{ backgroundColor: getColorForName(userData.user_email) }"
                        >
                          {{ userData.user_email.charAt(0).toUpperCase() }}
                        </div>
                        <div class="absolute -bottom-0.5 -right-0.5 h-3.5 w-3.5 rounded-full bg-green-500 border-2 border-white"></div>
                      </div>
                      <div class="hidden lg:block">
                        <p class="text-sm font-medium text-gray-800">
                          {{ getUserRole() === 'administrator' && demoPluginActive ? 'SuperAdmin' : displayUsername }}
                        </p>
                        <p class="text-xs text-gray-500">
                          {{ getUserRole().charAt(0).toUpperCase() + getUserRole().slice(1) }}
                        </p>
                      </div>
                    </div>
                  </template>

                  <!-- Keep your existing dropdown items unchanged -->
                  <b-dropdown-item 
                    v-if="kcCheckPermission('receptionist_profile') && getUserRole() === 'receptionist'"
                    :to="{ name: 'receptionist.profile' }"
                    class="flex items-center space-x-2 px-4 py-2 hover:bg-gray-50"
                  >
                    <i class="fas fa-user text-gray-500"></i>
                    <span>{{ formTranslation.common.my_profile }}</span>
                  </b-dropdown-item>
                  <!-- ... rest of your dropdown items ... -->
                </b-dropdown>
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- Updated main content section with sidebar margin -->
    <div 
      class="pt-20 overflow-auto" 
      style="width: calc(100% - var(--sidebar-width)); margin-left: var(--sidebar-width);"
    >
      <div class="container-fluid min-h-[70vh] w-full">
        <transition name="fade" mode="out-in">
          <router-view></router-view>
        </transition>
      </div>
    </div>
  </div>
</template>

<script>
import Footer from "./Footer";
import { fullScreen } from "../../config/helper";
import { post } from "../../config/request";
import VueQRCodeComponent from "vue-qrcode-component";
import ModalPopup from "../Modal/Index";

export default {
  components: {
    Footer,
    VueQRCodeComponent,
    ModalPopup,
  },
  data: () => ({
    showChatBot: false,
    demoUser: {
      doctor: "doctor_",
      Patient: "patient_",
      Receptionist: "receptionist_",
    },
    demoPluginActive: window.request_data.demo_plugin_active,
    adminImage:
      window.request_data.kiviCarePluginURL + "assets/images/male-doctor.png",
    documentationLink: "off",
    adminUrl: window.request_data.adminUrl,
    showQrcode: false,
    qrUrl: window.request_data.homePage + "?user=",
    splitEmail: "",
    currentDateTime: new Date(),
  }),
  mounted() {
    this.init();
    fullScreen();
    this.getRequestHelper();
    if (this.getUserRole() === "administrator") {
      this.splitEmail = this.userData.user_email.split("@")[0];
      this.qrUrl += this.splitEmail;
    }
    
    // Set CSS variables for sidebar width
    this.updateSidebarCSSVar();
    
    // Update currentDateTime every second
    this.timer = setInterval(() => {
      this.currentDateTime = new Date();
    }, 1000);
  },
  beforeDestroy() {
    // Clear the timer when component is destroyed to prevent memory leaks
    clearInterval(this.timer);
  },
  methods: {
    init() {
      if (localStorage.getItem("sidebarToggle") === "false") {
        this.$store.commit("TOGGLE_SIDEBAR", false);
      } else {
        this.$store.commit("TOGGLE_SIDEBAR", true);
      }
      new window.PerfectScrollbar("#sidenav-main", {
        wheelPropagation: false,
      });
    },
    getColorForName(name) {
      const colors = [
        "#f3e8ff"
      ];
      // Calculate a hash from the name
      let hash = 0;
      for (let i = 0; i < name.length; i++) {
        hash = name.charCodeAt(i) + ((hash << 5) - hash);
      }
      // Ensure hash is positive and use it to select a color
      const index = Math.abs(hash) % colors.length;
      return colors[index];
    },
    copyParentText(value, event) {
      $("#credentials i").removeClass("fa-clipboard");
      $("#credentials i").addClass("fa-copy");
      $("#credentials i").parent().attr("title", "Copy");
      $(event.target).removeClass("fa-copy");
      $(event.target).addClass("fa-clipboard");
      $(event.target).parent().attr("title", "Value Copied to clipboard");
      const elem = document.getElementById("modal-hidden-field");
      elem.value = value;
      elem.select();
      document.execCommand("copy");
    },
    getRequestHelper() {
      if (
        window.request_data.link_show_hide !== undefined &&
        window.request_data.link_show_hide !== ""
      ) {
        this.documentationLink = window.request_data.link_show_hide;
      }
    },
    logout() {
      this.$store.dispatch("logout", { self: this });
    },
    handleSideBarToggle() {
      this.$store.commit("TOGGLE_SIDEBAR", !this.fullSideBar);
    },
    
    updateSidebarCSSVar() {
      document.documentElement.style.setProperty(
        '--sidebar-width', 
        this.fullSideBar ? '16rem' : '5rem'
      );
    },
  },
  computed: {
    displayUsername() {
      console.log(this.$store.state.userDataModule.user.display_name, ">>><")
      return (
        this.$store.state.userDataModule.user !== undefined &&
          this.$store.state.userDataModule.user.display_name
          ? this.$store.state.userDataModule.user.display_name
          : ""
      );
    },
    fullSideBar() {
      return this.$store.state.fullSideBar;
    },
    userData() {
      console.log(this.$store.state.userDataModule.user, ">>>>>>>>>>>>>>>>>>")
      return this.$store.state.userDataModule.user;
    },
    greeting() {
      const hour = this.currentDateTime.getHours();
      if (hour < 12) {
        return "Good Morning1";
      } else if (hour < 18) {
        return "Good Afternoon";
      } else {
        return "Good Evening";
      }
    },
    formattedDateTime() {
      const day = String(this.currentDateTime.getDate()).padStart(2, "0");
      const month = String(this.currentDateTime.getMonth() + 1).padStart(
        2,
        "0"
      ); // Months are zero-based
      const year = this.currentDateTime.getFullYear();
      const hours = String(this.currentDateTime.getHours()).padStart(2, "0");
      const minutes = String(this.currentDateTime.getMinutes()).padStart(
        2,
        "0"
      );
      const seconds = String(this.currentDateTime.getSeconds()).padStart(
        2,
        "0"
      );
      return `${day}/${month}/${year}, ${hours}:${minutes}:${seconds}`;
    },
  },
  watch: {
    fullSideBar(newVal) {
      if (newVal) {
        $(".sidenav-toggler").addClass("active");
        $(".sidenav-toggler").data("action", "sidenav-unpin");
        $("body")
          .removeClass("g-sidenav-hidden")
          .addClass("g-sidenav-show g-sidenav-pinned");
        localStorage.setItem("sidebarToggle", true);
        
        // Update CSS variable
        this.updateSidebarCSSVar();
      } else {
        $(".sidenav-toggler").removeClass("active");
        $(".sidenav-toggler").data("action", "sidenav-pin");
        $("body")
          .removeClass("g-sidenav-pinned")
          .addClass("g-sidenav-hidden");
        $("body").find(".backdrop").remove();
        localStorage.setItem("sidebarToggle", false);
        
        // Update CSS variable
        this.updateSidebarCSSVar();
      }
    },
  },
};
</script>

<style>
/* Only keep essential styles that don't conflict with Tailwind */
.profile-dropdown .dropdown-menu {
  z-index: 2000 !important;
}

.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
}

/* Modern Medical Theme Styles */
.navbar-glass {
  /* background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9),
    rgba(255, 255, 255, 0.8)
  );
  backdrop-filter: blur(10px); */

  border-bottom: 1px solid rgba(231, 234, 243, 0.7);
  box-shadow: 0 2px 12px 0 rgba(231, 234, 243, 0.5);
}

.dropdown-item-animate {
  z-index: 1050;
  /* Ensure this is higher than surrounding elements */
  position: relative;
  /* Necessary to make z-index work if not already positioned */
}

.dropdown-item-animate .dropdown-menu {
  z-index: 2000;
  /* Boost this to ensure it appears on top */
  position: absolute;
  /* Ensure the menu is not affected by parent positioning */
}

/* Ensure parent containers don't clip the dropdown */
.dropdown {
  overflow: visible !important;
  /* Prevent clipping by parent elements */
}

.placeholder-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 34px;
  /* Adjust to your desired avatar size */
  width: 34px;
  /* Adjust to your desired avatar size */
  font-size: 16px;
  /* Adjust font size for initials */
  font-weight: bold;
  color: #000000;
  text-transform: uppercase;
  border-radius: 50%;
}

#navbarSupportedContent {
  height: 80px;
  background: white;
}

.navbar-glass::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  height: 80px;

  /* background: linear-gradient(
    45deg,
    rgba(41, 128, 185, 0.05),
    rgba(52, 152, 219, 0.05)
  ); */
  pointer-events: none;
}
.sidenav-header{border-bottom: 1px solid #8080803d;}

.transition-all {
  transition: all 0.3s ease;
}

.nav-link-icon {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  color: #2c3e50;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.nav-link-icon i {
  font-size: 1.25rem;
  margin-right: 0.5rem;
  color: #3498db;
}

.nav-text {
  font-size: 0.875rem;
  font-weight: 500;
}

.hover-scale {
  transition: transform 0.2s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.roleofuser {
  color: #999;
  font-weight: 100;
  font-size: 12px;
  text-align: start;
}

.pulse {
  animation: pulse 2s infinite;
}

.navbar-top {
  z-index: 1030; /* Increased z-index to ensure dropdown appears above other elements */
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}

.profile-image-wrapper {
  position: relative;
 
  transition: all 0.3s ease;
}

.status-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 10px;
  height: 10px;
  background-color: #2ecc71;
  border-radius: 50%;
}

/* Greeting Styles within Navbar */
.greeting-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.greeting-text {
  display: flex;
  flex-direction: column;
}

#panel {
  height: 100vh;
}

.greeting-message {
  font-size: 13px;
  font-weight: 600;
  color: rgba(0, 0, 0, .6)
}

/* .container-fluid {
  z-index: -4;
  position: relative;
} */

.current-datetime {
  font-size: 12px;
  color: #7f8c8d;
}

/* Additional z-index adjustments to ensure other elements do not overlap the dropdown */
.main-content {
  position: relative;
  z-index: 1;
  min-height: 100vh;
  transition: all 0.3s ease;
}

.container-fluid {
  position: relative;
  z-index: 1;
  max-width: 100%;
}

/* Ensure the dropdown menu is above other components */
.profile-dropdown .dropdown-menu {
  z-index: 2000 !important;
}
</style>
