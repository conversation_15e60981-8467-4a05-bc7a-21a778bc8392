<template>
  <div class="grid grid-cols-1">
    <div v-if="formLoader" class="w-full">
      <loader-component-2></loader-component-2>
    </div>
    <div v-else class="w-full">
      <form
        id="patientDataForm"
        @submit.prevent="handleSubmit"
        :novalidate="true"
      >
        <div class="bg-white rounded-lg shadow">
          <!-- Header -->
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex flex-col md:flex-row justify-between items-center">
              <h3 class="text-xl font-semibold">{{ cardTitle }}</h3>
              <div
                v-if="kcCheckPermission('patient_list')"
                class="mt-4 md:mt-0"
              >
                <button
                  type="button"
                  class="px-4 py-2 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center"
                  @click="$router.go(-1)"
                >
                  <svg
                    class="w-4 h-4 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M10 19l-7-7m0 0l7-7m-7 7h18"
                    />
                  </svg>
                  {{ formTranslation.common.back }}
                </button>
              </div>
            </div>
          </div>

          <!-- Form Content -->
          <div class="p-6">
            <!-- Basic Details Section -->
            <div class="mb-6">
              <h4 class="text-lg font-medium text-blue-600 mb-4">
                {{ formTranslation.common.basic_details }}
              </h4>

              <!-- First Row -->
              <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Unique ID -->
                <div v-if="u_id_enabled" class="space-y-2">
                  <label
                    for="first_uid"
                    class="block text-sm font-medium text-gray-700"
                  >
                    {{ formTranslation.patient.lbl_patient_unique_id }}
                    <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="first_uid"
                    v-model="patientData.u_id"
                    required
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <!-- First Name -->
                <div class="space-y-2">
                  <label
                    for="first_name"
                    class="block text-sm font-medium text-gray-700"
                  >
                    {{ formTranslation.common.fname }}
                    <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="first_name"
                    v-model="patientData.first_name"
                    :class="{
                      'border-red-500':
                        submitted && $v.patientData.first_name.$error,
                    }"
                    required
                    type="text"
                    :placeholder="formTranslation.patient.fname_plh"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <div
                    v-if="submitted && !$v.patientData.first_name.required"
                    class="text-red-500 text-sm"
                  >
                    {{ formTranslation.common.fname_required }}
                  </div>
                </div>

                <!-- Last Name -->
                <div class="space-y-2">
                  <label
                    for="last_name"
                    class="block text-sm font-medium text-gray-700"
                  >
                    {{ formTranslation.common.lname }}
                    <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="last_name"
                    v-model="patientData.last_name"
                    :class="{
                      'border-red-500':
                        submitted && $v.patientData.last_name.$error,
                    }"
                    required
                    type="text"
                    :placeholder="formTranslation.patient.lname_placeholder"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <div
                    v-if="submitted && !$v.patientData.last_name.required"
                    class="text-red-500 text-sm"
                  >
                    {{ formTranslation.common.lname_required }}
                  </div>
                </div>
              </div>

              <!-- Contact Information -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div class="space-y-2">
                  <label
                    for="email"
                    class="block text-sm font-medium text-gray-700"
                  >
                    {{ formTranslation.common.email }}
                    <span class="text-red-500">*</span>
                  </label>
                  <input
                    id="email"
                    v-model="patientData.user_email"
                    :class="{
                      'border-red-500':
                        submitted && $v.patientData.user_email.$error,
                    }"
                    required
                    type="email"
                    :placeholder="formTranslation.patient.email_placeholder"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <div
                    v-if="submitted && !$v.patientData.user_email.required"
                    class="text-red-500 text-sm"
                  >
                    {{ formTranslation.common.email_required }}
                  </div>
                </div>

                <div class="space-y-2">
                  <label
                    for="phone_number"
                    class="block text-sm font-medium text-gray-700"
                  >
                    {{ formTranslation.common.contact_no }}
                    <span class="text-red-500">*</span>
                  </label>
                  <VuePhoneNumberInput
                    v-model="patientData.mobile_number"
                    :default-country-code="defaultCountryCode"
                    id="phone_number"
                    @update="contactUpdateHandaler"
                    clearable
                    no-example
                    class="phone-input-tailwind"
                  />
                  <div
                    v-if="submitted && !$v.patientData.mobile_number.required"
                    class="text-red-500 text-sm"
                  >
                    {{ formTranslation.common.contact_num_required }}
                  </div>
                </div>
              </div>

              <!-- Date of Birth and Gender -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div v-if="!hideFields.includes('dob')" class="space-y-2">
                  <label
                    for="doc_birthdate"
                    class="block text-sm font-medium text-gray-700"
                  >
                    {{ formTranslation.common.dob }}
                    <span class="text-red-500">*</span>
                  </label>
                  <input
                    type="date"
                    v-model="patientData.dob"
                    id="doc_birthdate"
                    :max="new Date().toISOString().slice(0, 10)"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <div
                    v-if="submitted && !$v.patientData.dob.required"
                    class="text-red-500 text-sm"
                  >
                    {{ formTranslation.common.dob_required }}
                  </div>
                </div>

                <div id="patient_gender_div" class="space-y-2">
                  <label class="block text-sm font-medium text-gray-700">
                    {{ formTranslation.common.gender }}
                    <span class="text-red-500">*</span>
                  </label>
                  <div class="flex space-x-4">
                    <label class="inline-flex items-center">
                      <input
                        type="radio"
                        v-model="patientData.gender"
                        value="male"
                        class="form-radio text-blue-600"
                      />
                      <span class="ml-2">{{
                        formTranslation.common.male
                      }}</span>
                    </label>
                    <label class="inline-flex items-center">
                      <input
                        type="radio"
                        v-model="patientData.gender"
                        value="female"
                        class="form-radio text-blue-600"
                      />
                      <span class="ml-2">{{
                        formTranslation.common.female
                      }}</span>
                    </label>
                    <label
                      v-if="defaultUserRegistrationFormSettingData === 'on'"
                      class="inline-flex items-center"
                    >
                      <input
                        type="radio"
                        v-model="patientData.gender"
                        value="other"
                        class="form-radio text-blue-600"
                      />
                      <span class="ml-2">{{
                        formTranslation.common.other
                      }}</span>
                    </label>
                  </div>
                  <div
                    v-if="submitted && !$v.patientData.gender.required"
                    class="text-red-500 text-sm"
                  >
                    {{ formTranslation.common.gender_required }}
                  </div>
                </div>
              </div>

              <!-- Profile Image -->
              <div class="mt-6">
                <div class="relative w-32 h-32">
                  <div
                    class="w-full h-full rounded-full overflow-hidden bg-gray-100"
                  >
                    <div
                      class="w-full h-full bg-center bg-cover"
                      :style="'background-image: url(' + imagePreview + ');'"
                    ></div>
                  </div>
                  <button
                    @click.prevent="uploadProfile()"
                    class="absolute bottom-0 right-0 p-2 rounded-full bg-white shadow-lg hover:bg-gray-100"
                  >
                    <svg
                      class="w-5 h-5 text-gray-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
                      />
                    </svg>
                  </button>
                </div>
              </div>
            </div>

            <!-- Address Section -->
            <div
              v-if="!hideFields.includes('address')"
              class="border-t border-gray-200 pt-6"
            >
              <h4 class="text-lg font-medium text-blue-600 mb-4">
                {{ formTranslation.doctor.other_details }}
              </h4>

              <div class="space-y-6">
                <div class="space-y-2">
                  <label
                    for="address"
                    class="block text-sm font-medium text-gray-700"
                  >
                    {{ formTranslation.common.address }}
                  </label>
                  <textarea
                    id="address"
                    v-model="patientData.address"
                    :placeholder="formTranslation.patient.address_placeholder"
                    rows="3"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  ></textarea>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div v-if="!hideFields.includes('city')" class="space-y-2">
                    <label
                      for="city"
                      class="block text-sm font-medium text-gray-700"
                    >
                      {{ formTranslation.common.city }}
                    </label>
                    <input
                      id="city"
                      v-model="patientData.city"
                      :placeholder="formTranslation.patient.city_placeholder"
                      type="text"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div v-if="!hideFields.includes('country')" class="space-y-2">
                    <label
                      for="country"
                      class="block text-sm font-medium text-gray-700"
                    >
                      {{ formTranslation.common.country }}
                    </label>
                    <input
                      id="country"
                      v-model="patientData.country"
                      :placeholder="formTranslation.patient.country_placeholder"
                      type="text"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div
                    v-if="!hideFields.includes('postal_code')"
                    class="space-y-2"
                  >
                    <label
                      for="postal_code"
                      class="block text-sm font-medium text-gray-700"
                    >
                      {{ formTranslation.common.postal_code }}
                    </label>
                    <input
                      id="postal_code"
                      v-model="patientData.postal_code"
                      :placeholder="formTranslation.patient.pcode_placeholder"
                      type="text"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- Custom Fields Section -->
            <div
              v-if="isCustomeFieldExist"
              class="border-t border-gray-200 pt-6"
            >
              <h4 class="text-lg font-medium text-blue-600 mb-4">
                {{ formTranslation.doctor.extra_detail }}
              </h4>
              <get-custom-fields
                v-if="this.$route.params.id === undefined"
                module_type="patient_module"
                :module_id="String(0)"
                @bindCustomField="getCustomFieldsValues"
                :fieldsValue="customFieldsData"
                @customFieldAvailable="isCustomeFieldExist = true"
                @requiredCustomField="getRequireFields"
              ></get-custom-fields>
              <edit-custom-fields
                v-else
                module_type="patient_module"
                :module_id="String(this.$route.params.id)"
                @bindCustomField="getCustomFieldsValues"
                :fieldsValue="customFieldsData"
                @requiredCustomField="getRequireFields"
              ></edit-custom-fields>
            </div>
          </div>

          <!-- Footer -->
          <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
            <div class="flex justify-end space-x-4">
              <button
                v-if="!loading"
                type="submit"
                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                <svg
                  class="inline-block w-4 h-4 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"
                  />
                </svg>
                {{ formTranslation.patient.save_btn }}
              </button>
              <button
                v-else
                type="submit"
                disabled
                class="px-4 py-2 bg-blue-400 text-white rounded-md cursor-not-allowed"
              >
                <svg
                  class="inline-block w-4 h-4 mr-2 animate-spin"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  />
                </svg>
                {{ formTranslation.common.loading }}
              </button>
              <button
                type="button"
                @click="$router.go(-1)"
                class="px-4 py-2 border border-blue-600 text-blue-600 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                {{ formTranslation.common.cancel }}
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import VuePhoneNumberInput from "vue-phone-number-input";
import "vue-phone-number-input/dist/vue-phone-number-input.css";
import {
  required,
  numeric,
  alpha,
  email,
  minLength,
  maxLength,
  requiredIf,
} from "vuelidate/lib/validators";
import { post, get } from "../../config/request";
import {
  alphaSpace,
  emailValidate,
  phoneNumber,
  validateForm,
} from "../../config/helper";

export default {
  components: {
    VuePhoneNumberInput,
  },
  data: () => {
    return {
      u_id_enabled: "",
      patientData: {},
      loading: false,
      submitted: false,
      cardTitle: "Add patient",
      buttonText: '<i class="fa fa-save"></i> Save',
      qualification: {},
      bloodGroups: ["A+", "B+", "AB+", "O+", "A-", "B-", "AB-", "O-"],
      requiredFields: [],
      clinics: [],
      // imagePreview: pluginBASEURL + 'assets/images/kc-demo-img.png',
      imagePreview: pluginBASEURL + "assets/images/kc-demo-img.png",
      hideFields: [],
      formLoader: false,
      clinicMultiselectLoader: true,
      isCustomeFieldExist: false,
      defaultCountryCode: null,
      defaultUserRegistrationFormSettingData: "on",
    };
  },
  mounted() {
    this.getCountryCodeData();
    this.getUserRegistrationFormData();
    this.cardTitle = this.formTranslation.patient.add_patient;
    this.patientData = this.defaultPatientData();
    this.init();
    this.getClinics();
    this.getHideFieldsArrayFromFilter();
  },
  validations: {
    patientData: {
      first_name: {
        required,
        minLength: minLength(2),
        maxLength: maxLength(50),
      },
      last_name: {
        required,
        minLength: minLength(2),
        maxLength: maxLength(50),
      },
      user_email: {
        required,
      },
      mobile_number: {
        required,
        // phoneNumber,
        minLength: minLength(4),
        maxLength: maxLength(15),
      },
      gender: {
        required,
      },
      clinic_id: {
        required: requiredIf(function () {
          return (
            this.userData.addOns.kiviPro == true &&
            (this.getUserRole() == "administrator" ||
              this.getUserRole() == "doctor")
          );
        }),
      },
      // city: {
      //     maxLength: maxLength(30)
      // },
      // country: {
      //     maxLength: maxLength(30)
      // },
      // postal_code: {
      //     maxLength: maxLength(12)
      // },
      dob: { required },
    },
  },
  methods: {
    contactUpdateHandaler: function (val) {
      this.patientData.country_code = val.countryCode;
      this.patientData.country_calling_code = val.countryCallingCode;
    },
    init: function () {
      /// Code for the Edit functionality...
      if (this.$route.params.id !== undefined) {
        this.cardTitle = this.formTranslation.patient.edit_patient;
        this.buttonText =
          '<i class="fa fa-save"></i> ' + this.formTranslation.common.save;

        this.formLoader = true;
        get("patient_edit", {
          id: this.$route.params.id,
          p_id: this.$route.params.pid,
        })
          .then((response) => {
            setTimeout(() => {
              this.formLoader = false;
            }, 200);
            if (
              response.data.status !== undefined &&
              response.data.status === true
            ) {
              setTimeout(() => {
                this.patientData = response.data.data;
                if (
                  response.data.data.country_calling_code !== "" &&
                  response.data.data.country_calling_code !== undefined
                ) {
                  this.defaultCountryCode = response.data.data.country_code;
                }
                // this.patientData.dob = new Date(this.patientData.dob + ' 00:00');
                this.patientData.custom_fields = response.data.custom_filed;
                this.isCustomeFieldExist =
                  this.patientData.custom_fields !== undefined &&
                  this.patientData.custom_fields.length > 0;
                if (this.patientData.user_profile) {
                  this.imagePreview = this.patientData.user_profile;
                }
                // if(this.patientData.blood_group === undefined || this.patientData.blood_group === null ||  this.patientData.blood_group === ''){
                // this.patientData.blood_group = 'default'
                // }
                if (
                  this.patientData.u_id !== undefined &&
                  this.patientData.u_id === "" &&
                  this.userData.unquie_id_status !== undefined
                ) {
                  this.patientData.u_id = this.userData.unquie_id_value;
                }
              }, 200);
            }
          })
          .catch((error) => {
            this.formLoader = false;
            console.log(error);
            displayErrorMessage(this.formTranslation.widgets.record_not_found);
          });
      } else {
        if (this.userData.unquie_id_status !== undefined) {
          post("get_unique_id").then((data) => {
            this.patientData.u_id = data.data.data;
          });
          // this.patientData.u_id = this.userData.unquie_id_value;
        }
      }

      if (
        this.userData.unquie_id_status !== undefined &&
        this.userData.unquie_id_status == true
      ) {
        this.u_id_enabled = this.userData.unquie_id_status;
      } else {
        this.u_id_enabled = false;
      }
    },
    uploadProfile() {
      let _this = this;

      var custom_uploader = kivicareCustomImageUploader(this.formTranslation);

      custom_uploader.on("select", function () {
        var attachment = custom_uploader
          .state()
          .get("selection")
          .first()
          .toJSON();
        _this.imagePreview = attachment.url;
        _this.patientData.profile_image = attachment.id;
      });

      //Open the uploader dialog
      custom_uploader.open();
    },
    defaultPatientData: function () {
      return {
        first_name: "",
        last_name: "",
        username: "",
        user_email: "",
        user_pass: "",
        mobile_number: "",
        country_code: "",
        country_calling_code: "",
        gender: "",
        dob: "",
        about_me: "",
        address: "",
        city: "",
        country: "",
        postal_code: "",
        // blood_group: 'default',
        custom_fields: {},
        profile_image: "",
        u_id: "",
      };
    },
    handleSubmit: function () {
      this.loading = true;
      this.submitted = true;

      // stop here if form is invalid
      this.$v.$touch();
      this.$nextTick(() => {
        if (
          document.querySelector(".is-invalid") !== null &&
          document.querySelector(".is-invalid") !== undefined
        ) {
          document
            .querySelector(".is-invalid")
            .scrollIntoView({ block: "center", behavior: "smooth" });
        } else if (
          document.querySelector(".invalid-feedback") !== null &&
          document.querySelector(".invalid-feedback") !== undefined
        ) {
          document
            .querySelector(".invalid-feedback")
            .scrollIntoView({ block: "center", behavior: "smooth" });
        }
      });
      if (this.$v.patientData.$invalid) {
        this.loading = false;
        return;
      }
      if (this.requiredFields.length > 0) {
        this.loading = false;
        displayErrorMessage(
          this.formTranslation.common.all_required_field_validation
        );
        return;
      }
      if (validateForm("patientDataForm")) {
        // this.patientData.dob = moment(this.patientData.dob).format("YYYY-MM-DD");
        post("patient_save", this.patientData)
          .then((response) => {
            this.loading = false;
            if (
              response.data.status !== undefined &&
              response.data.status === true
            ) {
              displayMessage(response.data.message);
              this.$router.push({ name: "patient" });
            } else {
              // this.patientData.dob = new Date(this.patientData.dob + ' 00:00');
              displayErrorMessage(response.data.message);
            }
          })
          .catch((error) => {
            console.log(error);
            this.loading = false;
            displayErrorMessage(
              this.formTranslation.common.internal_server_error
            );
          });
      }
    },
    getCustomFieldsValues: function (fieldsObj) {
      if (!fieldsObj || fieldsObj === undefined) {
        return false;
      }
      this.patientData.custom_fields = fieldsObj;
    },
    getRequireFields: function (validateRequired) {
      this.requiredFields = validateRequired;
    },
    getClinics: function () {
      this.clinicMultiselectLoader = true;
      get("get_static_data", {
        data_type: "clinic_list",
      })
        .then((response) => {
          this.clinicMultiselectLoader = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.clinics = response.data.data;
          }
        })
        .catch((error) => {
          this.clinicMultiselectLoader = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    getHideFieldsArrayFromFilter: function () {
      get("get_hide_fields_array_from_filter", {})
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.hideFields = response.data.data;
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    getCountryCodeData: function () {
      get("get_country_code_settings_data", {})
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.defaultCountryCode = response.data.data.country_code;
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    getUserRegistrationFormData: function () {
      get("get_user_registration_form_settings_data", {})
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.defaultUserRegistrationFormSettingData =
              response.data.data.userRegistrationFormSettingData;
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
  },
  computed: {
    userData() {
      return this.$store.state.userDataModule.user;
    },
    customFieldsData() {
      return this.patientData.custom_fields
        ? this.patientData.custom_fields
        : [];
    },
    // formTranslation: function () {
    // return this.$store.state.staticDataModule.langTranslateData ;
    // }
  },
};
</script>
<style scoped>
[type="date"] {
  background: #fff
    url(https://cdn1.iconfinder.com/data/icons/cc_mono_icon_set/blacks/16x16/calendar_2.png)
    97% 50% no-repeat;
}
[type="date"]::-webkit-inner-spin-button {
  display: none;
}
[type="date"]::-webkit-calendar-picker-indicator {
  opacity: 0;
}
label {
  display: block;
}
#doc_birthdate {
  border: 1px solid #c4c4c4;
  border-radius: 5px;
  background-color: #fff;
  padding: 3px 5px;
  box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.1);
  /* width: 190px; */
  width: 100%;
  height: 45px;
  color: #8c9cad;
}
#doc_birthdate ::placeholder {
  color: #8c9cad;
}
</style>
