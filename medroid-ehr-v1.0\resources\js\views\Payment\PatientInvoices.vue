<template>
  <div>
    <div class="p-6 max-w-7xl mx-auto">
      <!-- Header Section -->
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold">
          {{ formTranslation.patient_bill.bills }}
        </h1>
        <button
          v-if="kcCheckPermission('patient_bill_add') && !billCreateModel"
          @click="billCreateModel = !billCreateModel"
          class="flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
        >
          <i class="fa fa-plus mr-2"></i>
          {{ formTranslation.patient_bill.add_bill }}
        </button>
      </div>

      <!-- Search and Filter Section -->
      <div class="flex flex-wrap items-center justify-between gap-4 mb-6">
        <div class="flex space-x-2">
          <button
            v-for="status in filterStatuses"
            :key="status.value"
            @click="handleStatusFilter(status.value)"
            :class="[
              'px-4 py-2 rounded-lg text-sm',
              currentStatus === status.value
                ? 'bg-purple-600 text-white'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200',
            ]"
          >
            {{ status.label }}
          </button>
        </div>
        <div class="flex items-center space-x-4">
          <div class="relative">
            <i class="fa fa-search absolute left-3 top-3 text-gray-400"></i>
            <input
              v-model="searchQuery"
              @input="handleSearch"
              placeholder="Search invoices..."
              class="pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
              type="text"
            />
          </div>
        </div>
      </div>

      <!-- Bills List -->
      <div class="space-y-4" v-if="!pageLoader">
        <div
          v-for="bill in filteredBills"
          :key="bill.id"
          v-show="!bill.last_row"
          class="bg-white text-card-foreground rounded-xl border shadow overflow-hidden"
        >
          <div class="p-6">
            <div
              class="flex flex-col md:flex-row md:items-center md:justify-between"
            >
              <div>
                <div class="flex items-center space-x-3">
                  <h3 class="font-semibold text-lg">
                    {{ bill.service_name || "N/A" }}
                  </h3>
                  <span
                    :class="[
                      'px-2 py-1 rounded-full text-sm',
                      bill.status === '0'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-yellow-100 text-yellow-800',
                    ]"
                  >
                    {{
                      bill.status === "0"
                        ? formTranslation.patient_bill.paid
                        : formTranslation.patient_bill.unpaid
                    }}
                  </span>
                </div>
                <p class="text-gray-600 mt-1">
                  {{ formTranslation.common.id }}: {{ bill.bill_id }}
                </p>
                <div
                  class="flex flex-wrap items-center gap-2 text-sm text-gray-600 mt-2"
                >
                  <template v-if="bill.doctor_name">
                    <span
                      >{{ formTranslation.common.doctor }}:
                      {{ bill.doctor_name }}</span
                    >
                    <span class="hidden md:inline">•</span>
                  </template>
                  <template v-if="bill.patient_name">
                    <span
                      >{{ formTranslation.common.patient }}:
                      {{ bill.patient_name }}</span
                    >
                    <span class="hidden md:inline">•</span>
                  </template>
                  <span
                    >{{ clinic_extra.prefix
                    }}{{ formatAmount(bill.actual_amount)
                    }}{{ clinic_extra.postfix }}</span
                  >
                </div>
                <div class="text-sm text-gray-600 mt-1" v-if="bill.created_at">
                  {{ formTranslation.common.date }}:
                  {{ formatDate(bill.created_at) }}
                </div>
              </div>
              <div class="mt-4 md:mt-0 flex items-center space-x-3">
                <button
                  v-if="
                    kcCheckPermission('patient_bill_edit') &&
                    bill.status === '1' &&
                    getUserRole() !== 'patient'
                  "
                  @click="billModelOpen(bill, 'edit')"
                  class="px-4 py-2 text-purple-600 bg-purple-50 rounded-lg hover:bg-purple-100"
                >
                  <i class="fa fa-pen-alt mr-2"></i
                  >{{ formTranslation.common.edit }}
                </button>
                <button
                  v-if="kcCheckPermission('patient_bill_view')"
                  @click="billModelOpen(bill, 'detail')"
                  class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  <i class="fa fa-file-invoice mr-2"></i
                  >{{ formTranslation.patient_bill.bill_details }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="pageLoader" class="text-center py-8">
        <loader-component-2></loader-component-2>
      </div>

      <!-- No Data State -->
      <div
        v-if="
          !pageLoader && (!billingList.data.length || !filteredBills.length)
        "
        class="text-center py-8"
      >
        <p class="text-gray-500">{{ formTranslation.common.no_data_found }}</p>
      </div>

      <!-- Pagination -->
      <div
        v-if="totalRows > serverParams.perPage"
        class="mt-6 flex justify-end"
      >
        <div class="flex items-center space-x-2">
          <button
            @click="onPageChange({ currentPage: serverParams.page - 1 })"
            :disabled="serverParams.page === 1"
            class="px-3 py-1 border rounded-lg hover:bg-gray-50 disabled:opacity-50"
          >
            Previous
          </button>
          <span class="text-sm text-gray-600">
            Page {{ serverParams.page }} of
            {{ Math.ceil(totalRows / serverParams.perPage) }}
          </span>
          <button
            @click="onPageChange({ currentPage: serverParams.page + 1 })"
            :disabled="
              serverParams.page >= Math.ceil(totalRows / serverParams.perPage)
            "
            class="px-3 py-1 border rounded-lg hover:bg-gray-50 disabled:opacity-50"
          >
            Next
          </button>
        </div>
      </div>

      <BillDetailsModal
        v-if="encounterId"
        :showBillDetailsModal="billDetailsModal"
        @update:showBillDetailsModal="billDetailsModal = $event"
        :encounter-id="encounterId"
        :clinic_extra="
          encounterData?.clinic_extra !== undefined
            ? encounterData?.clinic_extra
            : {}
        "
      />
    </div>
  </div>
</template>

<script>
import { post, get } from "../../config/request";
import BillDetailsModal from "../../components/PatientBill/BillDetailsModal.vue";
import _ from "lodash";
import moment from "moment";

export default {
  components: {
    BillDetailsModal,
  },

  data: () => ({
    pageLoader: true,
    billingList: {
      data: [],
      column: [],
    },
    serverParams: {
      columnFilters: {
        service_type: "",
        status: "",
      },
      sort: [
        {
          field: "",
          type: "",
        },
      ],
      page: 1,
      perPage: 10,
      searchTerm: "",
    },
    totalRows: 0,
    clinic_extra: {
      prefix: "",
      postfix: "",
    },
    billDetailsModal: false,
    billEditDetailsModal: false,
    billCreateModel: false,
    encounterId: 0,
    encounterData: {},
    doctorId: 0,
    currentStatus: "all",
    searchQuery: "",
    filterStatuses: [
      { value: "all", label: "All" },
      { value: "pending", label: "Pending" },
      { value: "paid", label: "Paid" },
    ],
  }),

  computed: {
    filteredBills() {
      let bills = this.billingList.data;

      // Filter out total row
      bills = bills.filter((bill) => !bill.last_row);

      // Filter by status
      if (this.currentStatus !== "all") {
        const statusMap = {
          pending: "1",
          paid: "0",
        };
        bills = bills.filter(
          (bill) => bill.status === statusMap[this.currentStatus]
        );
      }

      return bills;
    },

    totalRow() {
      return this.billingList.data.find((bill) => bill.last_row === "yes");
    },

    totalOutstanding() {
      return this.billingList.data
        .filter((bill) => !bill.last_row && bill.status === "1")
        .reduce((sum, bill) => sum + (parseFloat(bill.actual_amount) || 0), 0);
    },

    pendingClaims() {
      return this.billingList.data.filter(
        (bill) => !bill.last_row && bill.status === "1"
      ).length;
    },
  },

  mounted() {
    this.init();
  },

  methods: {
    init() {
      this.getBillingDataList();
    },

    formatAmount(amount) {
      if (!amount && amount !== 0) return "0.00";
      return parseFloat(amount).toFixed(2);
    },

    formatDate(date) {
      if (!date) return "";
      return moment(date).format("MMMM D, YYYY");
    },

    handleStatusFilter(status) {
      this.currentStatus = status;
      this.serverParams.columnFilters.status =
        status === "all" ? "" : status === "paid" ? "0" : "1";
      this.updateParams({
        columnFilters: this.serverParams.columnFilters,
        page: 1,
      });
    },

    handleSearch: _.debounce(function () {
      this.updateParams({
        searchTerm: this.searchQuery,
        page: 1,
      });
    }, 300),

    async getBillingDataList() {
      this.pageLoader = true;
      try {
        const params = _.cloneDeep(this.serverParams);
        if (params.columnFilters.created_at?.start) {
          params.columnFilters.created_at = {
            start: moment(params.columnFilters.created_at.start).format(
              "YYYY-MM-DD"
            ),
            end: moment(params.columnFilters.created_at.end).format(
              "YYYY-MM-DD"
            ),
          };
        }

        const response = await get("billing_record_list", params);
        if (response.data.status) {
          this.billingList.data = response.data.data;
          this.totalRows = response.data.total_rows;
          if (response.data.clinic_extra) {
            this.clinic_extra = response.data.clinic_extra;
          }
        } else {
          this.billingList.data = [];
          this.totalRows = 0;
        }
      } catch (error) {
        console.error("Error fetching billing data:", error);
        this.$toast.error("Failed to fetch billing data");
      } finally {
        this.pageLoader = false;
      }
    },

    updateParams(newProps) {
      this.serverParams = { ...this.serverParams, ...newProps };
      this.getBillingDataList();
    },

    onPageChange({ currentPage }) {
      this.updateParams({ page: currentPage });
    },

    billModelOpen(data, type) {
      this.encounterId = data.id;
      this.encounterData = data;
      this.doctorId = data.doctor_id;

      if (type === "edit") {
        this.billEditDetailsModal = true;
      } else {
        this.billDetailsModal = true;
      }
    },

    handleBillSave() {
      this.billDetailsModal = false;
      this.billEditDetailsModal = false;
      this.resetBillState();
      this.getBillingDataList();
    },

    handleBillCancel() {
      this.billDetailsModal = false;
      this.billEditDetailsModal = false;
      this.resetBillState();
    },

    resetBillState() {
      this.encounterId = 0;
      this.encounterData = {};
      this.doctorId = 0;
    },
  },
};
</script>

<style scoped>
.badge {
  @apply px-2 py-1 rounded-full text-sm;
}
.badge-success {
  @apply bg-green-100 text-green-800;
}
.badge-warning {
  @apply bg-yellow-100 text-yellow-800;
}
</style>
