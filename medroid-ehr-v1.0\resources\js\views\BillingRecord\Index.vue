# BillingView.vue
<template>
  <div class="min-h-screen p-6 bg-gradient-to-br from-pink-50 to-purple-50">
    <!-- Header Section -->
    <div class="mb-8 flex justify-between items-center">
      <div class="flex items-center gap-4">
        <button
          class="flex items-center gap-2 px-4 py-2 bg-black text-sm text-white rounded-lg shadow-sm hover:bg-gray-800">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
            <path d="m12 19-7-7 7-7"></path>
            <path d="M19 12H5"></path>
          </svg>
          <span>Back</span>
        </button>
        <h1 class="text-2xl font-semibold text-gray-800">
          {{ formTranslation.patient_bill.bills }}
        </h1>
      </div>
      <div class="flex gap-3">
        <module-data-export v-if="kcCheckPermission('patient_bill_export')" :module-data="billingList.data"
          :module-name="formTranslation.patient_bill.bills" module-type="billings">
          <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="7 10 12 15 17 10"></polyline>
            <line x1="12" x2="12" y1="15" y2="3"></line>
          </svg>
          {{ formTranslation.common.import }}
        </module-data-export>
        <button v-if="kcCheckPermission('patient_bill_add')"
          @click="billModelOpen({id:-1,doctor_id:-1,appointment_id:-1}, 'edit')"
          class="px-4 py-2 bg-black text-sm text-white rounded-lg hover:bg-gray-800 flex items-center gap-2">
          <span>{{ formTranslation.patient_bill.add_bill }}</span>
        </button>
      </div>
    </div>

    <!-- Global Search -->
    <div class="relative mb-6">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
        class="lucide lucide-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5">
        <circle cx="11" cy="11" r="8"></circle>
        <path d="m21 21-4.3-4.3"></path>
      </svg>
      <input type="text" v-model="serverParams.searchTerm" @input="globalFilter" :placeholder="formTranslation.common.search_bills_data_global_placeholder
        "
        class="w-full pl-10 pr-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400" />
    </div>

    <!-- Advanced Filters -->
    <div class="grid grid-cols-6 gap-4 mb-6">
      <!-- ID Filter -->
      <div class="relative">
        <input type="text" v-model="serverParams.columnFilters.bill_id" @input="onColumnFilter"
          :placeholder="formTranslation.common.id"
          class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400" />
        <button v-if="serverParams.columnFilters.bill_id" @click="clearFilter('bill_id')"
          class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clip-rule="evenodd" />
          </svg>
        </button>
      </div>

      <!-- Doctor Filter -->
      <div class="relative">
        <input type="text" v-model="serverParams.columnFilters.doctor_name" @input="onColumnFilter"
          placeholder="Filter By Doctor"
          class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400" />
        <button v-if="serverParams.columnFilters.doctor_name" @click="clearFilter('doctor_name')"
          class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clip-rule="evenodd" />
          </svg>
        </button>
      </div>

      <!-- Clinic Filter -->
      <div class="relative">
        <input type="text" v-model="serverParams.columnFilters.clinic_name" @input="onColumnFilter"
          placeholder="Filter By Clinic"
          class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400" />
        <button v-if="serverParams.columnFilters.clinic_name" @click="clearFilter('clinic_name')"
          class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clip-rule="evenodd" />
          </svg>
        </button>
      </div>

      <!-- Patient Filter -->
      <div class="relative">
        <input type="text" v-model="serverParams.columnFilters.patient_name" @input="onColumnFilter"
          placeholder="Filter By Patient"
          class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400" />
        <button v-if="serverParams.columnFilters.patient_name" @click="clearFilter('patient_name')"
          class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clip-rule="evenodd" />
          </svg>
        </button>
      </div>

      <!-- Date Filter -->
      <div class="relative">
        <vc-date-picker v-model="serverParams.columnFilters.created_at" @input="onColumnFilter" mode="range"
          :popover="{ visibility: 'click' }" class="w-full">
          <template v-slot="{ inputValue, inputEvents }">
            <input :value="inputValue" v-on="inputEvents" placeholder="Filter By Date"
              class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400" />
          </template>
        </vc-date-picker>
        <button v-if="serverParams.columnFilters.created_at" @click="clearFilter('created_at')"
          class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clip-rule="evenodd" />
          </svg>
        </button>
      </div>

      <!-- Status Filter -->
      <div class="relative">
        <select v-model="serverParams.columnFilters.status" @change="onColumnFilter"
          class="w-full px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400">
          <option value="">
            {{ formTranslation.static_data.dt_lbl_plh_sr_fltr_status }}
          </option>
          <option value="0">{{ formTranslation.patient_bill.paid }}</option>
          <option value="1">{{ formTranslation.patient_bill.unpaid }}</option>
        </select>
        <button v-if="serverParams.columnFilters.status" @click="clearFilter('status')"
          class="absolute right-8 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clip-rule="evenodd" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Table Section -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-x-auto">
      <!-- Loader -->
      <div v-show="pageLoader" class="flex justify-center items-center py-8">
        <loader-component-2 />
      </div>

      <!-- Table -->
      <!-- Replace just the table section in your BillingView.vue -->
      <table class="w-full" v-show="!pageLoader">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              <input type="checkbox" class="rounded border-gray-300" @change="selectAllRows" />
            </th>
            <th v-for="column in [
              'ID',
              'Doctor/Clinic',
              'Patient/Services',
              'Amount Details',
              'Date',
              'Status',
              'Actions',
            ]" :key="column" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {{ column }}
            </th>
          </tr>
        </thead>

        <tbody class="divide-y divide-gray-200">
          <tr v-for="(row, rowIndex) in billingList.data" :key="`row-${row.id}-${rowIndex}`" class="hover:bg-gray-50">
            <!-- Checkbox -->
            <td class="px-6 py-4 whitespace-nowrap">
              <input type="checkbox" class="rounded border-gray-300" v-model="selectedRows" :value="row.id" />
            </td>

            <!-- ID -->
            <td class="px-6 py-4 whitespace-nowrap">
              {{ row.bill_id }}
            </td>

            <!-- Doctor/Clinic -->
            <td class="px-6 py-4">
              <div class="text-sm font-medium text-gray-900">
                {{ row.doctor_name }}
              </div>
              <div class="text-xs text-gray-500">{{ row.clinic_name }}</div>
            </td>

            <!-- Patient/Services -->
            <td class="px-6 py-4">
              <div class="text-sm font-medium text-gray-900">
                {{ row.patient_name }}
              </div>
              <div class="text-xs text-gray-500">{{ row.service_name }}</div>
            </td>

            <!-- Amount Details -->
            <td class="px-6 py-4">
              <div class="space-y-1">
                <div class="flex justify-between text-sm">
                  <span class="text-gray-500">Total:</span>
                  <span class="font-medium">{{ row.total_amount }}</span>
                </div>
                <div class="flex justify-between text-sm font-medium">
                  <span class="text-gray-900">Due:</span>
                  <span class="text-gray-900">{{ row.actual_amount }}</span>
                </div>
              </div>
            </td>

            <!-- Date -->
            <td class="px-6 py-4 whitespace-nowrap">
              {{ row.created_at }}
            </td>

            <!-- Status -->
            <td class="px-6 py-4 whitespace-nowrap">
              <div v-if="row.bill_id>0">
                <span v-if="row.payment_status=='paid'" :class="[
                  'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                  row.payment_status	 === 'paid'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800',
                ]">
                  {{
                    row.payment_status	 === "paid"
                      ? formTranslation.patient_bill.paid
                      : formTranslation.patient_bill.unpaid
                  }}
                </span>
                <select 
                  v-else
                  v-model="row.payment_status"
                  @change="updatePaymentStatus(row)"
                  class="text-xs border border-gray-300 rounded p-1 focus:outline-none focus:ring-1 focus:ring-blue-500"
                >
                  <option value="unpaid">{{ formTranslation.patient_bill.unpaid }}</option>
                  <option value="paid">{{ formTranslation.patient_bill.paid }}</option>
                </select>
              </div>

            </td>

            <!-- Actions -->
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex gap-2" v-if="row.bill_id>0">
                <button v-if="
                  kcCheckPermission('patient_bill_edit') && row.status == '1'
                " @click="billModelOpen(row, 'edit')" class="p-1 hover:bg-gray-100 rounded"
                  :title="formTranslation.common.edit">
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-gray-600" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                  </svg>
                </button>
                <button v-if="kcCheckPermission('patient_bill_view')" @click="billModelOpen(row, 'detail')"
                  class="p-1 hover:bg-gray-100 rounded" :title="formTranslation.patient_bill.bill_details">
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-gray-600" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </button>
                <router-link v-if="kcCheckPermission('patient_encounter_view')" :to="{
                  name: 'patient-encounter.dashboard',
                  params: { encounter_id: row.id },
                }" class="p-1 hover:bg-gray-100 rounded" :title="formTranslation.patient_encounter.encounter_details">
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-blue-600" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </router-link>
              </div>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- Pagination -->
      <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
        <div class="flex items-center gap-2">
          <span class="text-sm text-gray-700">Rows per page:</span>
          <select v-model="serverParams.perPage" @change="onPerPageChange"
            class="border border-gray-300 rounded-md text-sm p-1">
            <option>10</option>
            <option>25</option>
            <option>50</option>
          </select>
        </div>
        <div class="flex items-center gap-4">
          <span class="text-sm text-gray-700">
            Page {{ serverParams.page }} of
            {{ Math.ceil(totalRows / serverParams.perPage) }}
          </span>
          <div class="flex gap-2">
            <button @click="onPageChange({ currentPage: serverParams.page - 1 })" :disabled="serverParams.page === 1"
              class="p-1 rounded hover:bg-gray-100 disabled:opacity-50">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-chevron-left w-5 h-5 text-gray-600">
                <path d="m15 18-6-6 6-6"></path>
              </svg>
            </button>
            <button @click="onPageChange({ currentPage: serverParams.page + 1 })" :disabled="serverParams.page >= Math.ceil(totalRows / serverParams.perPage)
              " class="p-1 rounded hover:bg-gray-100 disabled:opacity-50">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-chevron-right w-5 h-5 text-gray-600">
                <path d="m9 18 6-6-6-6"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Bill Generation Modal -->
    <BillFormModal v-if="encounterId && encounterData" :showGenerateBillModal="billEditDetailsModel"
      @update:showGenerateBillModal="billEditDetailsModel = $event" :encounter-id="encounterId"
      :checkOutVal="checkOutVal" @onBillSaved="handleBillSave" :patientBillData="patientBillData"
      :appointment-data="encounterData" :clinic_extra="encounterData?.clinic_extra !== undefined
        ? encounterData?.clinic_extra
        : {}
        " :doctorId="encounterData?.doctor_id" :userData="userData" :isFromEncounter="false" />

    <!-- View Bill Modal -->
    <BillDetailsModal :biilingId="biilingId"  :showBillDetailsModal="billDetailsModel"
      @update:showBillDetailsModal="billDetailsModel = $event" :encounter-id="encounterId" :ref="BillDetailsModalRef" :clinic_extra="encounterData?.clinic_extra !== undefined
        ? encounterData?.clinic_extra
        : {}
        " />
  </div>
</template>

<script>
import { post, get } from "../../config/request";
import BillDetailsModal from "../../components/PatientBill/BillDetailsModal";
import BillFormModal from "../../components/PatientBill/BillFormModal";
import ModalPopup from "../../components/Modal/Index";

export default {
  name: "BillingView",
  components: {
    BillDetailsModal,
    BillFormModal,
    ModalPopup,
  },
  data: () => {
    return {
      pageLoader: true,
      billingList: {
        data: [],
        column: [],
      },
      selectedRows: [],
      allRowsSelected: false,
      serverParams: {
        columnFilters: {
          bill_id: "",
          doctor_name: "",
          clinic_name: "",
          patient_name: "",
          created_at: "",
          status: "",
        },
        sort: [
          {
            field: "",
            type: "",
          },
        ],
        page: 1,
        perPage: 10,
        searchTerm: "",
      },
      oldServerParams: {
        columnFilters: {},
        searchTerm: "",
        perPage: 10,
      },
      totalRows: 0,
      clinic_extra: {
        prefix: "",
        postfix: "",
      },
      billDetailsModel: false,
      billEditDetailsModel: false,
      patientBillData: null,
      encounterId: 0,
      checkOutVal: 0,
      encounterData: {},
      doctorId: 0,
      billCreateModel: false,
      selected_encounter_id: "",
      withoutBillEncounterData: [],
      generateBillSubmittedFalse: false,
      generateBillLoader: false,
      biilingId:null
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      if (this.kcCheckPermission("patient_bill_add")) {
        this.getWithoutBillEncounterList();
      }
      this.billingList = this.defaultbillingList();
      this.getBillingDataList();
    },
    defaultbillingList() {
      return {
        column: [
          {
            field: "bill_id",
            label: this.formTranslation.common.id,
            sortable: true,
          },
          {
            field: "doctor_name",
            label: "Doctor/Clinic",
            sortable: true,
          },
          {
            field: "patient_name",
            label: "Patient/Services",
            sortable: true,
          },
          {
            field: "amount",
            label: "Amount Details",
            sortable: true,
          },
          {
            field: "created_at",
            label: this.formTranslation.patient_encounter.dt_lbl_date,
            sortable: true,
          },
          {
            field: "status",
            label: this.formTranslation.common.status,
            sortable: true,
          },
          {
            field: "actions",
            label: this.formTranslation.patient_encounter.dt_lbl_action,
            sortable: false,
          },
        ],
        data: [],
      };
    },
    getWithoutBillEncounterList() {
      get("get_without_bill_encounter_list", {})
        .then((data) => {
          if (data.data.status !== undefined && data.data.status === true) {
            this.withoutBillEncounterData = data.data.data;
          } else {
            this.withoutBillEncounterData = [];
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    getBillingDataList() {
      this.pageLoader = true;

      let params = {
        ...this.serverParams,
        columnFilters: { ...this.serverParams.columnFilters },
      };

      // Format date range if exists
      if (
        params.columnFilters.created_at &&
        typeof params.columnFilters.created_at === "object"
      ) {
        params.columnFilters.created_at = {
          start: moment(params.columnFilters.created_at.start).format(
            "YYYY-MM-DD"
          ),
          end: moment(params.columnFilters.created_at.end).format("YYYY-MM-DD"),
        };
      }

      get("billing_record_list", params)
        .then((response) => {
          this.pageLoader = false;
          if (response.data.status === true) {
            this.billingList.data = response.data.data;
            this.totalRows = response.data.total_rows;

            if (response.data.clinic_extra) {
              this.clinic_extra = response.data.clinic_extra;
            }
          } else {
            this.billingList.data = [];
            this.totalRows = 0;
          }
        })
        .catch((error) => {
          this.pageLoader = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    updateParams(newProps) {
      this.serverParams = Object.assign({}, this.serverParams, newProps);
      this.getBillingDataList();
    },
    onPageChange(params) {
      this.updateParams({ page: params.currentPage });
    },
    onPerPageChange() {
      this.updateParams({
        page: 1,
      });
    },
    onSortChange(params) {
      this.updateParams({
        sort: params,
      });
    },
    globalFilter: _.debounce(function () {
      this.updateParams({
        page: 1,
      });
    }, 300),
    onColumnFilter: _.debounce(function () {
      // Remove empty filters
      let filters = {};
      Object.keys(this.serverParams.columnFilters).forEach((key) => {
        if (this.serverParams.columnFilters[key]) {
          filters[key] = this.serverParams.columnFilters[key];
        }
      });

      // Handle date range specially
      if (filters.created_at && typeof filters.created_at === "object") {
        filters.created_at = {
          start: moment(filters.created_at.start).format("YYYY-MM-DD"),
          end: moment(filters.created_at.end).format("YYYY-MM-DD"),
        };
      }

      this.updateParams({
        columnFilters: filters,
        page: 1,
      });
    }, 300),
    clearFilter(filterName) {
      this.serverParams.columnFilters[filterName] = "";
      this.onColumnFilter();
    },
    encounterSelect() {
      if (
        this.selected_encounter_id === "" ||
        parseInt(this.selected_encounter_id) <= 0
      ) {
        return;
      }
      let _this = this;
      let data = this.withoutBillEncounterData.filter(function (value, key) {
        return _this.selected_encounter_id == value.id;
      });

      if (data.length > 0) {
        this.encounterData = data[0];
      }
    },
    generateBill() {
      if (
        this.selected_encounter_id === "" ||
        parseInt(this.selected_encounter_id) <= 0
      ) {
        this.generateBillSubmittedFalse = true;
        return;
      }
      this.generateBillLoader = true;
      this.generateBillSubmittedFalse = false;

      let _this = this;
      let data = this.withoutBillEncounterData.filter(function (value, key) {
        return _this.selected_encounter_id == value.id;
      });

      if (data.length > 0) {
        this.billModelOpen(data[0], "edit");
      }
      this.generateBillLoader = false;
      this.billCreateModel = false;
      this.selected_encounter_id = "";
    },
    billModelOpen(data, type) {
      
      this.billEditDetailsModel = true 
      this.encounterId = data.id;
      this.encounterData = data;
      this.doctorId = data.doctor_id;
      this.checkOutVal = data.appointment_id ? 1 : 0;

      if (type === "edit") {
        this.billEditDetailsModel = true;
      } else {
        this.biilingId= data.bill_id;
        this.encounterId = null;
        this.billDetailsModel = true;
      }
    },
    handleBillSave(data) {
      this.billDetailsModel = false;
      this.billEditDetailsModel = false;
      this.encounterId = 0;
      this.encounterData = {};
      this.doctorId = 0;
      this.checkOutVal = 0;
      this.getBillingDataList();
      if (this.kcCheckPermission("patient_bill_add")) {
        this.getWithoutBillEncounterList();
      }
    },
    handleBillCancel() {
      this.billDetailsModel = false;
      this.billEditDetailsModel = false;
      this.encounterId = 0;
      this.encounterData = {};
      this.doctorId = 0;
      this.checkOutVal = 0;
    },
    handleSort(column) {
      if (!column.sortable) return;

      let sort = {
        field: column.field,
        type: "desc",
      };

      if (this.serverParams.sort.field === column.field) {
        sort.type = this.serverParams.sort.type === "desc" ? "asc" : "desc";
      }

      this.updateParams({
        sort: sort,
        page: 1,
      });
    },

    selectAllRows(event) {
      this.allRowsSelected = event.target.checked;
      if (this.allRowsSelected) {
        this.selectedRows = this.billingList.data.map((row) => row.id);
      } else {
        this.selectedRows = [];
      }
    },

    formatCurrency(value) {
      return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
      }).format(value);
    },

    formatDate(date) {
      return moment(date).format("MMMM D, YYYY");
    },
    async updatePaymentStatus(row) {
    try {
      // Store the original status in case we need to revert
      const originalStatus = row.payment_status;
      
      // Show loading state if needed
      this.isLoading = true;
      
      // Make an API call to update the payment status
      // Replace with your actual API endpoint and authentication
    
      get('patient_bill_update_payment_status', {
        bill_id: row.bill_id,
        payment_status: row.payment_status
      })
        .then((response) => {
          
          if (response.data.status === true) {
            // Update the row data with the new status
            row.payment_status = response.data.data.payment_status;
            displayMessage(this.formTranslation.patient_bill.payment_status_update_failed)

          } else {
            // Revert to the original status if the API call failed
            row.payment_status = originalStatus;
          }
        })
        .catch((error) => {
          console.error('Error updating payment status:', error);
          // Revert to the original status if the API call failed
          row.payment_status = originalStatus;
        });
      
     
    } catch (error) {
      // Handle errors
      console.error('Error updating payment status:', error);
      
      // Revert to the original status if the API call failed
      row.payment_status = originalStatus;
      
      displayMessage(this.formTranslation.patient_bill.payment_status_update_failed)
     
    } finally {
      // Hide loading state
      this.isLoading = false;
    }
  }
  },
  computed: {
    userData() {
      return this.$store.state.userDataModule.user;
    },
  },
};
</script>
