<?php
/**
 * Patient Check-In System for Medroid
 * 
 * This file adds a shortcode and AJAX handlers for a patient check-in system
 * that works with the Medroid Clinic Management System.
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Add shortcode for patient check-in
add_shortcode('kivicare_checkin', 'kivicare_patient_checkin_shortcode');

// Register a WordPress page for the check-in URL
function kivicare_register_checkin_page() {
    // Check if the page already exists
    $check_in_page = get_page_by_path('check-in');
    
    if (!$check_in_page) {
        // Create a new page for the check-in
        $page_args = array(
            'post_title'    => 'Patient Check-In',
            'post_content'  => '[kivicare_checkin]',
            'post_status'   => 'publish',
            'post_type'     => 'page',
            'post_name'     => 'check-in'
        );
        
        wp_insert_post($page_args);
    }
}
add_action('init', 'kivicare_register_checkin_page', 10);

/**
 * Generate a secure clinic token from clinic ID
 * 
 * @param int $clinic_id The clinic ID
 * @return string The secure token
 */
function kivicare_generate_clinic_token($clinic_id) {
    error_log("Generating clinic token for clinic ID: $clinic_id");
    
    // Get or generate the clinic's unique salt
    $salt = get_option('kivicare_clinic_' . $clinic_id . '_salt');
    if (!$salt) {
        // Generate a new salt if one doesn't exist
        error_log("No salt found for clinic ID: $clinic_id. Generating new salt.");
        $salt = wp_generate_password(32, true, true);
        $salt_update_result = update_option('kivicare_clinic_' . $clinic_id . '_salt', $salt);
        error_log("Salt update result: " . ($salt_update_result ? "Success" : "Failed"));
    } else {
        error_log("Using existing salt for clinic ID: $clinic_id");
    }
    
    // Generate a token using the clinic ID and salt
    $token = hash_hmac('sha256', $clinic_id . time(), $salt);
    error_log("Generated token hash: " . substr($token, 0, 10) . "...");
    
    // Store the token to clinic ID mapping with an expiration time (20 days)
    $tokens = get_option('kivicare_clinic_tokens', array());
    error_log("Current token count in storage: " . count($tokens));
    
    $expiration = time() + (20 * DAY_IN_SECONDS);
    $tokens[$token] = array(
        'clinic_id' => $clinic_id,
        'expires' => $expiration
    );
    
    $update_result = update_option('kivicare_clinic_tokens', $tokens);
    error_log("Token storage update result: " . ($update_result ? "Success" : "Failed"));
    error_log("Token will expire on: " . date('Y-m-d H:i:s', $expiration));
    
    return $token;
}

/**
 * Get a clinic ID from a token
 * 
 * @param string $token The token to look up
 * @return int|bool The clinic ID or false if invalid
 */
function kivicare_get_clinic_id_from_token($token) {
    // First check if it's a registered token
    $tokens = get_option('kivicare_clinic_tokens', array());
    
    if (isset($tokens[$token])) {
        $token_data = $tokens[$token];
        
        // Check if token is expired
        if ($token_data['expires'] > time()) {
            return $token_data['clinic_id'];
        } else {
            // Remove expired token
            unset($tokens[$token]);
            update_option('kivicare_clinic_tokens', $tokens);
        }
    }
    
    // If no server-side token found, check if it might be a frontend-generated token
    // Our frontend tokens have the format: hashA-hashB-clinicId
    if (strpos($token, '-') !== false) {
        $parts = explode('-', $token);
        
        // Check if the last part is a numeric clinic ID
        if (count($parts) >= 3 && is_numeric(end($parts))) {
            $clinic_id = intval(end($parts));
            error_log("Extracted clinic ID {$clinic_id} from frontend token");
            
            // Verify this is a real clinic
            global $wpdb;
            $clinic_exists = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM {$wpdb->prefix}kc_clinics WHERE id = %d",
                $clinic_id
            ));
            
            if ($clinic_exists) {
                return $clinic_id;
            }
        }
        
        // Fallback to clinic ID 1 if we couldn't extract or verify the clinic ID
        error_log("Using default clinic ID 1 for frontend token");
        return 1;
    }
    
    return false;
}

/**
 * Get the valid token for a clinic ID
 * 
 * @param int $clinic_id The clinic ID
 * @return string The valid token (generates a new one if needed)
 */
function kivicare_get_clinic_token($clinic_id) {
    // Add logging for debugging
    error_log("Looking up token for clinic ID: $clinic_id");
    
    $tokens = get_option('kivicare_clinic_tokens', array());
    $current_time = time();
    
    // Look for an existing valid token for this clinic
    foreach ($tokens as $token => $data) {
        if ($data['clinic_id'] == $clinic_id && $data['expires'] > $current_time) {
            error_log("Found existing valid token for clinic ID: $clinic_id");
            return $token;
        }
    }
    
    error_log("No existing valid token found for clinic ID: $clinic_id. Generating new token.");
    
    // No valid token found, generate a new one
    $token = kivicare_generate_clinic_token($clinic_id);
    
    if ($token) {
        error_log("Successfully generated new token for clinic ID: $clinic_id");
    } else {
        error_log("Failed to generate new token for clinic ID: $clinic_id");
    }
    
    return $token;
}

/**
 * Cleanup expired tokens (run weekly)
 */
function kivicare_cleanup_expired_tokens() {
    $tokens = get_option('kivicare_clinic_tokens', array());
    $current_time = time();
    $updated = false;
    
    foreach ($tokens as $token => $data) {
        if ($data['expires'] <= $current_time) {
            unset($tokens[$token]);
            $updated = true;
        }
    }
    
    if ($updated) {
        update_option('kivicare_clinic_tokens', $tokens);
    }
}
add_action('wp_scheduled_events', 'kivicare_cleanup_expired_tokens');

/**
 * Register REST API endpoints for clinic tokens
 */
function kivicare_register_api_endpoints() {
    register_rest_route('kivicare/v1', '/get-clinic-token', array(
        'methods' => 'GET',
        'callback' => 'kivicare_api_get_clinic_token',
        'permission_callback' => function() {
            return is_user_logged_in();
        }
    ));
}
add_action('rest_api_init', 'kivicare_register_api_endpoints');

/**
 * REST API callback to get a clinic token
 */
function kivicare_api_get_clinic_token($request) {
    $clinic_id = isset($_GET['clinic_id']) ? intval($_GET['clinic_id']) : 0;
    
    if (!$clinic_id) {
        // Try to get clinic ID from current user if not provided
        $user_id = get_current_user_id();
        
        // Check user role
        $user = get_userdata($user_id);
        $user_roles = $user->roles;
        $kivicare_prefix = defined('KIVI_CARE_PREFIX') ? KIVI_CARE_PREFIX : 'kivicare_';
        
        if (in_array($kivicare_prefix . 'clinic_admin', $user_roles)) {
            // Get clinic ID for clinic admin
            global $wpdb;
            $clinic = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}kc_clinics WHERE clinic_admin_id = %d", 
                $user_id
            ));
            if ($clinic) {
                $clinic_id = $clinic->id;
            }
        } else if (in_array($kivicare_prefix . 'receptionist', $user_roles)) {
            // Get clinic ID for receptionist
            global $wpdb;
            $clinic_mapping = $wpdb->get_var($wpdb->prepare(
                "SELECT clinic_id FROM {$wpdb->prefix}kc_receptionist_clinic_mappings WHERE receptionist_id = %d LIMIT 1", 
                $user_id
            ));
            if ($clinic_mapping) {
                $clinic_id = intval($clinic_mapping);
            }
        }
    }
    
    if (!$clinic_id) {
        return new WP_Error('invalid_clinic', 'Invalid or missing clinic ID', array('status' => 400));
    }
    
    // Check if user has permission for this clinic
    // Simple permission check - is user admin, clinic admin, or receptionist for this clinic
    $has_permission = current_user_can('administrator');
    
    if (!$has_permission) {
        $user_id = get_current_user_id();
        $user = get_userdata($user_id);
        $user_roles = $user->roles;
        $kivicare_prefix = defined('KIVI_CARE_PREFIX') ? KIVI_CARE_PREFIX : 'kivicare_';
        
        if (in_array($kivicare_prefix . 'clinic_admin', $user_roles)) {
            // Check if user is owner of this clinic
            global $wpdb;
            $clinic = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM {$wpdb->prefix}kc_clinics WHERE id = %d AND clinic_admin_id = %d", 
                $clinic_id, $user_id
            ));
            $has_permission = ($clinic > 0);
        } else if (in_array($kivicare_prefix . 'receptionist', $user_roles)) {
            // Check if receptionist is mapped to this clinic
            global $wpdb;
            $mapping = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM {$wpdb->prefix}kc_receptionist_clinic_mappings 
                WHERE receptionist_id = %d AND clinic_id = %d", 
                $user_id, $clinic_id
            ));
            $has_permission = ($mapping > 0);
        }
    }
    
    if (!$has_permission) {
        return new WP_Error('unauthorized', 'You do not have permission for this clinic', array('status' => 403));
    }
    
    // Get or generate token
    $token = kivicare_get_clinic_token($clinic_id);
    
    if (!$token) {
        return new WP_Error('token_generation_failed', 'Failed to generate clinic token', array('status' => 500));
    }
    
    // Return the token and the full URL
    $site_url = site_url('/check-in/');
    $check_in_url = add_query_arg(array('token' => $token), $site_url);
    
    return array(
        'token' => $token,
        'clinic_id' => $clinic_id,
        'check_in_url' => $check_in_url
    );
}

/**
 * AJAX handler for getting clinic token
 */
function kivicare_ajax_get_clinic_token() {
    // Verify nonce for security
    check_ajax_referer('kc_nonce', 'security');
    
    // Check if user is logged in
    if (!is_user_logged_in()) {
        wp_send_json_error(['message' => 'User not logged in.']);
        return;
    }
    
    $clinic_id = isset($_POST['clinic_id']) ? $_POST['clinic_id'] : 0;
    
    // Handle 'auto' detection mode
    if ($clinic_id === 'auto' || !$clinic_id) {
        // Reset for detection logic and add extensive debugging
        $clinic_id = 0;
        error_log("Auto detecting clinic ID for current user...");
        
        // Try to get clinic ID from current user
        $user_id = get_current_user_id();
        error_log("Current user ID: $user_id");
        
        // Check user role
        $user = get_userdata($user_id);
        if (!$user) {
            wp_send_json_error(['message' => 'User not found']);
            return;
        }
        
        $user_roles = $user->roles;
        $kivicare_prefix = defined('KIVI_CARE_PREFIX') ? KIVI_CARE_PREFIX : 'kivicare_';
        error_log("Using Medroid prefix: $kivicare_prefix");
        error_log("User roles: " . print_r($user_roles, true));
        
        // First try to get clinic ID from existing transient for this user
        $transient_key = 'kivicare_user_clinic_' . $user_id;
        $cached_clinic_id = get_transient($transient_key);
        
        if (false !== $cached_clinic_id) {
            $clinic_id = intval($cached_clinic_id);
            error_log("Found clinic ID in transient cache: $clinic_id");
        }
        
        // Next try to get from usermeta
        if (!$clinic_id) {
            // Try different meta keys that might store clinic ID
            foreach (['clinic_id', 'kivicare_clinic_id', $kivicare_prefix . 'clinic_id'] as $meta_key) {
                $clinic_id_meta = get_user_meta($user_id, $meta_key, true);
                if (!empty($clinic_id_meta)) {
                    $clinic_id = intval($clinic_id_meta);
                    error_log("Found clinic ID in user meta '$meta_key': $clinic_id");
                    break;
                }
            }
        }
        
        // If still no clinic ID, try role-specific lookups
        if (!$clinic_id) {
            global $wpdb;
            
            // For clinic admin
            $clinic_admin_roles = array(
                $kivicare_prefix . 'clinic_admin',
                'clinic_admin',
                'kivicare_clinic_admin'
            );
            
            $is_clinic_admin = false;
            foreach ($clinic_admin_roles as $role) {
                if (in_array($role, $user_roles)) {
                    $is_clinic_admin = true;
                    break;
                }
            }
            
            if ($is_clinic_admin) {
                error_log("User is a clinic admin. Looking up clinic...");
                
                // Get clinic where user is owner
                $clinic = $wpdb->get_row($wpdb->prepare(
                    "SELECT id, name FROM {$wpdb->prefix}kc_clinics WHERE clinic_admin_id = %d", 
                    $user_id
                ));
                
                if ($clinic) {
                    $clinic_id = $clinic->id;
                    error_log("Found clinic ID {$clinic_id} ({$clinic->name}) for clinic admin");
                    
                    // Cache this result for future requests
                    set_transient($transient_key, $clinic_id, DAY_IN_SECONDS);
                } else {
                    error_log("No clinic found for clinic admin with ID: $user_id");
                    // Get all clinics for debugging
                    $all_clinics = $wpdb->get_results("SELECT id, name, clinic_admin_id FROM {$wpdb->prefix}kc_clinics LIMIT 10");
                    error_log("Available clinics (first 10): " . ($all_clinics ? print_r($all_clinics, true) : "None found"));
                }
            }
            
            // For receptionist
            $receptionist_roles = array(
                $kivicare_prefix . 'receptionist',
                'receptionist',
                'kivicare_receptionist'
            );
            
            $is_receptionist = false;
            foreach ($receptionist_roles as $role) {
                if (in_array($role, $user_roles)) {
                    $is_receptionist = true;
                    break;
                }
            }
            
            if ($is_receptionist && !$clinic_id) {
                error_log("User is a receptionist. Looking up clinic mapping...");
                
                // Get clinic mapping
                $clinic_mapping = $wpdb->get_row($wpdb->prepare(
                    "SELECT rcm.clinic_id, c.name FROM {$wpdb->prefix}kc_receptionist_clinic_mappings rcm
                    LEFT JOIN {$wpdb->prefix}kc_clinics c ON c.id = rcm.clinic_id
                    WHERE rcm.receptionist_id = %d 
                    LIMIT 1", 
                    $user_id
                ));
                
                if ($clinic_mapping) {
                    $clinic_id = intval($clinic_mapping->clinic_id);
                    $clinic_name = $clinic_mapping->name ?? 'Unknown';
                    error_log("Found clinic ID {$clinic_id} ({$clinic_name}) for receptionist");
                    
                    // Cache this result for future requests
                    set_transient($transient_key, $clinic_id, DAY_IN_SECONDS);
                } else {
                    error_log("No clinic mapping found for receptionist with ID: $user_id");
                    // Check all mappings for debugging
                    $all_mappings = $wpdb->get_results(
                        "SELECT rm.receptionist_id, rm.clinic_id, c.name as clinic_name, u.display_name as receptionist_name
                        FROM {$wpdb->prefix}kc_receptionist_clinic_mappings rm
                        LEFT JOIN {$wpdb->prefix}kc_clinics c ON c.id = rm.clinic_id
                        LEFT JOIN {$wpdb->users} u ON u.ID = rm.receptionist_id
                        LIMIT 10"
                    );
                    error_log("Available receptionist mappings (first 10): " . ($all_mappings ? print_r($all_mappings, true) : "None found"));
                }
            }
            
            // As a last resort, get the first clinic in the system
            if (!$clinic_id) {
                error_log("No clinic association found for user. Looking for first clinic in system...");
                $first_clinic = $wpdb->get_row("SELECT id, name FROM {$wpdb->prefix}kc_clinics ORDER BY id ASC LIMIT 1");
                if ($first_clinic) {
                    $clinic_id = $first_clinic->id;
                    error_log("Using first clinic in system as fallback: {$clinic_id} ({$first_clinic->name})");
                }
            }
        }
    } else {
        // Ensure clinic_id is an integer if it's not 'auto'
        $clinic_id = intval($clinic_id);
        error_log("Using explicitly provided clinic ID: $clinic_id");
    }
    
    // Final check if we have a valid clinic ID
    if (!$clinic_id) {
        error_log("WARNING: Could not determine clinic ID, defaulting to 1");
        $clinic_id = 1; // Default to ID 1 as final fallback
    }
    
    // Check if user has permission for this clinic
    // Simple permission check - is user admin, clinic admin, or receptionist for this clinic
    $has_permission = current_user_can('administrator');
    
    if (!$has_permission) {
        $user_id = get_current_user_id();
        $user = get_userdata($user_id);
        $user_roles = $user->roles;
        $kivicare_prefix = defined('KIVI_CARE_PREFIX') ? KIVI_CARE_PREFIX : 'kivicare_';
        
        if (in_array($kivicare_prefix . 'clinic_admin', $user_roles)) {
            // Check if user is owner of this clinic
            global $wpdb;
            $clinic = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM {$wpdb->prefix}kc_clinics WHERE id = %d AND clinic_admin_id = %d", 
                $clinic_id, $user_id
            ));
            $has_permission = ($clinic > 0);
        } else if (in_array($kivicare_prefix . 'receptionist', $user_roles)) {
            // Check if receptionist is mapped to this clinic
            global $wpdb;
            $mapping = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM {$wpdb->prefix}kc_receptionist_clinic_mappings 
                WHERE receptionist_id = %d AND clinic_id = %d", 
                $user_id, $clinic_id
            ));
            $has_permission = ($mapping > 0);
        }
    }
    
    if (!$has_permission) {
        wp_send_json_error(['message' => 'You do not have permission for this clinic']);
        return;
    }
    
    // Log that we're generating a token for this clinic ID
    error_log("Generating token for clinic ID: $clinic_id");
    
    // Get or generate token
    $token = kivicare_get_clinic_token($clinic_id);
    
    if (!$token) {
        error_log("Failed to generate token for clinic ID: $clinic_id");
        wp_send_json_error(['message' => 'Failed to generate clinic token']);
        return;
    }
    
    error_log("Successfully generated token for clinic ID: $clinic_id");
    
    // Return the token and the full URL
    $site_url = site_url('/check-in/');
    $check_in_url = add_query_arg(array('token' => $token), $site_url);
    
    error_log("Generated check-in URL: $check_in_url");
    
    wp_send_json_success([
        'token' => $token,
        'clinic_id' => $clinic_id,
        'check_in_url' => $check_in_url
    ]);
}
add_action('wp_ajax_kivicare_get_clinic_token', 'kivicare_ajax_get_clinic_token');

// Add AJAX handlers
add_action('wp_ajax_kivicare_patient_checkin', 'kivicare_process_patient_checkin');
add_action('wp_ajax_nopriv_kivicare_patient_checkin', 'kivicare_process_patient_checkin');

add_action('wp_ajax_kivicare_register_patient', 'kivicare_process_patient_registration');
add_action('wp_ajax_nopriv_kivicare_register_patient', 'kivicare_process_patient_registration');

add_action('wp_ajax_kivicare_verify_patient', 'kivicare_verify_returning_patient');
add_action('wp_ajax_nopriv_kivicare_verify_patient', 'kivicare_verify_returning_patient');

/**
 * Shortcode function for patient check-in
 */
function kivicare_patient_checkin_shortcode($atts) {
    // Extract attributes
    $atts = shortcode_atts(array(
        'clinic_id' => 0
    ), $atts, 'kivicare_checkin');
    
    $clinic_id = 0;
    
    // Check if token is passed via URL parameter
    if (isset($_GET['token']) && !empty($_GET['token'])) {
        // Get clinic ID from token
        $token = sanitize_text_field($_GET['token']);
        $clinic_id = kivicare_get_clinic_id_from_token($token);
        
        if (!$clinic_id) {
            return '<div class="kivi-widget text-red-600 p-4">Invalid or expired token. Please request a new check-in link from your clinic.</div>';
        }
    } 
    // Check if clinic_id is directly passed (for backward compatibility or admin use)
    else if (isset($_GET['clinic_id']) && !empty($_GET['clinic_id'])) {
        // Always allow numeric clinic ID for backward compatibility
        $clinic_id = intval($_GET['clinic_id']);
        if ($clinic_id > 0) {
            // Valid numeric clinic ID
            error_log("Using numeric clinic ID: $clinic_id from URL");
        } else {
            return '<div class="kivi-widget text-red-600 p-4">Invalid clinic ID. Please use a valid check-in link.</div>';
        }
    } else {
        $clinic_id = intval($atts['clinic_id']);
    }
    
    // Verify clinic exists
    global $wpdb;
    $clinic = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$wpdb->prefix}kc_clinics WHERE id = %d",
        $clinic_id
    ));
    
    if (empty($clinic)) {
        return '<div class="kivi-widget text-red-600 p-4">Clinic not found. Please check the clinic ID.</div>';
    }
    
    // Generate unique form IDs
    $checkin_form_id = 'kivicare-checkin-form-' . uniqid();
    $new_patient_form_id = 'kivicare-new-patient-form-' . uniqid();
    $returning_patient_form_id = 'kivicare-returning-patient-form-' . uniqid();
    
    // Define theme colors
    $primary_color = '#8E44EB';
    $secondary_color = '#E94B82';
    $gradient = 'linear-gradient(to right, #8E44EB, #E94B82)';
    $light_bg = '#FFFFFF';
    
    // Start output buffer
    ob_start();
    ?>
    <style>
        /* Basic text alignment */
        .kivi-care-check-in label,
        .kivi-care-check-in h3,
        .kivi-care-check-in p.invalid-feedback,
        .kivi-care-check-in .text-note {
            text-align: left !important;
        }
        
        /* Remove all height constraints */
        body, html {
            height: auto !important;
            overflow-y: auto !important;
        }
        
        /* Main container styling */
        .kivi-widget.kivi-care-check-in {
            height: auto !important;
            min-height: auto !important;
            overflow: visible !important;
            position: relative !important;
            margin: 20px 0 !important;
        }
        
        /* Tab panel specific fixes */
        .kivi-care-check-in .tab-panel {
            height: auto !important;
            max-height: none !important;
            overflow: visible !important;
            padding-bottom: 30px !important;
        }
        
        /* Form styling */
        .kivi-care-check-in form {
            display: flex !important;
            flex-direction: column !important;
            height: auto !important;
            max-height: none !important;
            overflow: visible !important;
        }
        
        /* New patient tab specific fixes */
        #panel-new-patient {
            height: auto !important;
            max-height: none !important;
            overflow: visible !important;
            position: relative !important;
        }
        
        /* Container reset */
        .kivi-care-check-in .w-full.max-w-4xl {
            height: auto !important;
            max-height: none !important;
            overflow: visible !important;
            margin: 0 auto !important;
        }
        
        /* Content area */
        .kivi-care-check-in .p-4.overflow-y-visible {
            height: auto !important;
            max-height: none !important;
            overflow: visible !important;
        }
        
        /* Responsive grid */
        @media (max-width: 768px) {
            .kivi-care-check-in .grid {
                grid-template-columns: 1fr !important;
            }
            .kivi-care-check-in .tab-button {
                padding-left: 0 !important;
                padding-right: 0 !important;
                font-size: 0.8rem !important;
            }
            .kivi-care-check-in .tab-button svg {
                width: 14px !important;
                height: 14px !important;
            }
            .kivi-care-check-in .tab-button span {
                white-space: nowrap !important;
                font-size: 0.8rem !important;
            }
        }
    </style>
    <div class="kivi-widget kivi-care-check-in font-sans" style="padding: 20px 0; height: auto; overflow: visible;">
        <div class="px-2 md:px-4 flex justify-center" style="height: auto; overflow: visible;">
            <div class="w-full max-w-4xl bg-white rounded-xl shadow-lg" style="height: auto; overflow: visible;">
                <!-- Header -->
                <div class="p-6 text-white" style="background: <?php echo esc_attr($gradient); ?>;">
                    <?php if (!empty($clinic->clinic_logo)) : ?>
                        <img src="<?php echo esc_url(wp_get_attachment_url($clinic->clinic_logo)); ?>" alt="<?php echo esc_attr($clinic->name); ?>" class="h-12 mb-3">
                    <?php endif; ?>
                    <h1 class="text-2xl font-bold mb-1 text-left">Patient Check-in Portal</h1>
                    <p class="opacity-90 text-left">Welcome to <?php echo esc_html($clinic->name); ?></p>
                    <?php if (!empty($clinic->address)) : ?>
                        <p class="opacity-75 text-sm mt-1 text-left"><?php echo esc_html($clinic->address); ?></p>
                    <?php endif; ?>
                </div>
                
                <!-- Tabs -->
                <div class="flex border-b" style="position: sticky; top: 0; background: white; z-index: 10;">
                    <button id="tab-checkin" class="tab-button active flex-1 py-3 px-2 sm:px-4 text-sm font-medium flex items-center justify-center gap-1 sm:gap-2 transition-all border-b-2 whitespace-nowrap" style="color: <?php echo esc_attr($primary_color); ?>; border-color: <?php echo esc_attr($primary_color); ?>;">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user">
                            <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
                            <circle cx="12" cy="7" r="4"></circle>
                        </svg>
                        <span>Check-in</span>
                    </button>
                    <button id="tab-new-patient" class="tab-button flex-1 py-3 px-2 sm:px-4 text-sm font-medium flex items-center justify-center gap-1 sm:gap-2 transition-all text-gray-500 hover:opacity-80 whitespace-nowrap" style="color: #666666; border-color: transparent;">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user-plus">
                            <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                            <circle cx="9" cy="7" r="4"></circle>
                            <line x1="19" x2="19" y1="8" y2="14"></line>
                            <line x1="22" x2="16" y1="11" y2="11"></line>
                        </svg>
                        <span class="whitespace-nowrap">New Patient</span>
                    </button>
                    <button id="tab-returning" class="tab-button flex-1 py-3 px-2 sm:px-4 text-sm font-medium flex items-center justify-center gap-1 sm:gap-2 transition-all text-gray-500 hover:opacity-80 whitespace-nowrap" style="color: #666666; border-color: transparent;">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users">
                            <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                            <circle cx="9" cy="7" r="4"></circle>
                            <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                        </svg>
                        <span>Returning</span>
                    </button>
                </div>
                
                <div class="p-4" style="height: auto; overflow: visible; position: static;">
                    <!-- Check-In Tab -->
                    <div id="panel-checkin" class="tab-panel space-y-6">
                        <div class="p-4 rounded-lg" style="border: 1px solid rgba(142, 68, 235, 0.2);">
                            <p class="text-gray-600">Please enter your email to check in for your scheduled appointment.</p>
                        </div>

                        <form id="<?php echo esc_attr($checkin_form_id); ?>" class="space-y-4 needs-validation">
                            <input type="hidden" name="action" value="kivicare_patient_checkin">
                            <input type="hidden" name="clinic_id" value="<?php echo esc_attr($clinic_id); ?>">
                            
                            <div>
                                <label for="checkin-email" class="block text-sm font-medium text-gray-700 mb-1 text-left">Email Address</label>
                                <input type="email" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 transition-all" 
                                    id="checkin-email" name="email" required placeholder="<EMAIL>" 
                                    style="--tw-ring-color: <?php echo esc_attr($primary_color); ?>;">
                                <p class="hidden invalid-feedback text-red-600 text-sm mt-1 text-left">Please enter a valid email address.</p>
                            </div>
                            
                            <button type="submit" class="w-full text-white py-3 px-4 rounded-lg font-medium hover:opacity-90 transition-colors flex items-center justify-center gap-2" 
                                style="background: <?php echo esc_attr($gradient); ?>;">
                                Check In
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right">
                                    <path d="m9 18 6-6-6-6"></path>
                                </svg>
                            </button>
                        </form>
                        <div id="checkin-result" class="mt-4"></div>
                    </div>
                    
                    <!-- New Patient Tab -->
                    <div id="panel-new-patient" class="tab-panel hidden space-y-4">
                        <div class="p-4 rounded-lg" style="border: 1px solid rgba(142, 68, 235, 0.2);">
                            <p class="text-gray-600">Please complete the form below to register as a new patient.</p>
                        </div>

                        <form id="<?php echo esc_attr($new_patient_form_id); ?>" class="space-y-3 needs-validation">
                            <input type="hidden" name="action" value="kivicare_register_patient">
                            <input type="hidden" name="clinic_id" value="<?php echo esc_attr($clinic_id); ?>">
                            
                            <!-- Patient Personal Information -->
                            <div class="space-y-3">
                                <h3 class="text-lg font-medium text-gray-800 pb-1 border-b border-gray-200 text-left">Personal Information</h3>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label for="first-name" class="block text-sm font-medium text-gray-700 mb-1 text-left">
                                            First Name <span class="text-red-600">*</span>
                                        </label>
                                        <input type="text" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 transition-all" 
                                            id="first-name" name="first_name" required
                                            style="--tw-ring-color: <?php echo esc_attr($primary_color); ?>;">
                                        <p class="hidden invalid-feedback text-red-600 text-sm mt-1">
                                            Please enter your first name.
                                        </p>
                                    </div>
                                    <div>
                                        <label for="last-name" class="block text-sm font-medium text-gray-700 mb-1">
                                            Last Name <span class="text-red-600">*</span>
                                        </label>
                                        <input type="text" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 transition-all" 
                                            id="last-name" name="last_name" required
                                            style="--tw-ring-color: <?php echo esc_attr($primary_color); ?>;">
                                        <p class="hidden invalid-feedback text-red-600 text-sm mt-1">
                                            Please enter your last name.
                                        </p>
                                    </div>
                                </div>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label for="register-email" class="block text-sm font-medium text-gray-700 mb-1">
                                            Email Address <span class="text-red-600">*</span>
                                        </label>
                                        <input type="email" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 transition-all" 
                                            id="register-email" name="email" required
                                            style="--tw-ring-color: <?php echo esc_attr($primary_color); ?>;">
                                        <p class="hidden invalid-feedback text-red-600 text-sm mt-1">
                                            Please enter a valid email address.
                                        </p>
                                    </div>
                                    <div>
                                        <label for="gender" class="block text-sm font-medium text-gray-700 mb-1">
                                            Gender <span class="text-red-600">*</span>
                                        </label>
                                        <select class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 transition-all" 
                                            id="gender" name="gender" required
                                            style="--tw-ring-color: <?php echo esc_attr($primary_color); ?>;">
                                            <option value="">Select Gender</option>
                                            <option value="male">Male</option>
                                            <option value="female">Female</option>
                                            <option value="other">Other</option>
                                        </select>
                                        <p class="hidden invalid-feedback text-red-600 text-sm mt-1">
                                            Please select your gender.
                                        </p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Contact Information -->
                            <div class="space-y-3">
                                <h3 class="text-lg font-medium text-gray-800 pb-1 border-b border-gray-200 text-left">Contact Information</h3>
                                
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div class="md:col-span-1">
                                        <label for="country-code" class="block text-sm font-medium text-gray-700 mb-1">
                                            Country Code <span class="text-red-600">*</span>
                                        </label>
                                        <select class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 transition-all" 
                                            id="country-code" name="country_code" required
                                            style="--tw-ring-color: <?php echo esc_attr($primary_color); ?>;">
                                            <option value="+44" selected>United Kingdom (+44)</option>
                                            <option value="+1">United States (+1)</option>
                                            <option value="+353">Ireland (+353)</option>
                                            <option value="+33">France (+33)</option>
                                            <option value="+49">Germany (+49)</option>
                                            <option value="+39">Italy (+39)</option>
                                            <option value="+34">Spain (+34)</option>
                                            <option value="+31">Netherlands (+31)</option>
                                            <option value="+61">Australia (+61)</option>
                                            <option value="+91">India (+91)</option>
                                        </select>
                                        <p class="hidden invalid-feedback text-red-600 text-sm mt-1">
                                            Please select a country code.
                                        </p>
                                    </div>
                                    <div class="md:col-span-2">
                                        <label for="mobile-number" class="block text-sm font-medium text-gray-700 mb-1">
                                            Mobile Number <span class="text-red-600">*</span>
                                        </label>
                                        <input type="tel" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 transition-all" 
                                            id="mobile-number" name="mobile_number" required
                                            style="--tw-ring-color: <?php echo esc_attr($primary_color); ?>;">
                                        <p class="hidden invalid-feedback text-red-600 text-sm mt-1">
                                            Please enter your mobile number.
                                        </p>
                                    </div>
                                </div>
                                
                                <div>
                                    <label for="dob" class="block text-sm font-medium text-gray-700 mb-1">
                                        Date of Birth 
                                    </label>
                                    <input type="date" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 transition-all" 
                                        id="dob" name="dob"
                                        style="--tw-ring-color: <?php echo esc_attr($primary_color); ?>;">
                                </div>
                                
                                <div>
                                    <label for="address" class="block text-sm font-medium text-gray-700 mb-1">
                                        Address
                                    </label>
                                    <textarea class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 transition-all" 
                                        id="address" name="address" rows="2"
                                        style="--tw-ring-color: <?php echo esc_attr($primary_color); ?>;"></textarea>
                                </div>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label for="city" class="block text-sm font-medium text-gray-700 mb-1">
                                            City
                                        </label>
                                        <input type="text" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 transition-all" 
                                            id="city" name="city"
                                            style="--tw-ring-color: <?php echo esc_attr($primary_color); ?>;">
                                    </div>
                                    <div>
                                        <label for="state" class="block text-sm font-medium text-gray-700 mb-1">
                                            State/Province
                                        </label>
                                        <input type="text" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 transition-all" 
                                            id="state" name="state"
                                            style="--tw-ring-color: <?php echo esc_attr($primary_color); ?>;">
                                    </div>
                                </div>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label for="country" class="block text-sm font-medium text-gray-700 mb-1">
                                            Country
                                        </label>
                                        <input type="text" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 transition-all" 
                                            id="country" name="country"
                                            style="--tw-ring-color: <?php echo esc_attr($primary_color); ?>;">
                                    </div>
                                    <div>
                                        <label for="postal-code" class="block text-sm font-medium text-gray-700 mb-1">
                                            Postal Code
                                        </label>
                                        <input type="text" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 transition-all" 
                                            id="postal-code" name="postal_code"
                                            style="--tw-ring-color: <?php echo esc_attr($primary_color); ?>;">
                                    </div>
                                </div>
                            </div>
                            
                            <!-- NHS Information -->
                            <div class="space-y-3">
                                <h3 class="text-lg font-medium text-gray-800 pb-1 border-b border-gray-200 text-left">NHS Information</h3>
                                
                                <div>
                                    <label for="nhs" class="block text-sm font-medium text-gray-700 mb-1">
                                        NHS Number
                                    </label>
                                    <input type="text" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 transition-all" 
                                        id="nhs" name="nhs" placeholder="NHS Number (if applicable)"
                                        style="--tw-ring-color: <?php echo esc_attr($primary_color); ?>;">
                                </div>
                                
                                <div>
                                    <label for="registered-gp-name" class="block text-sm font-medium text-gray-700 mb-1">
                                        Registered GP Name
                                    </label>
                                    <input type="text" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 transition-all" 
                                        id="registered-gp-name" name="registered_gp_name"
                                        style="--tw-ring-color: <?php echo esc_attr($primary_color); ?>;">
                                </div>
                                
                                <div>
                                    <label for="registered-gp-address" class="block text-sm font-medium text-gray-700 mb-1">
                                        Registered GP Address
                                    </label>
                                    <textarea class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 transition-all" 
                                        id="registered-gp-address" name="registered_gp_address" rows="2"
                                        style="--tw-ring-color: <?php echo esc_attr($primary_color); ?>;"></textarea>
                                </div>
                            </div>
                            
                            <!-- Insurance Information -->
                            <div class="space-y-3">
                                <h3 class="text-lg font-medium text-gray-800 pb-1 border-b border-gray-200 text-left">Insurance Information</h3>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label for="insurance-provider" class="block text-sm font-medium text-gray-700 mb-1">
                                            Insurance Provider
                                        </label>
                                        <select class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 transition-all" 
                                            id="insurance-provider" name="insurance_provider"
                                            style="--tw-ring-color: <?php echo esc_attr($primary_color); ?>;">
                                            <option value="">Select Insurance Provider</option>
                                            <option value="NHS">NHS</option>
                                            <option value="Bupa">Bupa</option>
                                            <option value="Aviva">Aviva</option>
                                            <option value="AXA">AXA</option>
                                            <option value="Allianz">Allianz</option>
                                            <option value="Cigna">Cigna</option>
                                            <option value="UnitedHealthcare">UnitedHealthcare</option>
                                            <option value="Aetna">Aetna</option>
                                            <option value="Humana">Humana</option>
                                            <option value="Blue Cross">Blue Cross</option>
                                            <option value="Other">Other</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label for="insurance-no" class="block text-sm font-medium text-gray-700 mb-1">
                                            Insurance Number
                                        </label>
                                        <input type="text" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 transition-all" 
                                            id="insurance-no" name="insurance_no"
                                            style="--tw-ring-color: <?php echo esc_attr($primary_color); ?>;">
                                    </div>
                                </div>
                            </div>
                            
                            <div>
                                <p class="text-sm text-gray-500 mb-4 text-note">
                                    <span class="text-red-600">*</span> Indicates required fields
                                </p>
                                <button type="submit" class="w-full text-white py-3 px-4 rounded-lg font-medium hover:opacity-90 transition-colors flex items-center justify-center gap-2" 
                                    style="background: linear-gradient(to right, #4CAF50, #2E7D32);">
                                    Register as New Patient
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right">
                                        <path d="m9 18 6-6-6-6"></path>
                                    </svg>
                                </button>
                            </div>
                        </form>
                        <div id="new-patient-result" class="mt-4"></div>
                    </div>
                    
                    <!-- Returning Patient Tab -->
                    <div id="panel-returning" class="tab-panel hidden space-y-6">
                        <div class="p-8 text-center">
                            <div class="mb-6">
                                <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="mx-auto text-gray-400">
                                    <rect width="18" height="18" x="3" y="4" rx="2" ry="2"></rect>
                                    <line x1="16" x2="16" y1="2" y2="6"></line>
                                    <line x1="8" x2="8" y1="2" y2="6"></line>
                                    <line x1="3" x2="21" y1="10" y2="10"></line>
                                    <path d="M8 14h.01"></path>
                                    <path d="M12 14h.01"></path>
                                    <path d="M16 14h.01"></path>
                                    <path d="M8 18h.01"></path>
                                    <path d="M12 18h.01"></path>
                                    <path d="M16 18h.01"></path>
                                </svg>
                            </div>
                            <h2 class="text-2xl font-bold mb-2 text-gray-700">Coming Soon</h2>
                            <p class="text-gray-600 mb-6">We're currently working on enhancing our returning patient experience.</p>
                            <p class="text-gray-600">This feature will be available in the near future. Please use the Check-in tab if you have an appointment today.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
    jQuery(document).ready(function($) {
        const primaryColor = '<?php echo esc_js($primary_color); ?>';
        
        // Function to ensure the container adapts to content height
        function adjustContainerHeight() {
            // Give the content time to render, then adjust container height
            setTimeout(function() {
                $(".kivi-widget.kivi-care-check-in").css("height", "auto");
            }, 100);
        }
        
        // Run on page load and initialize first tab properly
        adjustContainerHeight();
        
        // Initialize hidden tabs with proper CSS
        $('.tab-panel:not(:first)').css({
            'position': 'absolute',
            'left': '-9999px',
            'display': 'none'
        });
        
        // Ensure first panel is properly displayed
        $('.tab-panel:first').css({
            'position': 'static',
            'left': 'auto',
            'display': 'block'
        });
        
        // Tabs functionality
        $('.tab-button').on('click', function() {
            // Remove active class from all tabs
            $('.tab-button').removeClass('active').css({
                'color': '#666666',
                'border-color': 'transparent'
            });
            
            // Add active class to clicked tab
            $(this).addClass('active').css({
                'color': primaryColor,
                'border-color': primaryColor
            });
            
            // Hide all panels (use display none to fully unload them from layout)
            $('.tab-panel').addClass('hidden').css({
                'position': 'absolute',
                'left': '-9999px',
                'display': 'none'
            });
            
            // Show the corresponding panel
            var panelId = $(this).attr('id').replace('tab-', 'panel-');
            $('#' + panelId).removeClass('hidden').css({
                'position': 'static',
                'left': 'auto',
                'display': 'block'
            });
            
            // Reset scroll position
            $('html, body').scrollTop(0);
            
            // Adjust container height for new content
            adjustContainerHeight();
        });
        
        // Handle window resize
        $(window).on('resize', adjustContainerHeight);
        
        // Form validation
        const validateForm = function(form) {
            let isValid = true;
            
            // Reset validation state
            form.find('.invalid-feedback').addClass('hidden');
            
            // Check all required fields
            form.find('[required]').each(function() {
                if (!this.checkValidity()) {
                    $(this).siblings('.invalid-feedback').removeClass('hidden');
                    isValid = false;
                }
            });
            
            return isValid;
        };

        // Check-in form submission
        $('#<?php echo esc_js($checkin_form_id); ?>').on('submit', function(e) {
            e.preventDefault();
            
            if (validateForm($(this))) {
                var formData = $(this).serialize();
                
                // Disable form inputs and button during submission
                $(this).find('input, button').prop('disabled', true);
                
                // Show loading indicator
                $('#checkin-result').html('<div class="text-center py-4"><div class="flex items-center justify-center space-x-2"><div class="animate-pulse rounded-full bg-indigo-600 h-3 w-3"></div><div class="animate-pulse rounded-full bg-indigo-600 h-3 w-3" style="animation-delay: 0.2s"></div><div class="animate-pulse rounded-full bg-indigo-600 h-3 w-3" style="animation-delay: 0.4s"></div></div><p class="mt-3 text-gray-600 animate__animated animate__fadeIn">Checking in...</p></div>');
                
                $.ajax({
                    url: '<?php echo admin_url('admin-ajax.php'); ?>',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response.success) {
                            $('#checkin-result').html('<div class="bg-green-50 border border-green-200 rounded-lg text-green-800 p-4 shadow-sm animate__animated animate__fadeIn" role="alert"><div class="flex"><div class="flex-shrink-0"><i class="fas fa-check-circle text-green-500 text-xl mr-3"></i></div><p class="font-medium">' + response.data.message + '</p></div></div>');
                            
                            // Show success message for 2 seconds then reload page
                            setTimeout(function() {
                                window.location.reload();
                            }, 2000);
                        } else {
                            $('#checkin-result').html('<div class="bg-red-50 border border-red-200 rounded-lg text-red-800 p-4 shadow-sm animate__animated animate__fadeIn" role="alert"><div class="flex"><div class="flex-shrink-0"><i class="fas fa-exclamation-circle text-red-500 text-xl mr-3"></i></div><p class="font-medium">' + response.data.message + '</p></div></div>');
                            // Re-enable form inputs and button
                            $('#<?php echo esc_js($checkin_form_id); ?>').find('input, button').prop('disabled', false);
                        }
                    },
                    error: function() {
                        $('#checkin-result').html('<div class="bg-red-50 border border-red-200 rounded-lg text-red-800 p-4 shadow-sm animate__animated animate__fadeIn" role="alert"><div class="flex"><div class="flex-shrink-0"><i class="fas fa-exclamation-triangle text-red-500 text-xl mr-3"></i></div><p class="font-medium">An error occurred. Please try again.</p></div></div>');
                        // Re-enable form inputs and button
                        $('#<?php echo esc_js($checkin_form_id); ?>').find('input, button').prop('disabled', false);
                    }
                });
            }
        });

        // New patient form submission
        $('#<?php echo esc_js($new_patient_form_id); ?>').on('submit', function(e) {
            e.preventDefault();
            
            if (validateForm($(this))) {
                var formData = $(this).serialize();
                
                // Disable form inputs and button during submission
                $(this).find('input, select, textarea, button').prop('disabled', true);
                
                // Show loading indicator
                $('#new-patient-result').html('<div class="text-center py-4"><div class="flex items-center justify-center space-x-2"><div class="animate-pulse rounded-full bg-green-500 h-3 w-3"></div><div class="animate-pulse rounded-full bg-green-500 h-3 w-3" style="animation-delay: 0.2s"></div><div class="animate-pulse rounded-full bg-green-500 h-3 w-3" style="animation-delay: 0.4s"></div></div><p class="mt-3 text-gray-600 animate__animated animate__fadeIn">Registering...</p></div>');
                
                $.ajax({
                    url: '<?php echo admin_url('admin-ajax.php'); ?>',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response.success) {
                            $('#new-patient-result').html('<div class="bg-green-50 border border-green-200 rounded-lg text-green-800 p-4 shadow-sm animate__animated animate__fadeIn" role="alert"><div class="flex"><div class="flex-shrink-0"><i class="fas fa-check-circle text-green-500 text-xl mr-3"></i></div><p class="font-medium">' + response.data.message + '</p></div></div>');
                            
                            // Show success message for 3 seconds then reload page
                            setTimeout(function() {
                                window.location.reload();
                            }, 3000);
                        } else {
                            $('#new-patient-result').html('<div class="bg-red-50 border border-red-200 rounded-lg text-red-800 p-4 shadow-sm animate__animated animate__fadeIn" role="alert"><div class="flex"><div class="flex-shrink-0"><i class="fas fa-exclamation-circle text-red-500 text-xl mr-3"></i></div><p class="font-medium">' + response.data.message + '</p></div></div>');
                            // Re-enable form inputs and button
                            $('#<?php echo esc_js($new_patient_form_id); ?>').find('input, select, textarea, button').prop('disabled', false);
                        }
                    },
                    error: function() {
                        $('#new-patient-result').html('<div class="bg-red-50 border border-red-200 rounded-lg text-red-800 p-4 shadow-sm animate__animated animate__fadeIn" role="alert"><div class="flex"><div class="flex-shrink-0"><i class="fas fa-exclamation-triangle text-red-500 text-xl mr-3"></i></div><p class="font-medium">An error occurred. Please try again.</p></div></div>');
                        // Re-enable form inputs and button
                        $('#<?php echo esc_js($new_patient_form_id); ?>').find('input, select, textarea, button').prop('disabled', false);
                    }
                });
            }
        });

        // Returning patient form submission
        $('#<?php echo esc_js($returning_patient_form_id); ?>').on('submit', function(e) {
            e.preventDefault();
            
            if (validateForm($(this))) {
                var formData = $(this).serialize();
                $('#returning-result').html('<div class="text-center py-4"><div class="flex items-center justify-center space-x-2"><div class="animate-pulse rounded-full bg-blue-500 h-3 w-3"></div><div class="animate-pulse rounded-full bg-blue-500 h-3 w-3" style="animation-delay: 0.2s"></div><div class="animate-pulse rounded-full bg-blue-500 h-3 w-3" style="animation-delay: 0.4s"></div></div><p class="mt-3 text-gray-600 animate__animated animate__fadeIn">Verifying...</p></div>');
                
                $.ajax({
                    url: '<?php echo admin_url('admin-ajax.php'); ?>',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response.success) {
                            $('#returning-result').html('<div class="bg-green-50 border border-green-200 rounded-lg text-green-800 p-4 shadow-sm animate__animated animate__fadeIn" role="alert"><div class="flex"><div class="flex-shrink-0"><i class="fas fa-check-circle text-green-500 text-xl mr-3"></i></div><p class="font-medium">' + response.data.message + '</p></div></div>');
                            $('#booking-container').removeClass('hidden');
                            
                            // Show loading indicator
                            $('#booking-container').html('<div class="text-center py-8"><div class="animate-spin h-10 w-10 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div><p class="text-gray-600">Loading appointment booking form...</p></div>');
                            
                            // Use the Medroid booking shortcode for this clinic and patient
                            var shortcode = '[kivicareBookAppointment clinic_id="<?php echo esc_js($clinic_id); ?>" patient_id="' + response.data.patient_id + '"]';
                            
                            // Load the booking form via AJAX
                            $.post('<?php echo admin_url('admin-ajax.php'); ?>', {
                                action: 'kivicare_render_shortcode',
                                shortcode: shortcode
                            }, function(response) {
                                $('#booking-container').html(response);
                            });
                        } else {
                            $('#returning-result').html('<div class="bg-red-50 border border-red-200 rounded-lg text-red-800 p-4 shadow-sm animate__animated animate__fadeIn" role="alert"><div class="flex"><div class="flex-shrink-0"><i class="fas fa-exclamation-circle text-red-500 text-xl mr-3"></i></div><p class="font-medium">' + response.data.message + '</p></div></div>');
                            $('#booking-container').addClass('hidden');
                        }
                    },
                    error: function() {
                        $('#returning-result').html('<div class="bg-red-50 border border-red-200 rounded-lg text-red-800 p-4 shadow-sm animate__animated animate__fadeIn" role="alert"><div class="flex"><div class="flex-shrink-0"><i class="fas fa-exclamation-triangle text-red-500 text-xl mr-3"></i></div><p class="font-medium">An error occurred. Please try again.</p></div></div>');
                        $('#booking-container').addClass('hidden');
                    }
                });
            }
        });
    });
    </script>
    <?php
    return ob_get_clean();
}

/**
 * Process patient check-in
 */
function kivicare_process_patient_checkin() {
    // Verify nonce for security (implementation would go here)
    
    // Get the email and clinic ID
    $email = sanitize_email($_POST['email']);
    $clinic_id = intval($_POST['clinic_id']);
    
    if (empty($email)) {
        wp_send_json_error(['message' => 'Please enter a valid email address.']);
    }
    
    // Check if this email exists as a patient
    global $wpdb;
    
    // Get the user by email
    $user = get_user_by('email', $email);
    
    if (!$user) {
        wp_send_json_error(['message' => 'No patient found with this email. Please use the New Patient tab to register.']);
    }
    
    // Debug: Log user ID and email
    error_log('Patient check-in attempt: User ID: ' . $user->ID . ', Email: ' . $email);
    
    // Check if the user is associated with any patient records
    $patient_exists = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$wpdb->prefix}kc_patient_clinic_mappings WHERE patient_id = %d",
        $user->ID
    ));
    
    if ($patient_exists) {
        // User is a patient in our system
        error_log('Found patient in clinic mappings');
    } else {
        // Check if the user is a patient by role
        $user_role = get_user_meta($user->ID, 'wp_capabilities', true);
        
        if (empty($user_role) || !is_array($user_role)) {
            error_log('User has no roles defined');
            wp_send_json_error(['message' => 'This email is not registered as a patient. Please use the New Patient tab to register.']);
        }
        
        // Debug: Log user roles
        error_log('User roles: ' . print_r($user_role, true));
        
        // Check for any patient role (different naming conventions possible)
        $is_patient = false;
        foreach (array_keys($user_role) as $role_key) {
            if (strpos(strtolower($role_key), 'patient') !== false) {
                $is_patient = true;
                break;
            }
        }
        
        if (!$is_patient) {
            wp_send_json_error(['message' => 'This email is not registered as a patient. Please use the New Patient tab to register.']);
        }
    }
    
    // Check if the patient has an appointment today at this clinic
    $patient_id = $user->ID;
    $today = date('Y-m-d');
    
    // Debug
    error_log("Checking appointments for patient_id: $patient_id, clinic_id: $clinic_id, date: $today");
    
    // Try to find appointment with more forgiving query - only matching by patient_id and date
    $appointment = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$wpdb->prefix}kc_appointments 
        WHERE patient_id = %d AND appointment_start_date = %s",
        $patient_id, $today
    ));
    
    if (!$appointment) {
        // If no appointment found today, check if they have any upcoming appointments
        $future_appointment = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}kc_appointments 
            WHERE patient_id = %d AND appointment_start_date >= %s 
            ORDER BY appointment_start_date ASC
            LIMIT 1",
            $patient_id, $today
        ));
        
        if ($future_appointment) {
            $appt_date = date('l, F j, Y', strtotime($future_appointment->appointment_start_date));
            wp_send_json_error([
                'message' => "You don't have an appointment for today. Your next appointment is scheduled for $appt_date. You can book a new appointment using the Book Appointment tab."
            ]);
        } else {
            wp_send_json_error(['message' => 'No appointment found for today at this clinic. You can book an appointment using the Book Appointment tab.']);
        }
    }
    
    // Debug
    error_log("Appointment found: " . print_r($appointment, true));
    
    // Update the appointment status to "checked in" (status code 4)
    $old_status = $appointment->status;
    $update_result = $wpdb->update(
        $wpdb->prefix . 'kc_appointments',
        ['status' => 4], // Status 4 is "Checked In" in KiviCare
        ['id' => $appointment->id],
        ['%d'], // Integer format
        ['%d']
    );
    
    // Debug the update result
    error_log("Appointment status update result: " . ($update_result !== false ? "Success - rows affected: $update_result" : "Failed: " . $wpdb->last_error));
    
    // Trigger KiviCare's built-in appointment status update hooks
    if ($update_result && function_exists('do_action')) {
        // Prepare appointment data
        $appointment_data = (array)$appointment;
        $appointment_data['status'] = 4; // Updated status
        
        // Log the action we're about to do
        error_log("Triggering kc_appointment_status_update action for appointment ID: {$appointment->id}");
        
        // Trigger KiviCare's status update hook
        do_action('kc_appointment_status_update', $appointment_data, $old_status, 4);
        
        // For compatibility with other functions expecting the updated status data
        do_action('kc_appointment_update', $appointment_data);
    }
    
    // Get patient details
    $patient_name = $user->display_name;
    
    // Success message
    wp_send_json_success([
        'message' => "Welcome $patient_name! You have been checked in successfully for your appointment."
    ]);
}

/**
 * Process new patient registration
 */
function kivicare_process_patient_registration() {
    // Verify nonce for security (implementation would go here)
    
    // Get required form data
    $first_name = sanitize_text_field($_POST['first_name']);
    $last_name = sanitize_text_field($_POST['last_name']);
    $email = sanitize_email($_POST['email']);
    $country_code = sanitize_text_field($_POST['country_code'] ?? '+44'); // Default to UK
    $mobile_number = sanitize_text_field($_POST['mobile_number']);
    $gender = sanitize_text_field($_POST['gender']);
    $clinic_id = intval($_POST['clinic_id']);
    
    // Validate required fields
    if (empty($first_name) || empty($last_name) || empty($email) || empty($mobile_number) || empty($gender) || empty($country_code)) {
        wp_send_json_error(['message' => 'All required fields must be filled out.']);
    }
    
    // Check if email already exists
    if (email_exists($email)) {
        wp_send_json_error(['message' => 'This email is already registered. Please use the Check-In tab instead.']);
    }
    
    // Get optional form data
    $dob = !empty($_POST['dob']) ? sanitize_text_field($_POST['dob']) : '';
    $address = !empty($_POST['address']) ? sanitize_textarea_field($_POST['address']) : '';
    $city = !empty($_POST['city']) ? sanitize_text_field($_POST['city']) : '';
    $state = !empty($_POST['state']) ? sanitize_text_field($_POST['state']) : '';
    $country = !empty($_POST['country']) ? sanitize_text_field($_POST['country']) : '';
    $postal_code = !empty($_POST['postal_code']) ? sanitize_text_field($_POST['postal_code']) : '';
    $nhs = !empty($_POST['nhs']) ? sanitize_text_field($_POST['nhs']) : '';
    $registered_gp_name = !empty($_POST['registered_gp_name']) ? sanitize_text_field($_POST['registered_gp_name']) : '';
    $registered_gp_address = !empty($_POST['registered_gp_address']) ? sanitize_textarea_field($_POST['registered_gp_address']) : '';
    $insurance_provider = !empty($_POST['insurance_provider']) ? sanitize_text_field($_POST['insurance_provider']) : '';
    $insurance_no = !empty($_POST['insurance_no']) ? sanitize_text_field($_POST['insurance_no']) : '';
    
    // Debug
    error_log("Processing new patient registration for: $first_name $last_name, Email: $email, Phone: $country_code $mobile_number");
    
    // Create a new user
    $username = sanitize_user($email);
    $random_password = wp_generate_password(12, false);
    $user_id = wp_create_user($username, $random_password, $email);
    
    if (is_wp_error($user_id)) {
        error_log("Error creating user: " . $user_id->get_error_message());
        wp_send_json_error(['message' => 'Failed to create user: ' . $user_id->get_error_message()]);
    }
    
    // Set user role to patient using the Medroid prefix
    $user = new WP_User($user_id);
    
    // Use the standard Medroid patient role name
    $patient_role = defined('KIVI_CARE_PREFIX') ? KIVI_CARE_PREFIX . 'patient' : 'kivicare_patient';
    $user->set_role($patient_role);
    
    // Debug
    error_log("Set user role to: $patient_role");
    
    // Update user meta
    update_user_meta($user_id, 'first_name', $first_name);
    update_user_meta($user_id, 'last_name', $last_name);
    
    // Set the user's display name to their full name
    wp_update_user([
        'ID' => $user_id,
        'display_name' => $first_name . ' ' . $last_name
    ]);
    
    // Build full address
    $full_address = $address;
    if (!empty($city)) {
        $full_address .= (!empty($full_address) ? ', ' : '') . $city;
    }
    if (!empty($state)) {
        $full_address .= (!empty($full_address) ? ', ' : '') . $state;
    }
    if (!empty($country)) {
        $full_address .= (!empty($full_address) ? ', ' : '') . $country;
    }
    if (!empty($postal_code)) {
        $full_address .= (!empty($full_address) ? ' ' : '') . $postal_code;
    }
    
    // Save patient specific data
    $patient_data = [
        'mobile_number' => $mobile_number,
        'country_code' => $country_code,
        'gender' => $gender,
        'dob' => $dob,
        'address' => $address,
        'city' => $city,
        'state' => $state,
        'country' => $country,
        'postal_code' => $postal_code,
        'full_address' => $full_address,
        'nhs' => $nhs,
        'registered_gp_name' => $registered_gp_name,
        'registered_gp_address' => $registered_gp_address,
        'insurance_provider' => $insurance_provider,
        'insurance_no' => $insurance_no
    ];
    
    update_user_meta($user_id, 'basic_data', json_encode($patient_data));
    
    // Generate unique patient ID if setting is enabled
    if (function_exists('kcPatientUniqueIdEnable') && function_exists('generatePatientUniqueIdRegister')) {
        if (kcPatientUniqueIdEnable('status')) {
            $patient_unique_id = generatePatientUniqueIdRegister();
            update_user_meta($user_id, 'patient_unique_id', $patient_unique_id);
            error_log("Generated patient unique ID for new patient: " . $patient_unique_id);
        }
    }
    
    error_log("Saved patient data: " . json_encode($patient_data));
    
    // Link patient to clinic
    global $wpdb;
    $wpdb->insert(
        $wpdb->prefix . 'kc_patient_clinic_mappings',
        [
            'patient_id' => $user_id,
            'clinic_id' => $clinic_id,
            'created_at' => current_time('mysql')
        ],
        ['%d', '%d', '%s']
    );
    
    // Prepare email content with more details
    $email_content = "Hello $first_name,

Your patient account has been created successfully at " . get_bloginfo('name') . ".

Account Details:
- Username: $email
- Password: $random_password

Please log in to access your patient dashboard.

Regards,
The " . get_bloginfo('name') . " Team";
    
    // Send email to user with login details
    $mail_sent = wp_mail(
        $email,
        'Welcome to ' . get_bloginfo('name') . ' - Your Patient Account',
        $email_content
    );
    
    error_log("Email sent to $email: " . ($mail_sent ? 'Success' : 'Failed'));
    
    // Success message
    wp_send_json_success([
        'message' => "Thank you $first_name! Your patient account has been created successfully. Please check your email for login details."
    ]);
}

/**
 * Verify returning patient
 */
function kivicare_verify_returning_patient() {
    // Verify nonce for security (implementation would go here)
    
    // Get the email and clinic ID
    $email = sanitize_email($_POST['email']);
    $clinic_id = intval($_POST['clinic_id']);
    
    if (empty($email)) {
        wp_send_json_error(['message' => 'Please enter a valid email address.']);
    }
    
    // Check if this email exists as a patient
    $user = get_user_by('email', $email);
    
    if (!$user) {
        wp_send_json_error(['message' => 'No patient found with this email. Please use the New Patient tab to register.']);
    }
    
    // Debug: Log user ID and email
    error_log('Patient verification attempt: User ID: ' . $user->ID . ', Email: ' . $email);
    
    // Check if the user is associated with any patient records
    $patient_exists = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$wpdb->prefix}kc_patient_clinic_mappings WHERE patient_id = %d",
        $user->ID
    ));
    
    if ($patient_exists) {
        // User is a patient in our system
        error_log('Found patient in clinic mappings');
    } else {
        // Check if the user is a patient by role
        $user_role = get_user_meta($user->ID, 'wp_capabilities', true);
        
        if (empty($user_role) || !is_array($user_role)) {
            error_log('User has no roles defined');
            wp_send_json_error(['message' => 'This email is not registered as a patient. Please use the New Patient tab to register.']);
        }
        
        // Debug: Log user roles
        error_log('User roles: ' . print_r($user_role, true));
        
        // Check for any patient role (different naming conventions possible)
        $is_patient = false;
        foreach (array_keys($user_role) as $role_key) {
            if (strpos(strtolower($role_key), 'patient') !== false) {
                $is_patient = true;
                break;
            }
        }
        
        if (!$is_patient) {
            wp_send_json_error(['message' => 'This email is not registered as a patient. Please use the New Patient tab to register.']);
        }
    }
    
    // Verify the patient is linked to this clinic
    global $wpdb;
    $clinic_mapping = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$wpdb->prefix}kc_patient_clinic_mappings 
        WHERE patient_id = %d AND clinic_id = %d",
        $user->ID, $clinic_id
    ));
    
    if (!$clinic_mapping) {
        // If not linked, create the link
        $wpdb->insert(
            $wpdb->prefix . 'kc_patient_clinic_mappings',
            [
                'patient_id' => $user->ID,
                'clinic_id' => $clinic_id,
                'created_at' => current_time('mysql')
            ],
            ['%d', '%d', '%s']
        );
    }
    
    // Success message
    wp_send_json_success([
        'message' => "Welcome back {$user->display_name}! You can now book an appointment.",
        'patient_id' => $user->ID
    ]);
}

/**
 * Render a shortcode via AJAX
 */
add_action('wp_ajax_kivicare_render_shortcode', 'kivicare_render_shortcode');
add_action('wp_ajax_nopriv_kivicare_render_shortcode', 'kivicare_render_shortcode');

function kivicare_render_shortcode() {
    $shortcode = isset($_POST['shortcode']) ? $_POST['shortcode'] : '';
    if (!empty($shortcode)) {
        echo do_shortcode($shortcode);
    }
    wp_die();
}

// Load the patient check-in system
function kivicare_load_patient_checkin() {
    // Make sure Medroid is active
    if (!function_exists('kcGetModules')) {
        return;
    }
    
    // Add a new status for checked-in patients
    add_filter('kivi_care_appointment_statuses', 'kivicare_add_checkin_status');
    
    // Add color for checked-in status in the calendar
    add_filter('kivicare_appointment_calendar_color', 'kivicare_add_checkin_color');
    
    // Add icon for checked-in status in the admin UI
    add_filter('kivicare_appointment_icon', 'kivicare_add_checkin_icon', 10, 2);
}
add_action('plugins_loaded', 'kivicare_load_patient_checkin');

/**
 * Add color for checked-in appointments in the calendar
 */
function kivicare_add_checkin_color($colors) {
    // Status 4 = Checked In, set to a distinctive color (light blue)
    $colors[4] = '#3498db';
    return $colors;
}

/**
 * Add icon for checked-in status in appointment lists
 */
function kivicare_add_checkin_icon($icon, $status) {
    if ($status == 4) {
        return '<i class="fas fa-user-check text-primary"></i>';
    }
    return $icon;
}

/**
 * Add checked-in status to appointment status options
 */
function kivicare_add_checkin_status($statuses) {
    // Medroid uses numeric keys for appointment statuses
    // 0 = cancelled, 1 = booked, 3 = completed, 4 = checked in
    $statuses[4] = 'Checked In';
    error_log('Added Checked In status to appointment statuses: ' . print_r($statuses, true));
    return $statuses;
}