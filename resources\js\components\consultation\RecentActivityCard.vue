<template>
  <Card>
    <CardHeader>
      <CardTitle class="text-lg flex items-center">
        <Activity class="w-5 h-5 mr-2" />
        Recent Activity
      </CardTitle>
    </CardHeader>
    <CardContent>
      <div v-if="activities.length" class="space-y-3">
        <div 
          v-for="activity in activities" 
          :key="activity.id"
          class="flex items-start space-x-3 p-3 rounded-lg bg-gray-50"
        >
          <div class="flex-shrink-0">
            <component 
              :is="getActivityIcon(activity.type)" 
              class="w-4 h-4 mt-0.5"
              :class="getActivityColor(activity.type)"
            />
          </div>
          <div class="flex-1 min-w-0">
            <p class="text-sm font-medium text-gray-900">
              {{ activity.description }}
            </p>
            <div class="flex items-center space-x-2 mt-1">
              <p class="text-xs text-gray-500">
                {{ formatTimeAgo(activity.created_at) }}
              </p>
              <span class="text-xs text-gray-300">•</span>
              <p class="text-xs text-gray-500">
                {{ activity.user_name }}
              </p>
            </div>
          </div>
        </div>
      </div>
      
      <div v-else class="text-center py-6 text-gray-500">
        <Activity class="w-8 h-8 mx-auto mb-2 text-gray-300" />
        <p class="text-sm">No recent activity</p>
      </div>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  Activity, Plus, Edit, Trash2, FileText, Pill, 
  Camera, Mic, Download, User 
} from 'lucide-vue-next'

interface Activity {
  id: number
  type: string
  description: string
  user_name: string
  created_at: string
}

interface Props {
  consultation?: any
}

const props = defineProps<Props>()

// Component state
const activities = ref<Activity[]>([])

// Computed
const mockActivities = computed(() => {
  if (!props.consultation) return []
  
  return [
    {
      id: 1,
      type: 'consultation_created',
      description: 'Consultation started',
      user_name: 'Dr. Smith',
      created_at: new Date().toISOString()
    },
    {
      id: 2,
      type: 'vital_signs_added',
      description: 'Vital signs recorded',
      user_name: 'Dr. Smith',
      created_at: new Date(Date.now() - 5 * 60 * 1000).toISOString()
    },
    {
      id: 3,
      type: 'tab_added',
      description: 'Present concerns updated',
      user_name: 'Dr. Smith',
      created_at: new Date(Date.now() - 10 * 60 * 1000).toISOString()
    },
    {
      id: 4,
      type: 'prescription_added',
      description: 'Prescription added: Paracetamol',
      user_name: 'Dr. Smith',
      created_at: new Date(Date.now() - 15 * 60 * 1000).toISOString()
    }
  ]
})

// Methods
const getActivityIcon = (type: string) => {
  const icons = {
    'consultation_created': Plus,
    'consultation_updated': Edit,
    'consultation_deleted': Trash2,
    'vital_signs_added': Activity,
    'tab_added': Edit,
    'tab_updated': Edit,
    'prescription_added': Pill,
    'document_uploaded': FileText,
    'photo_taken': Camera,
    'voice_recorded': Mic,
    'export_generated': Download,
    'user_assigned': User
  }
  
  return icons[type] || Edit
}

const getActivityColor = (type: string) => {
  const colors = {
    'consultation_created': 'text-green-500',
    'consultation_updated': 'text-blue-500',
    'consultation_deleted': 'text-red-500',
    'vital_signs_added': 'text-purple-500',
    'tab_added': 'text-blue-500',
    'tab_updated': 'text-blue-500',
    'prescription_added': 'text-orange-500',
    'document_uploaded': 'text-indigo-500',
    'photo_taken': 'text-pink-500',
    'voice_recorded': 'text-yellow-500',
    'export_generated': 'text-gray-500',
    'user_assigned': 'text-teal-500'
  }
  
  return colors[type] || 'text-gray-500'
}

const formatTimeAgo = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)
  
  if (diffInSeconds < 60) {
    return 'Just now'
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60)
    return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600)
    return `${hours} hour${hours !== 1 ? 's' : ''} ago`
  } else {
    const days = Math.floor(diffInSeconds / 86400)
    return `${days} day${days !== 1 ? 's' : ''} ago`
  }
}

const loadActivities = () => {
  // For now, use mock data
  activities.value = mockActivities.value
  
  // In a real implementation, you would fetch from API:
  // const response = await axios.get(`/api/consultations/${props.consultation?.id}/activities`)
  // activities.value = response.data
}

// Initialize
onMounted(() => {
  loadActivities()
})
</script>
