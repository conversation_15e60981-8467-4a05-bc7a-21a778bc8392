<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update the status column to include all possible values
        DB::statement("ALTER TABLE automated_posts MODIFY COLUMN status ENUM('draft', 'generating_image', 'ready', 'posted', 'failed', 'pending', 'processing', 'published') DEFAULT 'draft'");
        
        // Add scheduled_for column if it doesn't exist
        if (!Schema::hasColumn('automated_posts', 'scheduled_for')) {
            Schema::table('automated_posts', function (Blueprint $table) {
                $table->timestamp('scheduled_for')->nullable()->after('scheduled_at');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert status column to original values
        DB::statement("ALTER TABLE automated_posts MODIFY COLUMN status ENUM('draft', 'generating_image', 'ready', 'posted', 'failed') DEFAULT 'draft'");
        
        // Remove scheduled_for column if it exists
        if (Schema::hasColumn('automated_posts', 'scheduled_for')) {
            Schema::table('automated_posts', function (Blueprint $table) {
                $table->dropColumn('scheduled_for');
            });
        }
    }
};