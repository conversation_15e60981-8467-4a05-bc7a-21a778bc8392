<!-- ShareModal.vue -->
<template>
  <div
    v-if="show"
    class="overflow-y-auto overflow-x-hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
  >
    <div class="bg-white rounded-lg max-w-lg w-full mx-4">
      <div class="p-4 border-b">
        <h3 class="text-lg font-medium">Share Document</h3>
      </div>

      <div class="p-4">
        <form @submit.prevent="handleSubmit">
          <div class="mb-4">
            <!-- GP Information Section - Simple version -->
            <div class="mb-3 border-b pb-3">
              <h4 class="text-sm font-semibold text-gray-700 mb-2">Registered GP Information</h4>
              <div class="mb-1">
                <span class="text-sm font-medium text-gray-700">GP Name:</span>
                <span class="ml-1">{{ patientMetaData?.registered_gp_name || "Not available" }}</span>
              </div>
              <div>
                <span class="text-sm font-medium text-gray-700">GP Address:</span> 
                <span class="ml-1">{{ patientMetaData?.registered_gp_address || "Not available" }}</span>
              </div>
            </div>

            <label class="block text-sm font-medium text-gray-700 mb-2">
              Select emails or contacts to share with
            </label>

            <!-- Email Selection -->
            <div class="relative">
              <div class="flex flex-wrap gap-2 p-2 border rounded min-h-[42px]">
                <!-- Selected Recipients -->
                <span
                  v-for="email in selectedEmails"
                  :key="email"
                  class="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-700 rounded text-sm"
                >
                  {{ email }}
                  <button
                    type="button"
                    class="hover:text-blue-900"
                    @click="removeEmail(email)"
                  >
                    ×
                  </button>
                </span>

                <!-- Input -->
                <input
                  v-model="searchQuery"
                  type="text"
                  class="flex-1 min-w-[120px] outline-none"
                  placeholder="Search contacts or type email..."
                  @input="handleSearch"
                  @keydown.enter.prevent="handleEnter"
                  @keydown.backspace="handleBackspace"
                  @focus="showDropdown = true"
                />
              </div>

              <!-- Dropdown -->
              <div
                v-if="showDropdown && (contacts.length > 0 || searchQuery)"
                class="absolute left-0 right-0 mt-1 bg-white border rounded shadow-lg max-h-48 overflow-y-auto z-10"
              >
                <!-- Manual email entry option -->
                <div
                  v-if="
                    searchQuery &&
                    isValidEmail(searchQuery) &&
                    !selectedEmails.includes(searchQuery) &&
                    !contacts.some(contact => contact.email === searchQuery)
                  "
                  class="px-3 py-2 hover:bg-gray-100 cursor-pointer"
                  @click="addEmail(searchQuery)"
                >
                  <div class="flex items-center">
                    <span class="mr-2">✉️</span>
                    <span>Add "{{ searchQuery }}"</span>
                  </div>
                </div>
                
                <!-- Contacts from search -->
                <div
                  v-for="contact in contacts"
                  :key="contact.id"
                  class="px-3 py-2 hover:bg-gray-100 cursor-pointer"
                  @click="addContactEmail(contact)"
                >
                  <div class="flex items-center">
                    <span class="mr-2">👤</span>
                    <div>
                      <div class="font-medium">{{ contact.name }}</div>
                      <div class="text-xs text-gray-500">{{ contact.email }}</div>
                    </div>
                  </div>
                </div>
                
                <!-- No results -->
                <div
                  v-if="searchQuery && contacts.length === 0 && !isValidEmail(searchQuery)"
                  class="px-3 py-2 text-gray-500"
                >
                  No contacts found. Type a valid email to add directly.
                </div>
              </div>
            </div>
          </div>

          <!-- Encryption Option -->
          <!-- <div class="mb-4">
            <div class="flex items-center py-2 px-3 bg-gray-50 rounded border">
              <input
                type="checkbox"
                id="encrypt-document"
                v-model="encryptDocument"
                class="mr-2 h-4 w-4"
              />
              <label for="encrypt-document" class="text-sm font-medium">
                Send with password protection
              </label>
            </div>
            <p class="text-xs text-gray-500 mt-1 pl-2">
              When enabled, the recipient will need a password to open the document.
            </p>
          </div> -->
          
          <!-- Buttons -->
          <div class="flex justify-end gap-2">
            <button
              type="button"
              class="px-4 py-2 text-sm border rounded hover:bg-gray-50"
              @click="$emit('close')"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="px-4 py-2 text-sm bg-black text-white rounded hover:bg-black disabled:opacity-50"
              :disabled="sharing || selectedEmails.length === 0"
            >
              <template v-if="sharing">
                <span class="inline-block animate-spin mr-2">↻</span>
                Sharing...
              </template>
              <template v-else> Share </template>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { debounce } from "lodash";
import { get } from "../../config/request";

export default {
  name: "ShareModal",

  props: {
    show: {
      type: Boolean,
      required: true,
    },
    document: {
      type: Object,
      required: true,
    },
    patientDetails: {
      type: Object,
      required: true,
    },
    patientMetaData: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {
      sharing: false,
      searchQuery: "",
      selectedEmails: [],
      contacts: [],
      searchResults: [],
      showDropdown: false,
      encryptDocument: true, // Default to encrypted for security
      fetchSuggestions: debounce(this.doFetchSuggestions, 300),
    };
  },

  created() {
    // Add patient email to selectedEmails if it exists
    if (this.patientDetails?.patient_email) {
      this.selectedEmails = [this.patientDetails.patient_email];
    }
  },

  methods: {
    isValidEmail(email) {
      const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return re.test(email);
    },

    async doFetchSuggestions(query) {
      if (!query.trim()) {
        this.contacts = [];
        return;
      }

      try {
        // First, try to search contacts
        const contactsResponse = await get("search_contacts", {
          search: query,
        });
        
        if (contactsResponse.data.status && contactsResponse.data.data.length > 0) {
          this.contacts = contactsResponse.data.data;
        } else {
          // If no contacts found, fall back to patient search
          const patientsResponse = await get("get_patients_by_email", {
            search: query,
          });
          
          if (patientsResponse.data.status && patientsResponse.data.data.length > 0) {
            // Convert patient emails to contact format
            this.contacts = patientsResponse.data.data.map(email => ({
              id: 'patient-' + Date.now() + Math.random().toString(36).substring(2, 8),
              name: 'Patient',
              email: email,
              display: email
            }));
          } else {
            this.contacts = [];
          }
        }
      } catch (error) {
        console.error("Error fetching suggestions:", error);
        this.contacts = [];
      }
    },

    handleSearch() {
      this.showDropdown = true;
      this.fetchSuggestions(this.searchQuery);
    },

    addEmail(email) {
      if (this.isValidEmail(email) && !this.selectedEmails.includes(email)) {
        this.selectedEmails.push(email);
        this.searchQuery = "";
        this.showDropdown = false;
      }
    },
    
    addContactEmail(contact) {
      if (this.isValidEmail(contact.email) && !this.selectedEmails.includes(contact.email)) {
        this.selectedEmails.push(contact.email);
        this.searchQuery = "";
        this.showDropdown = false;
      }
    },

    removeEmail(email) {
      this.selectedEmails = this.selectedEmails.filter((e) => e !== email);
    },

    handleEnter() {
      if (this.contacts.length > 0) {
        // If we have contacts in the dropdown, add the first one
        this.addContactEmail(this.contacts[0]);
      } else if (this.isValidEmail(this.searchQuery)) {
        // Otherwise add the typed email if valid
        this.addEmail(this.searchQuery);
      }
    },

    handleBackspace(event) {
      if (this.searchQuery === "" && this.selectedEmails.length > 0) {
        event.preventDefault();
        this.selectedEmails.pop();
      }
    },

    handleSubmit() {
      if (this.selectedEmails.length === 0) return;

      this.sharing = true;
      // Pass the encryption option to the parent component
      this.$emit("share", this.selectedEmails, this.encryptDocument);
      this.sharing = false;
    },
    
    // Close dropdown when clicking outside
    handleClickOutside(event) {
      if (this.$el && !this.$el.contains(event.target)) {
        this.showDropdown = false;
      }
    },
  },
  
  mounted() {
    document.addEventListener('click', this.handleClickOutside);
  },
  
  beforeDestroy() {
    document.removeEventListener('click', this.handleClickOutside);
  },
};
</script>
