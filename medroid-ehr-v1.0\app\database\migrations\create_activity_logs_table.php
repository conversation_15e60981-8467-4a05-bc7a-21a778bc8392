<?php
/**
 * Migration: Create Activity Logs Table
 */

if (!defined('ABSPATH')) {
    exit;
}

class CreateActivityLogsTable {
    /**
     * Run the migration
     */
    public function up() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $table_name = $wpdb->prefix . 'kc_activity_logs';
        
        $sql = "CREATE TABLE IF NOT EXISTS {$table_name} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            user_type varchar(50) NOT NULL,
            clinic_id bigint(20) DEFAULT NULL,
            patient_id bigint(20) DEFAULT NULL,
            activity_type varchar(100) NOT NULL,
            activity_description text NOT NULL,
            ip_address varchar(45) DEFAULT NULL,
            resource_id bigint(20) DEFAULT NULL,
            resource_type varchar(50) DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY  (id)
        ) {$charset_collate};";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Reverse the migration (optional)
     */
    public function down() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'kc_activity_logs';
        $wpdb->query("DROP TABLE IF EXISTS {$table_name}");
    }
}