<?php

namespace App\Http\Controllers;

use App\Models\Consultation;
use App\Models\Patient;
use App\Models\Provider;
use App\Models\Appointment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class ConsultationController extends Controller
{
    /**
     * Get consultations for the authenticated clinician.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        // Check if user is a clinician
        if (!$user->isClinician()) {
            return response()->json([
                'message' => 'Access denied. Only clinicians can access consultations.'
            ], 403);
        }

        $query = Consultation::with(['patient.user', 'appointment', 'diagnoses', 'prescriptions'])
                            ->where('provider_id', $user->provider->id ?? 0);

        // Apply filters
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('consultation_type')) {
            $query->where('consultation_type', $request->consultation_type);
        }

        if ($request->has('date_from')) {
            $query->where('consultation_date', '>=', $request->date_from);
        }

        if ($request->has('date_to')) {
            $query->where('consultation_date', '<=', $request->date_to);
        }

        if ($request->has('patient_id')) {
            $query->where('patient_id', $request->patient_id);
        }

        // Search by patient name
        if ($request->has('search')) {
            $search = $request->search;
            $query->whereHas('patient.user', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $consultations = $query->orderBy('consultation_date', 'desc')
                              ->paginate($request->get('per_page', 15));

        return response()->json([
            'data' => $consultations
        ]);
    }

    /**
     * Get a specific consultation.
     */
    public function show($id)
    {
        $user = Auth::user();
        
        // Check if user is a clinician
        if (!$user->isClinician()) {
            return response()->json([
                'message' => 'Access denied. Only clinicians can view consultations.'
            ], 403);
        }

        $consultation = Consultation::with([
            'patient.user',
            'provider.user',
            'appointment',
            'clinic',
            'notes.creator',
            'diagnoses',
            'treatmentPlans',
            'prescriptions.items',
            'medicalLetters'
        ])->find($id);

        if (!$consultation) {
            return response()->json([
                'message' => 'Consultation not found'
            ], 404);
        }

        // Check if user has access to this consultation
        if ($consultation->provider_id !== ($user->provider->id ?? 0)) {
            return response()->json([
                'message' => 'Access denied. You can only view your own consultations.'
            ], 403);
        }

        return response()->json([
            'data' => $consultation
        ]);
    }

    /**
     * Create a new consultation.
     */
    public function store(Request $request)
    {
        $user = Auth::user();
        
        // Check if user is a clinician
        if (!$user->isClinician()) {
            return response()->json([
                'message' => 'Access denied. Only clinicians can create consultations.'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'patient_id' => 'required|exists:patients,id',
            'appointment_id' => 'nullable|exists:appointments,id',
            'consultation_type' => 'required|string|max:50',
            'consultation_date' => 'required|date',
            'consultation_mode' => 'required|in:in_person,video,phone',
            'vital_signs' => 'nullable|array',
            'main_tabs' => 'nullable|array',
            'additional_tabs' => 'nullable|array',
            'is_telemedicine' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Get provider ID
        $provider = $user->provider;
        if (!$provider) {
            return response()->json([
                'message' => 'Provider profile not found. Please contact administrator.'
            ], 400);
        }

        $consultationData = $request->all();
        $consultationData['provider_id'] = $provider->id;
        $consultationData['clinic_id'] = $provider->clinic_id;
        $consultationData['status'] = 'draft';
        $consultationData['started_at'] = now();

        $consultation = Consultation::create($consultationData);

        return response()->json([
            'message' => 'Consultation created successfully',
            'data' => $consultation->load(['patient.user', 'provider.user'])
        ], 201);
    }

    /**
     * Update a consultation.
     */
    public function update(Request $request, $id)
    {
        $user = Auth::user();
        
        // Check if user is a clinician
        if (!$user->isClinician()) {
            return response()->json([
                'message' => 'Access denied. Only clinicians can update consultations.'
            ], 403);
        }

        $consultation = Consultation::find($id);

        if (!$consultation) {
            return response()->json([
                'message' => 'Consultation not found'
            ], 404);
        }

        // Check if user has access to this consultation
        if ($consultation->provider_id !== ($user->provider->id ?? 0)) {
            return response()->json([
                'message' => 'Access denied. You can only update your own consultations.'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'consultation_type' => 'sometimes|string|max:50',
            'status' => 'sometimes|in:draft,in_progress,completed,cancelled',
            'consultation_date' => 'sometimes|date',
            'duration_minutes' => 'sometimes|integer|min:1',
            'consultation_mode' => 'sometimes|in:in_person,video,phone',
            'vital_signs' => 'nullable|array',
            'main_tabs' => 'nullable|array',
            'additional_tabs' => 'nullable|array',
            'is_telemedicine' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $updateData = $request->only([
            'consultation_type', 'status', 'consultation_date', 'duration_minutes',
            'consultation_mode', 'vital_signs', 'main_tabs', 'additional_tabs',
            'is_telemedicine'
        ]);

        // Set completed_at if status is being changed to completed
        if ($request->status === 'completed' && $consultation->status !== 'completed') {
            $updateData['completed_at'] = now();
        }

        $consultation->update($updateData);

        return response()->json([
            'message' => 'Consultation updated successfully',
            'data' => $consultation->fresh()->load(['patient.user', 'provider.user'])
        ]);
    }

    /**
     * Delete a consultation.
     */
    public function destroy($id)
    {
        $user = Auth::user();
        
        // Check if user is a clinician
        if (!$user->isClinician()) {
            return response()->json([
                'message' => 'Access denied. Only clinicians can delete consultations.'
            ], 403);
        }

        $consultation = Consultation::find($id);

        if (!$consultation) {
            return response()->json([
                'message' => 'Consultation not found'
            ], 404);
        }

        // Check if user has access to this consultation
        if ($consultation->provider_id !== ($user->provider->id ?? 0)) {
            return response()->json([
                'message' => 'Access denied. You can only delete your own consultations.'
            ], 403);
        }

        // Only allow deletion of draft consultations
        if ($consultation->status !== 'draft') {
            return response()->json([
                'message' => 'Only draft consultations can be deleted.'
            ], 400);
        }

        $consultation->delete();

        return response()->json([
            'message' => 'Consultation deleted successfully'
        ]);
    }

    /**
     * Create consultation from appointment.
     */
    public function createFromAppointment($appointmentId)
    {
        $user = Auth::user();
        
        // Check if user is a clinician
        if (!$user->isClinician()) {
            return response()->json([
                'message' => 'Access denied. Only clinicians can create consultations.'
            ], 403);
        }

        $appointment = Appointment::with(['patient', 'provider', 'service'])->find($appointmentId);

        if (!$appointment) {
            return response()->json([
                'message' => 'Appointment not found'
            ], 404);
        }

        // Check if user has access to this appointment
        if ($appointment->provider_id !== ($user->provider->id ?? 0)) {
            return response()->json([
                'message' => 'Access denied. You can only create consultations for your own appointments.'
            ], 403);
        }

        // Check if consultation already exists for this appointment
        $existingConsultation = Consultation::where('appointment_id', $appointmentId)->first();
        if ($existingConsultation) {
            return response()->json([
                'message' => 'Consultation already exists for this appointment',
                'data' => $existingConsultation
            ], 400);
        }

        $consultation = Consultation::create([
            'appointment_id' => $appointment->id,
            'patient_id' => $appointment->patient_id,
            'provider_id' => $appointment->provider_id,
            'clinic_id' => $appointment->provider->clinic_id,
            'consultation_type' => 'general',
            'status' => 'in_progress',
            'consultation_date' => $appointment->date,
            'consultation_mode' => $appointment->is_telemedicine ? 'video' : 'in_person',
            'is_telemedicine' => $appointment->is_telemedicine,
            'chief_complaint' => $appointment->reason,
            'started_at' => now(),
        ]);

        return response()->json([
            'message' => 'Consultation created from appointment successfully',
            'data' => $consultation->load(['patient.user', 'provider.user', 'appointment'])
        ], 201);
    }

    /**
     * Get the consultation template structure.
     */
    public function getTemplate()
    {
        $templatePath = resource_path('json/consultation_template.json');

        if (!file_exists($templatePath)) {
            return response()->json([
                'message' => 'Consultation template not found'
            ], 404);
        }

        $template = json_decode(file_get_contents($templatePath), true);

        return response()->json([
            'data' => $template
        ]);
    }
}
