/**
 * JavaScript to ensure login pages display in fullscreen mode
 * This script removes theme elements even if they're added dynamically after page load
 */
(function() {
    // Run on DOMContentLoaded
    document.addEventListener('DOMContentLoaded', function() {
        // Check if this is a login page
        if (document.body.classList.contains('kivicare-fullscreen-page')) {
            // List of selectors to target and remove
            const elementsToRemove = [
                'header', 'footer', '.site-header', '.site-footer', 
                'nav:not(.wp-block-kivi-care-register-login nav)', 
                '.navbar', '.menu', '.navigation', '#masthead', '#colophon',
                '.header', '.footer', '#header', '#footer'
            ];
            
            // Function to remove elements
            function removeElements() {
                elementsToRemove.forEach(function(selector) {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(function(element) {
                        // Don't remove elements inside our login content
                        if (!isDescendantOfLoginContent(element)) {
                            element.style.display = 'none';
                            element.style.visibility = 'hidden';
                            element.style.opacity = '0';
                            element.style.height = '0';
                            element.style.overflow = 'hidden';
                            element.style.pointerEvents = 'none';
                        }
                    });
                });
                
                // Show only the content area with full height
                const mainContent = document.querySelector('.entry-content, .content-area, #main, .site-main, main, .page-content');
                if (mainContent) {
                    mainContent.style.minHeight = '100vh';
                    mainContent.style.display = 'block';
                    mainContent.style.width = '100%';
                    mainContent.style.maxWidth = '100%';
                    mainContent.style.padding = '0';
                    mainContent.style.margin = '0';
                }
                
                // Ensure our login blocks are visible
                const loginBlocks = document.querySelectorAll('.wp-block-kivi-care-register-login, .wp-block-shortcode');
                loginBlocks.forEach(function(block) {
                    block.style.display = 'block';
                    block.style.visibility = 'visible';
                    block.style.opacity = '1';
                    block.style.minHeight = '100vh';
                });
                
                // Set body to full height
                document.body.style.minHeight = '100vh';
                document.body.style.margin = '0';
                document.body.style.padding = '0';
                document.documentElement.style.marginTop = '0';
                
                // Hide admin bar
                const adminBar = document.getElementById('wpadminbar');
                if (adminBar) {
                    adminBar.style.display = 'none';
                }
            }
            
            // Check if an element is inside our login content
            function isDescendantOfLoginContent(element) {
                const loginContent = document.querySelector('.wp-block-kivi-care-register-login, .wp-block-shortcode');
                return loginContent && loginContent.contains(element);
            }
            
            // Run immediately
            removeElements();
            
            // Also run after a short delay (for dynamically added elements)
            setTimeout(removeElements, 100);
            setTimeout(removeElements, 500);
            setTimeout(removeElements, 1000);
            
            // Observe DOM changes to handle dynamically added elements
            const observer = new MutationObserver(function(mutations) {
                removeElements();
            });
            
            // Start observing
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
    });
})();