<template>
  <div class="min-h-screen p-6 bg-gradient-to-br from-pink-50 to-purple-50">
    <!-- Header Section -->
    <div class="mb-8 flex justify-between items-center">
      <div class="flex items-center gap-4">
        <router-link :to="{ name: 'dashboard' }"
          class="flex items-center gap-2 px-4 py-2 bg-black text-sm text-white rounded-lg shadow-sm hover:bg-gray-800">
          <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor"
            stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="m12 19-7-7 7-7" />
            <path d="M19 12H5" />
          </svg>
          <span>Back</span>
        </router-link>
        <h1 class="text-2xl font-semibold text-gray-800">
          {{ formTranslation.clinic.receptionists_list }}
        </h1>
      </div>
      <div class="flex gap-2">

        <module-data-import v-if="
          userData.addOns.kiviPro &&
          kcCheckPermission('receptionist_add') &&
          kivicareCompareVersion(requireProVersion, userData.pro_version)
        " ref="module_data_import" @reloadList="getReceptionistList"
          :is-import-modal-open="showImportModal" :required-data="[
            { label: formTranslation.receptionist.first_name, value: 'first_name', required: true },
            { label: formTranslation.receptionist.last_name, value: 'last_name', required: true },
            { label: formTranslation.receptionist.email, value: 'email', required: true },
            {
              label: formTranslation.common.country_calling_code,
              value: 'country_calling_code',
              required: false
            },
            {
              label: formTranslation.common.country_code,
              value: 'country_code',
              required: false
            },
            {
              label: formTranslation.receptionist.receptionist_contact,
              value: 'contact',
              required: true
            },
            {
              label: formTranslation.receptionist.gender,
              value: 'gender',
              required: true,
              options: [
                { label: formTranslation.receptionist.male, value: 'male' },
                { label: formTranslation.receptionist.female, value: 'female' },
                { label: formTranslation.receptionist.other, value: 'other' }
              ]
            },
            {
              label: formTranslation.receptionist.clinic,
              value: 'clinic_id',
              required: userData.role !== 'clinic_admin',
              type: 'select'
            }
          ]" :module-name="formTranslation.common.receptionist" module-type="receptionist"
          :validate-before-import="true" :show-progress="true" />

        <!-- data export module -->
        <module-data-export v-if="kcCheckPermission('receptionist_export')" :module-data="receptionistsList.data"
          :module-name="formTranslation.clinic.receptionists_list" module-type="receptionist">
          <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="7 10 12 15 17 10"></polyline>
            <line x1="12" x2="12" y1="15" y2="3"></line>
          </svg>
          {{ formTranslation.common.import }}
        </module-data-export>

        <button v-if="kcCheckPermission('receptionist_add')"
          class="flex items-center gap-2 px-4 py-2 bg-black text-sm text-white rounded-lg hover:bg-gray-800"
          @click="showAddReceptionistModal = true">
          <i class="fa fa-plus"></i>
          {{ formTranslation.clinic.add_receptionist }}
        </button>
      </div>
    </div>

    <!-- Global Search -->
    <div class="relative mb-6">
      <svg xmlns="http://www.w3.org/2000/svg"
        class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" viewBox="0 0 24 24" fill="none"
        stroke="currentColor" stroke-width="2">
        <circle cx="11" cy="11" r="8" />
        <path d="m21 21-4.3-4.3" />
      </svg>
      <input v-model="serverParams.searchTerm" @input="globalFilter({ searchTerm: serverParams.searchTerm })"
        :placeholder="formTranslation.common.search_receptionist_global_placeholder
          "
        class="w-full pl-10 pr-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
        type="text" />
    </div>

    <!-- Column Filters -->
    <div class="grid grid-cols-6 gap-4 mb-6">
      <input v-model="serverParams.columnFilters.ID"
        @input="onColumnFilter({ columnFilters: serverParams.columnFilters })" placeholder="ID"
        class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
        type="text" />
      <input v-model="serverParams.columnFilters.display_name"
        @input="onColumnFilter({ columnFilters: serverParams.columnFilters })"
        :placeholder="formTranslation.receptionist.dt_plh_name_fltr"
        class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
        type="text" />
      <input v-model="serverParams.columnFilters.clinic_name"
        @input="onColumnFilter({ columnFilters: serverParams.columnFilters })"
        :placeholder="formTranslation.receptionist.dt_lbl_clinic_name"
        class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
        type="text" />
      <input v-model="serverParams.columnFilters.user_email"
        @input="onColumnFilter({ columnFilters: serverParams.columnFilters })"
        :placeholder="formTranslation.receptionist.dt_plh_email_fltr"
        class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
        type="text" />
      <input v-model="serverParams.columnFilters.mobile_number"
        @input="onColumnFilter({ columnFilters: serverParams.columnFilters })"
        :placeholder="formTranslation.receptionist.dt_plh_mobilr_fltr"
        class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
        type="text" />
      <select v-model="serverParams.columnFilters.user_status"
        @change="onColumnFilter({ columnFilters: serverParams.columnFilters })"
        class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400">
        <option value="">{{ formTranslation.receptionist.dt_all }}</option>
        <option value="0">{{ formTranslation.common.active }}</option>
        <option value="1">{{ formTranslation.common.inactive }}</option>
      </select>
    </div>

    <!-- Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-x-auto">
      <table class="w-full">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              <input type="checkbox" class="rounded border-gray-300" @change="selectAll" :checked="isAllSelected" />
            </th>
            <th v-for="column in receptionistsList.column" :key="column.field"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              @click="onSortChange({ field: column.field })">
              {{ column.label }}
            </th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
          <tr v-for="(row, index) in receptionistsList.data" :key="row.ID" class="hover:bg-gray-50">
            <td class="px-6 py-4 whitespace-nowrap">
              <input type="checkbox" class="rounded border-gray-300" v-model="selectedRows" :value="row" />
            </td>
            <td class="px-6 py-4 whitespace-nowrap">{{ row.ID }}</td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <img v-if="row.profile_image" :src="row.profile_image" class="h-10 w-10 rounded-full mr-3" />
                <b-avatar v-else :text="getInitials(row.display_name)" class="mr-3"></b-avatar>
                {{ row.display_name }}
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">{{ row.clinic_name }}</td>
            <td class="px-6 py-4 whitespace-nowrap">{{ row.user_email }}</td>
            <td class="px-6 py-4 whitespace-nowrap">{{ row.mobile_number }}</td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center justify-center space-x-2">
                <!-- Toggle button -->
                <toggle-switch v-if="kcCheckPermission('receptionist_edit')"
                  :value="row.user_status === '0' ? 'on' : 'off'" @input="
                    (value) => {
                      // Update the local state immediately for the toggle animation
                      row.user_status = value === 'on' ? '0' : '1';
                      // Then call the status change function
                      changeModuleValueStatus({
                        module_type: 'doctors',
                        id: row.ID,
                        value: value === 'on' ? '0' : '1',
                      });
                    }
                  " on-value="on" off-value="off" />
                <span :class="[
                  'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                  row.user_status === '0'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800',
                ]">
                  {{
                    row.user_status === "0"
                      ? formTranslation.common.active
                      : formTranslation.common.inactive
                  }}
                </span>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex gap-2">
                <button v-if="kcCheckPermission('receptionist_edit')" class="p-1 hover:bg-gray-100 rounded"
                  @click="editReceptionist(row)">
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-gray-600" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2">
                    <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z" />
                  </svg>
                </button>
                <button v-if="kcCheckPermission('receptionist_edit')" class="p-1 hover:bg-gray-100 rounded"
                  @click="resendRequest(row.ID)">
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-gray-600" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2">
                    <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z" />
                    <path d="M14 2v4a2 2 0 0 0 2 2h4" />
                  </svg>
                </button>
                <button v-if="kcCheckPermission('receptionist_delete')" class="p-1 hover:bg-gray-100 rounded"
                  @click="deleteReceptionistData(index + 1)">
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-red-500" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2">
                    <path d="M3 6h18" />
                    <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
                    <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
                  </svg>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- Pagination -->
      <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200">
        <div class="flex items-center gap-2">
          <span class="text-sm text-gray-700">Rows per page:</span>
          <select v-model="serverParams.perPage" @change="
            onPerPageChange({
              currentPage: serverParams.page,
              currentPerPage: serverParams.perPage,
            })
            " class="border border-gray-300 rounded-md text-sm p-1">
            <option>10</option>
            <option>25</option>
            <option>50</option>
          </select>
        </div>
        <div class="flex items-center gap-4">
          <span class="text-sm text-gray-700">
            Page {{ serverParams.page }} of
            {{ Math.ceil(totalRows / serverParams.perPage) }}
          </span>
          <div class="flex gap-2">
            <button class="p-1 rounded hover:bg-gray-100 disabled:opacity-50" :disabled="serverParams.page === 1"
              @click="onPageChange({ currentPage: serverParams.page - 1 })">
              <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-gray-600" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2">
                <path d="m15 18-6-6 6-6" />
              </svg>
            </button>
            <button class="p-1 rounded hover:bg-gray-100 disabled:opacity-50" :disabled="serverParams.page >= Math.ceil(totalRows / serverParams.perPage)
              " @click="onPageChange({ currentPage: serverParams.page + 1 })">
              <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-gray-600" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2">
                <path d="m9 18 6-6-6-6" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <ReceptionFormModal :initial-data="receptionistFormData" :openAddReceptionistModal="showAddReceptionistModal"
      :editReceptionistId="editReceptionistId" @close="showAddReceptionistModal = false"
      @refresh-list="getReceptionistList" />
  </div>
</template>

<script>
import { post, get } from "../../config/request";
import ModalPopup from "../../components/Modal/Index";
import _ from "lodash";
import ReceptionFormModal from "./ReceptionFormModal";

export default {
  name: "ReceptionistList",

  components: {
    ModalPopup,
    ReceptionFormModal,
  },

  data() {
    return {
      pageLoader: true,
      showImportModal: false,
      showAddReceptionistModal: false,
      receptionistsList: {
        data: [],
        column: [],
      },
      clinic: [],
      filterClinic: [],
      receptionistFormData: {},
      editReceptionistId: null,
      verifyModalData: {},
      verifyPopupModal: false,
      verifyButtonLoading: false,
      selectedRows: [],
      serverParams: {
        columnFilters: {},
        sort: [
          {
            field: "",
            type: "",
          },
        ],
        page: 1,
        perPage: 10,
        searchTerm: "",
        type: "list",
      },
      oldServerParams: {
        columnFilters: {},
        searchTerm: "",
        perPage: 10,
      },
      totalRows: 0,
      globalCheckboxApplyData: {},
      globalCheckboxApplyDataActions: [],
    };
  },

  computed: {
    isAllSelected() {
      return (
        this.receptionistsList.data.length > 0 &&
        this.selectedRows.length === this.receptionistsList.data.length
      );
    },

    clinics() {
      return this.$store.state.clinic;
    },

    userData() {
      return this.$store.state.userDataModule.user;
    },
  },

  mounted() {
    this.init();
  },

  methods: {
    init() {
      this.getReceptionistList();
      this.receptionistsList = this.defaultDoctorDataList();
      this.globalCheckboxApplyData = this.defaultGlobalCheckboxApplyData();
      this.globalCheckboxApplyDataActions =
        this.defaultGlobalCheckboxApplyDataActions();

      setTimeout(() => {
        this.clinic = this.clinics;
        this.clinic.forEach((element) => {
          this.filterClinic.push({ value: element.id, text: element.label });
        });
      }, 1000);
    },

    getReceptionistList() {
      get("receptionist_list", this.serverParams)
        .then((data) => {
          this.pageLoader = false;
          if (data.data.status !== undefined && data.data.status === true) {
            this.receptionistsList.data = data.data.data;
            this.totalRows = data.data.total_rows;
          } else {
            this.receptionistsList.data = [];
            this.totalRows = 0;
          }
        })
        .catch((error) => {
          this.pageLoader = false;
          console.error(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },

    selectAll() {
      if (this.isAllSelected) {
        this.selectedRows = [];
      } else {
        this.selectedRows = [...this.receptionistsList.data];
      }
    },

    onPageChange(params) {
      this.serverParams.page = params.currentPage;
      this.getReceptionistList();
    },

    onPerPageChange(params) {
      if (this.oldServerParams.perPage === params.currentPerPage) {
        return;
      }
      this.oldServerParams.perPage = params.currentPerPage;
      this.serverParams.perPage = params.currentPerPage;
      this.serverParams.page = 1;
      this.getReceptionistList();
    },

    globalFilter: _.debounce(function (params) {
      if (this.oldServerParams.searchTerm === params.searchTerm) {
        return;
      }
      this.oldServerParams.searchTerm = params.searchTerm;
      this.serverParams.searchTerm = params.searchTerm;
      this.serverParams.page = 1;
      this.getReceptionistList();
    }, 300),

    onColumnFilter: _.debounce(function (params) {
      const hasFilters = Object.values(params.columnFilters).some(
        (value) => value
      );
      const hadFilters = Object.values(this.oldServerParams.columnFilters).some(
        (value) => value
      );

      if (hasFilters || hadFilters) {
        this.oldServerParams.columnFilters = { ...params.columnFilters };
        this.serverParams.columnFilters = params.columnFilters;
        this.serverParams.page = 1;
        this.getReceptionistList();
      }
    }, 300),

    editReceptionist(row = {}) {
      this.editReceptionistId = row.id || null;
      this.receptionistFormData = { ...row };
      this.showAddReceptionistModal = true;

      // If editing, fetch the full clinic data
      if (this.editReceptionistId) {
        this.fetchReceptionistData(this.editReceptionistId);
      }
    },

    async fetchReceptionistData(id) {
      try {
        const response = await get("receptionist_edit", { id: id });
        if (response.data?.status) {
          this.receptionistFormData = response.data.data;
        }
      } catch (error) {
        console.error("Error fetching clinic admin data:", error);
        displayErrorMessage(
          this.formTranslation.clinic.doctor_record_not_found
        );
      }
    },

    deleteReceptionistData(index) {
      if (this.receptionistsList.data[index - 1]) {
        this.$swal
          .fire({
            title: this.formTranslation.clinic_schedule.dt_are_you_sure,
            text: this.formTranslation.receptionist
              .press_yes_to_delete_receptionist,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#dc3545", // Bootstrap danger color
            cancelButtonColor: "#6c757d", // Bootstrap secondary color
            confirmButtonText: this.formTranslation.common.yes,
            cancelButtonText: this.formTranslation.common.cancel,
          })
          .then((result) => {
            if (result.isConfirmed) {
              get("receptionist_delete", {
                id: this.receptionistsList.data[index - 1].ID,
              })
                .then((data) => {
                  if (data.data.status === true) {
                    this.receptionistsList.data.splice(index - 1, 1);
                    this.$swal.fire({
                      icon: "success",
                      title: "Success",
                      text: data.data.message,
                      timer: 1500,
                      showConfirmButton: false,
                    });
                  }
                })
                .catch((error) => {
                  console.error(error);
                  this.$swal.fire({
                    icon: "error",
                    title: "Error",
                    text: this.formTranslation.common.internal_server_error,
                    timer: 1500,
                    showConfirmButton: false,
                  });
                });
            }
          });
      }
    },

    resendRequest(id) {
      post("resend_credential", { id })
        .then((data) => {
          if (data.data.status === true) {
            displayMessage(data.data.message);
          } else {
            displayErrorMessage(data.data.message);
          }
        })
        .catch((error) => {
          console.error(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },

    verifyUser(data) {
      this.verifyButtonLoading = true;
      post("verify_user", { data })
        .then((response) => {
          this.verifyButtonLoading = false;
          if (response.data.status === true) {
            displayMessage(response.data.message);
            this.verifyPopupModal = false;
            this.getReceptionistList();
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          this.verifyButtonLoading = false;
          console.error(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },

    getInitials(name) {
      if (name) {
        const parts = name.split(" ");
        return parts.map((part) => part.charAt(0).toUpperCase()).join("");
      }
      return "-";
    },

    defaultDoctorDataList() {
      return {
        column: [
          {
            field: "ID",
            label: this.formTranslation.common.id,
            sortable: true,
          },
          {
            field: "display_name",
            label: this.formTranslation.receptionist.dt_lbl_name,
            sortable: true,
          },
          {
            field: "clinic_name",
            label: this.formTranslation.receptionist.dt_lbl_clinic_name,
            sortable: false,
          },
          {
            field: "user_email",
            label: this.formTranslation.receptionist.dt_lbl_email,
            sortable: true,
          },
          {
            field: "mobile_number",
            label: this.formTranslation.receptionist.dt_lbl_mobile,
            sortable: false,
          },
          {
            field: "user_status",
            label: this.formTranslation.receptionist.dt_lbl_status,
            sortable: true,
          },
          {
            field: "actions",
            label: this.formTranslation.receptionist.dt_lbl_action,
            sortable: false,
          },
        ],
        data: [],
      };
    },

    defaultGlobalCheckboxApplyData() {
      return {
        action_perform: "delete",
        module: "receptionists",
        data: [],
      };
    },

    defaultGlobalCheckboxApplyDataActions() {
      return [
        {
          value: "active",
          label: this.formTranslation.service.dt_active,
        },
        {
          value: "inactive",
          label: this.formTranslation.service.dt_inactive,
        },
        {
          value: "resend_credential",
          label: this.formTranslation.receptionist.resend_credential,
        },
        {
          value: "delete",
          label: this.formTranslation.clinic_schedule.dt_lbl_dlt,
        },
      ];
    },
  },
};
</script>
