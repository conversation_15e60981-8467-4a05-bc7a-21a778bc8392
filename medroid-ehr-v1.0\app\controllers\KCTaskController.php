<?php

namespace App\Controllers;

use App\baseClasses\KCBase;
use App\baseClasses\KCRequest;
use App\models\KCTask;
use App\models\KCTaskAssignee;
use App\models\KCTaskComment;
use App\models\KCTaskAttachment;
use Exception;

class KCTaskController extends KCBase {

	public $db;

	/**
	 * @var KCRequest
	 */
	private $request;

	public function __construct() {
		global $wpdb;
		$this->db = $wpdb;
		$this->request = new KCRequest();
		parent::__construct();
	}

	public function index() {
		// Get request data and user info
		$request_data = $this->request->getInputs();
		$user_id = get_current_user_id();
		$user_role = $this->getLoginUserRole();

		$filters = [];
		
		// Apply common filters from request
		$filter_fields = [
			'clinic_id' => 'int',
			'status' => 'text',
			'priority' => 'text',
			'category' => 'text',
			'is_archived' => 'int',
			'due_date_start' => 'text',
			'due_date_end' => 'text',
			'assignee_id' => 'int',
			'creator_id' => 'int'
		];
		
		foreach ($filter_fields as $field => $type) {
			if (!empty($request_data[$field])) {
				if ($field === 'status' && $request_data[$field] === 'all') {
					continue;
				}
				if ($field === 'priority' && $request_data[$field] === 'all') {
					continue;
				}
				
				if ($type === 'int') {
					$filters[$field] = (int)$request_data[$field];
				} else {
					$filters[$field] = sanitize_text_field($request_data[$field]);
				}
			}
		}
		
		// Handle pagination and sorting
		if (!empty($request_data['orderby'])) {
			$filters['orderby'] = sanitize_text_field($request_data['orderby']);
			$filters['order'] = !empty($request_data['order']) ? sanitize_text_field($request_data['order']) : 'ASC';
		}
		
		$task_model = new KCTask();
		$task_assignee_model = new KCTaskAssignee();
		$tasks = [];
		$view_type = !empty($request_data['view']) ? sanitize_text_field($request_data['view']) : 'all';

		// Handle pagination parameters
		if (!empty($request_data['page'])) {
			$filters['page'] = (int)$request_data['page'];
		}
		
		if (!empty($request_data['per_page'])) {
			$filters['per_page'] = (int)$request_data['per_page'];
		}
		
		// Handle search parameter
		if (!empty($request_data['search'])) {
			$filters['search'] = sanitize_text_field($request_data['search']);
		}
		
		// Handle tasks based on role
		if ($user_role != 'administrator') {
			$filters['logged_in_user_id'] = $user_id;

			if ($user_role === 'kiviCare_clinic_admin') {
				// Clinic admin sees clinic tasks plus their own
				$clinic_id = kcGetClinicIdOfClinicAdmin();
				if ($clinic_id) {
					$filters['clinic_id'] = $clinic_id;
				}
			} else if($user_role === 'kiviCare_doctor'){
				// Doctors can see tasks related to their clinic
				$clinic_id = kcGetClinicIdOfDoctor();
				if ($clinic_id) {
					$filters['clinic_id'] = $clinic_id;
				}
			} else if($user_role === 'kiviCare_patient'){
				// Patients can only see tasks related to them
				$filters['patient_id'] = $user_id;
			}
			$filters['clinic_id'] = $clinic_id;
		}
		$result = $task_model->getAllTasks($filters);
		// Return response
		if (empty($result['tasks'])) {
			wp_send_json([
				'status' => true,
				'message' => esc_html__('No tasks found', 'kc-lang'),
				'data' => [],
				'total' => 0,
				'page' => $result['page'],
				'per_page' => $result['per_page'],
				'total_pages' => 0
			]);
		} else {
			wp_send_json([
				'status' => true,
				'message' => esc_html__('Tasks retrieved successfully', 'kc-lang'),
				'data' => $result['tasks'],
				'total' => $result['total'],
				'page' => $result['page'],
				'per_page' => $result['per_page'],
				'total_pages' => $result['total_pages']
			]);
		}
	}

	public function save() {
		// Get all request inputs
		$request_data = $this->request->getInputs();
		
		try {
			// Validate required fields
			if (empty($request_data['title'])) {
				wp_send_json([
					'status' => false,
					'message' => esc_html__('Task title is required', 'kc-lang')
				]);
			}
	
			if (empty($request_data['clinic_id'])) {
				wp_send_json([
					'status' => false,
					'message' => esc_html__('Clinic is required', 'kc-lang')
				]);
			}
	
			// Prepare task data
			$task_data = [
				'title' => sanitize_text_field($request_data['title']),
				'description' => isset($request_data['description']) ? sanitize_textarea_field($request_data['description']) : '',
				'clinic_id' => (int)$request_data['clinic_id'],
				'patient_id' => !empty($request_data['patient_id']) ? (int)$request_data['patient_id'] : null,
				'priority' => !empty($request_data['priority']) ? sanitize_text_field($request_data['priority']) : 'medium',
				'status' => !empty($request_data['status']) ? sanitize_text_field($request_data['status']) : 'pending',
				'due_date' => !empty($request_data['due_date']) ? sanitize_text_field($request_data['due_date']) : null,
				'reminder_date' => !empty($request_data['reminder_date']) ? sanitize_text_field($request_data['reminder_date']) : null,
				'category' => !empty($request_data['category']) ? sanitize_text_field($request_data['category']) : null,
				'repeating' => !empty($request_data['repeating']) ? sanitize_text_field($request_data['repeating']) : null,
			];
			
			// Set creator_id for new tasks
			if (empty($request_data['id'])) {
				$task_data['creator_id'] = get_current_user_id();
				$task_data['created_at'] = current_time('mysql');
			}
	
			$task_model = new KCTask();
			$task_assignee_model = new KCTaskAssignee();
			$is_update = !empty($request_data['id']);
			$task_id = null;
	
			// Save task
			if ($is_update) {
				// Update existing task
				$task_id = (int)$request_data['id'];
				
				// Check if task exists
				$existing_task = $task_model->getTaskById($task_id);
				if (empty($existing_task)) {
					wp_send_json([
						'status' => false,
						'message' => esc_html__('Task not found', 'kc-lang')
					]);
				}
				
				// Check permission to edit this task
				$current_user_id = get_current_user_id();
				$user_role = $this->getLoginUserRole();
				
				if ($existing_task->creator_id != $current_user_id && 
					!in_array($user_role, [$this->getAdminRole(), $this->getClinicAdminRole()])) {
					wp_send_json([
						'status' => false,
						'message' => esc_html__('You do not have permission to edit this task', 'kc-lang')
					]);
				}
				
				// Add updated_at timestamp
				$task_data['updated_at'] = current_time('mysql');
				
				$task_model->saveTask($task_data, $task_id);
				$message = esc_html__('Task updated successfully', 'kc-lang');
			} else {
				// Create new task
				$task_id = $task_model->saveTask($task_data);
				$message = esc_html__('Task created successfully', 'kc-lang');
			}
	
			// Handle assignees
			if (!empty($request_data['assignees']) && is_array($request_data['assignees'])) {
				// Get existing assignees
				$existing_assignees = $task_assignee_model->getTaskAssignees($task_id);
				$existing_assignee_ids = array_map(function($a) {
					return $a->assignee_id;
				}, $existing_assignees);
				
				// Process new assignees
				foreach ($request_data['assignees'] as $assignee) {
					$assignee_id = is_array($assignee) ? (int)$assignee['id'] : (int)$assignee;
					if (!in_array($assignee_id, $existing_assignee_ids)) {
						$task_assignee_model->assignTask($task_id, $assignee_id);
					}
				}
				
				// Remove assignees not in the new list
				$new_assignee_ids = array_map(function($a) {
					return is_array($a) ? (int)$a['id'] : (int)$a;
				}, $request_data['assignees']);
				
				foreach ($existing_assignee_ids as $existing_id) {
					if (!in_array($existing_id, $new_assignee_ids)) {
						$task_assignee_model->unassignTask($task_id, $existing_id);
					}
				}
			}
	
			// Run actions after save/update
			if ($is_update) {
				do_action('kc_task_updated', $task_id, $task_data);
			} else {
				do_action('kc_task_created', $task_id, $task_data);
			}
	
			// Send email notification to assignees
			if (!empty($request_data['assignees'])) {
				// Get assignee IDs
				$assignee_ids = array_map(function($a) {
					return is_array($a) ? (int)$a['id'] : (int)$a;
				}, $request_data['assignees']);

				// Determine action type for email template
				$action_type = $is_update ? 'updated' : 'created';

				// Get attachment path if provided
				$attachment_path = !empty($request_data['email_attachment']) ? sanitize_text_field($request_data['email_attachment']) : '';

				// Send notification emails using helper
				sendTaskNotificationEmails($task_id, $assignee_ids, $action_type, $attachment_path);
				
				// Send system notifications to assignees
				if (function_exists('kc_send_notification_to_multiple')) {
					// Get creator name
					$creator_name = '';
					$creator_data = get_userdata(get_current_user_id());
					if ($creator_data) {
						$creator_name = $creator_data->display_name;
					}
					
					// Create notification title and message
					$notification_title = $is_update 
						? sprintf(__('Task Updated: %s', 'kc-lang'), $task_data['title'])
						: sprintf(__('New Task: %s', 'kc-lang'), $task_data['title']);
						
					$notification_message = $is_update
						? sprintf(__('%s updated a task assigned to you.', 'kc-lang'), $creator_name)
						: sprintf(__('%s assigned you a new task.', 'kc-lang'), $creator_name);
						
					// Send system notification
					kc_send_notification_to_multiple(
						$assignee_ids,
						$notification_title,
						$notification_message,
						'info',
						$task_id,
						'task'
					);
				}
				
				// Check if the task is already overdue when created
				if (!empty($task_data['due_date'])) {
					$due_date = new \DateTime($task_data['due_date']);
					$today = new \DateTime(current_time('Y-m-d'));
					
					if ($due_date < $today) {
						// Task is already overdue, send an overdue notification immediately
						sendTaskOverdueEmails($task_id);
					}
				}
			}

			wp_send_json([
				'status' => true,
				'message' => $message,
				'data' => [
					'id' => $task_id,
					'is_updated' => $is_update
				]
			]);
	
		} catch (Exception $e) {
			wp_send_json([
				'status' => false,
				'message' => $e->getMessage()
			]);
		}
	}

	public function edit() {
		// if (!kcCheckPermission('task_edit') && !kcCheckPermission('task_view')) {
		// 	wp_send_json(kcUnauthorizeAccessResponse(403));
		// }

		$request_data = $this->request->getInputs();

		if (!isset($request_data['id'])) {
			wp_send_json(kcThrowExceptionResponse(esc_html__('Task ID is required', 'kc-lang'), 400));
		}

		$task_id = (int)$request_data['id'];
		$task = (new KCTask())->getTaskById($task_id);

		if (empty($task)) {
			wp_send_json(kcThrowExceptionResponse(esc_html__('Task not found', 'kc-lang'), 404));
		}

		// Format assignees for frontend
		if (!empty($task->assignees)) {
			$formatted_assignees = [];
			foreach ($task->assignees as $assignee) {
				$formatted_assignees[] = [
					'id' => $assignee->assignee_id,
					'name' => $assignee->assignee_name,
					'completed_at' => $assignee->completed_at
				];
			}
			$task->assignees = $formatted_assignees;
		}

		wp_send_json([
			'status' => true,
			'message' => esc_html__('Task details', 'kc-lang'),
			'data' => $task
		]);
	}

	public function deleteTask() {
		// if (!kcCheckPermission('task_delete')) {
		// 	wp_send_json(kcUnauthorizeAccessResponse(403));
		// }

		$request_data = $this->request->getInputs();

		if (!isset($request_data['id'])) {
			wp_send_json([
				'status' => false,
				'message' => esc_html__('Task ID is required', 'kc-lang')
			]);
		}

		$task_id = (int)$request_data['id'];
		$task_model = new KCTask();
		$task_assignee_model = new KCTaskAssignee();
		
		// Check if user has permission to delete this task
		$task = $task_model->getTaskById($task_id);
		if (empty($task)) {
			wp_send_json([
				'status' => false,
				'message' => esc_html__('Task not found', 'kc-lang')
			]);
		}
		
		// Only task creator, admin, and clinic admin can delete tasks
		$current_user_id = get_current_user_id();
		$user_role = $this->getLoginUserRole();
		
		if ($task->creator_id != $current_user_id && 
			$user_role !== $this->getAdminRole() && 
			$user_role !== $this->getClinicAdminRole()) {
			wp_send_json([
				'status' => false,
				'message' => esc_html__('You do not have permission to delete this task', 'kc-lang')
			]);
		}

		try {
			// First, get the assignees before deleting the task for notification
			$assignees = $task_assignee_model->getTaskAssignees($task_id);
			$assignee_ids = array_map(function($a) {
				return $a->assignee_id;
			}, $assignees);
			
			// Make a copy of the task data before deletion (for use in notification)
			$task_snapshot = clone $task;
			
			// Also notify task creator if not in assignees
			if (!in_array($task->creator_id, $assignee_ids)) {
				$assignee_ids[] = $task->creator_id;
			}
			
			// Remove duplicates
			$assignee_ids = array_unique($assignee_ids);
			
			// Delete task and related data (assignees, comments, attachments)
			// Cascade delete will be handled by foreign keys in the database
			$result = $task_model->delete(['id' => $task_id]);

			if ($result) {
				// Send notification about task deletion to all assignees
				if (!empty($assignee_ids) && function_exists('sendTaskNotificationEmails')) {
					// Get user info for better context
					$user_info = get_userdata($current_user_id);
					$user_name = $user_info ? $user_info->display_name : esc_html__('A user', 'kc-lang');
					
					// Additional data for notification
					$additional_data = [
						'deleted_by' => $user_name,
						'deleted_by_id' => $current_user_id
					];
					
					// Send notifications
					sendTaskNotificationEmails($task_id, $assignee_ids, 'deleted', '', $additional_data);
				}
				
				wp_send_json([
					'status' => true,
					'message' => esc_html__('Task deleted successfully', 'kc-lang')
				]);
			} else {
				wp_send_json([
					'status' => false,
					'message' => esc_html__('Failed to delete task', 'kc-lang')
				]);
			}
		} catch (Exception $e) {
			wp_send_json([
				'status' => false,
				'message' => $e->getMessage()
			]);
		}
	}

	public function updateTask() {
		// if (!kcCheckPermission('task_edit')) {
		// 	wp_send_json(kcUnauthorizeAccessResponse(403));
		// }

		$request_data = $this->request->getInputs();

		if (!isset($request_data['id']) || !isset($request_data['status'])) {
			wp_send_json([
				'status' => false,
				'message' => esc_html__('Task ID and status are required', 'kc-lang')
			]);
		}

		$task_id = (int)$request_data['id'];
		$new_status = sanitize_text_field($request_data['status']);
		$current_user_id = get_current_user_id();

		try {
			$task_model = new KCTask();
			$task_assignee_model = new KCTaskAssignee();
			
			// Get the task before update to note the old status
			$task = $task_model->getTaskById($task_id);
			if (empty($task)) {
				wp_send_json([
					'status' => false,
					'message' => esc_html__('Task not found', 'kc-lang')
				]);
			}
			
			$old_status = $task->status;
			
			// Skip update if status hasn't changed
			if ($old_status === $new_status) {
				wp_send_json([
					'status' => true,
					'message' => esc_html__('Task status already set to this value', 'kc-lang')
				]);
				return;
			}
			
			// Perform the status update
			$result = $task_model->updateTaskStatus($task_id, $new_status);

			if ($result) {
				// Send notification about status change
				$assignees = $task_assignee_model->getTaskAssignees($task_id);
				$assignee_ids = array_map(function($a) {
					return $a->assignee_id;
				}, $assignees);
				
				// Also notify task creator if not in assignees
				if (!in_array($task->creator_id, $assignee_ids)) {
					$assignee_ids[] = $task->creator_id;
				}
				
				// Remove the current user from notification (they already know the status changed)
				// $assignee_ids = array_diff($assignee_ids, [$current_user_id]);
				
				// Only send notification if there are recipients
				if (!empty($assignee_ids) && function_exists('sendTaskNotificationEmails')) {
					// Get user info
					$user_info = get_userdata($current_user_id);
					$user_name = $user_info ? $user_info->display_name : esc_html__('A user', 'kc-lang');
					
					// Additional data for notification
					$additional_data = [
						'old_status' => $old_status,
						'new_status' => $new_status,
						'changed_by_name' => $user_name,
						'changed_by_id' => $current_user_id
					];
					
					// Send notifications
					sendTaskNotificationEmails($task_id, $assignee_ids, 'status_changed', '', $additional_data);
				}
				
				wp_send_json([
					'status' => true,
					'message' => esc_html__('Task status updated successfully', 'kc-lang')
				]);
			} else {
				wp_send_json([
					'status' => false,
					'message' => esc_html__('Failed to update task status', 'kc-lang')
				]);
			}
		} catch (Exception $e) {
			wp_send_json([
				'status' => false,
				'message' => $e->getMessage()
			]);
		}
	}

	public function completeTask() {
		// Check user permissions early
		// if (!$this->userHasKivicareRole()) {
		// 	wp_send_json(kcUnauthorizeAccessResponse(403));
		// 	exit;
		// }
	
		$request_data = $this->request->getInputs();
	
		// Validate task ID input
		if (empty($request_data['id'])) {
			wp_send_json([
				'status' => false,
				'message' => esc_html__('Task ID is required', 'kc-lang'),
				'error_code' => 'missing_task_id'
			]);
			exit;
		}
	
		$task_id = (int)$request_data['id'];
		$user_id = get_current_user_id();
	
		try {
			// Initialize models
			$task_model = new KCTask();
			$task_assignee_model = new KCTaskAssignee();
	
			// Validate task existence
			$task = $task_model->getTaskById($task_id);
			if (empty($task)) {
				wp_send_json([
					'status' => false,
					'message' => esc_html__('Task not found', 'kc-lang'),
					'error_code' => 'task_not_found'
				]);
				exit;
			}
	
			// Check if task is already completed
			if ($task->status === 'completed') {
				wp_send_json([
					'status' => false,
					'message' => esc_html__('Task is already completed', 'kc-lang'),
					'error_code' => 'task_already_completed'
				]);
				exit;
			}
	
			// Verify user is assigned to the task
			// $is_assigned = $task_assignee_model->isUserAssignedToTask($task_id, $user_id);
			// if (!$is_assigned) {
			// 	wp_send_json([
			// 		'status' => false,
			// 		'message' => esc_html__('You are not assigned to this task', 'kc-lang'),
			// 		'error_code' => 'not_assigned'
			// 	]);
			// 	exit;
			// }
	
			// Mark task as completed for current user
			$result = $task_model->updateTaskStatus($task_id, "completed");

			if (!$result) {
				wp_send_json([
					'status' => false,
					'message' => esc_html__('Failed to mark task as completed', 'kc-lang'),
					'error_code' => 'completion_failed'
				]);
				exit;
			}
	
			// Check if all assignees have completed the task
			$assignees = $task_assignee_model->getTaskAssignees($task_id);
			$all_completed = true;
			
			foreach ($assignees as $assignee) {
				if (empty($assignee->completed_at)) {
					$all_completed = false;
					break;
				}
			}
	
			// Update overall task status if all assignees completed
			if ($all_completed) {
				$task_model->updateTaskStatus($task_id, 'completed');
				
				// Send task completion notification
				if (function_exists('sendTaskStatusUpdateEmails')) {
					sendTaskStatusUpdateEmails($task_id, 'completed', $user_id);
				}
			}
	
			// Prepare response data
			$response_data = [
				'status' => true,
				'message' => esc_html__('Task marked as completed', 'kc-lang'),
				'data' => [
					'task_id' => $task_id,
					'all_assignees_completed' => $all_completed
				]
			];
	
			wp_send_json($response_data);
	
		} catch (Exception $e) {
			// Log the full error for debugging
			wp_send_json([
				'status' => false,
				'message' => esc_html__('An unexpected error occurred', 'kc-lang'),
				'error_code' => 'internal_error',
				'error_details' => $e->getMessage()
			]);
		}
	}
	
	public function archiveTask() {
		// if (!kcCheckPermission('task_edit')) {
		// 	wp_send_json(kcUnauthorizeAccessResponse(403));
		// }

		$request_data = $this->request->getInputs();

		if (!isset($request_data['id']) || !isset($request_data['is_archived'])) {
			wp_send_json([
				'status' => false,
				'message' => esc_html__('Task ID and archive status are required', 'kc-lang')
			]);
		}

		$task_id = (int)$request_data['id'];
		$is_archived = (int)$request_data['is_archived'];

		try {
			$task_model = new KCTask();
			$result = $task_model->update(
				['is_archived' => $is_archived],
				['id' => $task_id]
			);

			if ($result) {
				$message = $is_archived 
					? esc_html__('Task archived successfully', 'kc-lang')
					: esc_html__('Task unarchived successfully', 'kc-lang');
					
				wp_send_json([
					'status' => true,
					'message' => $message
				]);
			} else {
				wp_send_json([
					'status' => false,
					'message' => esc_html__('Failed to update task archive status', 'kc-lang')
				]);
			}
		} catch (Exception $e) {
			wp_send_json([
				'status' => false,
				'message' => $e->getMessage()
			]);
		}
	}

	/**
	 * Get comments for a task
	 */
	public function getTaskComments() {
		try {
			// Check user permissions
			if (!$this->userHasKivicareRole()) {
				wp_send_json(kcUnauthorizeAccessResponse(403));
				return;
			}

			$request_data = $this->request->getInputs();
			
			// Log for debugging
			error_log('getTaskComments received request: ' . json_encode($request_data));

			if (!isset($request_data['task_id'])) {
				wp_send_json([
					'status' => false,
					'message' => esc_html__('Task ID is required', 'kc-lang')
				]);
				return;
			}

			$task_id = (int)$request_data['task_id'];
			
			// Verify task exists and user has access to it
			$task = (new KCTask())->getTaskById($task_id);
			if (empty($task)) {
				error_log('Task not found with ID: ' . $task_id);
				wp_send_json([
					'status' => false,
					'message' => esc_html__('Task not found', 'kc-lang')
				]);
				return;
			}
			
			// Get the comments for this task
			$comment_model = new KCTaskComment();
			$comments = $comment_model->getTaskComments($task_id);
			
			// Always return an array even if no comments
			if (!is_array($comments)) {
				$comments = [];
			}
			
			error_log('Found ' . count($comments) . ' comments for task ID: ' . $task_id);

			wp_send_json([
				'status' => true,
				'message' => esc_html__('Task comments retrieved successfully', 'kc-lang'),
				'data' => $comments
			]);
		} catch (\Exception $e) {
			error_log('Error in getTaskComments: ' . $e->getMessage());
			wp_send_json([
				'status' => false,
				'message' => esc_html__('Error retrieving task comments', 'kc-lang'),
				'error' => $e->getMessage()
			]);
		}
	}

	/**
	 * Add a comment to a task
	 */
	public function addTaskComment() {
		if (!$this->userHasKivicareRole()) {
			wp_send_json(kcUnauthorizeAccessResponse(403));
		}

		$request_data = $this->request->getInputs();

		if (!isset($request_data['task_id']) || !isset($request_data['comment']) || empty($request_data['comment'])) {
			wp_send_json([
				'status' => false,
				'message' => esc_html__('Task ID and comment text are required', 'kc-lang')
			]);
		}

		$task_id = (int)$request_data['task_id'];
		$comment_text = sanitize_textarea_field($request_data['comment']);
		$user_id = get_current_user_id();

		// Verify task exists
		$task = (new KCTask())->getTaskById($task_id);
		if (empty($task)) {
			wp_send_json([
				'status' => false,
				'message' => esc_html__('Task not found', 'kc-lang')
			]);
		}

		try {
			$comment_model = new KCTaskComment();
			
			// Handle editing existing comment if comment_id is provided
			if (!empty($request_data['id'])) {
				$comment_id = (int)$request_data['id'];
				$existing_comment = $comment_model->get_by(['id' => $comment_id], '=', true);
				
				// Verify the comment exists and belongs to this task
				if (empty($existing_comment) || $existing_comment->task_id != $task_id) {
					wp_send_json([
						'status' => false,
						'message' => esc_html__('Comment not found', 'kc-lang')
					]);
				}
				
				// Only the comment author or admin/clinic admin can edit the comment
				if ($existing_comment->user_id != $user_id && 
					$this->getLoginUserRole() !== $this->getAdminRole() && 
					$this->getLoginUserRole() !== $this->getClinicAdminRole()) {
					wp_send_json([
						'status' => false,
						'message' => esc_html__('You do not have permission to edit this comment', 'kc-lang')
					]);
				}
				
				// Update the comment
				$result = $comment_model->update(
					['comment' => $comment_text],
					['id' => $comment_id]
				);
				
				$message = esc_html__('Comment updated successfully', 'kc-lang');
			} else {
				// Add new comment
				$result = $comment_model->addComment($task_id, $comment_text, $user_id);
				$message = esc_html__('Comment added successfully', 'kc-lang');
			}

			if ($result) {
				// Get all comments for the task to return updated list
				$comments = $comment_model->getTaskComments($task_id);
				
				// Send notification about the comment action
				$task_assignee_model = new KCTaskAssignee();
				$assignees = $task_assignee_model->getTaskAssignees($task_id);
				$assignee_ids = array_map(function($a) {
					return $a->assignee_id;
				}, $assignees);
				
				// Also notify task creator if not in assignees
				if (!in_array($task->creator_id, $assignee_ids)) {
					$assignee_ids[] = $task->creator_id;
				}
				
				// Remove the current user from notifications
				// $assignee_ids = array_diff($assignee_ids, [$user_id]);
				
				// Only send notification if there are recipients
				if (!empty($assignee_ids) && function_exists('sendTaskNotificationEmails')) {
					// Get user info
					$user_info = get_userdata($user_id);
					$user_name = $user_info ? $user_info->display_name : esc_html__('A user', 'kc-lang');
					
					// Determine notification type
					$action_type = !empty($request_data['id']) ? 'comment_updated' : 'comment_added';
					
					// Additional data for notification
					$additional_data = [
						'comment_text' => $comment_text,
						'comment_by' => $user_name,
						'comment_by_id' => $user_id
					];
					
					// Send notifications
					sendTaskNotificationEmails($task_id, $assignee_ids, $action_type, '', $additional_data);
				}
				
				wp_send_json([
					'status' => true,
					'message' => $message,
					'data' => $comments
				]);
			} else {
				wp_send_json([
					'status' => false,
					'message' => esc_html__('Failed to save comment', 'kc-lang')
				]);
			}
		} catch (Exception $e) {
			wp_send_json([
				'status' => false,
				'message' => $e->getMessage()
			]);
		}
	}

	/**
	 * Edit an existing task comment
	 */
	public function editTaskComment() {
		// Simply pass the request to addTaskComment which handles both add and edit
		$this->addTaskComment();
	}

	/**
	 * Delete a task comment
	 */
	public function deleteTaskComment() {
		if (!$this->userHasKivicareRole()) {
			wp_send_json(kcUnauthorizeAccessResponse(403));
		}

		$request_data = $this->request->getInputs();

		if (!isset($request_data['id']) || !isset($request_data['task_id'])) {
			wp_send_json([
				'status' => false,
				'message' => esc_html__('Comment ID and Task ID are required', 'kc-lang')
			]);
		}

		$comment_id = (int)$request_data['id'];
		$task_id = (int)$request_data['task_id'];
		$user_id = get_current_user_id();
		$user_role = $this->getLoginUserRole();

		try {
			$comment_model = new KCTaskComment();
			$comment = $comment_model->get_by(['id' => $comment_id], '=', true);
			
			if (empty($comment)) {
				wp_send_json([
					'status' => false,
					'message' => esc_html__('Comment not found', 'kc-lang')
				]);
			}
			
			// Verify the comment belongs to the specified task
			if ((int)$comment->task_id != $task_id) {
				wp_send_json([
					'status' => false,
					'message' => esc_html__('Comment does not belong to this task', 'kc-lang')
				]);
			}
			
			// Verify the user has permission to delete this comment
			// Only the comment author, admin, or clinic admin can delete comments
			if ((int)$comment->user_id != $user_id && 
				$user_role !== $this->getAdminRole() && 
				$user_role !== $this->getClinicAdminRole()) {
				wp_send_json([
					'status' => false,
					'message' => esc_html__('You do not have permission to delete this comment', 'kc-lang')
				]);
			}
			
			// Save comment text before deletion for notification
			$comment_text = $comment->comment;
			$comment_user_id = $comment->user_id;
			
			// Delete the comment
			$result = $comment_model->deleteComment($comment_id);

			if ($result) {
				// Get task info for notifications
				$task = (new KCTask())->getTaskById($task_id);
				
				// Send notification about comment deletion
				if ($task) {
					// Get task assignees
					$task_assignee_model = new KCTaskAssignee();
					$assignees = $task_assignee_model->getTaskAssignees($task_id);
					$assignee_ids = array_map(function($a) {
						return $a->assignee_id;
					}, $assignees);
					
					// Also notify task creator if not in assignees
					if (!in_array($task->creator_id, $assignee_ids)) {
						$assignee_ids[] = $task->creator_id;
					}
					
					// Remove the current user from notifications
					// $assignee_ids = array_diff($assignee_ids, [$user_id]);
					
					// Only send notification if there are recipients
					if (!empty($assignee_ids) && function_exists('sendTaskNotificationEmails')) {
						// Get info about deleter
						$user_info = get_userdata($user_id);
						$user_name = $user_info ? $user_info->display_name : esc_html__('A user', 'kc-lang');
						
						// Get info about comment author if different from deleter
						$comment_user_name = $user_name;
						if ($comment_user_id != $user_id) {
							$comment_user_info = get_userdata($comment_user_id);
							$comment_user_name = $comment_user_info ? $comment_user_info->display_name : esc_html__('A user', 'kc-lang');
						}
						
						// Additional data for notification
						$additional_data = [
							'comment_text' => $comment_text,
							'comment_by' => $comment_user_name,
							'deleted_by' => $user_name,
							'deleted_by_id' => $user_id
						];
						
						// Send notifications
						sendTaskNotificationEmails($task_id, $assignee_ids, 'comment_deleted', '', $additional_data);
					}
				}
				
				// Get all comments for the task to return updated list
				$comments = $comment_model->getTaskComments($task_id);
				
				wp_send_json([
					'status' => true,
					'message' => esc_html__('Comment deleted successfully', 'kc-lang'),
					'data' => $comments
				]);
			} else {
				wp_send_json([
					'status' => false,
					'message' => esc_html__('Failed to delete comment', 'kc-lang')
				]);
			}
		} catch (Exception $e) {
			error_log('KiviCare Task Comment Delete Error: ' . $e->getMessage());
			wp_send_json([
				'status' => false,
				'message' => $e->getMessage()
			]);
		}
	}

	/**
	 * Get attachments for a task
	 * This is a fallback that forwards the request to KCTaskAttachmentController
	 */
	public function getTaskAttachments() {
		$attachmentController = new KCTaskAttachmentController();
		return $attachmentController->index();
	}

	/**
	 * Upload an attachment to a task
	 * This is a fallback that forwards the request to KCTaskAttachmentController
	 */
	public function uploadTaskAttachment() {
		$attachmentController = new KCTaskAttachmentController();
		return $attachmentController->upload();
	}

	/**
	 * Delete a task attachment
	 * This is a fallback that forwards the request to KCTaskAttachmentController
	 */
	public function deleteTaskAttachment() {
		$attachmentController = new KCTaskAttachmentController();
		return $attachmentController->delete();
	}
}