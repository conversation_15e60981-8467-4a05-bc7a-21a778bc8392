<template>
  <Dialog :open="true" @update:open="$emit('close')">
    <DialogContent class="max-w-2xl">
      <DialogHeader>
        <DialogTitle class="flex items-center">
          <Pill class="w-5 h-5 mr-2" />
          Add Prescription
        </DialogTitle>
        <DialogDescription>
          Add a new medication prescription for this consultation.
        </DialogDescription>
      </DialogHeader>

      <form @submit.prevent="savePrescription" class="space-y-6">
        <!-- Medication Name -->
        <div class="space-y-2">
          <Label for="medication_name">Medication Name *</Label>
          <div class="relative">
            <Input
              id="medication_name"
              v-model="form.medication_name"
              placeholder="Search for medication..."
              required
              @input="searchMedications"
              class="pr-10"
            />
            <Search class="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          </div>
          
          <!-- Medication Search Results -->
          <div v-if="medicationSuggestions.length" class="border rounded-md max-h-40 overflow-y-auto">
            <div
              v-for="medication in medicationSuggestions"
              :key="medication"
              @click="selectMedication(medication)"
              class="px-3 py-2 hover:bg-gray-50 cursor-pointer border-b last:border-b-0"
            >
              {{ medication }}
            </div>
          </div>
        </div>

        <!-- Dosage and Frequency Row -->
        <div class="grid grid-cols-2 gap-4">
          <div class="space-y-2">
            <Label for="dosage">Dosage</Label>
            <Input
              id="dosage"
              v-model="form.dosage"
              placeholder="e.g., 500mg"
            />
          </div>
          <div class="space-y-2">
            <Label for="frequency">Frequency</Label>
            <Select v-model="form.frequency">
              <SelectTrigger>
                <SelectValue placeholder="Select frequency" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="once_daily">Once daily</SelectItem>
                <SelectItem value="twice_daily">Twice daily</SelectItem>
                <SelectItem value="three_times_daily">Three times daily</SelectItem>
                <SelectItem value="four_times_daily">Four times daily</SelectItem>
                <SelectItem value="as_needed">As needed</SelectItem>
                <SelectItem value="custom">Custom</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <!-- Duration -->
        <div class="space-y-2">
          <Label for="duration">Duration</Label>
          <div class="grid grid-cols-2 gap-4">
            <Input
              v-model="durationValue"
              type="number"
              min="1"
              placeholder="Duration"
            />
            <Select v-model="durationUnit">
              <SelectTrigger>
                <SelectValue placeholder="Unit" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="days">Days</SelectItem>
                <SelectItem value="weeks">Weeks</SelectItem>
                <SelectItem value="months">Months</SelectItem>
                <SelectItem value="ongoing">Ongoing</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <!-- Instructions -->
        <div class="space-y-2">
          <Label for="instructions">Instructions</Label>
          <Textarea
            id="instructions"
            v-model="form.instructions"
            placeholder="Special instructions for taking this medication..."
            rows="3"
          />
        </div>

        <!-- Quick Instruction Templates -->
        <div class="space-y-2">
          <Label>Quick Instructions</Label>
          <div class="flex flex-wrap gap-2">
            <Button
              v-for="template in instructionTemplates"
              :key="template"
              type="button"
              variant="outline"
              size="sm"
              @click="addInstructionTemplate(template)"
            >
              {{ template }}
            </Button>
          </div>
        </div>

        <!-- Status -->
        <div class="space-y-2">
          <Label for="status">Status</Label>
          <Select v-model="form.status">
            <SelectTrigger>
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="discontinued">Discontinued</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <!-- Actions -->
        <div class="flex justify-end space-x-3 pt-4">
          <Button type="button" variant="outline" @click="$emit('close')">
            Cancel
          </Button>
          <Button type="submit" :disabled="saving">
            <Loader2 v-if="saving" class="w-4 h-4 mr-2 animate-spin" />
            Add Prescription
          </Button>
        </div>
      </form>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Pill, Search, Loader2 } from 'lucide-vue-next'
import axios from 'axios'

interface Props {
  consultationId: string
}

interface Emits {
  (e: 'close'): void
  (e: 'saved'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Form data
const form = ref({
  medication_name: '',
  dosage: '',
  frequency: '',
  duration: '',
  instructions: '',
  status: 'active'
})

const durationValue = ref('')
const durationUnit = ref('')
const saving = ref(false)
const medicationSuggestions = ref([])

// Common medications for suggestions
const commonMedications = [
  'Paracetamol', 'Ibuprofen', 'Aspirin', 'Amoxicillin', 'Metformin',
  'Lisinopril', 'Atorvastatin', 'Omeprazole', 'Amlodipine', 'Simvastatin',
  'Levothyroxine', 'Warfarin', 'Furosemide', 'Prednisolone', 'Salbutamol'
]

const instructionTemplates = [
  'Take with food',
  'Take on empty stomach',
  'Take before bedtime',
  'Take with plenty of water',
  'Do not crush or chew',
  'Avoid alcohol',
  'Complete the full course'
]

// Watch duration inputs to combine them
watch([durationValue, durationUnit], ([value, unit]) => {
  if (value && unit) {
    if (unit === 'ongoing') {
      form.value.duration = 'Ongoing'
    } else {
      form.value.duration = `${value} ${unit}`
    }
  }
})

// Methods
const searchMedications = () => {
  const query = form.value.medication_name.toLowerCase()
  if (query.length < 2) {
    medicationSuggestions.value = []
    return
  }
  
  medicationSuggestions.value = commonMedications.filter(med =>
    med.toLowerCase().includes(query)
  ).slice(0, 5)
}

const selectMedication = (medication: string) => {
  form.value.medication_name = medication
  medicationSuggestions.value = []
}

const addInstructionTemplate = (template: string) => {
  if (form.value.instructions) {
    form.value.instructions += '. ' + template
  } else {
    form.value.instructions = template
  }
}

const savePrescription = async () => {
  try {
    saving.value = true
    
    const payload = {
      consultation_id: props.consultationId,
      ...form.value
    }
    
    await axios.post('/api/consultation-prescriptions', payload)
    
    emit('saved')
    emit('close')
  } catch (error) {
    console.error('Error saving prescription:', error)
    alert('Failed to save prescription. Please try again.')
  } finally {
    saving.value = false
  }
}
</script>
