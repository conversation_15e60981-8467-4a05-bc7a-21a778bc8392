<!-- Create.vue -->
<template>
  <div class="flex flex-col space-y-6">
    <form id="encounterDataForm" @submit.prevent="handleSubmit" novalidate>
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Consultation Date -->
          <div>
            <label
              for="encounter_date"
              class="block text-sm font-medium text-gray-700"
            >
              {{ formTranslation.patient_encounter.encounter_date }}
              <span class="text-red-500">*</span>
            </label>
            <div class="relative mt-1">
              <input
                type="date"
                id="encounter_date"
                v-model="formData.date"
                class="block w-full px-4 py-2 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                :class="{
                  'border-red-500': submitted && $v.formData.date.$error,
                }"
              />
              <div
                class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none"
              >
                <i class="fa fa-calendar text-gray-400"></i>
              </div>
            </div>
            <div
              v-if="submitted && !$v.formData.date.required"
              class="text-red-500 text-sm mt-1"
            >
              {{ formTranslation.patient_encounter.encounter_date_required }}
            </div>
          </div>

          <!-- Clinic Selection -->
          <div v-if="showClinicSelection">
            <label
              for="clinic_id"
              class="block text-sm font-medium text-gray-700"
            >
              {{ formTranslation.clinic.select_clinic }}
              <span class="text-red-500">*</span>
            </label>
            <div class="relative mt-1">
              <select
                id="clinic_id"
                v-model="formData.clinic_id"
                @change="clinicChange"
                :disabled="clinicMultiselectLoader"
                class="block w-full px-4 py-2 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 appearance-none"
              >
                <option value="">
                  {{ formTranslation.patient_encounter.tag_select_clinic_plh }}
                </option>
                <option
                  v-for="option in clinic"
                  :key="option.id"
                  :value="option.id"
                >
                  {{ option.label }}
                </option>
              </select>
              <div
                class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none"
              >
                <i class="fa fa-chevron-down text-gray-400"></i>
              </div>
            </div>
            <div
              v-if="submitted && !formData.clinic_id"
              class="text-red-500 text-sm mt-1"
            >
              {{ formTranslation.common.clinic_is_required }}
            </div>
          </div>

          <!-- Doctor Selection -->
          <div v-if="getUserRole() !== 'doctor'" class="mt-4 md:mt-0">
            <label
              for="doctor_id"
              class="block text-sm font-medium text-gray-700"
            >
              {{ formTranslation.common.doctor }}
              <span class="text-red-500">*</span>
            </label>
            <div class="relative mt-1">
              <select
                id="doctor_id"
                v-model="formData.doctor_id"
                :disabled="doctorMultiselectLoader"
                class="block w-full px-4 py-2 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 appearance-none"
              >
                <option value="">
                  {{ formTranslation.patient_encounter.tag_select_doctor }}
                </option>
                <option
                  v-for="doctor in doctors"
                  :key="doctor.id"
                  :value="doctor.id"
                >
                  {{ doctor.label }}
                </option>
              </select>
              <div
                class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none"
              >
                <i class="fa fa-chevron-down text-gray-400"></i>
              </div>
            </div>
            <div
              v-if="submitted && !formData.doctor_id"
              class="text-red-500 text-sm mt-1"
            >
              {{ formTranslation.appointments.doc_required }}
            </div>
          </div>

          <!-- Patient Selection -->
          <div v-if="patientField" class="mt-4 md:mt-0">
            <label
              for="patient_id"
              class="block text-sm font-medium text-gray-700"
            >
              {{ formTranslation.common.patient }}
              <span class="text-red-500">*</span>
            </label>
            <div class="relative mt-1">
              <select
                id="patient_id"
                v-model="formData.patient_id"
                :disabled="patientMultiselectLoader"
                class="block w-full px-4 py-2 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 appearance-none"
              >
                <option value="">
                  {{ formTranslation.patient_encounter.tag_patient_type_plh }}
                </option>
                <option
                  v-for="patient in patients"
                  :key="patient.id"
                  :value="patient.id"
                >
                  {{ patient.label }}
                </option>
              </select>
              <div
                class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none"
              >
                <i class="fa fa-chevron-down text-gray-400"></i>
              </div>
            </div>
            <div
              v-if="submitted && !formData.patient_id"
              class="text-red-500 text-sm mt-1"
            >
              {{ formTranslation.patient_bill.patient_required }}
            </div>
          </div>

          <!-- Description (Full Width) -->
          <div class="col-span-1 md:col-span-2">
            <label
              for="description"
              class="block text-sm font-medium text-gray-700"
            >
              {{ formTranslation.appointments.description }}
            </label>
            <textarea
              id="description"
              v-model="formData.description"
              rows="3"
              class="block w-full mt-1 px-4 py-2 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 resize-none"
              :placeholder="formTranslation.appointments.description"
            ></textarea>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3 mt-6">
          <button
            type="button"
            @click="getCloseForm"
            class="px-4 py-2 bg-white border border-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
          >
            {{ formTranslation.common.cancel }}
          </button>
          <button
            v-if="!loading"
            type="submit"
            class="px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <i class="fa fa-save mr-2"></i
            >{{ formTranslation.patient_encounter.save_btn }}
          </button>
          <button
            v-else
            disabled
            class="px-4 py-2 bg-indigo-400 text-white text-sm font-medium rounded-md cursor-not-allowed"
          >
            <i class="fa fa-sync fa-spin mr-2"></i
            >{{ formTranslation.common.loading }}
          </button>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import { required, requiredIf } from "vuelidate/lib/validators";
import { post, get } from "../../config/request";
import { validateForm } from "../../config/helper";
import moment from "moment";

export default {
  name: "CreateEncounter",

  props: {
    encounterId: {
      type: [Number, String],
      default: -1,
    },
    encounterData: {
      type: Object,
      default: null,
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    patientField: {
      type: Boolean,
      default: true,
    },
    clinicField: {
      type: Boolean,
      default: true,
    },
  },

  data() {
    return {
      formData: this.defaultEncounterData(),
      doctors: [],
      patients: [],
      loading: false,
      submitted: false,
      clinicMultiselectLoader: true,
      doctorMultiselectLoader: true,
      patientMultiselectLoader: true,
      patientRoleName: "patient",
    };
  },

  validations() {
    return {
      formData: {
        date: { required },
        patient_id: {
          required: requiredIf(() => this.patientField),
        },
        doctor_id: {
          required: requiredIf(() => !this.isDoctorSelectionEnabled),
        },
        clinic_id: {
          required: requiredIf(() => this.showClinicSelection),
        },
      },
    };
  },

  computed: {
    showClinicSelection() {
      return (
        this.userData?.addOns?.kiviPro &&
        (this.getUserRole() === "administrator" ||
          this.getUserRole() === "doctor") &&
        this.clinicField
      );
    },
    isDoctorSelectionEnabled() {
      return this.getUserRole() === "doctor";
    },
    userId() {
      return this.$store.state.userDataModule.user.ID;
    },
    userData() {
      return this.$store.state.userDataModule.user;
    },
    clinic() {
      this.clinicMultiselectLoader = false;
      return this.$store.state.clinic;
    },
    activeClinicId() {
      return (
        this.$store.state.activeClinicId || this.userData?.default_clinic_id
      );
    },
  },

  async mounted() {
    await this.init();
  },

  methods: {
    async init() {
      try {
        this.formData = this.defaultEncounterData();

        if (this.isEdit && this.encounterData) {
          const { doctor_id, patient_id, clinic_id, encounter_date, description } =
            this.encounterData;

          this.formData = {
            ...this.formData,
            doctor_id: typeof doctor_id === "object" ? doctor_id.id : doctor_id,
            patient_id:
              typeof patient_id === "object" ? patient_id.id : patient_id,
            clinic_id: typeof clinic_id === "object" ? clinic_id.id : clinic_id,
            date: encounter_date ? moment(encounter_date).format('YYYY-MM-DD') : moment().format('YYYY-MM-DD'),
            description,
          };

          if (this.formData.clinic_id) {
            if (this.getUserRole() !== "doctor")
              await this.loadDoctorsForClinic(this.formData.clinic_id);
            if (this.getUserRole() !== "patient")
              await this.getClinicPatients(this.formData.clinic_id);
          }
        } else {
          if (this.getUserRole() !== "doctor") await this.getDoctorsData();
          if (this.getUserRole() !== "patient")
            await this.getClinicPatients(this.activeClinicId);

          if (this.$route.params.patient_id) {
            this.formData.patient_id = this.$route.params.patient_id;
          }
        }
      } catch (error) {
        console.error("Initialization error:", error);
        displayErrorMessage(this.formTranslation.common.internal_server_error);
      }
    },

    async getDoctorsData() {
      try {
        this.doctorMultiselectLoader = true;
        const clinic_id = this.activeClinicId;

        const response = await get("get_static_data", {
          data_type: "clinic_doctors",
          clinic_id,
        });

        if (response.data.status) {
          this.doctors = response.data.data || [];
        }
      } catch (error) {
        console.error("Error loading doctors:", error);
        displayErrorMessage(this.formTranslation.common.internal_server_error);
      } finally {
        this.doctorMultiselectLoader = false;
      }
    },

    async getClinicPatients(clinic_id) {
      try {
        this.patientMultiselectLoader = true;
        const response = await get("get_static_data", {
          data_type: "users",
          user_type: this.patientRoleName,
          request_clinic_id: clinic_id,
        });

        if (response.data.status) {
          this.patients = response.data.data || [];
        }
      } catch (error) {
        console.error("Error loading patients:", error);
        displayErrorMessage(this.formTranslation.common.internal_server_error);
      } finally {
        this.patientMultiselectLoader = false;
      }
    },

    defaultEncounterData() {
      return {
        date: new Date(),
        clinic_id: this.activeClinicId || "",
        doctor_id: this.getUserRole() === "doctor" ? this.userId : "",
        patient_id: "",
        description: "",
        added_by: this.userId,
        status: 1,
      };
    },

    async clinicChange(event) {
      try {
        const selectedClinicId = event?.target?.value || event;
        if (!selectedClinicId) return;

        this.formData.doctor_id = "";
        this.formData.patient_id = "";

        // Rest of the method stays the same
      } catch (error) {
        console.error("Error in clinic change:", error);
        displayErrorMessage(this.formTranslation.common.internal_server_error);
      }
    },

    async loadDoctorsForClinic(clinicId) {
      try {
        this.doctorMultiselectLoader = true;
        const response = await get("get_static_data", {
          data_type: "get_users_by_clinic",
          clinic_id: clinicId,
          type: "doctor",
        });

        if (response.data.status) {
          this.doctors = response.data.data || [];
        } else {
          throw new Error(response.data.message);
        }
      } catch (error) {
        console.error("Error loading doctors:", error);
        displayErrorMessage(this.formTranslation.common.internal_server_error);
      } finally {
        this.doctorMultiselectLoader = false;
      }
    },

    async handleSubmit() {
  try {
    this.loading = true;
    this.submitted = true;

    this.$v.$touch();
    if (this.$v.formData.$invalid) {
      return;
    }

    if (!validateForm("encounterDataForm")) {
      return;
    }

    const selectedClinic = this.clinic.find(c => c.id === this.formData.clinic_id);
    const selectedDoctor = this.doctors.find(d => d.id === this.formData.doctor_id);
    const selectedPatient = this.patients.find(p => p.id === this.formData.patient_id);

    const submitData = {
      ...this.formData,
      id: this.isEdit ? this.encounterId : undefined,
      encounter_date: moment(this.formData.date).format("YYYY-MM-DD"),
      clinic_id: {
        id: this.formData.clinic_id,
        label: selectedClinic?.label || ""
      },
      doctor_id: this.getUserRole() === "doctor" 
        ? {
            id: this.userId,
            label: this.userData?.display_name || ""
          }
        : {
            id: this.formData.doctor_id,
            label: selectedDoctor?.label || ""
          },
      patient_id: {
        id: this.formData.patient_id,
        label: selectedPatient?.label || ""
      }
    };

    const response = await post(
      this.isEdit ? "patient_encounter_save" : "patient_encounter_save", 
      submitData
    );

    if (response.data.status) {
      displayMessage(response.data.message);
      this.$emit("getPatientEncountersData");
      this.$emit("closeEncounterForm");
    } else {
      throw new Error(response.data.message);
    }
  } catch (error) {
    console.error("Submit error:", error);
    displayErrorMessage(error.message || this.formTranslation.common.internal_server_error);
  } finally {
    this.loading = false;
  }
},

    getCloseForm() {
      this.$emit("closeEncounterForm");
    },
  },
};
</script>
