<?php

namespace Database\Factories;

use App\Models\Clinic;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Clinic>
 */
class ClinicFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Clinic::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Allow factory usage in testing environment
        if (app()->environment('testing')) {
            return [
                'name' => fake()->company() . ' Medical Center',
                'slug' => fake()->unique()->slug(),
                'description' => fake()->paragraph(),
                'email' => fake()->unique()->companyEmail(),
                'phone' => fake()->phoneNumber(),
                'website' => fake()->url(),
                'address' => fake()->streetAddress(),
                'city' => fake()->city(),
                'state' => fake()->state(),
                'postal_code' => fake()->postcode(),
                'country' => fake()->country(),
                'operating_hours' => [
                    'monday' => '9:00-17:00',
                    'tuesday' => '9:00-17:00',
                    'wednesday' => '9:00-17:00',
                    'thursday' => '9:00-17:00',
                    'friday' => '9:00-17:00',
                    'saturday' => '9:00-12:00',
                    'sunday' => 'closed'
                ],
                'services_offered' => [
                    'General Consultation',
                    'Preventive Care',
                    'Diagnostic Services'
                ],
                'license_number' => 'LIC-' . fake()->unique()->numerify('######'),
                'tax_id' => 'TAX-' . fake()->unique()->numerify('########'),
                'is_active' => true,
                'accepts_new_patients' => fake()->boolean(80),
                'telemedicine_enabled' => fake()->boolean(70),
                'insurance_accepted' => [
                    'Blue Cross Blue Shield',
                    'Aetna',
                    'Cigna'
                ],
                'logo' => null,
                'primary_color' => fake()->hexColor(),
                'secondary_color' => fake()->hexColor(),
            ];
        }

        throw new \Exception('ClinicFactory is disabled for production use. Create clinics manually through proper channels.');
    }

    /**
     * Indicate that the clinic is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the clinic doesn't accept new patients.
     */
    public function notAcceptingPatients(): static
    {
        return $this->state(fn (array $attributes) => [
            'accepts_new_patients' => false,
        ]);
    }

    /**
     * Indicate that the clinic doesn't offer telemedicine.
     */
    public function noTelemedicine(): static
    {
        return $this->state(fn (array $attributes) => [
            'telemedicine_enabled' => false,
        ]);
    }

    /**
     * Create a clinic with minimal data.
     */
    public function minimal(): static
    {
        return $this->state(fn (array $attributes) => [
            'description' => null,
            'email' => null,
            'phone' => null,
            'website' => null,
            'address' => null,
            'city' => null,
            'state' => null,
            'postal_code' => null,
            'operating_hours' => null,
            'services_offered' => null,
            'license_number' => null,
            'tax_id' => null,
            'insurance_accepted' => null,
            'logo' => null,
            'primary_color' => null,
            'secondary_color' => null,
        ]);
    }
}
