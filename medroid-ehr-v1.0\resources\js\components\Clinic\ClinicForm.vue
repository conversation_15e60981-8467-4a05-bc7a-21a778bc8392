<template>
  <div class="">
    <!-- Modal Backdrop -->
    <div
      class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-40"
      @click="handleCancel"
    ></div>

    <!-- Modal Content -->
    <div class="fixed inset-0 z-50 overflow-y-auto">
      <div class="min-h-screen px-4 py-6 flex items-center justify-center">
        <div
          class="fixed inset-0 bg-black/20 backdrop-blur-sm"
          @click="handleCancel"
        ></div>

        <div class="relative w-full max-w-4xl bg-white rounded-xl shadow-xl">
          <!-- Modal Header -->
          <div
            class="sticky top-0 z-10 flex items-center justify-between p-6 border-b border-gray-100 bg-white rounded-t-xl"
          >
            <h3 class="text-xl font-semibold text-gray-900">
              {{
                isEditMode
                  ? formTranslation.clinic.edit_clinic
                  : formTranslation.clinic.add_clinic
              }}
            </h3>
            <button
              @click="handleCancel"
              class="text-gray-400 hover:text-gray-500"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                class="w-6 h-6"
              >
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div>

          <!-- Modal Body -->
          <div class="p-6 max-h-[calc(100vh-200px)]">
            <!-- Loading State -->
            <div
              v-if="formLoader"
              class="flex justify-center items-center h-screen"
            >
              <div
                class="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-black"
              ></div>
            </div>

            <!-- Form -->
            <form
              v-else
              @submit.prevent="handleSubmit"
              novalidate
              class="bg-white"
              id="clinicForm"
            >
              <!-- Form Header Section -->
              <div class="border-b border-gray-200 pb-6">
                <div class="flex items-center space-x-3">
                  <div class="flex-shrink-0">
                    <!-- Logo Upload Section - Moved to top for better visibility -->
                    <div class="relative">
                      <input
                        id="file"
                        type="button"
                        @click="uploadProfile()"
                        class="hidden"
                      />
                      <label for="file" class="relative cursor-pointer group">
                        <div
                          class="w-24 h-24 rounded-full bg-gray-100 flex items-center justify-center overflow-hidden"
                        >
                          <img
                            v-if="defaultImage"
                            :src="defaultImage"
                            class="w-full h-full object-cover"
                          />
                          <div
                            v-else
                            class="w-full h-full flex items-center justify-center bg-gray-50"
                          >
                            <i
                              class="fas fa-hospital text-gray-400 text-3xl"
                            ></i>
                          </div>
                          <div
                            class="absolute inset-0 bg-black bg-opacity-40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center"
                          >
                            <i class="fas fa-camera text-white text-xl"></i>
                          </div>
                        </div>
                      </label>
                    </div>
                  </div>
                  <div>
                    <h4 class="text-xl font-semibold text-gray-900">
                      {{ formTranslation.common.basic_details }}
                    </h4>
                    <p class="text-sm text-gray-500 mt-1">
                      Enter your clinic's basic information below
                    </p>
                  </div>
                </div>
              </div>

              <div class="py-6 space-y-8">
                <!-- Basic Info Section -->
                <div class="grid grid-cols-1 md:grid-cols-1 gap-x-6 gap-y-8">
                  <!-- Name Field -->
                  <div class="col-span-1">
                    <label
                      for="name"
                      class="block text-sm font-medium text-gray-700"
                    >
                      {{ formTranslation.common.name }}
                      <span class="text-red-500">*</span>
                    </label>
                    <div class="mt-1 relative">
                      <input
                        id="name"
                        v-model="formData.name"
                        :class="{
                          'border-red-500':
                            submitted && $v.formData.name.$error,
                          'focus:ring-black focus:border-black':
                            !$v.formData.name.$error,
                        }"
                        :placeholder="formTranslation.clinic.clinic_name_plh"
                        required
                        name="name"
                        type="text"
                        class="w-full rounded-lg px-4 py-2.5 border border-gray-300 shadow-sm"
                      />
                      <p
                        v-if="submitted && !$v.formData.name.required"
                        class="mt-1 text-sm text-red-500"
                      >
                        {{ formTranslation.common.name_required }}
                      </p>
                    </div>
                  </div>
                </div>

                <!-- Basic Info Section -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-8">
                  <!-- Email Field -->
                  <div class="col-span-1">
                    <label
                      for="email"
                      class="block text-sm font-medium text-gray-700"
                    >
                      {{ formTranslation.common.email }}
                      <span class="text-red-500">*</span>
                    </label>
                    <div class="mt-1 relative">
                      <div
                        class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                      >
                        <i class="fas fa-envelope text-gray-400"></i>
                      </div>
                      <input
                        id="email"
                        v-model="formData.email"
                        :class="{
                          'border-red-500':
                            submitted && $v.formData.email.$error,
                          'focus:ring-black focus:border-black':
                            !$v.formData.email.$error,
                        }"
                        :placeholder="formTranslation.clinic.email_plh"
                        required
                        name="email"
                        type="email"
                        class="w-full rounded-lg pl-10 pr-4 py-2.5 border border-gray-300 shadow-sm"
                      />
                    </div>
                    <p
                      v-if="submitted && !$v.formData.email.required"
                      class="mt-1 text-sm text-red-500"
                    >
                      {{ formTranslation.common.email_required }}
                    </p>
                  </div>

                  <!-- Contact No Field -->
                  <div class="col-span-1">
                    <label
                      for="contact_no"
                      class="block text-sm font-medium text-gray-700"
                    >
                      {{ formTranslation.common.contact_no }}
                      <span class="text-red-500">*</span>
                    </label>
                    <div class="mt-1">
                      <VuePhoneNumberInput
                        v-model="formData.telephone_no"
                        id="contact_no"
                        :default-country-code="defaultCountryCode"
                        @update="contactUpdateHandaler"
                        no-example
                        class="phone-input-custom"
                      />
                    </div>
                    <p
                      v-if="submitted && !$v.formData.telephone_no.required"
                      class="mt-1 text-sm text-red-500"
                    >
                      {{ formTranslation.common.contact_num_required }}
                    </p>
                  </div>

                  <!-- Specialties Section -->
                  <div class="col-span-1">
                    <label
                      for="contact_no"
                      class="block text-sm font-medium text-gray-700"
                    >
                      {{ formTranslation.clinic.note_specialization }}
                      <span class="text-red-500">*</span>
                    </label>
                    <div class="mt-1">
                      <multi-select
                        v-model="formData.specialties"
                        id="specialties"
                        :tag-placeholder="formTranslation.clinic.clinic_sp_plh"
                        :placeholder="formTranslation.clinic.search_placeholder"
                        label="label"
                        track-by="id"
                        :loading="specializationMultiselectLoader"
                        :options="specialization"
                        :multiple="true"
                        :taggable="true"
                        @tag="addNewSpecialization"
                        class="multiselect-custom"
                      />
                    </div>
                  </div>

                  <!-- Status Section -->
                  <div class="col-span-1">
                    <label
                      for="status"
                      class="block text-sm font-medium text-gray-700"
                    >
                      {{ formTranslation.common.status }}
                      <span class="text-red-500">*</span>
                    </label>
                    <select
                      id="status"
                      v-model="formData.status"
                      name="status"
                      class="mt-1 w-full rounded-lg px-4 py-2.5 border border-gray-300 shadow-sm focus:border-black focus:ring-black"
                    >
                      <option value="">
                        {{ formTranslation.appointments.select_status }}
                      </option>
                      <option value="1">
                        {{ formTranslation.common.active }}
                      </option>
                      <option value="0">
                        {{ formTranslation.common.inactive }}
                      </option>
                    </select>
                  </div>
                </div>

                <!-- Address Section -->
                <div class="border-t border-gray-200 pt-8">
                  <h4 class="text-lg font-medium text-gray-900 mb-4">
                    Location Details
                  </h4>

                  <div class="space-y-6">
                    <!-- Address Field -->
                    <div>
                      <label
                        for="address"
                        class="block text-sm font-medium text-gray-700"
                      >
                        {{ formTranslation.common.address }}
                        <span class="text-red-500">*</span>
                      </label>
                      <div class="mt-1">
                        <textarea
                          id="address"
                          v-model="formData.address"
                          :placeholder="formTranslation.clinic.address_plh"
                          rows="3"
                          class="w-full rounded-lg px-4 py-2 border border-gray-300 shadow-sm focus:border-black focus:ring-black"
                        ></textarea>
                      </div>
                    </div>

                    <!-- City, Country, Postal Code -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div
                        v-for="field in ['city', 'country', 'postal_code']"
                        :key="field"
                      >
                        <label
                          :for="field"
                          class="block text-sm font-medium text-gray-700"
                        >
                          {{ formTranslation.common[field] }}
                          <span class="text-red-500">*</span>
                        </label>
                        <input
                          :id="field"
                          v-model="formData[field]"
                          :placeholder="formTranslation.clinic[`${field}_plh`]"
                          :name="field"
                          type="text"
                          class="mt-1 w-full rounded-lg px-4 py-2.5 border border-gray-300 shadow-sm focus:border-black focus:ring-black"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <hr />

                <div class="">
                  <div class="">
                    <h4 class="text-xl font-semibold text-primary mb-6">
                      {{ formTranslation.clinic.clinic_admin_detail }}
                    </h4>

                    <div class="grid md:grid-cols-12 gap-6">
                      <div class="md:col-span-10 space-y-6">
                        <div class="grid md:grid-cols-2 gap-6">
                          <!-- First Name -->
                          <div>
                            <label
                              for="first_name"
                              class="block text-sm font-medium text-gray-700 mb-2"
                            >
                              {{ formTranslation.common.fname }}
                              <span class="text-red-500">*</span>
                            </label>
                            <input
                              id="first_name"
                              v-model="formData.first_name"
                              :class="[
                                'w-full px-4 py-2 border rounded-lg',
                                submitted && $v.formData.first_name.$error
                                  ? 'border-red-500 focus:ring-red-500'
                                  : 'border-gray-300 focus:border-primary focus:ring-primary',
                              ]"
                              :placeholder="formTranslation.clinic.fname_plh"
                              type="text"
                            />
                            <div
                              v-if="
                                submitted && !$v.formData.first_name.required
                              "
                              class="text-red-500 text-sm mt-1"
                            >
                              {{ formTranslation.common.fname_required }}
                            </div>
                          </div>

                          <!-- Last Name -->
                          <div>
                            <label
                              for="last_name"
                              class="block text-sm font-medium text-gray-700 mb-2"
                            >
                              {{ formTranslation.common.lname }}
                              <span class="text-red-500">*</span>
                            </label>
                            <input
                              id="last_name"
                              v-model="formData.last_name"
                              :class="[
                                'w-full px-4 py-2 border rounded-lg',
                                submitted && $v.formData.last_name.$error
                                  ? 'border-red-500 focus:ring-red-500'
                                  : 'border-gray-300 focus:border-primary focus:ring-primary',
                              ]"
                              :placeholder="
                                formTranslation.receptionist.lname_plh
                              "
                              type="text"
                            />
                            <div
                              v-if="
                                submitted && !$v.formData.last_name.required
                              "
                              class="text-red-500 text-sm mt-1"
                            >
                              {{ formTranslation.common.lname_required }}
                            </div>
                          </div>

                          <!-- Email -->
                          <div>
                            <label
                              for="email"
                              class="block text-sm font-medium text-gray-700 mb-2"
                            >
                              {{ formTranslation.common.email }}
                              <span class="text-red-500">*</span>
                            </label>
                            <input
                              id="email"
                              v-model="formData.user_email"
                              :class="[
                                'w-full px-4 py-2 border rounded-lg',
                                submitted && $v.formData.user_email.$error
                                  ? 'border-red-500 focus:ring-red-500'
                                  : 'border-gray-300 focus:border-primary focus:ring-primary',
                              ]"
                              :placeholder="formTranslation.clinic.email_plh"
                              type="email"
                            />
                            <div
                              v-if="
                                submitted && !$v.formData.user_email.required
                              "
                              class="text-red-500 text-sm mt-1"
                            >
                              {{ formTranslation.common.email_required }}
                            </div>
                            <div
                              v-else-if="
                                submitted &&
                                !$v.formData.user_email.emailValidate
                              "
                              class="text-red-500 text-sm mt-1"
                            >
                              {{ formTranslation.common.invalid_email }}
                            </div>
                          </div>

                          <!-- Mobile Number -->
                          <div>
                            <label
                              for="mobile_number"
                              class="block text-sm font-medium text-gray-700 mb-2"
                            >
                              {{ formTranslation.common.contact_no }}
                              <span class="text-red-500">*</span>
                            </label>
                            <VuePhoneNumberInput
                              v-model="formData.mobile_number"
                              :default-country-code="defaultCountryCode_admin"
                              id="mobile_number"
                              @update="contactUpdateHandaler_admin"
                              clearable
                              no-example
                            />
                            <div
                              v-if="
                                submitted && !$v.formData.mobile_number.required
                              "
                              class="text-red-500 text-sm mt-1"
                            >
                              {{ formTranslation.common.contact_num_required }}
                            </div>
                            <div
                              v-else-if="
                                submitted &&
                                (!$v.formData.mobile_number.minLength ||
                                  !$v.formData.mobile_number.maxLength)
                              "
                              class="text-red-500 text-sm mt-1"
                            >
                              {{ formTranslation.common.contact_validation_1 }}
                            </div>
                          </div>

                          <!-- Date of Birth -->
                          <div>
                            <label
                              for="dob"
                              class="block text-sm font-medium text-gray-700 mb-2"
                            >
                              {{ formTranslation.common.dob }}
                            </label>
                            <input
                              type="date"
                              v-model="formData.dob"
                              name="doc_birthdate"
                              id="doc_birthdate"
                              :max="new Date().toISOString().slice(0, 10)"
                              class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:border-primary focus:ring-primary"
                            />
                          </div>

                          <!-- Gender -->
                          <div>
                            <label
                              class="block text-sm font-medium text-gray-700 mb-2"
                            >
                              {{ formTranslation.common.gender }}
                              <span class="text-red-500">*</span>
                            </label>
                            <div class="flex space-x-4">
                              <div class="flex items-center">
                                <input
                                  type="radio"
                                  id="male"
                                  v-model="formData.gender"
                                  value="male"
                                  class="h-4 w-4 border-gray-300 text-primary focus:ring-primary"
                                />
                                <label
                                  for="male"
                                  class="ml-2 block text-sm text-gray-900"
                                >
                                  {{ formTranslation.common.male }}
                                </label>
                              </div>
                              <div class="flex items-center">
                                <input
                                  type="radio"
                                  id="female"
                                  v-model="formData.gender"
                                  value="female"
                                  class="h-4 w-4 border-gray-300 text-primary focus:ring-primary"
                                />
                                <label
                                  for="female"
                                  class="ml-2 block text-sm text-gray-900"
                                >
                                  {{ formTranslation.common.female }}
                                </label>
                              </div>
                              <div
                                v-if="
                                  defaultUserRegistrationFormSettingData ===
                                  'on'
                                "
                                class="flex items-center"
                              >
                                <input
                                  type="radio"
                                  id="other"
                                  v-model="formData.gender"
                                  value="other"
                                  class="h-4 w-4 border-gray-300 text-primary focus:ring-primary"
                                />
                                <label
                                  for="other"
                                  class="ml-2 block text-sm text-gray-900"
                                >
                                  {{ formTranslation.common.other }}
                                </label>
                              </div>
                            </div>
                            <div
                              v-if="submitted && !$v.formData.gender.required"
                              class="text-red-500 text-sm mt-1"
                            >
                              {{ formTranslation.common.gender_required }}
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Profile Image Upload -->
                      <div class="md:col-span-2 flex justify-center">
                        <div class="w-full max-w-xs">
                          <div class="relative">
                            <div class="kivicare-avatar-upload">
                              <div
                                class="kivicare-avatar-edit absolute top-2 right-2 z-10"
                              >
                                <input
                                  id="admin_file"
                                  type="button"
                                  ref="admin_file"
                                  class="opacity-0 absolute inset-0 cursor-pointer"
                                  @click="uploadAdmin()"
                                />
                                <label
                                  for="admin_file"
                                  class="bg-white shadow-md rounded-full p-2 inline-block cursor-pointer"
                                  v-b-tooltip.hover
                                  :title="
                                    formTranslation.clinic.edit_profile_img
                                  "
                                >
                                  <i class="fas fa-pencil-alt text-primary"></i>
                                </label>
                              </div>
                              <div class="kivicare-avatar-preview">
                                <div
                                  id="adminImagePreview"
                                  class="w-full h-32 bg-cover bg-center rounded-lg"
                                  :style="
                                    'background-image: url(' +
                                    adminPreview +
                                    ');'
                                  "
                                ></div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                  </div>
                </div>
              </div>

              <!-- Form Footer -->
              <div
                class="sticky bottom-0 flex justify-end gap-3 px-6 py-4 bg-white border-t border-gray-200 mt-8"
              >
                <button
                  type="button"
                  @click="handleCancel"
                  class="px-6 py-2.5 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium text-sm"
                >
                  {{ formTranslation.common.cancel }}
                </button>
                <button
                  type="submit"
                  class="px-6 py-2.5 bg-black text-white rounded-lg hover:bg-black font-medium text-sm flex items-center gap-2"
                  :disabled="loading"
                >
                  <i
                    class="fa"
                    :class="loading ? 'fa-spinner fa-spin' : 'fa-save'"
                  ></i>
                  {{ formTranslation.common.save }}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import VuePhoneNumberInput from "vue-phone-number-input";
import "vue-phone-number-input/dist/vue-phone-number-input.css";
import { required, minLength, maxLength } from "vuelidate/lib/validators";
import { validateForm, phoneNumber, emailValidate } from "../../config/helper";
import { post, get } from "../../config/request";

export default {
  name: "ClinicForm",

  components: {
    VuePhoneNumberInput,
  },

  props: {
    initialData: {
      type: Object,
      default: () => ({}),
    },
    isEditMode: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      formData: {
        name: "",
        email: "",
        telephone_no: "",
        country_calling_code: "",
        country_code: "",
        address: "",
        city: "",
        country: "",
        postal_code: "",
        status: 1,
        specialties: [],
        currency_prefix: "$",
        currency_postfix: "",
        clinic_profile: "",
        first_name: "",
        last_name: "",
        mobile_number: "",
        country_calling_code_admin: "",
        country_code_admin: "",
        gender: "",
        user_email: "",
        dob: "",
        profile_image: "",
      },
      loading: false,
      submitted: false,
      defaultImage:
        window.request_data.kiviCarePluginURL + "assets/images/kc-demo-img.png",
      adminPreview:
        window.request_data.kiviCarePluginURL + "assets/images/kc-demo-img.png",
      formLoader: false,
      defaultCountryCode: null,
      defaultCountryCode_admin: null,
      defaultUserRegistrationFormSettingData: "on",
    };
  },

  validations: {
    formData: {
      name: { required },
      email: { required, emailValidate },
      telephone_no: {
        required,
        minLength: minLength(4),
        maxLength: maxLength(15),
      },
      status: { required },
      city: { required },
      address: { required },
      country: { required },
      postal_code: { required },
      first_name: { required },
      last_name: { required },
      mobile_number: {
        required,
        minLength: minLength(4),
        maxLength: maxLength(15),
      },
      user_email: {
        required,
        emailValidate,
      },
      specialties: {
        required,
        minLength: minLength(1),
      },
      gender: { required },
    },
  },

  mounted() {
    this.getCountryCodeData();
    this.getUserRegistrationFormData();

    // Populate form with initial data if in edit mode
    if (this.isEditMode && this.initialData) {
      this.formData = {
        ...this.defaultClinicData(),
        ...this.initialData,
      };
    }
  },

  watch: {
    initialData: {
      handler(newData) {
        if (newData) {
          this.formData = {
            ...this.defaultClinicData(),
            ...newData,
          };
        }
      },
      immediate: true,
    },
  },

  methods: {
    contactUpdateHandaler(val) {
      this.formData.country_code = val.countryCode;
      this.formData.country_calling_code = val.countryCallingCode;
    },

    contactUpdateHandaler_admin(val) {
      this.formData.country_code_admin = val.countryCode;
      this.formData.country_calling_code_admin = val.countryCallingCode;
    },

    handleCancel() {
      this.$emit("cancel");
    },

    handleSubmit() {
      this.submitted = true;
      this.$v.$touch();

      if (this.$v.$invalid) {
        // Optional: Scroll to first invalid field
        this.$nextTick(() => {
          const firstInvalidField = document.querySelector(".border-red-500");
          if (firstInvalidField) {
            firstInvalidField.scrollIntoView({
              behavior: "smooth",
              block: "center",
            });
          }
        });
        return;
      }

      this.loading = true;

      // Prepare submission data
      const submissionData = { ...this.formData };

      post("clinic_save", submissionData)
        .then((response) => {
          this.loading = false;

          if (response.data.status === true) {
            displayMessage(response.data.message);
            this.$emit("submit-success", response.data);
            // Close modal or navigate
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          this.loading = false;
          displayErrorMessage(
            this.formTranslation?.common?.internal_server_error ||
              "An error occurred"
          );
        });
    },

    defaultClinicData() {
      return {
        name: "",
        email: "",
        telephone_no: "",
        country_calling_code: "",
        country_code: "",
        address: "",
        city: "",
        country: "",
        postal_code: "",
        status: 1,
        specialties: [],
        currency_prefix: "$",
        currency_postfix: "",
        clinic_profile: "",
        first_name: "",
        last_name: "",
        mobile_number: "",
        country_calling_code_admin: "",
        country_code_admin: "",
        gender: "",
        user_email: "",
        dob: "",
        profile_image: "",
      };
    },

    addNewSpecialization(value) {
      let specialitiesObj = {
        label: value,
        type: "specialization",
        value: value.replace(" ", "_"),
        status: 1,
      };

      post("static_data_save", specialitiesObj)
        .then((response) => {
          if (response.data && response.data.status === true) {
            this.formData.specialties.push({
              id: response.data.insert_id,
              label: value,
            });
          }
        })
        .catch((error) => {
          console.error("Add specialization error", error);
          displayErrorMessage(
            this.formTranslation?.common?.internal_server_error ||
              "An error occurred"
          );
        });
    },

    uploadAdmin() {
      const custom_uploader = kivicareCustomImageUploader(this.formTranslation);

      custom_uploader.on("select", () => {
        const attachment = custom_uploader
          .state()
          .get("selection")
          .first()
          .toJSON();

        this.adminPreview = attachment.url;
        this.formData.profile_image = attachment.id;
      });

      custom_uploader.open();
    },

    uploadProfile() {
      const custom_uploader = kivicareCustomImageUploader(this.formTranslation);

      custom_uploader.on("select", () => {
        const attachment = custom_uploader
          .state()
          .get("selection")
          .first()
          .toJSON();

        this.defaultImage = attachment.url;
        this.formData.clinic_profile = attachment.id;
      });

      custom_uploader.open();
    },

    getCountryCodeData() {
      get("get_country_code_settings_data", {})
        .then((response) => {
          if (response.data && response.data.status === true) {
            this.defaultCountryCode = response.data.data.country_code;
            this.defaultCountryCode_admin = response.data.data.country_code;
          }
        })
        .catch((error) => {
          console.error("Country code fetch error", error);
          displayErrorMessage(
            this.formTranslation?.common?.internal_server_error ||
              "An error occurred"
          );
        });
    },

    getUserRegistrationFormData() {
      get("get_user_registration_form_settings_data", {})
        .then((response) => {
          if (response.data && response.data.status === true) {
            this.defaultUserRegistrationFormSettingData =
              response.data.data.userRegistrationFormSettingData;
          }
        })
        .catch((error) => {
          console.error("User registration form settings error", error);
          displayErrorMessage(
            this.formTranslation?.common?.internal_server_error ||
              "An error occurred"
          );
        });
    },
  },

  computed: {
    specialization() {
      return this.$store.state.staticDataModule.static_data.specialization;
    },
    specializationMultiselectLoader() {
      return this.$store.state.staticDataModule.static_data_loader;
    },
  },
};
</script>
