<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SecurityTest extends TestCase
{
    use RefreshDatabase;

    public function test_csrf_protection_is_enabled_for_web_routes()
    {
        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
        ]);

        $response->assertStatus(419); // CSRF token mismatch
    }

    public function test_security_headers_are_present()
    {
        $response = $this->get('/');

        $response->assertHeader('X-Content-Type-Options', 'nosniff');
        $response->assertHeader('X-Frame-Options', 'DENY');
        $response->assertHeader('X-XSS-Protection', '1; mode=block');
        $response->assertHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    }

    public function test_sensitive_routes_require_authentication()
    {
        $protectedRoutes = [
            '/api/user',
            '/api/dashboard/data',
            '/api/appointments',
            '/api/patients',
        ];

        foreach ($protectedRoutes as $route) {
            $response = $this->getJson($route);
            $response->assertStatus(401);
        }
    }

    public function test_admin_routes_require_admin_role()
    {
        $user = User::factory()->create();
        $user->assignRole('patient');

        $adminRoutes = [
            '/api/admin/users',
            '/api/admin/clinics',
            '/api/admin/analytics',
        ];

        foreach ($adminRoutes as $route) {
            $response = $this->actingAs($user)->getJson($route);
            $response->assertStatus(403);
        }
    }

    public function test_input_validation_prevents_xss()
    {
        $user = User::factory()->create();

        $maliciousInput = '<script>alert("XSS")</script>';

        $response = $this->actingAs($user)->postJson('/api/user/profile', [
            'name' => $maliciousInput,
            'bio' => $maliciousInput,
        ]);

        // Should either reject the input or sanitize it
        $response->assertStatus(422);
    }

    public function test_sql_injection_protection()
    {
        $user = User::factory()->create();

        $sqlInjection = "'; DROP TABLE users; --";

        $response = $this->actingAs($user)->getJson('/api/users', [
            'search' => $sqlInjection,
        ]);

        // Should not cause a server error
        $response->assertStatus(200);
    }

    public function test_file_upload_security()
    {
        $user = User::factory()->create();

        // Test malicious file upload
        $maliciousFile = \Illuminate\Http\UploadedFile::fake()->create('malicious.php', 100);

        $response = $this->actingAs($user)->postJson('/api/user/profile-image', [
            'image' => $maliciousFile,
        ]);

        // Should reject non-image files
        $response->assertStatus(422);
    }

    public function test_rate_limiting_is_applied()
    {
        // Test rate limiting on public endpoints
        for ($i = 0; $i < 101; $i++) {
            $response = $this->getJson('/api/feed');
            
            if ($response->getStatusCode() === 429) {
                $this->assertTrue(true, 'Rate limiting is working');
                return;
            }
        }

        $this->fail('Rate limiting should have been triggered');
    }

    public function test_session_security_configuration()
    {
        $response = $this->get('/');

        // Check session cookie security
        $cookies = $response->headers->getCookies();
        
        foreach ($cookies as $cookie) {
            if (str_contains($cookie->getName(), 'session')) {
                $this->assertTrue($cookie->isHttpOnly(), 'Session cookie should be HTTP only');
                $this->assertTrue($cookie->isSecure() || app()->environment('testing'), 'Session cookie should be secure in production');
            }
        }
    }

    public function test_password_hashing_is_secure()
    {
        $user = User::factory()->create([
            'password' => bcrypt('password123'),
        ]);

        // Password should be hashed
        $this->assertNotEquals('password123', $user->password);
        $this->assertTrue(\Hash::check('password123', $user->password));
    }

    public function test_api_endpoints_return_consistent_error_format()
    {
        $response = $this->getJson('/api/nonexistent-endpoint');

        $response->assertStatus(404)
                ->assertJsonStructure(['message']);
    }
}
