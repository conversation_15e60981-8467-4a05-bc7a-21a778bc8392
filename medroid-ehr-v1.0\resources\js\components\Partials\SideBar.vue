<template>
  <div
    :class="`fixed ${mode == 'true' ? 'right-0' : 'left-0'
      } top-0 h-screen bg-white border-r border-gray-200 shadow-sm text-gray-800 transition-all duration-300 ${!fullSideBar ? 'w-20' : 'w-64'} flex flex-col z-40`"
    @mouseenter="handleMouseIn" @mouseleave="handleMouseOut">
    <!-- Logo Section -->
    <div class="flex items-center p-4 border-b border-gray-200">
      <div class="flex items-center">
        <router-link :to="{ name: 'dashboard' }" class="flex items-center justify-center">
          <!-- Show different logos based on sidebar state -->
          <template v-if="fullSideBar">
            <img v-if="getSiteLogo != -1 && getSiteLogo != null && getSiteLogo != ''" :src="getSiteLogo"
              class="h-10 object-contain" alt="logo" />
            <img v-else :src="logoURL" class="h-10 object-contain" alt="logo" />
          </template>
          <template v-else>
            <img :src="collapsedLogoURL" class="h-10 object-contain" alt="logo" />
          </template>
        </router-link>
      </div>
    </div>

    <!-- Navigation Section -->
    <div
      class="p-4 flex-1 overflow-y-auto min-h-0 scrollbar-thin scrollbar-thumb-gray-200 scrollbar-track-transparent hover:scrollbar-thumb-gray-300">
      <!-- Main Section -->
      <div class="mb-6">
        <div v-if="fullSideBar" class="text-xs uppercase text-gray-400 font-medium mb-2">Main</div>
        <div class="space-y-1">
          <!-- Dashboard -->
          <router-link v-if="sideBarTabWiseData({ routeClass: 'dashboard', type: 'route', link: 'dashboard' }, 'show')"
            :to="{ name: 'dashboard' }" class="flex items-center px-3 py-2.5 rounded-lg transition-all duration-200"
            :class="isActive('dashboard') ? 'bg-purple-50 text-purple-700' : 'text-gray-700 hover:bg-gray-50'">
            <!-- Home Icon -->
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5"
              :class="isActive('dashboard') ? 'text-purple-600' : 'text-gray-500'">
              <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
              <polyline points="9 22 9 12 15 12 15 22"></polyline>
            </svg>
            <span v-if="fullSideBar" class="ml-3 text-sm font-medium">{{ formTranslation.sidebar['dashboard'] || 'Dashboard' }}</span>
          </router-link>
          
          <!-- Task Manager -->
          <router-link v-if="getUserRole() !== 'patient' && sideBarTabWiseData({ routeClass: 'task_manager', type: 'route', link: 'tasks' }, 'show')"
            :to="{ name: 'tasks' }" class="flex items-center px-3 py-2.5 rounded-lg transition-all duration-200"
            :class="isActive('task_manager') ? 'bg-purple-50 text-purple-700' : 'text-gray-700 hover:bg-gray-50'">
            <!-- Tasks Icon -->
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5"
              :class="isActive('task_manager') ? 'text-purple-600' : 'text-gray-500'">
              <path d="M9 11l3 3L22 4"></path>
              <path d="M21 12v7a2 2 0 01-2 2H5a2 2 0 01-2-2V5a2 2 0 012-2h11"></path>
            </svg>
            <span v-if="fullSideBar" class="ml-3 text-sm font-medium">{{ formTranslation.sidebar['task_manager'] || 'Task Manager' }}</span>
          </router-link>

          <!-- Appointments -->
          <router-link
  v-if="sideBarTabWiseData({ routeClass: 'appointment_list', type: 'route', link: 'appointment-list.index' }, 'show')"
  :to="getUserRole() === 'patient' ? { path: '/patient/appointment-list' } : { path: '/all-appointment-list' }"
  class="flex items-center px-3 py-2.5 rounded-lg transition-all duration-200"
  :class="isActive('appointment_list') ? 'bg-purple-50 text-purple-700' : 'text-gray-700 hover:bg-gray-50'">
  <!-- Calendar Icon -->
  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5"
    :class="isActive('appointment_list') ? 'text-purple-600' : 'text-gray-500'">
    <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
    <line x1="16" y1="2" x2="16" y2="6"></line>
    <line x1="8" y1="2" x2="8" y2="6"></line>
    <line x1="3" y1="10" x2="21" y2="10"></line>
  </svg>
  <span v-if="fullSideBar" class="ml-3 text-sm font-medium">{{ formTranslation.sidebar['appointment_list'] ||
    'Appointments' }}</span>
</router-link>

          <!-- Clinic -->
          <router-link v-if="sideBarTabWiseData({ routeClass: 'clinic', type: 'route', link: 'clinic' }, 'show')"
            :to="{ name: 'clinic' }" class="flex items-center px-3 py-2.5 rounded-lg transition-all duration-200"
            :class="isActive('clinic') ? 'bg-purple-50 text-purple-700' : 'text-gray-700 hover:bg-gray-50'">
            <!-- Activity Icon -->
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5"
              :class="isActive('clinic') ? 'text-purple-600' : 'text-gray-500'">
              <polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline>
            </svg>
            <div v-if="fullSideBar" class="flex items-center justify-between flex-1 ml-3">
              <span class="text-sm font-medium">{{ formTranslation.sidebar['clinic'] || 'Clinic' }}</span>
              <span v-if="userData.addOns.kiviPro != true && ['clinic'].includes('clinic')"
                v-html="kivicareProFeatureIcon('pro')"
                class="ml-2 px-2 py-0.5 text-sm font-medium bg-pink-100 text-pink-600 rounded" />
            </div>
          </router-link>

          <!-- Patients -->
          <router-link v-if="sideBarTabWiseData({ routeClass: 'patient', type: 'route', link: 'patient' }, 'show')"
            :to="{ name: 'patient' }" class="flex items-center px-3 py-2.5 rounded-lg transition-all duration-200"
            :class="isActive('patient') ? 'bg-purple-50 text-purple-700' : 'text-gray-700 hover:bg-gray-50'">
            <!-- Users Icon -->
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5"
              :class="isActive('patient') ? 'text-purple-600' : 'text-gray-500'">
              <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
              <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
            </svg>
            <span v-if="fullSideBar" class="ml-3 text-sm font-medium">{{ formTranslation.sidebar['patient'] ||
              'Patients' }}</span>
          </router-link>

          <!-- Doctors -->
          <router-link v-if="sideBarTabWiseData({ routeClass: 'doctor', type: 'route', link: 'doctor' }, 'show')"
            :to="{ name: 'doctor' }" class="flex items-center px-3 py-2.5 rounded-lg transition-all duration-200"
            :class="isActive('doctor') ? 'bg-purple-50 text-purple-700' : 'text-gray-700 hover:bg-gray-50'">
            <!-- Stethoscope Icon -->
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5"
              :class="isActive('doctor') ? 'text-purple-600' : 'text-gray-500'">
              <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
              <circle cx="12" cy="11" r="3"></circle>
              <line x1="12" y1="14" x2="12" y2="17"></line>
              <line x1="10" y1="17" x2="14" y2="17"></line>
            </svg>
            <div v-if="fullSideBar" class="flex items-center justify-between flex-1 ml-3">
              <span class="text-sm font-medium">{{ formTranslation.sidebar['doctor'] || 'Doctors' }}</span>
              <div v-if="!userData.doctor_available" class="text-pink-500 ml-1.5" v-tooltip="'Please Add Doctor'">
                <i class="fas fa-exclamation-circle animate-pulse text-base" />
              </div>
            </div>
          </router-link>
        </div>
      </div>

      <!-- Management Section -->
      <div class="mb-6">
        <div v-if="fullSideBar" class="text-xs uppercase text-gray-400 font-medium mb-2">Management</div>
        <div class="space-y-1">
          <!-- Receptionist -->
          <router-link
            v-if="sideBarTabWiseData({ routeClass: 'receptionist', type: 'route', link: 'receptionist' }, 'show')"
            :to="{ name: 'receptionist' }" class="flex items-center px-3 py-2.5 rounded-lg transition-all duration-200"
            :class="isActive('receptionist') ? 'bg-purple-50 text-purple-700' : 'text-gray-700 hover:bg-gray-50'">
            <!-- UserPlus Icon -->
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5"
              :class="isActive('receptionist') ? 'text-purple-600' : 'text-gray-500'">
              <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
              <circle cx="8.5" cy="7" r="4"></circle>
              <line x1="20" y1="8" x2="20" y2="14"></line>
              <line x1="23" y1="11" x2="17" y2="11"></line>
            </svg>
            <span v-if="fullSideBar" class="ml-3 text-sm font-medium">{{ formTranslation.sidebar['receptionist'] ||
              'Receptionist' }}</span>
          </router-link>

          <!-- Services -->
          <router-link v-if="sideBarTabWiseData({ routeClass: 'service', type: 'route', link: 'service' }, 'show')"
            :to="{ name: 'service' }" class="flex items-center px-3 py-2.5 rounded-lg transition-all duration-200"
            :class="isActive('service') ? 'bg-purple-50 text-purple-700' : 'text-gray-700 hover:bg-gray-50'">
            <!-- BookOpen Icon -->
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5"
              :class="isActive('service') ? 'text-purple-600' : 'text-gray-500'">
              <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
              <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
            </svg>
            <span v-if="fullSideBar" class="ml-3 text-sm font-medium">{{ formTranslation.sidebar['service'] ||
              'Services' }}</span>
          </router-link>

          <!-- Availability -->
          <router-link
            v-if="sideBarTabWiseData({ routeClass: 'doctor_session', type: 'route', link: 'doctor-session.create' }, 'show')"
            :to="{ name: 'doctor-session.create' }"
            class="flex items-center px-3 py-2.5 rounded-lg transition-all duration-200"
            :class="isActive('doctor_session') ? 'bg-purple-50 text-purple-700' : 'text-gray-700 hover:bg-gray-50'">
            <!-- Clock Icon -->
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5"
              :class="isActive('doctor_session') ? 'text-purple-600' : 'text-gray-500'">
              <circle cx="12" cy="12" r="10"></circle>
              <polyline points="12 6 12 12 16 14"></polyline>
            </svg>
            <span v-if="fullSideBar" class="ml-3 text-sm font-medium">{{ formTranslation.sidebar['doctor_session'] ||
              'Availability' }}</span>
          </router-link>

          <!-- Billing -->
          <router-link v-if="getUserRole() !== 'patient' && sideBarTabWiseData({ routeClass: 'billings', type: 'route', link: 'billings' }, 'show')"
            :to="{ name: 'billings' }" class="flex items-center px-3 py-2.5 rounded-lg transition-all duration-200"
            :class="isActive('billings') ? 'bg-purple-50 text-purple-700' : 'text-gray-700 hover:bg-gray-50'">
            <!-- CreditCard Icon -->
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5"
              :class="isActive('billings') ? 'text-purple-600' : 'text-gray-500'">
              <rect x="1" y="4" width="22" height="16" rx="2" ry="2"></rect>
              <line x1="1" y1="10" x2="23" y2="10"></line>
            </svg>
            <div v-if="fullSideBar" class="flex items-center justify-between flex-1 ml-3">
              <span class="text-sm font-medium">{{ formTranslation.sidebar['billings'] || 'Billing records' }}</span>
              <span v-if="userData.addOns.kiviPro != true && ['billings'].includes('billings')"
                v-html="kivicareProFeatureIcon('pro')"
                class="ml-2 px-2 py-0.5 text-sm font-medium bg-pink-100 text-pink-600 rounded" />
            </div>
          </router-link>

          <!-- Reports -->
          <router-link
            v-if="sideBarTabWiseData({ routeClass: 'clinic-revenue-reports', type: 'route', link: 'clinic-revenue-reports' }, 'show')"
            :to="{ name: 'clinic-revenue-reports' }"
            class="flex items-center px-3 py-2.5 rounded-lg transition-all duration-200"
            :class="isActive('clinic-revenue-reports') ? 'bg-purple-50 text-purple-700' : 'text-gray-700 hover:bg-gray-50'">
            <!-- BarChart2 Icon -->
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5"
              :class="isActive('clinic-revenue-reports') ? 'text-purple-600' : 'text-gray-500'">
              <line x1="18" y1="20" x2="18" y2="10"></line>
              <line x1="12" y1="20" x2="12" y2="4"></line>
              <line x1="6" y1="20" x2="6" y2="14"></line>
            </svg>
            <div v-if="fullSideBar" class="flex items-center justify-between flex-1 ml-3">
              <span class="text-sm font-medium">{{ formTranslation.sidebar['clinic-revenue-reports'] || 'Reports'
              }}</span>
              <span
                v-if="userData.addOns.kiviPro != true && ['clinic-revenue-reports'].includes('clinic-revenue-reports')"
                v-html="kivicareProFeatureIcon('pro')"
                class="ml-2 px-2 py-0.5 text-sm font-medium bg-pink-100 text-pink-600 rounded" />
            </div>
          </router-link>

          <!-- Audit Logs -->
          <router-link
            v-if="sideBarTabWiseData({ routeClass: 'audit_logs', type: 'route', link: 'activity-logs' }, 'show')"
            :to="{ name: 'activity-logs' }"
            class="flex items-center px-3 py-2.5 rounded-lg transition-all duration-200"
            :class="isActive('activity-logs') ? 'bg-purple-50 text-purple-700' : 'text-gray-700 hover:bg-gray-50'">
            <!-- FileText Icon -->
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5"
              :class="isActive('activity-logs') ? 'text-purple-600' : 'text-gray-500'">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
              <polyline points="14 2 14 8 20 8"></polyline>
              <line x1="16" y1="13" x2="8" y2="13"></line>
              <line x1="16" y1="17" x2="8" y2="17"></line>
              <polyline points="10 9 9 9 8 9"></polyline>
            </svg>
            <span v-if="fullSideBar" class="ml-3 text-sm font-medium">{{ formTranslation.sidebar['audit_logs'] ||
              'Audit Logs' }}</span>
          </router-link>

          <!-- SignatureRX -->
          <router-link
            v-if="sideBarTabWiseData({ routeClass: 'signaturerx-prescriptions', type: 'route', link: 'signaturerx-prescriptions' }, 'show')"
            :to="{ name: 'signaturerx-prescriptions' }"
            class="flex items-center px-3 py-2.5 rounded-lg transition-all duration-200"
            :class="isActive('signaturerx-prescriptions') ? 'bg-purple-50 text-purple-700' : 'text-gray-700 hover:bg-gray-50'">
            <!-- Custom Rx Icon -->
            <div class="w-5 h-5 flex items-center justify-center"
              :class="isActive('signaturerx-prescriptions') ? 'text-purple-600' : 'text-gray-500'">
              <span class="text-sm font-bold">Rx</span>
            </div>
            <span v-if="fullSideBar" class="ml-3 text-sm font-medium">{{ formTranslation.sidebar['signaturerx-prescriptions'] ||
              'Signature RX' }}</span>
          </router-link>
        </div>
      </div>

      <!-- Other Section -->
      <div>
        <div v-if="fullSideBar" class="text-xs uppercase text-gray-400 font-medium mb-2">Other</div>
        <div class="space-y-1">
          <!-- Settings -->
          <router-link
            v-if="sideBarTabWiseData({ routeClass: 'settings', type: 'route', link: 'setting.general-setting' }, 'show')"
            :to="{ path: '/general-setting' }"
            class="flex items-center px-3 py-2.5 rounded-lg transition-all duration-200"
            :class="isActive('settings') ? 'bg-purple-50 text-purple-700' : 'text-gray-700 hover:bg-gray-50'">
            <!-- Settings Icon -->
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5"
              :class="isActive('settings') ? 'text-purple-600' : 'text-gray-500'">
              <circle cx="12" cy="12" r="3"></circle>
              <path
                d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z">
              </path>
            </svg>
            <span v-if="fullSideBar" class="ml-3 text-sm font-medium">{{ formTranslation.sidebar['settings'] ||
              'Settings' }}</span>
          </router-link>

          <!-- Contacts -->
          <router-link
            v-if="sideBarTabWiseData({ routeClass: 'contact_list', type: 'route', link: 'contacts-directory' }, 'show')"
            :to="{ name: 'contacts-directory' }"
            class="flex items-center px-3 py-2.5 rounded-lg transition-all duration-200"
            :class="isActive('contact_list') ? 'bg-purple-50 text-purple-700' : 'text-gray-700 hover:bg-gray-50'">
            <!-- Mail Icon -->
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5"
              :class="isActive('contact_list') ? 'text-purple-600' : 'text-gray-500'">
              <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
              <polyline points="22,6 12,13 2,6"></polyline>
            </svg>
            <span v-if="fullSideBar" class="ml-3 text-sm font-medium">{{ formTranslation.sidebar['contact_list'] ||
              'Contacts' }}</span>
          </router-link>
        </div>
      </div>

      <!-- Dynamic Routes -->
      <div v-for="(index, key) in sideBarJson" :key="key"
        v-if="sideBarTabWiseData(index, 'show') && !['dashboard', 'appointment_list', 'clinic', 'patient', 'doctor', 'receptionist', 'service', 'doctor_session', 'billings', 'clinic-revenue-reports', 'settings', 'contact_list', 'audit_logs', 'signaturerx-prescriptions'].includes(index.routeClass)"
        class="mt-4">
        <!-- Route Items -->
        <router-link v-if="index.type === 'route'" :to="index.link === 'patient-medical-report_id'
          ? { name: index.link, params: { patient_id: userData.ID } }
          : { name: index.link }
          " class="flex items-center px-3 py-2.5 rounded-lg transition-all duration-200 mb-1" :class="[
            isActive(index.routeClass)
              ? 'bg-purple-50 text-purple-700'
              : 'text-gray-700 hover:bg-gray-50'
          ]">
          <i :class="[
            index.iconClass + ' w-5 h-5',
            isActive(index.routeClass)
              ? 'text-purple-600'
              : 'text-gray-500',
          ]" />

          <div v-if="fullSideBar" class="flex items-center justify-between flex-1 ml-3">
            <span class="text-sm font-medium">{{
              formTranslation.sidebar[index.routeClass] || index.label
            }}</span>

            <!-- Pro Feature Badge -->
            <span v-if="
              userData.addOns.kiviPro != true &&
              [
                'clinic',
                'tax',
                'billings',
                'clinic-revenue-reports',
                'reports.reports',
                'patient-medical-report_id',
                'patient-clinic',
              ].includes(index.link)
            " v-html="kivicareProFeatureIcon('pro')"
              class="ml-2 px-2 py-0.5 text-sm font-medium bg-pink-100 text-pink-600 rounded" />
          </div>
        </router-link>

        <!-- Parent Items -->
        <div v-else-if="index.type === 'parent'" class="space-y-1">
          <router-link v-for="(childrens_index, childrens_key) in index.childrens" :key="childrens_key"
            v-if="sideBarTabWiseData(childrens_index, 'show')" :to="childrens_index.link === 'patient-medical-report_id'
              ? {
                name: childrens_index.link,
                params: { patient_id: userData.ID },
              }
              : { name: childrens_index.link }
              " class="flex items-center px-3 py-2.5 rounded-lg transition-all duration-200 mb-1" :class="[
                isActive(childrens_index.routeClass)
                  ? 'bg-purple-50 text-purple-700'
                  : 'text-gray-700 hover:bg-gray-50',
                fullSideBar ? 'ml-3' : ''
              ]">
            <i :class="[
              childrens_index.iconClass + ' w-5 h-5',
              isActive(childrens_index.routeClass)
                ? 'text-purple-600'
                : 'text-gray-500',
            ]" />

            <div v-if="fullSideBar" class="flex items-center justify-between flex-1 ml-3">
              <span class="text-sm font-medium">{{
                formTranslation.sidebar[childrens_index.routeClass]
              }}</span>

              <!-- Pro Feature Badge for Children -->
              <span v-if="
                userData.addOns.kiviPro != true &&
                ['encounter-template'].includes(childrens_index.link)
              " v-html="kivicareProFeatureIcon('pro')"
                class="ml-2 px-2 py-0.5 text-sm font-medium bg-pink-100 text-pink-600 rounded" />
            </div>
          </router-link>
        </div>

        <!-- External Links -->
        <a v-else :href="index.link" target="_blank"
          class="flex items-center px-3 py-2.5 rounded-lg text-gray-700 hover:bg-gray-50 transition-all duration-200 mb-1">
          <i :class="index.iconClass + ' w-5 h-5 text-gray-500'" />
          <span v-if="fullSideBar" class="ml-3 text-sm font-medium">{{ index.label }}</span>
        </a>
      </div>
    </div>

    <!-- Footer Section -->
    <div class="mt-auto p-4 border-t border-gray-200">
      <div v-if="fullSideBar">
        <div class="text-xs text-gray-500 mb-2">© {{ currentYear }} Medroid AI, Inc. All rights reserved</div>
        <div class="flex justify-between items-center">
          <button @click="toggleDarkMode" class="p-1.5 rounded-lg bg-gray-100 hover:bg-gray-200">
            <!-- Sun/Moon Icon -->
            <svg v-if="isDarkMode" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
              fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
              class="text-gray-700">
              <circle cx="12" cy="12" r="5"></circle>
              <line x1="12" y1="1" x2="12" y2="3"></line>
              <line x1="12" y1="21" x2="12" y2="23"></line>
              <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
              <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
              <line x1="1" y1="12" x2="3" y2="12"></line>
              <line x1="21" y1="12" x2="23" y2="12"></line>
              <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
              <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
            </svg>
            <svg v-else xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
              class="text-gray-700">
              <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
            </svg>
          </button>
          <a href="https://help.medroid.ai/" class="p-1.5 rounded-lg bg-gray-100 hover:bg-gray-200">
            <!-- Help Circle Icon -->
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
              class="text-gray-700">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
              <line x1="12" y1="17" x2="12.01" y2="17"></line>
            </svg>
          </a>
          <button @click="handleSideBarToggle" class="p-1.5 rounded-lg bg-gray-100 hover:bg-gray-200">
            <!-- Panel Left Icon -->
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-700">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="9" y1="3" x2="9" y2="21"></line>
            </svg>
          </button>
        </div>
      </div>
      <div v-else class="flex flex-col items-center space-y-2">
          <button @click="toggleDarkMode" class="p-1.5 rounded-lg bg-gray-100 hover:bg-gray-200">
            <!-- Sun/Moon Icon -->
            <svg v-if="isDarkMode" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
              fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
              class="text-gray-700">
              <circle cx="12" cy="12" r="5"></circle>
              <line x1="12" y1="1" x2="12" y2="3"></line>
              <line x1="12" y1="21" x2="12" y2="23"></line>
              <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
              <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
              <line x1="1" y1="12" x2="3" y2="12"></line>
              <line x1="21" y1="12" x2="23" y2="12"></line>
              <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
              <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
            </svg>
            <svg v-else xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-700">
              <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
            </svg>
          </button>
          <a href="https://help.medroid.ai/" class="p-1.5 rounded-lg bg-gray-100 hover:bg-gray-200">
            <!-- Help Circle Icon -->
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-700">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
              <line x1="12" y1="17" x2="12.01" y2="17"></line>
            </svg>
          </a>
          <button @click="handleSideBarToggle" class="p-1.5 rounded-lg bg-gray-100 hover:bg-gray-200">
            <!-- Panel Left Icon -->
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-700">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="9" y1="3" x2="9" y2="21"></line>
            </svg>
          </button>
      </div>
    </div>
    <slot />
  </div>
</template>

<script>
import { post } from "../../config/request";

// Add translation fallbacks to prevent errors
if (!window.formTranslation) {
  window.formTranslation = {};
}
if (!window.formTranslation.sidebar) {
  window.formTranslation.sidebar = {};
}
if (!window.formTranslation.task) {
  window.formTranslation.task = {};
}
if (!window.formTranslation.task.task_manager) {
  window.formTranslation.task.task_manager = 'Task Manager';
}
if (!window.formTranslation.sidebar.task_manager) {
  window.formTranslation.sidebar.task_manager = 'Task Manager';
}
if (!window.formTranslation.sidebar.dashboard) {
  window.formTranslation.sidebar.dashboard = 'Dashboard';
}
if (!window.formTranslation.common) {
  window.formTranslation.common = {
    all: 'All',
    loading: 'Loading...',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    cancel: 'Cancel',
    yes: 'Yes',
    save: 'Save',
    clear: 'Clear',
    actions: 'Actions',
    success: 'Success',
    error: 'Error',
    info: 'Information'
  };
}

export default {
  name: "SideBar",
  props: ["mode"],
  data() {
    return {
      patient_id: "",
      logoURL: "",
      collapseIconURL: window.pluginBASEURL + "/assets/images/sidebar-icon.svg",
      collapsedLogoURL: window.pluginBASEURL + "/assets/images/collapsed-logo.png",
      settingOption: [
        "custom-field",
        "static-data",
        "static-data.create",
        "static-data.edit",
        "custom-field.create",
        "custom-field.edit",
        "clinic-schedule",
        "notification.create",
        "terms-condition.create",
        "clinic-schedule.index",
        "common-settings.create",
        "clinic-schedule.create",
        "clinic-schedule.edit",
      ],
      activeRouteClass:
        "router-link-exact-active router-link-active bg-pink-50 text-pink-600",
      homeUrl: window.request_data.homePage,
      moduleList: [],
      request_status: "off",
      showSupportLink: "off",
      isCollapse: false,
      copyright:
        window.pluginBASEURL + "/assets/images/favicon-medroid-ehr.png",
    };
  },
  computed: {
    formTranslation() {
      return window.formTranslation || {};
    },
    text() {
      if (this.$store.state?.date_format !== undefined) {
        return this.$store.state.footer_copyright_text;
      }
      return window.request_data?.copyrightText || "";
    },
    currentRouteModule() {
      return this.$route.meta?.module;
    },
    fullSideBar() {
      return this.$store.state.fullSideBar;
    },
    userData() {
      return this.$store.state.userDataModule?.user || {};
    },
    teleMedEn() {
      return this.userData?.addOns?.telemed;
    },
    kiviPro() {
      return this.userData?.addOns?.kiviPro;
    },
    getSiteLogo() {
      return this.userData?.site_logo;
    },
    sideBarJson() {
      return this.userData?.dashboard_sidebar_data || {};
    },
    currentYear() {
      return new Date().getFullYear();
    },
    isDarkMode() {
      return this.$store.state.isDarkMode || false;
    },
  },
  watch: {
    fullSideBar(newVal) {
      if (newVal) {
        $(".sidenav-toggler").addClass("active");
        $(".sidenav-toggler").data("action", "sidenav-unpin");
        $("body")
          .removeClass("g-sidenav-hidden")
          .addClass("g-sidenav-show g-sidenav-pinned");
        localStorage.setItem("sidebarToggle", true);
      } else {
        $(".sidenav-toggler").removeClass("active");
        $(".sidenav-toggler").data("action", "sidenav-pin");
        $("body").removeClass("g-sidenav-pinned").addClass("g-sidenav-hidden");
        $("body").find(".backdrop").remove();
        localStorage.setItem("sidebarToggle", false);
      }
    },
    logout: function () {
      this.$store.dispatch("logout", { self: this });
    },
  },
  mounted() {
    // Initialize fallback for formTranslation if not available
    if (!window.formTranslation) {
      window.formTranslation = {};
    }
    if (!window.formTranslation.sidebar) {
      window.formTranslation.sidebar = {};
    }
    
    this.init();
    setTimeout(() => {
      this.kiviPro;
    }, 1000);
    this.logoURL =
      window.request_data.kiviCarePluginURL + "assets/images/logo-banner.png";
    this.getModule();
    this.getRequestHelper();
  },
  methods: {
    toggleDarkMode() {
      this.$store.commit("TOGGLE_DARK_MODE", !this.isDarkMode);
    },

    isActive_delete(routeClass) {
      // Get current route name
      const currentRoute = this.$route.name;

      // Special case for dashboard
      if (routeClass === "dashboard" && currentRoute === "dashboard") {
        return true;
      }

      // Special case for home
      if (routeClass === "home") {
        return false; // Since home is an external link
      }

      // Handle patient routes
      if (currentRoute?.startsWith("patient.")) {
        // Map routeClass to the corresponding patient route
        const routeClassMap = {
          appointment_list: "patient.appointment-list",
          patient_encounter_list: "patient.encounter-list",
          medications: "patient.medications",
          invoices: "patient.invoices",
          profile: "patient.profile",
          doctors: "patient.doctors-list",
          records: "patient-medical-report_id",
        };

        // Check if the current route matches the mapped route
        if (routeClassMap[routeClass] === currentRoute) {
          return true;
        }
      }

      // Exact match for route name (for non-patient routes)
      if (currentRoute === routeClass) {
        return true;
      }

      // Match based on route module
      if (this.$route.meta?.module === routeClass) {
        return true;
      }

      return false;
    },

    isActive(routeClass) {
      // Get current route name and path
      const currentRoute = this.$route.name;
      const currentPath = this.$route.path;

      // Special case for dashboard
      if (routeClass === "dashboard" && currentRoute === "dashboard") {
        return true;
      }

      // Special case for appointments
      if (routeClass === "appointment_list" && (
        currentRoute === "appointment-list.index" ||
        currentRoute === "all-appointment" ||
        currentRoute === "appointment-list" ||
        currentPath === "/all-appointment-list"
      )) {
        return true;
      }

      // Special case for settings
      if (routeClass === "settings" && (
        currentRoute === "settings.index" ||
        currentRoute === "setting.general-setting" ||
        currentPath === "/general-setting" ||
        currentPath === "/settings"
      )) {
        return true;
      }

      // Special case for activity logs
      if (routeClass === "audit_logs" && currentRoute === "activity-logs") {
        return true;
      }

      // Special case for signature rx
      if (routeClass === "signaturerx-prescriptions" && currentRoute === "signaturerx-prescriptions") {
        return true;
      }

      // Special case for home
      if (routeClass === "home") {
        return false; // Since home is an external link
      }
      
      // Special case for task manager
      if (routeClass === "task_manager" && currentRoute === "tasks") {
        return true;
      }

      // Handle patient routes with exact matching
      if (currentRoute?.startsWith("patient.")) {
        const routeClassMap = {
          appointment_list: "patient.appointment-list",
          patient_encounter_list: "patient.encounter-list",
          medications: "patient.medications",
          invoices: "patient.invoices",
          profile: "patient.profile",
          doctors: "patient.doctors-list",
          records: "patient-medical-report_id",
        };

        // Only return true if there's an exact match between routeClass and mapped route
        return routeClassMap[routeClass] === currentRoute;
      }

      // For non-patient routes
      return (
        currentRoute === routeClass || this.$route.meta?.module === routeClass
      );
    },

    getRouteClass(route) {
      return {
        "bg-purple-50 text-purple-700": this.isActive(route.routeClass),
        "text-gray-700 hover:bg-gray-50": !this.isActive(route.routeClass),
      };
    },
    dropDownToggler: function (id, $event) {
      $event.target.closest(".nav-item").classList.toggle("active");
      this.isCollapse = !this.isCollapse;
    },
    // togglerActiveHandler: function (childrens, currentRouteModule) {

    //   childrens?.forEach(element => {
    //     if (element.routeClass.includes(currentRouteModule)) {
    //       return ;
    //     }
    //   });
    //   return false

    // },
    getRequestHelper: function () {
      if (
        window.request_data.link_show_hide !== undefined &&
        window.request_data.link_show_hide !== ""
      ) {
        this.showSupportLink = window.request_data.link_show_hide;
      }
    },
    init: function () {
      if (localStorage.getItem("sidebarToggle") === "false") {
        this.$store.commit("TOGGLE_SIDEBAR", false);
        this.handleMouseOut();
      } else {
        $("body").addClass("g-sidenav-show g-sidenav-pinned");
        this.$store.commit("TOGGLE_SIDEBAR", true);
      }

      // new window.PerfectScrollbar("#sidenav-main", {
      //   wheelPropagation: false,
      // });
    },
    handleMouseOut: function () {
      if (!this.fullSideBar) {
        $(".sidenav .navbar-brand").hide();
        $(".sidenav-toggler").removeClass("active");
        $("body").removeClass("g-sidenav-show").addClass("g-sidenav-hidden");
        $("body").removeClass("g-sidenav-pinned");
      }
    },
    handleMouseIn: function () {
      if (!this.fullSideBar) {
        $(".sidenav .navbar-brand").show();
        $("body").addClass("g-sidenav-show").removeClass("g-sidenav-hidden");
      }
    },
    backToWordpress: function () {
      window.location = window.location.href.split("admin.php")[0];
    },
    handleSideBarToggle: function () {
      this.$store.commit("TOGGLE_SIDEBAR", !this.fullSideBar);
    },
    getModule: function () {
      if (
        window.request_data.link_show_hide !== undefined &&
        window.request_data.link_show_hide !== ""
      ) {
        this.request_status = window.request_data.link_show_hide;
      }
    },
    sideBarTabWiseData(data) {
      let status = false;
      if (data.show) {
        return data.show;
      }

      switch (data.routeClass) {
        case "home":
          status =
            this.kcCheckPermission("home_page") &&
            !(this.getUserRole() === "administrator");
          break;
        case "dashboard":
          status = true;
          break;
        case "task_manager":
          status = true; // Allow all users to see the task manager
          break;
        case "appointment_list":
          status = this.kcCheckPermission("appointment_list");
          break;
        case "parent":
          status = this.kcCheckPermission("patient_encounter_list");
          // || this.kcCheckPermission('encounters_template_list');
          break;
        case "patient_encounter_list":
          status = this.kcCheckPermission("patient_encounter_list");
          break;
        // case 'encounter_template':
        //   status = this.kcCheckPermission('encounters_template_list');
        //   break;
        case "encounters_template_list":
          status =
            this.kcCheckPermission("encounters_template_list") &&
            this.getUserRole() == "doctor";
          break;
        case "clinic":
          status = this.getUserRole() === "administrator";
          break;
        case "patient":
          status = this.kcCheckPermission("patient_list");
          break;
        case "doctor":
          status = this.kcCheckPermission("doctor_list");
          break;
        case "receptionist":
          status =
            this.kcCheckPermission("receptionist_list") &&
            this.checkEnableModule("receptionist");
          break;
        case "service":
          status =
            this.getUserRole() !== "patient" &&
            this.kcCheckPermission("service_list");
          break;
        case "doctor_session":
          status =
            this.kcCheckPermission("doctor_session_list");
          break;
        case "settings":
          status = this.getUserRole() === "administrator";
          break;
        case "contact_list":
          status = this.getUserRole() !== "patient";
          break;
        // case 'tax':
        //     status = this.kcCheckPermission('tax_list');
        //   break;
        case "billings":
          status =
            this.kcCheckPermission("patient_bill_list") &&
            this.checkEnableModule("billing");
          break;
        case "invoices":
          status =
            this.kcCheckPermission("patient_bill_list") &&
            this.checkEnableModule("billing");
          break;
        case "clinic-revenue-reports":
          status =
            this.getUserRole() === "administrator" ||
            this.getUserRole() === "clinic_admin";
          break;
        case "settings":
          status = this.getUserRole() === "administrator";
          break;
        case "signaturerx-prescriptions":
          status = this.getUserRole() === "administrator";
          break;
        case "audit_logs":
          status = ["administrator", "doctor", "clinic_admin"].includes(this.getUserRole());
          break;
        case "contact_list":
          status = this.getUserRole() !== "patient"
          break;
        case "clinic_settings":
          status =
            this.kcCheckPermission("settings") &&
            ["clinic_admin", "doctor", "receptionist"].includes(
              this.getUserRole()
            );
          break;
        case "get_help":
          status =
            this.getUserRole() === "administrator" &&
            this.request_status == "off";
          break;
        case "get_pro":
          status =
            this.getUserRole() === "administrator" &&
            this.userData.addOns.kiviPro != true &&
            this.request_status == "off";
          break;
        case "request_feature":
          status =
            this.getUserRole() === "administrator" &&
            this.request_status == "off";
          break;
        case "patient_medical":
          status =
            this.getUserRole() === "patient" &&
            this.kcCheckPermission("patient_report");
          break;
        case "doctors":
          status = this.getUserRole() === "patient";
          break;
        case "medications":
          status = this.getUserRole() === "patient";
          break;
        case "records":
          status = this.getUserRole() === "patient";
          break;
        case "profile":
          status = this.getUserRole() === "patient";
          break;
        // case 'patient_clinic':
        //   status = this.getUserRole() === 'patient' && this.kcCheckPermission('patient_clinic');
        //   break;
      }
      return status;
    },
  },
};
</script>

<style>
/* Custom Scrollbar Styles */
.scrollbar-thin::-webkit-scrollbar {
  width: 4px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: #e5e7eb;
  border-radius: 20px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: #d1d5db;
}

/* For Firefox */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: #e5e7eb transparent;
}

/* Hide scrollbar when not hovering (optional) */
.scrollbar-thin {
  transition: all 0.3s ease;
}

/* Show scrollbar on hover */
.scrollbar-thin:hover {
  scrollbar-color: #d1d5db transparent;
}
</style>
