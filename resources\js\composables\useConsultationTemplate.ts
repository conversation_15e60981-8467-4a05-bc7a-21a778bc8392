import { ref, computed } from 'vue'
import axios from 'axios'

export interface VitalSignField {
  key: string
  type: string
  unit?: string
  label: string
  placeholder?: string
  required: boolean
  min?: number
  max?: number
  calculated?: boolean
  formula?: string
}

export interface TabField {
  key: string
  type: string
  label: string
  placeholder?: string
  required: boolean
  multiple: boolean
  order?: number
  templates?: string[]
  icon?: string
}

export interface ConsultationTemplate {
  vital_signs: Record<string, VitalSignField>
  main_tabs: Record<string, TabField>
  additional_tabs: Record<string, TabField>
}

export interface ConsultationData {
  vital_signs?: Record<string, any>
  main_tabs?: Record<string, Array<{ id: number; content: string; created_at: string }>>
  additional_tabs?: Record<string, Array<{ id: number; content: string; created_at: string }>>
}

export function useConsultationTemplate() {
  const template = ref<ConsultationTemplate | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Load template from API
  const loadTemplate = async () => {
    try {
      loading.value = true
      error.value = null
      
      const response = await axios.get('/api/consultations/template')
      template.value = response.data.data
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to load consultation template'
      console.error('Error loading consultation template:', err)
    } finally {
      loading.value = false
    }
  }

  // Get vital signs fields as array
  const vitalSignsFields = computed(() => {
    if (!template.value?.vital_signs) return []
    return Object.values(template.value.vital_signs)
  })

  // Get main tabs fields as array (sorted by order)
  const mainTabsFields = computed(() => {
    if (!template.value?.main_tabs) return []
    return Object.values(template.value.main_tabs).sort((a, b) => (a.order || 0) - (b.order || 0))
  })

  // Get additional tabs fields as array
  const additionalTabsFields = computed(() => {
    if (!template.value?.additional_tabs) return []
    return Object.values(template.value.additional_tabs)
  })

  // Initialize empty consultation data based on template
  const initializeConsultationData = (): ConsultationData => {
    const data: ConsultationData = {
      vital_signs: {},
      main_tabs: {},
      additional_tabs: {}
    }

    // Initialize vital signs with empty values
    if (template.value?.vital_signs) {
      Object.keys(template.value.vital_signs).forEach(key => {
        data.vital_signs![key] = ''
      })
    }

    // Initialize main tabs with empty arrays
    if (template.value?.main_tabs) {
      Object.keys(template.value.main_tabs).forEach(key => {
        data.main_tabs![key] = []
      })
    }

    // Additional tabs start empty (user adds them as needed)
    data.additional_tabs = {}

    return data
  }

  // Add new entry to a tab
  const addTabEntry = (consultationData: ConsultationData, tabType: 'main_tabs' | 'additional_tabs', tabKey: string, content: string) => {
    if (!consultationData[tabType]) {
      consultationData[tabType] = {}
    }
    
    if (!consultationData[tabType]![tabKey]) {
      consultationData[tabType]![tabKey] = []
    }

    const newEntry = {
      id: Date.now(), // Simple ID generation
      content,
      created_at: new Date().toISOString()
    }

    consultationData[tabType]![tabKey].push(newEntry)
  }

  // Remove entry from a tab
  const removeTabEntry = (consultationData: ConsultationData, tabType: 'main_tabs' | 'additional_tabs', tabKey: string, entryId: number) => {
    if (consultationData[tabType]?.[tabKey]) {
      consultationData[tabType]![tabKey] = consultationData[tabType]![tabKey].filter(entry => entry.id !== entryId)
    }
  }

  // Update entry in a tab
  const updateTabEntry = (consultationData: ConsultationData, tabType: 'main_tabs' | 'additional_tabs', tabKey: string, entryId: number, content: string) => {
    if (consultationData[tabType]?.[tabKey]) {
      const entry = consultationData[tabType]![tabKey].find(entry => entry.id === entryId)
      if (entry) {
        entry.content = content
      }
    }
  }

  // Calculate BMI if height and weight are provided
  const calculateBMI = (consultationData: ConsultationData) => {
    const weight = parseFloat(consultationData.vital_signs?.weight || '0')
    const height = parseFloat(consultationData.vital_signs?.height || '0')
    
    if (weight > 0 && height > 0) {
      const heightInMeters = height / 100
      const bmi = weight / (heightInMeters * heightInMeters)
      consultationData.vital_signs!.bmi = bmi.toFixed(1)
    }
  }

  // Validate consultation data
  const validateConsultationData = (consultationData: ConsultationData): string[] => {
    const errors: string[] = []

    // Check required main tabs
    if (template.value?.main_tabs) {
      Object.entries(template.value.main_tabs).forEach(([key, field]) => {
        if (field.required && (!consultationData.main_tabs?.[key] || consultationData.main_tabs[key].length === 0)) {
          errors.push(`${field.label} is required`)
        }
      })
    }

    // Check vital signs ranges
    if (template.value?.vital_signs && consultationData.vital_signs) {
      Object.entries(template.value.vital_signs).forEach(([key, field]) => {
        const value = parseFloat(consultationData.vital_signs![key] || '0')
        if (value > 0) {
          if (field.min !== undefined && value < field.min) {
            errors.push(`${field.label} must be at least ${field.min}${field.unit || ''}`)
          }
          if (field.max !== undefined && value > field.max) {
            errors.push(`${field.label} must not exceed ${field.max}${field.unit || ''}`)
          }
        }
      })
    }

    return errors
  }

  return {
    template,
    loading,
    error,
    loadTemplate,
    vitalSignsFields,
    mainTabsFields,
    additionalTabsFields,
    initializeConsultationData,
    addTabEntry,
    removeTabEntry,
    updateTabEntry,
    calculateBMI,
    validateConsultationData
  }
}
