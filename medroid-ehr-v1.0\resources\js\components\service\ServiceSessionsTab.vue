<template>
  <div class="w-full">
    <div class="bg-white shadow-md rounded-lg p-4">
      <div class="mb-4 flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-800">{{ formTranslation.service.specific_availability || 'Service Specific Availability' }}</h3>
        <button type="button" @click.prevent.stop="openSessionModal"
          class="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors">
          {{ formTranslation.service.add_session || 'Add Session' }}
        </button>
      </div>

      <!-- Sessions List -->
      <div v-if="loading" class="flex justify-center py-8">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
      </div>

      <div v-else-if="sessions.length === 0" class="bg-gray-50 rounded-lg p-6 text-center">
        <div class="flex flex-col items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24"
            stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <p class="text-gray-500">{{ formTranslation.service.no_sessions_found || 'No service-specific availability sessions found. Click Add Session to create a new one.' }}</p>
          <p class="text-sm text-gray-400 mt-2">Service ID: {{ selectedServiceData.service_id }}</p>
        </div>
      </div>

      <div v-else>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- Use a truly unique :key here to avoid collisions -->
          <div
            v-for="(session, index) in sessions"
            :key="session.id + '_' + session.day + '_' + index"
            class="bg-gray-50 rounded-lg p-4 border border-gray-200"
          >
            <!-- Session Header with Day -->
            <div class="flex justify-between mb-3">
              <div class="flex items-center">
                <span class="font-bold text-gray-800 text-lg">{{ getDayName(session.day) }}</span>
                <span class="ml-2 px-2 py-0.5 bg-blue-100 text-blue-800 text-xs rounded-full">
                  {{ session.slots?.length || 0 }} {{ session.slots?.length === 1 ? 'slot' : 'slots' }}
                </span>
              </div>
              <div class="flex">
                <button @click="editSession(session)" class="text-blue-600 mr-2 hover:bg-blue-50 p-1 rounded">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                </button>
                <button @click="deleteSession(session.id)" class="text-red-600 hover:bg-red-50 p-1 rounded">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
            </div>

            <!-- Session Settings Summary -->
            <div class="mb-3 p-2 bg-gray-100 rounded flex flex-wrap gap-3">
              <div class="px-2 py-1 bg-white rounded border border-gray-200 text-sm">
                <span class="text-gray-500">{{ formTranslation.service.buffer_time || 'Buffer Time' }}:</span>
                <span class="ml-1 font-medium text-gray-900">{{ session.buffertime }} {{ formTranslation.service.min || 'min' }}</span>
              </div>
            </div>

            <!-- Time Slots -->
            <div class="grid grid-cols-1 gap-2">
              <div
                v-for="(slot, slotIndex) in session.slots"
                :key="slotIndex"
                class="bg-white p-3 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow"
              >
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <span class="text-xl font-medium text-gray-800">{{ formatTime(slot.start) }}</span>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mx-2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                    <span class="text-xl font-medium text-gray-800">{{ formatTime(slot.end) }}</span>
                  </div>
                  <div class="text-sm">
                    <span class="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded">
                      {{ calculateDuration(slot.start, slot.end) }}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Session ID for debugging - can be removed in production -->
            <div class="mt-3 pt-2 border-t border-gray-200 text-xs text-gray-400">
              ID: {{ session.id }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Session Modal -->
    <div v-if="showSessionModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="relative bg-white rounded-lg shadow-xl w-full max-w-4xl p-6 mx-4 h-full overflow-auto">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold text-gray-800">
            {{ editMode ? (formTranslation.service.edit_session || 'Edit Session') : (formTranslation.service.add_new_session || 'Add New Session') }}
          </h3>
          <button @click="closeSessionModal" class="text-gray-400 hover:text-gray-600">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
              stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <!-- Days Selection -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">{{ formTranslation.service.select_days || 'Select Days' }}</label>
            <div class="grid grid-cols-4 gap-2">
              <div v-for="day in weekDays" :key="day.value" class="relative">
                <input type="checkbox" :id="'day-' + day.value" v-model="selectedDays" :value="day" class="hidden peer">
                <label :for="'day-' + day.value"
                  class="flex items-center justify-center px-3 py-2 text-sm border rounded-md cursor-pointer peer-checked:bg-purple-600 peer-checked:text-white peer-checked:border-purple-600 hover:bg-gray-50">
                  {{ day.label }}
                </label>
              </div>
            </div>
          </div>

          <!-- Buffer Time Only -->
          <div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">{{ formTranslation.service.buffer_time_minutes || 'Buffer Time (minutes)' }}</label>
              <input type="number" v-model="bufferTime" min="0" max="60" step="5"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500">
            </div>
            
            <!-- Hidden field for time slot -->
            <input type="hidden" v-model="timeSlot">
          </div>
        </div>

        <!-- Time Slots Configuration -->
        <div v-for="(day, dayIndex) in selectedDays" :key="dayIndex" class="mb-6">
          <div class="flex items-center justify-between mb-2">
            <h4 class="font-medium text-gray-800">{{ day.label }}</h4>
            <button @click="addTimeSlot(dayIndex)"
              class="px-3 py-1 bg-gray-100 text-gray-600 rounded hover:bg-gray-200 text-sm flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              {{ formTranslation.service.add_time_slot || 'Add Time Slot' }}
            </button>
          </div>

          <div v-for="(slot, slotIndex) in getTimeSlots(dayIndex)" :key="slotIndex"
            class="flex items-center mb-2 bg-gray-50 p-3 rounded">
            <div class="grid grid-cols-2 gap-4 flex-1">
              <div>
                <label class="block text-xs font-medium text-gray-500 mb-1">{{ formTranslation.service.start_time || 'Start Time' }}</label>
                <input type="time" v-model="slot.start"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500">
              </div>
              <div>
                <label class="block text-xs font-medium text-gray-500 mb-1">{{ formTranslation.service.end_time || 'End Time' }}</label>
                <input type="time" v-model="slot.end"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500">
              </div>
            </div>
            <button @click="removeTimeSlot(dayIndex, slotIndex)" class="ml-3 text-red-500 hover:text-red-700"
              :disabled="getTimeSlots(dayIndex).length <= 1">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </button>
          </div>
        </div>

        <div class="flex justify-end mt-6">
          <button type="button" @click.prevent.stop="closeSessionModal"
            class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 mr-2 hover:bg-gray-50">
            {{ formTranslation.common.cancel || 'Cancel' }}
          </button>
          <button type="button" @click.prevent.stop="saveSession" :disabled="saveDisabled || savingSession"
            class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:bg-purple-300 flex items-center">
            <span v-if="savingSession" class="mr-2">
              <svg class="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none"
                viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                </path>
              </svg>
            </span>
            {{ formTranslation.common.save || 'Save' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { post, get } from '../../config/request';

export default {
  name: 'ServiceSessionsTab',
  props: {
    selectedServiceData: {
      type: Object,
      default: () => ({}),
    }
  },
  data() {
    return {
      loading: true,
      sessions: [],
      showSessionModal: false,
      editMode: false,
      currentSessionId: null,
      currentDayBeingEdited: null,
      timeSlot: 15,
      bufferTime: 0,
      weekDays: [
        { label: formTranslation.calendar ? formTranslation.calendar.mon || 'Mon' : 'Mon', value: 'mon', name: 'mon' },
        { label: formTranslation.calendar ? formTranslation.calendar.tue || 'Tue' : 'Tue', value: 'tue', name: 'tue' },
        { label: formTranslation.calendar ? formTranslation.calendar.wed || 'Wed' : 'Wed', value: 'wed', name: 'wed' },
        { label: formTranslation.calendar ? formTranslation.calendar.thu || 'Thu' : 'Thu', value: 'thu', name: 'thu' },
        { label: formTranslation.calendar ? formTranslation.calendar.fri || 'Fri' : 'Fri', value: 'fri', name: 'fri' },
        { label: formTranslation.calendar ? formTranslation.calendar.sat || 'Sat' : 'Sat', value: 'sat', name: 'sat' },
        { label: formTranslation.calendar ? formTranslation.calendar.sun || 'Sun' : 'Sun', value: 'sun', name: 'sun' }
      ],
      selectedDays: [],
      timeSlots: {},
      savingSession: false
    }
  },
  computed: {
    saveDisabled() {
      return this.selectedDays.length === 0 ||
        Object.keys(this.timeSlots).length === 0 ||
        this.hasInvalidTimeSlots();
    }
  },
  mounted() {
    this.loadServiceSessions();
  },
  methods: {
    loadServiceSessions() {
      this.loading = true;

      console.log('Selected service data:', this.selectedServiceData);

      if (!this.selectedServiceData.service_id || !this.selectedServiceData.clinic_id || !this.selectedServiceData.doctor_id) {
        console.error('Missing required data for service sessions:', {
          service_id: this.selectedServiceData.service_id,
          clinic_id: this.selectedServiceData.clinic_id,
          doctor_id: this.selectedServiceData.doctor_id
        });
        this.sessions = [];
        this.loading = false;
        return;
      }

      const serviceId = parseInt(this.selectedServiceData.service_id);
      const clinicId = parseInt(this.selectedServiceData.clinic_id);
      const doctorId = parseInt(this.selectedServiceData.doctor_id);

      if (!serviceId) {
        console.error('Invalid service_id (must be a number):', this.selectedServiceData.service_id);
        this.sessions = [];
        this.loading = false;
        return;
      }

      console.log('Requesting service sessions with:', {
        service_id: serviceId,
        clinic_id: clinicId,
        doctor_id: doctorId,
        types: {
          service_id: typeof serviceId,
          clinic_id: typeof clinicId,
          doctor_id: typeof doctorId
        }
      });

      const timestamp = new Date().getTime();

      get('clinic_session_list_service', {
        service_id: serviceId,
        clinic_id: clinicId,
        doctor_id: doctorId,
        is_service_session: 1,
        _ts: timestamp
      })
        .then(response => {
          console.log('Service sessions response:', response.data);
          if (response.data.status) {
            let filteredSessions = [];
            
            if (response.data.data.clinic_sessions) {
              const targetServiceId = parseInt(this.selectedServiceData.service_id);
              filteredSessions = response.data.data.clinic_sessions.filter(session => {
                const sessionServiceId = parseInt(session.service_id);
                const matches = sessionServiceId === targetServiceId;
                
                console.log(
                  `Session ID: ${session.id}, ` + 
                  `Service ID from session: ${sessionServiceId} (${typeof sessionServiceId}), ` + 
                  `Target service ID: ${targetServiceId} (${typeof targetServiceId}), ` + 
                  `Match: ${matches}`
                );
                
                return matches;
              });
              
              console.log('Filtered sessions with matching service_id:', filteredSessions);
            }
            
            this.sessions = [];
            
            filteredSessions.forEach(session => {
              if (session.days && Array.isArray(session.days)) {
                session.days.forEach(day => {
                  if (day.slots && day.slots.length > 0) {
                    this.sessions.push({
                      id: session.id,
                      clinic_id: session.clinic_id.id,
                      doctor_id: session.doctor_id,
                      service_id: session.service_id,
                      day: day.name,
                      doctor_name: session.doctor_name,
                      clinic_name: session.clinic_name,
                      buffertime: session.buffertime,
                      time_slot: session.time_slot,
                      slots: day.slots.map(slot => ({
                        ...slot,
                        id: session.id
                      }))
                    });
                  }
                });
              }
            });
            
            console.log('Processed sessions for UI:', this.sessions);
          } else {
            this.sessions = [];
            console.log('No sessions found');
          }
        })
        .catch(error => {
          console.error('Failed to load service sessions:', error);
          if (typeof displayErrorMessage === 'function') {
            displayErrorMessage(formTranslation.service ? formTranslation.service.failed_to_load_sessions || 'Failed to load service sessions' : 'Failed to load service sessions');
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },

    openSessionModal() {
      this.editMode = false;
      this.currentSessionId = null;
      this.currentDayBeingEdited = null;
      this.selectedDays = [];
      this.timeSlots = {};
      this.timeSlot = 15;
      this.bufferTime = 15;
      this.showSessionModal = true;
    },

    closeSessionModal() {
      this.showSessionModal = false;
    },

    editSession(session) {
      console.log('Editing session:', session);
      this.editMode = true;
      this.currentSessionId = session.id;
      
      this.timeSlot = session.time_slot !== undefined ? parseInt(session.time_slot) : '';
      this.bufferTime = session.buffertime !== undefined ? parseInt(session.buffertime) : '';

      console.log(`Using time_slot=${this.timeSlot}, buffertime=${this.bufferTime} from database`);

      const dayValue = session.day || '';
      console.log('Day value for editing:', dayValue);
      
      const normalizedDayValue = dayValue.toLowerCase();
      
      const dayObj = this.weekDays.find(d => d.value === normalizedDayValue);
      if (!dayObj) {
        console.error('Day not found, creating default day object');
        const defaultDay = { 
          value: normalizedDayValue, 
          label: this.capitalizeFirstLetter(normalizedDayValue),
          name: normalizedDayValue
        };
        this.selectedDays = [defaultDay];
      } else {
        this.selectedDays = [dayObj];
      }

      this.currentDayBeingEdited = normalizedDayValue;
      
      console.log('Selected day for editing:', this.selectedDays[0]);
      console.log('Current day being edited:', this.currentDayBeingEdited);
      
      console.log('Current sessions for all days:', this.sessions);
      
      if (session.slots && session.slots.length > 0) {
        this.timeSlots = {
          0: session.slots.map(slot => {
            return {
              start: this.convertTo24Hour(slot.start),
              end: this.convertTo24Hour(slot.end),
              id: session.id
            };
          })
        };
      } else {
        const startFormatted = this.convertTo24Hour(session.start_time || '');
        const endFormatted = this.convertTo24Hour(session.end_time || '');
        
        this.timeSlots = {
          0: [{
            start: startFormatted,
            end: endFormatted,
            id: session.id
          }]
        };
      }

      console.log('Prepared time slots for form:', this.timeSlots);
      this.showSessionModal = true;
    },
    
    capitalizeFirstLetter(string) {
      if (!string) return '';
      return string.charAt(0).toUpperCase() + string.slice(1).toLowerCase();
    },
    
    formatTime(timeStr) {
      if (!timeStr) return '00:00';
      if (timeStr.includes('AM') || timeStr.includes('PM')) {
        return timeStr;
      }
      try {
        const [hours, minutes] = timeStr.split(':');
        if (!hours || !minutes) return timeStr;
        
        const hour = parseInt(hours, 10);
        const minute = parseInt(minutes, 10);
        
        if (isNaN(hour) || isNaN(minute)) return timeStr;
        
        let period = 'AM';
        let hour12 = hour;
        
        if (hour >= 12) {
          period = 'PM';
          hour12 = hour === 12 ? 12 : hour - 12;
        }
        
        if (hour12 === 0) {
          hour12 = 12;
        }
        
        return `${hour12}:${minute.toString().padStart(2, '0')} ${period}`;
      } catch (error) {
        console.error('Error formatting time:', error);
        return timeStr;
      }
    },

    convertTo24Hour(timeStr) {
      if (!timeStr) return '';
      if (!timeStr.includes(' ')) {
        return timeStr;
      }
      const [time, modifier] = timeStr.split(' ');
      let [hours, minutes] = time.split(':');

      if (modifier === 'PM' && hours !== '12') {
        hours = parseInt(hours, 10) + 12;
      }
      if (modifier === 'AM' && hours === '12') {
        hours = '00';
      }
      hours = hours.toString().padStart(2, '0');

      return `${hours}:${minutes}`;
    },

    addTimeSlot(dayIndex) {
      if (!this.timeSlots[dayIndex]) {
        this.$set(this.timeSlots, dayIndex, []);
      }
      this.timeSlots[dayIndex].push({
        start: '',
        end: ''
      });
    },

    removeTimeSlot(dayIndex, slotIndex) {
      if (this.timeSlots[dayIndex] && this.timeSlots[dayIndex].length > 1) {
        this.timeSlots[dayIndex].splice(slotIndex, 1);
      }
    },

    getTimeSlots(dayIndex) {
      if (!this.timeSlots[dayIndex]) {
        this.$set(this.timeSlots, dayIndex, [{ start: '', end: '' }]);
      }
      return this.timeSlots[dayIndex];
    },

    hasInvalidTimeSlots() {
      for (const dayIndex in this.timeSlots) {
        const slots = this.timeSlots[dayIndex];
        for (const slot of slots) {
          if (!slot.start || !slot.end) {
            console.log('Invalid slot - missing start or end time:', slot);
            return true;
          }
          if (slot.start && slot.end && slot.start >= slot.end) {
            console.log('Invalid slot - start time not before end time:', slot);
            return true;
          }
        }
      }
      console.log('All time slots are valid');
      return false;
    },

    saveSession() {
      console.log('Selected days:', this.selectedDays);
      console.log('Time slots:', this.timeSlots);

      if (!this.selectedServiceData.service_id || !this.selectedServiceData.clinic_id || !this.selectedServiceData.doctor_id) {
        console.error('Missing required data for session creation:', {
          service_id: this.selectedServiceData.service_id,
          clinic_id: this.selectedServiceData.clinic_id,
          doctor_id: this.selectedServiceData.doctor_id
        });
        if (typeof displayErrorMessage === 'function') {
          displayErrorMessage(formTranslation.service ? formTranslation.service.missing_data || 'Missing required data for service session' : 'Missing required data for service session');
        }
        return;
      }

      const existingDaySlots = {};
      console.log('Current sessions before saving:', this.sessions);
      
      if (this.editMode) {
        console.log('Edit mode active - current day being edited:', this.currentDayBeingEdited);
      }
      
      this.sessions.forEach(session => {
        if (session.day && session.slots && session.slots.length > 0) {
          const dayKey = session.day.toLowerCase();
          if (this.editMode && dayKey === this.currentDayBeingEdited) {
            console.log(`Skipping day ${dayKey} as it's being edited`);
            return;
          }
          existingDaySlots[dayKey] = session.slots.map(slot => ({
            start: this.convertTo24Hour(slot.start),
            end: this.convertTo24Hour(slot.end)
          }));
        }
      });
      
      console.log('Existing day slots after filtering out edited day:', existingDaySlots);

      const allDays = [
        { name: 'mon', label: 'Mon', slots: existingDaySlots['mon'] || [] },
        { name: 'tue', label: 'Tue', slots: existingDaySlots['tue'] || [] },
        { name: 'wed', label: 'Wed', slots: existingDaySlots['wed'] || [] },
        { name: 'thu', label: 'Thu', slots: existingDaySlots['thu'] || [] },
        { name: 'fri', label: 'Fri', slots: existingDaySlots['fri'] || [] },
        { name: 'sat', label: 'Sat', slots: existingDaySlots['sat'] || [] },
        { name: 'sun', label: 'Sun', slots: existingDaySlots['sun'] || [] }
      ];

      this.selectedDays.forEach((selectedDay, index) => {
        const dayIndex = allDays.findIndex(day => day.name === selectedDay.name);
        if (dayIndex !== -1 && this.timeSlots[index]) {
          const newSlots = this.timeSlots[index]
            .filter(slot => slot.start && slot.end)
            .map(slot => ({
              start: slot.start,
              end: slot.end
            }));
            
          if (this.editMode) {
            allDays[dayIndex].slots = newSlots;
          } else {
            if (allDays[dayIndex].slots && allDays[dayIndex].slots.length > 0) {
              const existingSlotStrings = allDays[dayIndex].slots.map(slot => 
                `${slot.start}-${slot.end}`
              );
              const uniqueNewSlots = newSlots.filter(newSlot => 
                !existingSlotStrings.includes(`${newSlot.start}-${newSlot.end}`)
              );
              allDays[dayIndex].slots = [...allDays[dayIndex].slots, ...uniqueNewSlots];
            } else {
              allDays[dayIndex].slots = newSlots;
            }
          }
        }
      });

      console.log('Final days data for saving:', allDays);

      const bufferTime = parseInt(this.bufferTime) || 0;
      const serviceId = parseInt(this.selectedServiceData.service_id) || 0;
      const clinicId = parseInt(this.selectedServiceData.clinic_id) || 0;
      const doctorId = parseInt(this.selectedServiceData.doctor_id) || 0;
      
      if (!serviceId) {
        console.error('Missing or invalid service_id:', this.selectedServiceData.service_id);
        if (typeof displayErrorMessage === 'function') {
          displayErrorMessage(this.$t('Service ID is missing or invalid. Cannot save sessions.'));
        }
        this.savingSession = false;
        return;
      }
      
      const sessionData = {
        buffertime: bufferTime,
        days: allDays,
        service_id: serviceId,
        clinic_id: clinicId,
        doctor_id: doctorId,
        time_slot: bufferTime
      };
      
      console.log('Service data being sent to API:', {
        service_id: sessionData.service_id,
        service_id_type: typeof sessionData.service_id,
        clinic_id: sessionData.clinic_id,
        clinic_id_type: typeof sessionData.clinic_id,
        doctor_id: sessionData.doctor_id,
        doctor_id_type: typeof sessionData.doctor_id
      });

      if (this.editMode && this.currentSessionId) {
        sessionData.id = parseInt(this.currentSessionId);
      }

      console.log('Saving session with data:', sessionData);
      this.savingSession = true;

      post('clinic_session_save', sessionData)
        .then(response => {
          console.log('Save response:', response.data);
          if (response.data.status) {
            if (typeof displayMessage === 'function') {
              displayMessage(response.data.message);
            }
            this.closeSessionModal();
            this.loadServiceSessions();
          } else {
            if (typeof displayErrorMessage === 'function') {
              displayErrorMessage(response.data.message);
            }
          }
        })
        .catch(error => {
          console.error('Failed to save service session:', error);
          if (typeof displayErrorMessage === 'function') {
            displayErrorMessage(formTranslation.service ? formTranslation.service.failed_to_save || 'Failed to save service session' : 'Failed to save service session');
          }
        })
        .finally(() => {
          this.savingSession = false;
        });
    },

    deleteSession(sessionId) {
      const sessionToDelete = this.sessions.find(session => session.id === sessionId);
      
      if (!sessionToDelete) {
        console.error('Session not found for deletion:', sessionId);
        if (typeof displayErrorMessage === 'function') {
          displayErrorMessage(formTranslation.service ? formTranslation.service.session_not_found || 'Session not found for deletion' : 'Session not found for deletion');
        }
        return;
      }
      
      const dayToDelete = sessionToDelete.day;
      console.log('Deleting session for day:', dayToDelete);
      
      if (confirm(formTranslation.service ? (formTranslation.service.confirm_delete || 'Are you sure you want to delete this session for ') + this.getDayName(dayToDelete) + '?' : 'Are you sure you want to delete this session for ' + this.getDayName(dayToDelete) + '?')) {
        // Instead of deleting the entire session, we'll update it by removing only the specified day
        
        // First, capture all existing sessions
        const existingDaySlots = {};
        this.sessions.forEach(session => {
          if (session.day && session.slots && session.slots.length > 0 && session.day !== dayToDelete) {
            existingDaySlots[session.day] = session.slots.map(slot => ({
              start: slot.start,
              end: slot.end
            }));
          }
        });
        
        console.log('Existing day slots after removing', dayToDelete, ':', existingDaySlots);
        
        const allDays = [
          { name: 'mon', label: 'Mon', slots: existingDaySlots['mon'] || [] },
          { name: 'tue', label: 'Tue', slots: existingDaySlots['tue'] || [] },
          { name: 'wed', label: 'Wed', slots: existingDaySlots['wed'] || [] },
          { name: 'thu', label: 'Thu', slots: existingDaySlots['thu'] || [] },
          { name: 'fri', label: 'Fri', slots: existingDaySlots['fri'] || [] },
          { name: 'sat', label: 'Sat', slots: existingDaySlots['sat'] || [] },
          { name: 'sun', label: 'Sun', slots: existingDaySlots['sun'] || [] }
        ];
        
        const sessionData = {
          id: sessionId,
          buffertime: parseInt(sessionToDelete.buffertime),
          days: allDays,
          service_id: parseInt(this.selectedServiceData.service_id),
          clinic_id: parseInt(this.selectedServiceData.clinic_id),
          doctor_id: parseInt(this.selectedServiceData.doctor_id),
          time_slot: parseInt(sessionToDelete.buffertime)
        };
        
        console.log('Updating session with day removed:', sessionData);
        
        post('clinic_session_save', sessionData)
          .then(response => {
            console.log('Update response after day removal:', response.data);
            if (response.data.status) {
              if (typeof displayMessage === 'function') {
                displayMessage(formTranslation.service ? (formTranslation.service.session_removed || 'Session for ') + this.getDayName(dayToDelete) + (formTranslation.service.has_been_removed || ' has been removed') : 'Session for ' + this.getDayName(dayToDelete) + ' has been removed');
              }
              this.loadServiceSessions();
            } else {
              if (typeof displayErrorMessage === 'function') {
                displayErrorMessage(response.data.message);
              }
            }
          })
          .catch(error => {
            console.error('Failed to delete service session day:', error);
            if (typeof displayErrorMessage === 'function') {
              displayErrorMessage(formTranslation.service ? formTranslation.service.failed_to_delete || 'Failed to delete service session day' : 'Failed to delete service session day');
            }
          });
      }
    },

    getDayName(dayCode) {
      const day = this.weekDays.find(d => d.value === dayCode);
      return day ? day.label : dayCode;
    },
    
    calculateDuration(start, end) {
      if (!start || !end) return '';
      try {
        let startHours = 0, startMinutes = 0;
        if (start.includes(':')) {
          const [hours, minutes] = start.split(':');
          startHours = parseInt(hours, 10);
          startMinutes = parseInt(minutes, 10);
        }
        
        let endHours = 0, endMinutes = 0;
        if (end.includes(':')) {
          const [hours, minutes] = end.split(':');
          endHours = parseInt(hours, 10);
          endMinutes = parseInt(minutes, 10);
        }
        
        let startTotalMinutes = startHours * 60 + startMinutes;
        let endTotalMinutes = endHours * 60 + endMinutes;
        
        if (endTotalMinutes < startTotalMinutes) {
          endTotalMinutes += 24 * 60;
        }
        
        const durationMinutes = endTotalMinutes - startTotalMinutes;
        const hours = Math.floor(durationMinutes / 60);
        const minutes = durationMinutes % 60;
        
        if (hours > 0) {
          return `${hours}h ${minutes > 0 ? minutes + 'm' : ''}`;
        } else {
          return `${minutes}m`;
        }
      } catch (error) {
        console.error('Error calculating duration:', error);
        return '';
      }
    }
  }
}
</script>
