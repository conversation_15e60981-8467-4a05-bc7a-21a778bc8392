<?php

namespace App\models;

use App\baseClasses\MDModel;

class KCChatConversation extends MDModel {
    public function __construct() {
        parent::__construct('chat_conversations');
    }

    /**
     * Get all conversations for a user
     * 
     * @param int $user_id User ID
     * @return array Conversations
     */
    public function getConversationsForUser($user_id) {
        global $wpdb;
        
        // Get conversation IDs where the user is a member
        $member_conversations = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT conversation_id FROM {$wpdb->prefix}md_chat_members WHERE user_id = %d",
                $user_id
            )
        );
        
        if (empty($member_conversations)) {
            return [];
        }
        
        // Extract conversation IDs
        $conversation_ids = array_map(function($item) {
            return $item->conversation_id;
        }, $member_conversations);
        
        $conversation_ids_string = implode(',', $conversation_ids);
        
        // Get conversations
        $conversations = $wpdb->get_results(
            "SELECT * FROM {$wpdb->prefix}md_chat_conversations WHERE id IN ({$conversation_ids_string}) ORDER BY last_activity DESC"
        );
        
        return $conversations;
    }

    /**
     * Get members of a conversation
     * 
     * @param int $conversation_id Conversation ID
     * @return array Members
     */
    public function getConversationMembers($conversation_id) {
        global $wpdb;
        
        $members = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT m.*, u.display_name 
                FROM {$wpdb->prefix}md_chat_members m 
                JOIN {$wpdb->users} u ON m.user_id = u.ID 
                WHERE m.conversation_id = %d",
                $conversation_id
            )
        );
        
        return $members;
    }

    /**
     * Check if a user is a member of a conversation
     * 
     * @param int $conversation_id Conversation ID
     * @param int $user_id User ID
     * @return bool True if member, false otherwise
     */
    public function isUserMember($conversation_id, $user_id) {
        global $wpdb;
        
        // Validate parameters
        $conversation_id = intval($conversation_id);
        $user_id = intval($user_id);
        
        if ($conversation_id <= 0 || $user_id <= 0) {
            error_log('Invalid parameters in isUserMember - conversation_id: ' . $conversation_id . ', user_id: ' . $user_id);
            return false;
        }
        
        // Check if the members table exists
        $table_exists = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_schema = %s
                AND table_name = %s",
                DB_NAME,
                $wpdb->prefix . 'md_chat_members'
            )
        );
        
        if (!$table_exists) {
            error_log('Chat members does not exist, creating tables now');
        }
        
        try {
            // Check if user is a member of this conversation
            $result = $wpdb->get_var(
                $wpdb->prepare(
                    "SELECT COUNT(*) FROM {$wpdb->prefix}md_chat_members WHERE conversation_id = %d AND user_id = %d",
                    $conversation_id,
                    $user_id
                )
            );
            
            $is_member = (int)$result > 0;
            
            // Log the result for debugging
            error_log('isUserMember check for user ' . $user_id . ' in conversation ' . $conversation_id . ': ' . ($is_member ? 'Yes' : 'No'));
            
            return $is_member;
        } catch (\Exception $e) {
            error_log('Error in isUserMember: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Add a member to a conversation
     * 
     * @param int $conversation_id Conversation ID
     * @param int $user_id User ID
     * @return bool|int False on error, member ID on success
     */
    public function addMember($conversation_id, $user_id) {
        global $wpdb;
        
        // Check if the members table exists
        $table_exists = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_schema = %s
                AND table_name = %s",
                DB_NAME,
                $wpdb->prefix . 'md_chat_members'
            )
        );

        // Check if user is already a member
        $is_member = $this->isUserMember($conversation_id, $user_id);
        
        if ($is_member) {
            return false;
        }
        
        // Add member
        try {
            $result = $wpdb->insert(
                "{$wpdb->prefix}md_chat_members",
                [
                    'conversation_id' => $conversation_id,
                    'user_id' => $user_id,
                    'joined_at' => current_time('mysql')
                ],
                ['%d', '%d', '%s']
            );
            
            if (!$result) {
                error_log('Error adding member to conversation: ' . $wpdb->last_error);
                return false;
            }
            
            return $wpdb->insert_id;
        } catch (\Exception $e) {
            error_log('Exception adding member to conversation: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Remove a member from a conversation
     * 
     * @param int $conversation_id Conversation ID
     * @param int $user_id User ID
     * @return bool True on success, false on failure
     */
    public function removeMember($conversation_id, $user_id) {
        global $wpdb;
        
        $result = $wpdb->delete(
            "{$wpdb->prefix}md_chat_members",
            [
                'conversation_id' => $conversation_id,
                'user_id' => $user_id
            ],
            ['%d', '%d']
        );
        
        return $result !== false;
    }

    /**
     * Remove all members from a conversation
     * 
     * @param int $conversation_id Conversation ID
     * @return bool True on success, false on failure
     */
    public function removeAllMembers($conversation_id) {
        global $wpdb;
        
        $result = $wpdb->delete(
            "{$wpdb->prefix}md_chat_members",
            ['conversation_id' => $conversation_id],
            ['%d']
        );
        
        return $result !== false;
    }

    /**
     * Find a direct conversation between two users
     * 
     * @param int $user_id1 First user ID
     * @param int $user_id2 Second user ID
     * @return object|null Conversation if found, null otherwise
     */
    public function findDirectConversation($user_id1, $user_id2) {
        global $wpdb;
        
        // Ensure the table exists
        $table_exists = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_schema = %s
                AND table_name = %s",
                DB_NAME,
                $wpdb->prefix . 'md_chat_members'
            )
        );
        
        if (!$table_exists) {
            error_log('Table ' . $wpdb->prefix . 'md_chat_members does not exist in findDirectConversation');
            return null;
        }
        
        // Make sure user IDs are integers and valid
        $user_id1 = intval($user_id1);
        $user_id2 = intval($user_id2);
        
        if (!$user_id1 || !$user_id2) {
            error_log('Invalid user IDs in findDirectConversation: ' . $user_id1 . ', ' . $user_id2);
            return null;
        }
        
        try {
            // Direct query to find conversation with both users (more efficient approach)
            $sql = $wpdb->prepare(
                "SELECT c.* FROM {$wpdb->prefix}md_chat_conversations c
                WHERE c.type = 'direct'
                AND c.id IN (
                    SELECT m1.conversation_id 
                    FROM {$wpdb->prefix}md_chat_members m1
                    WHERE m1.user_id = %d
                    AND EXISTS (
                        SELECT 1 FROM {$wpdb->prefix}md_chat_members m2
                        WHERE m2.conversation_id = m1.conversation_id
                        AND m2.user_id = %d
                    )
                )
                LIMIT 1",
                $user_id1, $user_id2
            );
            
            error_log('Executing SQL: ' . $sql);
            $common_conversation = $wpdb->get_row($sql);
            
            if ($common_conversation) {
                return $common_conversation;
            }
            
            // Fallback to original approach if the more efficient query returns nothing
            // Find direct conversations that user1 is a member of
            $user1_conversations = $wpdb->get_results(
                $wpdb->prepare(
                    "SELECT m.conversation_id 
                    FROM {$wpdb->prefix}md_chat_members m 
                    JOIN {$wpdb->prefix}md_chat_conversations c ON m.conversation_id = c.id 
                    WHERE m.user_id = %d AND c.type = 'direct'",
                    $user_id1
                )
            );
            
            if (empty($user1_conversations)) {
                return null;
            }
            
            // Extract conversation IDs
            $conversation_ids = array_map(function($item) {
                return $item->conversation_id;
            }, $user1_conversations);
            
            if (empty($conversation_ids)) {
                return null;
            }
            
            $conversation_ids_string = implode(',', $conversation_ids);
            
            // Check if user2 is a member of any of these conversations
            $common_conversation = $wpdb->get_row(
                $wpdb->prepare(
                    "SELECT c.* 
                    FROM {$wpdb->prefix}md_chat_members m 
                    JOIN {$wpdb->prefix}md_chat_conversations c ON m.conversation_id = c.id 
                    WHERE m.user_id = %d AND m.conversation_id IN ({$conversation_ids_string}) 
                    LIMIT 1",
                    $user_id2
                )
            );
            
            return $common_conversation;
        } catch (\Exception $e) {
            error_log('Exception in findDirectConversation: ' . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Override the insert method to ensure tables exist and add debugging
     *
     * @param array $data Data to insert
     * @return int|false ID of new record or false on failure
     */
    public function insert($data) {
        global $wpdb;
        
        error_log('Attempting to insert conversation: ' . json_encode($data));
        
        // Ensure database tables exist
        if (function_exists('kivicare_maybe_create_chat_tables')) {
            kivicare_maybe_create_chat_tables();
        }
        
        // Check if table exists
        $table_exists = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_schema = %s
                AND table_name = %s",
                DB_NAME,
                $wpdb->prefix . 'md_chat_conversations'
            )
        );
        
        if (!$table_exists) {
            error_log('Table ' . $wpdb->prefix . 'md_chat_conversations does not exist. Creating tables...');
            // Check again after attempted creation
            $table_exists = $wpdb->get_var(
                $wpdb->prepare(
                    "SELECT COUNT(*)
                    FROM information_schema.tables
                    WHERE table_schema = %s
                    AND table_name = %s",
                    DB_NAME,
                    $wpdb->prefix . 'md_chat_conversations'
                )
            );
            
            if (!$table_exists) {
                error_log('Failed to create ' . $wpdb->prefix . 'md_chat_conversations table');
                return false;
            }
        }
        
        // Direct insert to avoid any parent::insert issues
        try {
            $formats = [];
            foreach ($data as $value) {
                if (is_int($value)) {
                    $formats[] = '%d';
                } elseif (is_float($value)) {
                    $formats[] = '%f';
                } else {
                    $formats[] = '%s';
                }
            }
            
            $result = $wpdb->insert(
                $wpdb->prefix . 'md_chat_conversations',
                $data,
                $formats
            );
            
            if ($result === false) {
                error_log('Error inserting conversation: ' . $wpdb->last_error);
                return false;
            }
            
            $insert_id = $wpdb->insert_id;
            error_log('Successfully inserted conversation with ID: ' . $insert_id);
            return $insert_id;
        } catch (\Exception $e) {
            error_log('Exception inserting chat conversation: ' . $e->getMessage());
            if (method_exists($e, 'getTraceAsString')) {
                error_log('Stack trace: ' . $e->getTraceAsString());
            }
            return false;
        }
    }
    
}