<?php

namespace App\Console\Commands;

use App\Models\BotPersona;
use App\Models\BotUser;
use App\Models\AutomatedPost;
use App\Services\BotContentGenerationService;
use App\Services\GoogleImagenService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DirectPostGenerator extends Command
{
    protected $signature = 'bot:direct-generate {--count=76 : Number of posts to generate} {--force : Force generation}';
    protected $description = 'Generate bot posts directly without queue system';

    public function handle()
    {
        $count = (int) $this->option('count');
        $force = $this->option('force');
        
        $this->info("🚀 Generating {$count} posts directly...");
        
        // Get active personas with bot users
        $personas = BotPersona::where('is_active', true)
            ->with('botUser')
            ->whereHas('botUser')
            ->get();
            
        if ($personas->count() === 0) {
            $this->error("❌ No active personas with bot users found");
            return 1;
        }
        
        $this->info("Found {$personas->count()} active personas");
        
        $postsPerPersona = ceil($count / $personas->count());
        $generated = 0;
        $errors = 0;
        
        foreach ($personas as $persona) {
            $personaPosts = 0;
            
            for ($i = 0; $i < $postsPerPersona && $generated < $count; $i++) {
                try {
                    $this->info("Generating post for {$persona->first_name} {$persona->last_name}...");
                    
                    if ($this->createPost($persona)) {
                        $generated++;
                        $personaPosts++;
                        $this->line("  ✅ Post {$generated}/{$count} created");
                    } else {
                        $errors++;
                        $this->line("  ❌ Failed to create post");
                    }
                    
                } catch (\Exception $e) {
                    $errors++;
                    $this->error("  ❌ Error: " . $e->getMessage());
                }
            }
            
            $this->info("Generated {$personaPosts} posts for {$persona->first_name} {$persona->last_name}");
        }
        
        $this->info("🎯 Generation complete:");
        $this->info("  ✅ Successfully generated: {$generated} posts");
        $this->info("  ❌ Errors: {$errors}");
        
        return 0;
    }
    
    private function createPost(BotPersona $persona): bool
    {
        try {
            // Create automated post record
            $post = AutomatedPost::create([
                'bot_user_id' => $persona->botUser->id,
                'bot_persona_id' => $persona->id,
                'caption' => $this->generateSimpleCaption($persona),
                'hashtags' => $this->generateHashtags($persona),
                'status' => 'draft',
                'scheduled_at' => now()->addMinutes(rand(1, 60)),
                'generation_prompts' => [
                    'caption_prompt' => "Simple health post for {$persona->content_focus}",
                    'image_prompt' => $persona->image_prompt ?? "Health and wellness image"
                ]
            ]);
            
            // Update status to ready (skip image generation for now)
            $post->update([
                'status' => 'ready',
                'image_url' => null // We'll add images later
            ]);
            
            return true;
            
        } catch (\Exception $e) {
            $this->error("Failed to create post: " . $e->getMessage());
            return false;
        }
    }
    
    private function generateSimpleCaption(BotPersona $persona): string
    {
        $captions = [
            'yoga' => [
                "Start your day with mindful movement 🧘‍♀️ Even 5 minutes of gentle stretching can transform your morning routine. #YogaLife #Mindfulness",
                "Finding balance isn't just about the poses - it's about bringing peace to your entire day ✨ #InnerPeace #YogaWisdom",
                "Your breath is your anchor in every storm 🌊 Take a moment to breathe deeply and center yourself #Breathwork #Meditation"
            ],
            'fitness' => [
                "Progress over perfection 💪 Every rep, every step, every effort counts toward your goals #FitnessJourney #StayStrong",
                "Your body can do it. It's your mind you need to convince 🔥 #MindOverMatter #FitnessMotivation",
                "Consistency beats intensity every time 🎯 Small daily actions lead to big results #ConsistencyIsKey #HealthyHabits"
            ],
            'nutrition' => [
                "Nourish your body with intention 🥗 Every healthy choice is an investment in your future self #HealthyEating #Nutrition",
                "Food is fuel, not the enemy 🌱 Choose options that energize and sustain you #NutritionTips #HealthyChoices",
                "Your relationship with food sets the tone for your relationship with yourself 💚 #MindfulEating #SelfCare"
            ],
            'wellness' => [
                "Self-care isn't selfish - it's essential 🌸 Take time today to do something that fills your cup #SelfCare #Wellness",
                "Small moments of joy create a lifetime of happiness ✨ What's bringing you joy today? #Gratitude #WellnessJourney",
                "Your mental health matters just as much as your physical health 🧠💙 #MentalHealthMatters #Wellness"
            ]
        ];
        
        $focus = $persona->content_focus ?? 'wellness';
        $options = $captions[$focus] ?? $captions['wellness'];
        
        return $options[array_rand($options)];
    }
    
    private function generateHashtags(BotPersona $persona): array
    {
        $baseHashtags = ['#Health', '#Wellness', '#HealthyLiving', '#SelfCare'];
        
        $focusHashtags = [
            'yoga' => ['#Yoga', '#Mindfulness', '#Meditation', '#YogaLife', '#InnerPeace'],
            'fitness' => ['#Fitness', '#WorkoutMotivation', '#StayStrong', '#FitnessJourney', '#Exercise'],
            'nutrition' => ['#Nutrition', '#HealthyEating', '#NutritionTips', '#HealthyFood', '#CleanEating'],
            'wellness' => ['#MentalHealth', '#WellnessJourney', '#Gratitude', '#Mindset', '#PositiveVibes']
        ];
        
        $focus = $persona->content_focus ?? 'wellness';
        $specific = $focusHashtags[$focus] ?? $focusHashtags['wellness'];
        
        return array_merge($baseHashtags, array_slice($specific, 0, 3));
    }
}