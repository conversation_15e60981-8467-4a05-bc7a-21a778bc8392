<?php
/**
 * Notification Helpers for Kivicare
 * 
 * This file contains helper functions for working with system-wide notifications
 */

use App\Models\KCNotification;

/**
 * Send a notification to a user
 * 
 * @param int $user_id The recipient user ID
 * @param string $title The notification title
 * @param string $message The notification message body
 * @param string $type The notification type (info, success, warning, error)
 * @param int|null $reference_id Optional related entity ID (appointment ID, patient ID, etc)
 * @param string|null $reference_type Optional reference type (appointment, patient, task, etc)
 * @return int|bool ID of the new notification or false on failure
 */
function kc_send_notification($user_id, $title, $message, $type = 'info', $reference_id = null, $reference_type = null) {
    if (empty($user_id) || !is_numeric($user_id)) {
        error_log('Error: Invalid user ID for notification');
        return false;
    }

    if (empty($title) || empty($message)) {
        error_log('Error: Notification title and message are required');
        return false;
    }

    // Validate notification type
    $valid_types = ['info', 'success', 'warning', 'error'];
    if (!in_array($type, $valid_types)) {
        $type = 'info'; // Default to info if invalid type
    }

    $notification_data = [
        'user_id' => $user_id,
        'title' => $title,
        'message' => $message,
        'type' => $type,
    ];

    // Add optional reference data if provided
    if (!empty($reference_id) && is_numeric($reference_id)) {
        $notification_data['reference_id'] = $reference_id;
    }

    if (!empty($reference_type)) {
        $notification_data['reference_type'] = $reference_type;
    }

    $notification_model = new KCNotification();
    $notification_id = $notification_model->createNotification($notification_data);

    if (!$notification_id) {
        error_log('Error creating notification: ' . print_r($notification_data, true));
        return false;
    }

    do_action('kc_after_notification_created', $notification_id, $notification_data);

    return $notification_id;
}

/**
 * Send a notification to multiple users
 * 
 * @param array $user_ids Array of recipient user IDs
 * @param string $title The notification title
 * @param string $message The notification message body
 * @param string $type The notification type (info, success, warning, error)
 * @param int|null $reference_id Optional related entity ID (appointment ID, patient ID, etc)
 * @param string|null $reference_type Optional reference type (appointment, patient, task, etc)
 * @return array Array of notification IDs created (user_id => notification_id)
 */
function kc_send_notification_to_multiple($user_ids, $title, $message, $type = 'info', $reference_id = null, $reference_type = null) {
    if (empty($user_ids) || !is_array($user_ids)) {
        error_log('Error: Invalid user IDs for multiple notifications');
        return [];
    }

    $notification_ids = [];

    foreach ($user_ids as $user_id) {
        $notification_id = kc_send_notification($user_id, $title, $message, $type, $reference_id, $reference_type);
        
        if ($notification_id) {
            $notification_ids[$user_id] = $notification_id;
        }
    }

    return $notification_ids;
}

/**
 * Send a notification to specific user roles
 * 
 * @param array $roles Array of WordPress user roles
 * @param string $title The notification title
 * @param string $message The notification message body
 * @param string $type The notification type (info, success, warning, error)
 * @param int|null $reference_id Optional related entity ID (appointment ID, patient ID, etc)
 * @param string|null $reference_type Optional reference type (appointment, patient, task, etc)
 * @param int|null $clinic_id Optional clinic ID to limit the notification to users of a specific clinic
 * @return array Array of notification IDs created
 */
function kc_send_notification_to_roles($roles, $title, $message, $type = 'info', $reference_id = null, $reference_type = null, $clinic_id = null) {
    if (empty($roles) || !is_array($roles)) {
        error_log('Error: Invalid roles for role-based notifications');
        return [];
    }

    // Get users with the specified roles
    $args = [
        'role__in' => $roles,
        'fields' => 'ID',
    ];
    
    $users = get_users($args);
    
    if (empty($users)) {
        return [];
    }
    
    // If clinic_id is specified, filter users by clinic
    if (!empty($clinic_id) && function_exists('kcGetClinicUsers')) {
        $clinic_users = kcGetClinicUsers($clinic_id);
        $users = array_intersect($users, $clinic_users);
    }
    
    return kc_send_notification_to_multiple($users, $title, $message, $type, $reference_id, $reference_type);
}

/**
 * Get count of unread notifications for a user
 * 
 * @param int $user_id The user ID
 * @return int Count of unread notifications
 */
function kc_get_unread_notification_count($user_id) {
    if (empty($user_id)) {
        return 0;
    }

    $notification_model = new KCNotification();
    return $notification_model->countUserNotifications($user_id, true);
}

/**
 * Mark notifications as read
 * 
 * @param int|array $notification_ids Single ID or array of notification IDs
 * @param int|null $user_id Optional user ID for verification
 * @return bool Success or failure
 */
function kc_mark_notifications_read($notification_ids, $user_id = null) {
    if (empty($notification_ids)) {
        return false;
    }

    $notification_model = new KCNotification();
    return $notification_model->markAsRead($notification_ids, $user_id);
}

/**
 * Mark all notifications as read for a user
 * 
 * @param int $user_id The user ID
 * @return bool Success or failure
 */
function kc_mark_all_notifications_read($user_id) {
    if (empty($user_id)) {
        return false;
    }

    $notification_model = new KCNotification();
    return $notification_model->markAllAsRead($user_id);
}

/**
 * Send appointment-related notification
 * 
 * @param int $appointment_id The appointment ID
 * @param string $action The action (created, updated, cancelled, etc)
 * @param int|null $notify_user_id Optional specific user to notify
 * @return bool Success or failure
 */
function kc_send_appointment_notification($appointment_id, $action, $notify_user_id = null) {
    if (empty($appointment_id)) {
        return false;
    }

    // Get appointment details
    $appointment = kcGetAppointmentDetails($appointment_id);
    
    if (empty($appointment)) {
        return false;
    }
    
    // Determine notification details based on action
    switch ($action) {
        case 'created':
            $title = __('New Appointment Created', 'kc-lang');
            $message = sprintf(__('A new appointment has been created with %s at %s.', 'kc-lang'), 
                $appointment['doctor_name'], 
                date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($appointment['appointment_start_date']))
            );
            $type = 'info';
            break;
            
        case 'updated':
            $title = __('Appointment Updated', 'kc-lang');
            $message = sprintf(__('Appointment with %s has been updated to %s.', 'kc-lang'), 
                $appointment['doctor_name'], 
                date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($appointment['appointment_start_date']))
            );
            $type = 'info';
            break;
            
        case 'cancelled':
            $title = __('Appointment Cancelled', 'kc-lang');
            $message = sprintf(__('Appointment with %s on %s has been cancelled.', 'kc-lang'), 
                $appointment['doctor_name'], 
                date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($appointment['appointment_start_date']))
            );
            $type = 'warning';
            break;
            
        case 'reminder':
            $title = __('Appointment Reminder', 'kc-lang');
            $message = sprintf(__('Reminder: You have an appointment with %s tomorrow at %s.', 'kc-lang'), 
                $appointment['doctor_name'], 
                date_i18n(get_option('time_format'), strtotime($appointment['appointment_start_date']))
            );
            $type = 'info';
            break;
            
        default:
            $title = __('Appointment Notification', 'kc-lang');
            $message = sprintf(__('Update regarding your appointment with %s on %s.', 'kc-lang'), 
                $appointment['doctor_name'], 
                date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($appointment['appointment_start_date']))
            );
            $type = 'info';
    }
    
    // Determine who to notify
    if (!empty($notify_user_id)) {
        // Notify specific user
        return kc_send_notification(
            $notify_user_id,
            $title,
            $message,
            $type,
            $appointment_id,
            'appointment'
        );
    } else {
        // Notify both patient and doctor
        $notifications = [];
        
        // Notify patient
        if (!empty($appointment['patient_id'])) {
            $notifications[] = kc_send_notification(
                $appointment['patient_id'],
                $title,
                $message,
                $type,
                $appointment_id,
                'appointment'
            );
        }
        
        // Notify doctor
        if (!empty($appointment['doctor_id'])) {
            $notifications[] = kc_send_notification(
                $appointment['doctor_id'],
                $title,
                $message,
                $type,
                $appointment_id,
                'appointment'
            );
        }
        
        return !empty(array_filter($notifications));
    }
}

/**
 * Schedule cleanup of old notifications
 */
function kc_schedule_notification_cleanup() {
    if (!wp_next_scheduled('kc_notification_cleanup')) {
        wp_schedule_event(time(), 'daily', 'kc_notification_cleanup');
    }
}

/**
 * Notification cleanup task (to be run by WP Cron)
 */
function kc_run_notification_cleanup() {
    $notification_model = new KCNotification();
    
    // Delete notifications older than 90 days
    $deleted = $notification_model->cleanupOldNotifications(90);
    
    if ($deleted > 0) {
        error_log(sprintf('Notification cleanup: Deleted %d old notifications', $deleted));
    }
}

// Register the cleanup hook
add_action('kc_notification_cleanup', 'kc_run_notification_cleanup');