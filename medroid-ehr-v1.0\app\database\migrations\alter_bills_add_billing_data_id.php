<?php
/**
 * Migration: Alter bills table to add billing_data_id column
 * File: alter_bills_add_billing_data_id.php
 */
if (!defined('ABSPATH')) {
    exit;
}

class AlterBillsAddBillingDataId {
    /**
     * Run the migration - adds billing_data_id column to kc_bills table
     */
    public function up() {
        global $wpdb;
        error_log("[Migration] Altering kc_bills table to add billing_data_id column");
        
        $table = $wpdb->prefix . 'kc_bills';
        
        // Check if table exists
        $table_exists = $wpdb->get_var($wpdb->prepare("SHOW TABLES LIKE %s", $table));
        if (!$table_exists) {
            error_log("[Migration] Table kc_bills does not exist, skipping migration.");
            return false;
        }
        
        // Check if column already exists
        $column_exists = $wpdb->get_var($wpdb->prepare("SHOW COLUMNS FROM {$table} LIKE %s", 'billing_data_id'));
        if ($column_exists) {
            error_log("[Migration] Column billing_data_id already exists, skipping addition.");
            
            // Check if index exists
            $index_exists = $wpdb->get_var("SHOW INDEX FROM {$table} WHERE Column_name = 'billing_data_id'");
            if (!$index_exists) {
                // Add only the index
                $wpdb->query("ALTER TABLE {$table} ADD KEY billing_data_id (billing_data_id)");
                if ($wpdb->last_error) {
                    error_log("[Migration] Error adding index: " . $wpdb->last_error);
                    return false;
                }
                error_log("[Migration] Index on billing_data_id added successfully.");
            } else {
                error_log("[Migration] Index on billing_data_id already exists, skipping.");
            }
            
            return true;
        }
        
        // Add column and index
        $wpdb->query("ALTER TABLE {$table} 
                     ADD COLUMN billing_data_id bigint(20) DEFAULT NULL,
                     ADD KEY billing_data_id (billing_data_id)");
        
        if ($wpdb->last_error) {
            error_log("[Migration] Error altering table: " . $wpdb->last_error);
            return false;
        }
        
        error_log("[Migration] Column billing_data_id and index added successfully.");
        return true;
    }
    
    /**
     * Reverse the migration - removes the billing_data_id column
     */
    public function down() {
        global $wpdb;
        error_log("[Migration] Removing billing_data_id column from kc_bills table");
        
        $table = $wpdb->prefix . 'kc_bills';
        
        // Check if table exists
        $table_exists = $wpdb->get_var($wpdb->prepare("SHOW TABLES LIKE %s", $table));
        if (!$table_exists) {
            error_log("[Migration] Table kc_bills does not exist, skipping rollback.");
            return false;
        }
        
        // Check if column exists
        $column_exists = $wpdb->get_var($wpdb->prepare("SHOW COLUMNS FROM {$table} LIKE %s", 'billing_data_id'));
        if (!$column_exists) {
            error_log("[Migration] Column billing_data_id does not exist, skipping removal.");
            return true;
        }
        
        // Check if index exists and drop it first
        $index_exists = $wpdb->get_var("SHOW INDEX FROM {$table} WHERE Column_name = 'billing_data_id'");
        if ($index_exists) {
            $wpdb->query("ALTER TABLE {$table} DROP INDEX billing_data_id");
            if ($wpdb->last_error) {
                error_log("[Migration] Error dropping index: " . $wpdb->last_error);
                // Continue with column drop attempt even if index drop fails
            } else {
                error_log("[Migration] Index on billing_data_id dropped successfully.");
            }
        }
        
        // Drop column
        $wpdb->query("ALTER TABLE {$table} DROP COLUMN billing_data_id");
        
        if ($wpdb->last_error) {
            error_log("[Migration] Error dropping column: " . $wpdb->last_error);
            return false;
        }
        
        error_log("[Migration] Column billing_data_id removed successfully.");
        return true;
    }
}