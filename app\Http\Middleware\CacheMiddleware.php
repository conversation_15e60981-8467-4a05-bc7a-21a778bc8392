<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\Response;

class CacheMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, int $minutes = 60): Response
    {
        // Only cache GET requests
        if (!$request->isMethod('GET')) {
            return $next($request);
        }

        // Don't cache authenticated requests by default
        if ($request->user()) {
            return $next($request);
        }

        $key = $this->getCacheKey($request);

        // Check if we have a cached response
        if (Cache::has($key)) {
            $cachedResponse = Cache::get($key);
            
            return response($cachedResponse['content'])
                ->withHeaders($cachedResponse['headers'])
                ->header('X-Cache', 'HIT');
        }

        $response = $next($request);

        // Only cache successful responses
        if ($response->getStatusCode() === 200) {
            $cacheData = [
                'content' => $response->getContent(),
                'headers' => $response->headers->all(),
            ];

            Cache::put($key, $cacheData, now()->addMinutes($minutes));
            $response->header('X-Cache', 'MISS');
        }

        return $response;
    }

    /**
     * Generate a cache key for the request.
     */
    protected function getCacheKey(Request $request): string
    {
        $url = $request->fullUrl();
        $userAgent = $request->userAgent();
        
        return 'http_cache:' . md5($url . $userAgent);
    }
}
