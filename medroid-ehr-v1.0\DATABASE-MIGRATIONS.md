# Database Migrations Guide

This document explains the optimized database migration system for Medroid EHR and how to add new database tables for future modules.

## How It Works

The database migration system is designed to:

1. Create tables automatically when the plugin is activated
2. Show admin notices when database updates are needed
3. Provide a one-click update button for administrators
4. Run updates automatically when the plugin is updated
5. Track version numbers to ensure migrations only run when needed
6. Provide a centralized place to manage all database schemas

## Performance Optimization

To minimize server load, the migration system:

1. Only checks if migrations are needed once per day
2. Only shows notices to admin users
3. Only performs migration checks in the admin area
4. Uses AJAX to run migrations without page reload
5. Tracks the last time migrations were checked
6. Only runs migrations when version numbers change

## Adding a New Table

When you need to add a new table for a module, follow these steps:

1. Open `/app/database/kc-database-migration.php`
2. Find the `setup_table_schemas()` method
3. Add your new table schema to the `$this->tables` array

Example:

```php
// Your new module table
$this->tables['kc_your_module_table'] = "CREATE TABLE IF NOT EXISTS {$wpdb->prefix}kc_your_module_table (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    field1 varchar(100) NOT NULL,
    field2 text NOT NULL,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY  (id)
) $charset_collate;";
```

## For External Module Developers

If you're developing an add-on module that requires its own tables, you can use the `kivicare_database_tables` filter to add your tables to the migration system:

```php
// Add your table to the migration system
add_filter('kivicare_database_tables', function($tables) {
    global $wpdb;
    $charset_collate = $wpdb->get_charset_collate();
    
    // Add your table schema
    $tables['kc_your_addon_table'] = "CREATE TABLE IF NOT EXISTS {$wpdb->prefix}kc_your_addon_table (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        field1 varchar(100) NOT NULL,
        field2 text NOT NULL,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY  (id)
    ) $charset_collate;";
    
    return $tables;
});
```

## Forcing Migrations to Run

If you need to manually trigger migrations (e.g., in a custom update script), you can use:

```php
// Run migrations forcefully
kivicare_run_database_migrations();
```

## Modifying Existing Tables

To add columns to existing tables or modify their structure:

1. Add a custom migration function that checks if the column exists before adding it
2. Add the function to the `kivicare_database_tables` filter

Example:

```php
// Add column to existing table
function add_column_to_existing_table($tables) {
    global $wpdb;
    
    // Check if column exists
    $column_exists = $wpdb->get_results("SHOW COLUMNS FROM {$wpdb->prefix}kc_existing_table LIKE 'new_column'");
    
    if (empty($column_exists)) {
        // Add column if it doesn't exist
        $wpdb->query("ALTER TABLE {$wpdb->prefix}kc_existing_table ADD new_column VARCHAR(100) DEFAULT NULL");
    }
    
    return $tables;
}

// Add the function to the filter
add_filter('kivicare_database_tables', 'add_column_to_existing_table', 20);
```

## Best Practices

1. Always use `IF NOT EXISTS` when creating tables
2. Use appropriate column types and lengths
3. Keep related functionality in the same table when possible
4. Use version tracking to prevent unnecessary migrations
5. Test migrations thoroughly on a development environment before deploying to production
6. Use WordPress database functions (`$wpdb->query`, etc.) rather than direct SQL when possible
7. Always include proper indexes for fields that will be used in WHERE clauses

## Troubleshooting

If tables are not being created:

1. Click the "Update Database Now" button in the admin notice
2. If no notice appears, go to any admin page to trigger the check
3. Verify your SQL syntax is correct
4. Check that you have proper database permissions
5. For urgent updates, call `kivicare_run_database_migrations()` directly
6. Check the WordPress debug log for errors