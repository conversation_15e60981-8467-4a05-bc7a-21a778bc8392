<template>
    <div class="mx-auto">
        <div v-if="loading" class="text-center py-8">
            <p>Loading subscription data...</p>
        </div>
        
        <div v-else-if="error" class="bg-red-50 p-4 rounded-lg shadow-sm text-red-700">
            {{ error }}
        </div>
        
        <div v-else-if="!hasSubscription" class="bg-gray-50 p-8 rounded-lg shadow-sm text-center">
            <p class="text-lg text-gray-700">You don't have an active subscription.</p>
            <a :href="levels_link" class="mt-4 inline-block px-5 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 shadow-sm transition-all duration-200">
                View Subscription Options
            </a>
        </div>
        
        <template v-else>
            <!-- Subscription Card -->
            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                <!-- Card Header -->
                <div class="border-b border-gray-100 p-6 bg-gradient-to-r from-blue-50 to-gray-50">
                    <div class="flex flex-col md:flex-row md:justify-between md:items-center">
                        <div>
                            <h2 class="text-xl font-semibold text-gray-800">{{ subscription.name }}</h2>
                            <div class="mt-1 text-gray-500">Active Subscription</div>
                        </div>
                        <div class="mt-4 md:mt-0">
                            <span
                                class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 shadow-sm">
                                {{ subscription.status }}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Subscription Details -->
                <div class="px-6 py-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <div v-for="(detail, index) in subscriptionDetails" :key="index"
                            class="bg-gray-50 p-4 rounded-lg border-l-4 border-blue-400">
                            <div class="text-sm text-gray-500 uppercase tracking-wide">{{ detail.label }}</div>
                            <div class="text-lg font-semibold mt-1 text-gray-800" v-html="detail.value"></div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="px-6 py-5 bg-gray-50 border-t border-gray-100">
                    <div class="flex flex-wrap gap-3">
                        <div v-for="(button, index) in actionButtons" :key="index">
                            <button v-if="!button.link" @click="handleButtonClick(button.action)" :class="[
                                'inline-flex items-center px-5 py-2 border text-sm font-medium rounded-md shadow-sm transition-all duration-200',
                                button.primary ?
                                    'border-transparent text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500' :
                                    'border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                            ]">
                            {{ button.text }}
                        </button>
                        <a :href="button.link" v-else :class="[
                                'inline-flex items-center px-5 py-2 border text-sm font-medium rounded-md shadow-sm transition-all duration-200',
                                button.primary ?
                                    'border-transparent text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500' :
                                    'border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                            ]">{{ button.text }}</a>
                        </div>
                      
                    </div>
                </div>
            </div>

            <!-- Payment History -->
            <div v-if="paymentHistory.length > 0" class="bg-white rounded-lg shadow-sm overflow-hidden mt-6">
                <div class="border-b border-gray-100 p-6 bg-gradient-to-r from-blue-50 to-gray-50">
                    <h2 class="text-xl font-semibold text-gray-800">Payment History</h2>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th v-for="header in tableHeaders" :key="header" scope="col"
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b-2 border-gray-200">
                                    {{ header }}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr v-for="payment in paymentHistory" :key="payment.date"
                                class="hover:bg-gray-50 transition-colors duration-150">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-blue-500 font-medium">{{ payment.date }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-700">{{ payment.level }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-800" v-html="payment.total"></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span :class="[
                                        'px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full shadow-sm',
                                        payment.status === 'Paid' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                    ]">
                                        {{ payment.status }}
                                    </span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <div v-else class="bg-white rounded-lg shadow-sm overflow-hidden mt-6">
                <div class="border-b border-gray-100 p-6 bg-gradient-to-r from-blue-50 to-gray-50">
                    <h2 class="text-xl font-semibold text-gray-800">Payment History</h2>
                </div>
                <div class="p-6 text-center text-gray-500">
                    No payment history available.
                </div>
            </div>
        </template>
    </div>
</template>

<script>
import { post, get } from "../../config/request";

export default {
    name: 'Subscription',
    data() {
        return {
            loading: true,
            error: null,
            hasSubscription: false,
            subscription: {
                name: '',
                status: ''
            },
            subscriptionDetails: [],
            actionButtons: [],
            tableHeaders: [],
            paymentHistory: [],
            levels_link:''
        }
    },
    mounted() {
        this.fetchSubscriptionData();
    },
    methods: {
        fetchSubscriptionData() {
            this.loading = true;
            
            // Using the get method from your request config
            get('get_clinic_user_subscription', {})
                .then(response => {
                    // Check if we have data
                    if (response && response.data) {
                        const data = response.data;
                        
                        // Update component data with API response
                        this.hasSubscription = data.has_subscription || false;
                        this.subscription = data.subscription || { name: '', status: '' };
                        this.subscriptionDetails = data.subscription_details || [];
                        this.actionButtons = data.action_buttons || [];
                        this.tableHeaders = data.table_headers || [];
                        this.paymentHistory = data.payment_history || [];
                        this.levels_link = data.levels_link|| ''
                        
                        this.error = null;
                    } else {
                        this.error = "Invalid response format";
                    }
                })
                .catch(error => {
                    console.error("Error fetching subscription data:", error);
                    this.error = "Failed to load subscription data. Please try again.";
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        
        handleButtonClick(action) {
            if (action === 'update-billing') {
                // Redirect to billing update page or open modal
                this.$router.push('/settings/billing');
            } else if (action === 'cancel-subscription') {
                this.cancleSubscriptionModel();
            }
        },
        
        cancleSubscriptionModel() {
            this.$swal
                .fire({
                    title: "Are you sure you want to cancel?",
                    text: "You'll still have access until the end of your current billing period",
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#d33",
                    cancelButtonColor: "#3085d6",
                    confirmButtonText: "Yes, Cancel Subscription",
                    cancelButtonText: "No, Keep Subscription",
                })
                .then((result) => {
                    if (result.isConfirmed) {
                        // Show loading state
                        this.$swal.fire({
                            title: "Cancelling...",
                            html: "Please wait while we process your request",
                            allowOutsideClick: false,
                            didOpen: () => {
                                this.$swal.showLoading();
                            },
                        });

                        // Make API call
                        get("cancel_subscription", {})
                            .then((response) => {
                                if (response.data.success) {
                                    // Success scenario
                                    this.$swal.fire({
                                        icon: "success",
                                        title: "Cancelled",
                                        text: "Your subscription has been cancelled successfully",
                                        showConfirmButton: false,
                                        timer: 2000,
                                    });

                                    // Refresh subscription data
                                    this.fetchSubscriptionData();
                                } else {
                                    // Handle API error
                                    this.$swal.fire({
                                        icon: "error",
                                        title: "Error",
                                        text: response.data.message || "Failed to cancel subscription",
                                        showConfirmButton: true,
                                    });
                                }
                            })
                            .catch((error) => {
                                // Handle network/other errors
                                this.$swal.fire({
                                    icon: "error",
                                    title: "Error",
                                    text: "Something went wrong. Please try again.",
                                    showConfirmButton: true,
                                });
                            });
                    }
                });
        }
    }
}
</script>

<style scoped>
/* Any component-specific styles could go here */
/* Most styling is handled by the Tailwind classes in the template */
</style>