<template>
  <div>
    <div class="p-6 max-w-7xl mx-auto">
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold">Consultations</h1>
      </div>

      <!-- Stats Grid -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div
          v-for="(stat, index) in stats"
          :key="index"
          class="bg-white text-card-foreground rounded-xl border shadow"
        >
          <div class="p-4">
            <p class="text-gray-600 font-semibold text-lg">{{ stat.label }}</p>
            <p class="text-2xl font-bold text-purple-600">{{ stat.value }}</p>
          </div>
        </div>
      </div>

      <!-- Filters -->
      <div class="flex flex-wrap items-center justify-between gap-4 mb-6">
        <!-- <div class="flex space-x-2">
          <button
            v-for="(filter, index) in filters"
            :key="index"
            :class="[
              'px-4 py-2 rounded-lg text-sm',
              filter.active
                ? 'bg-purple-600 text-white'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200',
            ]"
            @click="handleFilterClick(index)"
          >
            {{ filter.label }}
          </button>
        </div> -->
        <div class="w-full">
          <div class="relative">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="w-4 h-4 absolute left-3 top-3 text-gray-400"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <circle cx="11" cy="11" r="8"></circle>
              <path d="m21 21-4.3-4.3"></path>
            </svg>
            <input
              v-model="serverParams.searchTerm"
              @input="handleGlobalSearch"
              placeholder="Search consultations..."
              class="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
              type="text"
            />
          </div>
          <!-- <button
            class="flex items-center px-3 py-2 border rounded-lg hover:bg-gray-50"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="w-4 h-4 mr-2"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <polygon
                points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"
              ></polygon>
            </svg>
            Filter
          </button> -->
        </div>
      </div>

      <!-- Consultations List -->
      <div
        v-if="patientConsultationList.length === 0"
        class="bg-white rounded-xl text-center py-8 text-gray-500"
      >
        No consultations yet
      </div>
      <div v-else class="space-y-4">
        <ConsultationCard
          v-for="consultation in patientConsultationList"
          :key="consultation.id"
          :consultation="consultation"
        />
      </div>

      <!-- AI Assistant Section -->
      <div
        role="alert"
        class="relative w-full rounded-lg border px-4 py-3 text-sm [&amp;>svg+div]:translate-y-[-3px] [&amp;>svg]:absolute [&amp;>svg]:left-4 [&amp;>svg]:top-4 [&amp;>svg]:text-foreground [&amp;>svg~*]:pl-7 bg-background text-foreground mt-6 bg-purple-50 border-purple-100"
      >
        <div class="absolute -top-3 right-6">
          <span
            class="bg-purple-100 text-purple-600 text-xs font-medium px-5 py-1 rounded-full"
          >
            Coming Soon
          </span>
        </div>
        <div class="flex items-start space-x-4">
          <div
            class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="lucide lucide-message-square w-4 h-4 text-purple-600"
            >
              <path
                d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"
              ></path>
            </svg>
          </div>
          <div>
            <h3 class="font-semibold text-purple-900">AI Health Assistant</h3>
            <div class="text-sm [&amp;_p]:leading-relaxed mt-1 text-purple-800">
              Based on your recent cardiology consultation, you're due for a
              follow-up in 2 weeks. Would you like me to help schedule your next
              appointment with Dr. Chen?
            </div>
            <div class="mt-3 flex space-x-4">
              <button
                class="text-sm px-3 py-1 bg-purple-600 text-white rounded-md hover:bg-purple-700"
              >
                Schedule Follow-up</button
              ><button
                class="text-sm px-3 py-1 text-purple-600 hover:bg-purple-100 rounded-md"
              >
                Remind Me Later
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Modals -->
      <ModalPopup v-if="showTemplateForm" @close="closeTemplateForm">
        <div class="p-6">
          <h2 class="text-xl font-semibold mb-4">
            {{ formTranslation.common.add_encounter_template }}
          </h2>
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                {{ formTranslation.encounter_dashboard.template_name }}
                <span class="text-red-500">*</span>
              </label>
              <input
                v-model="templateForm.name"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
                :class="{ 'border-red-500': templateErrors.name }"
                :placeholder="
                  formTranslation.encounter_dashboard.template_name_placeholder
                "
                type="text"
                required
              />
              <p v-if="templateErrors.name" class="mt-1 text-sm text-red-600">
                {{ templateErrors.name }}
              </p>
            </div>
          </div>
        </div>
      </ModalPopup>

      <ModalPopup v-if="billDetailsModel" @close="closeBillDetails">
        <BillDetails
          :encounter-id="selectedEncounterId"
          :clinic-extra="clinic_extra"
          @onBillCancel="closeBillDetails"
        />
      </ModalPopup>

      <ModalPopup v-if="showCustomForm" @close="closeCustomForm">
        <CustomForm
          :data="customFormData"
          :view-mode="customFormViewMode"
          @closeModal="closeCustomForm"
        />
      </ModalPopup>
    </div>
  </div>
</template>

<script>
import { get, post } from "../../config/request";
import BillDetails from "../../components/PatientBill/BillDetails";
import ModalPopup from "../../components/Modal/Index";
import CustomForm from "../CustomForm/Form.vue";
import { debounce } from "lodash";
import ConsultationDetails from "./ConsultationDetails.vue";
import ConsultationCard from "./ConsultationCard.vue";

export default {
  name: "PatientConsultationList",

  components: {
    BillDetails,
    ModalPopup,
    CustomForm,
    ConsultationDetails,
    ConsultationCard,
  },

  data() {
    return {
      // Core Data States
      patientConsultationList: [],
      patientEncounterList: {
        data: [],
        totalRows: 0,
      },
      loading: false,
      error: null,

      // Stats
      stats: [
        {
          type: "total_consultations",
          label: "Total Consultations",
          value: "0",
        },
        { type: "upcoming", label: "Upcoming", value: "0" },
        { type: "completed", label: "Completed", value: "0" },
        {
          type: "different_specialists",
          label: "Different Specialists",
          value: "0",
        },
      ],

      // Filters
      filters: [
        { label: "All", active: true },
        { label: "Upcoming", active: false },
        { label: "Completed", active: false },
      ],

      // UI States
      visible: false,
      showAddForm: false,
      showEditForm: false,
      showForm: false, // Added this
      showTemplateForm: false,
      showCustomForm: false,
      billDetailsModel: false,
      isEditing: false,

      // Selected Items
      showDetailsModal: false,
      selectedConsultation: null,
      selectedRows: [],
      selectedEncounter: null,
      selectedEncounterId: null,
      encounterId: -1,
      bulkAction: "",

      // Server Parameters
      serverParams: {
        columnFilters: {},
        sort: {
          field: "",
          type: "",
        },
        page: 1,
        perPage: 10,
        searchTerm: "",
      },

      // Request Parameters
      patientEncountersRequest: {
        login_id: 0,
        patient_id: 0,
      },

      // Forms Data
      templateForm: {
        name: "",
        clinic_id: null,
      },
      templateErrors: {
        name: null,
      },
      customFormData: null,
      customFormViewMode: false,

      // Field Visibility
      doctorField: false,
      patientField: false,
      clinicField: true,

      // Configuration
      pageSizes: [10, 25, 50, 100],
      isEncounterTemp: false,
      clinic_extra: {
        prefix: "",
        postfix: "",
      },
    };
  },

  computed: {
    encounterName() {
      return this.isEncounterTemp
        ? this.formTranslation?.patient_bill?.encounter_template_list
        : this.formTranslation?.patient_bill?.patients_encounter_list;
    },

    // Table Configuration
    tableColumns() {
      if (this.isEncounterTemp) {
        return [
          {
            field: "id",
            label: this.formTranslation?.common?.id || "ID",
            sortable: true,
          },
          {
            field: "encounters_template_name",
            label:
              this.formTranslation?.patient_encounter_template?.dt_lbl_name ||
              "Name",
            sortable: true,
          },
          {
            field: "actions",
            label:
              this.formTranslation?.patient_encounter?.dt_lbl_action ||
              "Actions",
            sortable: false,
          },
        ];
      }

      return [
        {
          field: "id",
          label: this.formTranslation?.common?.id || "ID",
          sortable: true,
        },
        {
          field: "doctor_name",
          label:
            this.formTranslation?.patient_encounter?.dt_lbl_doc_name ||
            "Doctor",
          hidden: this.doctorField,
          sortable: true,
        },
        {
          field: "clinic_name",
          label:
            this.formTranslation?.patient_encounter?.dt_lbl_clinic || "Clinic",
          sortable: true,
        },
        {
          field: "patient_name",
          label:
            this.formTranslation?.patient_encounter?.dt_lbl_patient ||
            "Patient",
          sortable: true,
        },
        {
          field: "encounter_date",
          label: this.formTranslation?.patient_encounter?.dt_lbl_name || "Date",
          sortable: true,
        },
        {
          field: "status",
          label: this.formTranslation?.common?.status || "Status",
          sortable: true,
        },
        {
          field: "actions",
          label:
            this.formTranslation?.patient_encounter?.dt_lbl_action || "Actions",
          sortable: false,
        },
      ];
    },

    // Permissions & Conditions
    canAddEncounter() {
      return (
        !this.isEncounterTemp && this.kcCheckPermission("patient_encounter_add")
      );
    },

    canAddTemplate() {
      return (
        this.isEncounterTemp &&
        this.kcCheckPermission("encounters_template_add")
      );
    },

    // Selection States
    isAllSelected() {
      return (
        this.patientEncounterList.data.length > 0 &&
        this.selectedRows.length === this.patientEncounterList.data.length
      );
    },

    isIndeterminate() {
      return (
        this.selectedRows.length > 0 &&
        this.selectedRows.length < this.patientEncounterList.data.length
      );
    },

    // Pagination
    canGoPrevious() {
      return this.serverParams.page > 1;
    },

    canGoNext() {
      return (
        this.serverParams.page <
        Math.ceil(
          this.patientEncounterList.totalRows / this.serverParams.perPage
        )
      );
    },

    paginationText() {
      const start =
        (this.serverParams.page - 1) * this.serverParams.perPage + 1;
      const end = Math.min(
        start + this.serverParams.perPage - 1,
        this.patientEncounterList.totalRows
      );
      return `${start}-${end} of ${this.patientEncounterList.totalRows}`;
    },

    // Store Getters
    login_id() {
      return this.$store.state.userDataModule.user.ID;
    },

    userData() {
      return this.$store.state.userDataModule.user;
    },
  },

  watch: {
    $route(to, from) {
      this.initializeComponent();
    },

    "templateForm.name"(newVal) {
      this.templateErrors.name = !newVal
        ? this.formTranslation.patient_bill.encounter_template_name_required
        : null;
    },
    stats: {
      handler(newStats) {
        // Deep watcher for stats
        console.log("Stats updated:", newStats);
      },
      deep: true,
    },
  },

  mounted() {
    try {
      this.initializeComponent();

      if ("patient_id" in this.$route.params) {
        this.patientField = false;
        this.clinicField = false;
        this.patientEncountersRequest.patient_id =
          this.$route.params.patient_id;
      } else {
        this.patientField = true;
        this.clinicField = true;
      }
    } catch (error) {
      console.error("Initialization error:", error);
      if (typeof displayErrorMessage === "function") {
        displayErrorMessage(error.message || "Error initializing component");
      }
    }
  },

  methods: {
    navigateToBooking() {
      window.location.href = "/projects/medroid/appoiintment";
    },
    async initializeComponent() {
      try {
        this.setTemplateMode(false);
        this.setupUserSpecificFields();
        this.initializeRequestParameters();
        await this.fetchEncounters();
      } catch (error) {
        console.error("Component initialization error:", error);
        throw error;
      }
    },

    setTemplateMode(isRouterChange) {
      try {
        const routeName = isRouterChange
          ? isRouterChange.name
          : this.$route.name;
        this.isEncounterTemp = routeName === "encounter-template";
      } catch (error) {
        console.error("Error setting template mode:", error);
        throw error;
      }
    },

    setupUserSpecificFields() {
      const userRole = this.getUserRole();
      this.doctorField = userRole === "doctor";
      this.patientField = userRole === "patient";
      this.clinicField = !this.$route.params.patient_id;
    },

    initializeRequestParameters() {
      if (this.$route.params.patient_id === undefined) {
        this.patientEncountersRequest.login_id = this.login_id;
        this.patientEncountersRequest.patient_id = 0;
      } else {
        this.patientEncountersRequest.patient_id =
          this.$route.params.patient_id;
        this.patientEncountersRequest.login_id = 0;
      }
    },

    // Data Fetching
    async fetchEncounters() {
      this.loading = true;
      this.error = null;

      try {
        const endpoint = this.isEncounterTemp
          ? "get_encounter_templates"
          : "patient_encounter_list";

        const params = {
          ...this.patientEncountersRequest,
          ...this.serverParams,
        };

        const statsResponse = await get("patient_encounter_stats", params);

        // Update stats if the statistics API call is successful
        if (statsResponse?.status) {
          let statsData = statsResponse.data.data;
          console.log("statsData", statsData);
          this.stats = [
            {
              type: "total_consultations",
              label: "Total Consultations",
              value: statsData.total_consultations || "0",
            },
            {
              type: "upcoming",
              label: "Upcoming",
              value: statsData.upcoming_consultations || "0",
            },
            {
              type: "completed",
              label: "Completed",
              value: statsData.completed_consultations || "0",
            },
            {
              type: "different_specialists",
              label: "Different Specialists",
              value: statsData.different_specialists || "0",
            },
          ];
        }

        const response = await get(endpoint, params);

        if (response?.data?.status || response?.data?.success) {
          // Check both status and success
          if (this.isEncounterTemp) {
            this.patientConsultationList = response.data.data?.list || [];
          } else {
            this.patientConsultationList = response.data.data || [];
          }

          if (response.data.clinic_extra) {
            this.clinic_extra = response.data.clinic_extra;
          }
        }
      } catch (err) {
        console.error("Error:", err);
        this.error = err.message || "Error fetching encounters";

        if (typeof displayErrorMessage === "function") {
          displayErrorMessage(this.error);
        } else if (this.$toast) {
          this.$toast.error(this.error);
        } else {
          alert(this.error); // Fallback
        }
      } finally {
        this.loading = false;
      }
    },

    // Event Handlers
    handleGlobalSearch: debounce(function (event) {
      this.serverParams.page = 1;
      this.fetchEncounters();
    }, 300),

    handleSort(field) {
      if (this.serverParams.sort.field === field) {
        if (this.serverParams.sort.type === "asc") {
          this.serverParams.sort.type = "desc";
        } else if (this.serverParams.sort.type === "desc") {
          this.serverParams.sort.field = "";
          this.serverParams.sort.type = "";
        } else {
          this.serverParams.sort.type = "asc";
        }
      } else {
        this.serverParams.sort.field = field;
        this.serverParams.sort.type = "asc";
      }
      this.fetchEncounters();
    },

    updateColumnFilter: debounce(function (field, value) {
      if (value === "" || value === null) {
        delete this.serverParams.columnFilters[field];
      } else {
        this.serverParams.columnFilters[field] = value;
      }
      this.serverParams.page = 1;
      this.fetchEncounters();
    }, 300),

    handlePerPageChange() {
      this.serverParams.page = 1;
      this.fetchEncounters();
    },

    // Navigation
    previousPage() {
      if (this.canGoPrevious) {
        this.serverParams.page--;
        this.fetchEncounters();
      }
    },

    nextPage() {
      if (this.canGoNext) {
        this.serverParams.page++;
        this.fetchEncounters();
      }
    },

    // Form Handlers
    handleEncounterForm(encounterData = {}) {
      if (!this.showAddForm) {
        this.visible = true;
        this.showAddForm = true;
        this.showEditForm = false;
        this.selectedEncounter = encounterData;
      } else {
        this.closeEncounterForm();
      }
    },

    closeEncounterForm() {
      this.visible = false;
      this.encounterId = -1;
      this.showAddForm = false;
      this.showEditForm = false;
      this.selectedEncounter = null;
    },

    viewDetails(consult) {
      this.selectedConsultation = consult;
      this.showDetailsModal = true;
    },

    closeDetailsModal() {
      this.showDetailsModal = false;
      this.selectedConsultation = null;
    },

    handleEncounterTemplateForm() {
      this.showTemplateForm = true;
      this.templateForm = {
        name: "",
        clinic_id: this.$store.state.activeClinicId,
      };
    },

    async saveTemplate() {
      if (!this.templateForm.name) {
        this.templateErrors.name =
          this.formTranslation.patient_bill.encounter_template_name_required;
        return;
      }

      try {
        this.loading = true;
        const response = await post("add_encounter_temp", {
          template_name: this.templateForm.name,
          added_by: this.login_id,
          clinic_id: this.templateForm.clinic_id,
        });

        if (response?.data?.success) {
          this.closeTemplateForm();
          this.fetchEncounters();
          if (typeof displayMessage === "function") {
            displayMessage(response.data.message);
          }
        } else {
          throw new Error(response?.data?.message);
        }
      } catch (err) {
        if (typeof displayErrorMessage === "function") {
          displayErrorMessage(
            err.response?.data?.message || "Failed to create template"
          );
        }
      } finally {
        this.loading = false;
      }
    },

    closeTemplateForm() {
      this.showTemplateForm = false;
      this.templateForm = {
        name: "",
        clinic_id: null,
      };
      this.templateErrors = {
        name: null,
      };
    },

    // Row Actions
    editEncounterData(encounter) {
      this.selectedEncounter = { ...encounter };
      this.encounterId = encounter.id;
      this.showEditForm = true;
      this.showAddForm = false;
      this.visible = true;
      window.scrollTo({ top: 0, behavior: "smooth" });
    },

    async deleteEncounter(id) {
      try {
        this.loading = true;
        const endpoint = this.isEncounterTemp
          ? "delete_encounter_temp"
          : "patient_encounter_delete";

        const response = await post(endpoint, { id });

        if (response?.data?.success) {
          this.selectedRows = this.selectedRows.filter((rowId) => rowId !== id);
          await this.fetchEncounters();
          this.$store.dispatch("showSuccessMessage", response.data.message);
        } else {
          throw new Error(response?.data?.message);
        }
      } catch (err) {
        this.$store.dispatch(
          "showErrorMessage",
          err.response?.data?.message || "Failed to delete encounter"
        );
      } finally {
        this.loading = false;
      }
    },

    confirmDelete(id) {
      if (this.loading) return;

      this.$root.$emit("show-confirmation-dialog", {
        title: this.formTranslation?.clinic_schedule?.dt_are_you_sure,
        message: this.formTranslation?.common?.py_delete,
        confirmText: this.formTranslation?.common?.yes,
        cancelText: this.formTranslation?.common?.cancel,
        type: "danger",
        onConfirm: () => this.deleteEncounter(id),
      });
    },

    // Bulk Actions
    async handleBulkAction() {
      if (!this.bulkAction || !this.selectedRows.length || this.loading) return;

      if (this.bulkAction === "delete") {
        this.$root.$emit("show-confirmation-dialog", {
          title: this.formTranslation?.clinic_schedule?.dt_are_you_sure,
          message: this.formTranslation?.common?.py_delete,
          confirmText: this.formTranslation?.common?.yes,
          cancelText: this.formTranslation?.common?.cancel,
          type: "danger",
          onConfirm: this.deleteBulkEncounters,
        });
      }
    },

    async deleteBulkEncounters() {
      try {
        this.loading = true;
        const response = await post("module_wise_multiple_data_update", {
          action_perform: "delete",
          module: this.isEncounterTemp
            ? "patient_encounter_template"
            : "patient_encounter_list",
          data: this.selectedRows,
        });

        if (response?.data?.success) {
          this.selectedRows = [];
          this.bulkAction = "";
          await this.fetchEncounters();
          this.$store.dispatch("showSuccessMessage", response.data.message);
        } else {
          throw new Error(response?.data?.message);
        }
      } catch (err) {
        this.$store.dispatch(
          "showErrorMessage",
          err.response?.data?.message || "Failed to delete encounters"
        );
      } finally {
        this.loading = false;
      }
    },

    // Selection Handlers
    toggleSelectAll() {
      if (this.isAllSelected) {
        this.selectedRows = [];
      } else {
        this.selectedRows = this.patientEncounterList.data.map((row) => row.id);
      }
    },

    // Custom Form Handlers
    openCustomForm(row, formData) {
      this.customFormData = {
        ...formData,
        module_id:
          formData.module_type === "appointment_module"
            ? row.appointment_id
            : row.id,
      };
      this.customFormViewMode = row.status === "0";
      this.showCustomForm = true;
    },

    closeCustomForm() {
      this.showCustomForm = false;
      this.customFormData = null;
      this.customFormViewMode = false;
    },

    // Bill Details Handlers
    openBillDetails(row) {
      this.selectedEncounterId = row.id;
      this.billDetailsModel = true;
    },

    closeBillDetails() {
      this.billDetailsModel = false;
      this.selectedEncounterId = null;
    },

    // Permission Checks
    canEditEncounter(encounter) {
      return this.isEncounterTemp
        ? this.kcCheckPermission("encounters_template_edit")
        : this.kcCheckPermission("patient_encounter_edit") &&
            this.getUserRole() !== "patient";
    },

    canDeleteEncounter(encounter) {
      return this.isEncounterTemp
        ? this.kcCheckPermission("encounters_template_delete")
        : this.kcCheckPermission("patient_encounter_delete") &&
            this.getUserRole() !== "patient";
    },

    canViewDashboard(encounter) {
      return this.isEncounterTemp
        ? this.kcCheckPermission("encounters_template_view")
        : this.kcCheckPermission("patient_encounter_view");
    },

    canViewBodyChart(encounter) {
      return (
        this.userData.addOns.bodyChart &&
        this.kcCheckPermission("body_chart_list")
      );
    },

    canViewBillDetails(encounter) {
      return this.checkEnableModule("billing") && encounter.status === "0";
    },

    canViewCustomForm(encounter, form) {
      return (
        this.userData.addOns.kiviPro &&
        this.customFormCondition(encounter, form)
      );
    },

    // Utility Methods
    formatColumnValue(value, column) {
      if (column.field === "encounter_date") {
        return this.formatDate(value);
      }
      return value;
    },

    formatDate(date) {
      if (!date) return "";
      return new Date(date).toLocaleDateString(undefined, {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    },

    customFormCondition(encounter, form) {
      return (
        form &&
        (!form.clinic_ids?.length ||
          form.clinic_ids.includes(encounter.clinic_id)) &&
        ((form.module_type === "appointment_module" &&
          encounter.appointment_id) ||
          form.module_type === "patient_encounter_module")
      );
    },

    getDashboardRoute(row) {
      return {
        name: "patient-encounter.dashboard",
        params: { encounter_id: row.id },
        query: { isEncounterTemp: this.isEncounterTemp },
      };
    },

    getBodyChartRoute(row) {
      return {
        name: "patient-encounter.body-chart",
        params: { encounter_id: row.id },
      };
    },

    // Permission Helper Methods
    canViewDashboard(row) {
      if (this.isEncounterTemp) {
        return this.kcCheckPermission("encounters_template_view");
      }
      return this.kcCheckPermission("patient_encounter_view");
    },

    canViewBodyChart(row) {
      return (
        this.userData.addOns.bodyChart === true &&
        this.kcCheckPermission("body_chart_list")
      );
    },
  },
};
</script>
