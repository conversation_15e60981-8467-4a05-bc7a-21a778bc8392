<script src="https://cdn.tailwindcss.com"></script>
<?php 
$site_logo = !empty($config_options[KIVI_CARE_PREFIX . 'site_logo']) ? wp_get_attachment_url($config_options[KIVI_CARE_PREFIX . 'site_logo']) : esc_js(KIVI_CARE_DIR_URI.'/assets/images/logo-banner.png');
$currency_detail = kcGetClinicCurrenyPrefixAndPostfix();
$currency_prefix = !empty($currency_detail['prefix']) ? $currency_detail['prefix'] : '' ;
?>

<div class="min-h-full w-full kc-clinic-register text-start" id="clinic-register">
    <div class="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
        <div class="max-w-6xl mx-auto px-4 py-8">
            <?php if (isset($_GET['payment-success'])):  ?>
                <div class="max-w-xl mx-auto p-6 bg-white rounded-lg shadow-sm text-center">
                    <div class="mb-8">
                        <div class="flex justify-center mb-6">
                            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-check w-10 h-10 text-green-600">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <path d="m9 12 2 2 4-4"></path>
                                </svg>
                            </div>
                        </div>
                        <h1 class="text-2xl font-semibold mb-3"><?php esc_html_e('Welcome to Medroid AI!', 'kc-lang'); ?></h1>
                        <p class="text-gray-600"><?php esc_html_e('Your account has been successfully created', 'kc-lang'); ?></p>
                    </div>
                    <div class="bg-blue-50 p-4 rounded-lg mb-8">
                        <div class="flex items-center justify-center mb-3">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mail w-5 h-5 text-blue-600 mr-2">
                                <rect width="20" height="16" x="2" y="4" rx="2"></rect>
                                <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path>
                            </svg>
                            <span class="text-blue-800 font-medium"><?php esc_html_e('Check your email', 'kc-lang'); ?></span>
                        </div>
                        <p class="text-sm text-blue-800"><?php esc_html_e("We've sent your login credentials and getting started guide to your registered email address.", 'kc-lang'); ?></p>
                    </div>
                    <div class="mb-8">
                        <h2 class="font-medium mb-4"><?php esc_html_e("Here's what's next:", 'kc-lang'); ?></h2>
                        <div class="space-y-4 text-left">
                            <div class="flex items-start space-x-3">
                                <div class="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                                    <span class="text-purple-600 text-sm">1</span>
                                </div>
                                <div>
                                    <h3 class="font-medium"><?php esc_html_e('Log in to your account', 'kc-lang'); ?></h3>
                                    <p class="text-sm text-gray-600"><?php esc_html_e('Access your dashboard using the credentials sent to your email', 'kc-lang'); ?></p>
                                </div>
                            </div>
                            <div class="flex items-start space-x-3">
                                <div class="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                                    <span class="text-purple-600 text-sm">2</span>
                                </div>
                                <div>
                                    <h3 class="font-medium"><?php esc_html_e('Complete your clinic profile', 'kc-lang'); ?></h3>
                                    <p class="text-sm text-gray-600"><?php esc_html_e('Add your clinic details and customize your settings', 'kc-lang'); ?></p>
                                </div>
                            </div>
                            <div class="flex items-start space-x-3">
                                <div class="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                                    <span class="text-purple-600 text-sm">3</span>
                                </div>
                                <div>
                                    <h3 class="font-medium"><?php esc_html_e('Start managing appointments', 'kc-lang'); ?></h3>
                                    <p class="text-sm text-gray-600"><?php esc_html_e('Begin scheduling and managing patient visits', 'kc-lang'); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="grid grid-cols-2 gap-4 mb-8 text-left">
                        <div class="flex items-start space-x-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar w-5 h-5 text-purple-600 mt-1">
                                <path d="M8 2v4"></path>
                                <path d="M16 2v4"></path>
                                <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                                <path d="M3 10h18"></path>
                            </svg>
                            <div class="text-sm">
                                <p class="font-medium"><?php esc_html_e('Smart Scheduling', 'kc-lang'); ?></p>
                                <p class="text-gray-600"><?php esc_html_e('Ready to use', 'kc-lang'); ?></p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-stethoscope w-5 h-5 text-purple-600 mt-1">
                                <path d="M4.8 2.3A.3.3 0 1 0 5 2H4a2 2 0 0 0-2 2v5a6 6 0 0 0 6 6v0a6 6 0 0 0 6-6V4a2 2 0 0 0-2-2h-1a.2.2 0 1 0 .3.3"></path>
                                <path d="M8 15v1a6 6 0 0 0 6 6v0a6 6 0 0 0 6-6v-4"></path>
                                <circle cx="20" cy="10" r="2"></circle>
                            </svg>
                            <div class="text-sm">
                                <p class="font-medium"><?php esc_html_e('Patient Management', 'kc-lang'); ?></p>
                                <p class="text-gray-600"><?php esc_html_e('Ready to use', 'kc-lang'); ?></p>
                            </div>
                        </div>
                    </div>
                    <a href="<?php echo admin_url('admin.php?page=dashboard') ?>" class="w-full py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 hover:text-white transition-colors flex items-center justify-center">
                        <?php esc_html_e('Goto Dashboard', 'kc-lang'); ?>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right w-4 h-4 ml-2">
                            <path d="M5 12h14"></path>
                            <path d="m12 5 7 7-7 7"></path>
                        </svg>
                    </a>
                    <div class="mt-6 text-sm text-gray-600">
                        <p><?php esc_html_e('Need help getting started?', 'kc-lang'); ?></p>
                        <a href="https://help.medroid.ai/" target="_blank" class="text-purple-600 font-medium hover:underline"><?php esc_html_e('Contact our support team', 'kc-lang'); ?></a>
                    </div>
                </div>
            <?php
            else:
            ?>
                <div class="max-w-xl mx-auto mb-8">
                    <div class="flex items-center justify-between mb-8">
                        <div class="flex items-center gap-2">
                            <!-- <div class="w-8 h-8">
                                <svg width="32" height="30" viewBox="0 0 24 24" class="text-purple-600 w-full h-full">
                                    <path fill="currentColor"
                                        d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"></path>
                                </svg>
                                <img class="pt-1 pr-2" src="<?php echo esc_js(KIVI_CARE_DIR_URI.'/assets/images/collapsed-logo.png')?>" >
                            </div>
                            <span class="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-blue-600">Medroid AI</span> -->
                            <img src="<?php echo esc_url($site_logo ?? "#") ?>" alt="logo" class="w-48">
                        </div>
                        <div class="flex items-center gap-4">
                            <div class="flex items-center gap-2 current-active-step-dot">
                                <div class="w-3 h-3 rounded-full bg-purple-600"></div>
                                <div class="w-3 h-3 rounded-full bg-gray-200"></div>
                                <div class="w-3 h-3 rounded-full bg-gray-200"></div>
                            </div>
                            <span class="text-sm text-gray-600">Step <span class="current-active-step">1</span> of 3</span>
                        </div>
                    </div>
                </div>
                <div class="form-step active">
                    <div class="max-w-xl mx-auto">
                        <div class="bg-white rounded-2xl shadow-xl p-8">
                            <div class="mb-8">
                                <h1 class="text-2xl font-bold mb-6">Complete Your Registration</h1>
                                <form class="clinic_register_form space-y-6">

                                    <?php // Fetch PMP Membership Levels
                                    if ($levels = $this->pmpro_getAllLevels()) {
                                        // ob_get_clean();
                                        // Get the ID from query parameter
                                            $filter_id = isset($_GET['plan_id']) ? $_GET['plan_id'] : null;


                                            // Filter the array if an ID is provided
                                            if ($filter_id !== null) {
                                                $levels = array_filter($levels, function($level) use ($filter_id) {
                                                    return $level['id'] == $filter_id;
                                                });
                                                
                                                // Reset array keys
                                                $levels = array_values($levels);
                                            }
                                        foreach ($levels as $level) : extract($level) ?>
                                            <label class="relative block pt-1 subscription-plan-card-wrapper">
                                                <input type="radio" name="subscription_plan"
                                                    value="<?php echo esc_attr($id); ?>" class="peer sr-only" <?php echo count($levels)==1?"checked='checked'":'' ?> />
                                                    
                                                <div class="subscription-plan-card bg-gradient-to-r from-purple-50 to-blue-50 rounded-xl p-6 mt-1 mb-6 <?php echo count($levels)==1?"border-purple-600 ring-1 ring-purple-600":'' ?>">
                                                    <div class="flex justify-between items-start mb-4">
                                                        <div>
                                                            <h3 class="font-semibold text-gray-900"><?php echo esc_html($plan_name); ?></h3>
                                                            <div class="flex items-baseline mt-1">
                                                                <?php if (!empty($level['sale_price']) && $level['sale_price'] < $price) : ?>
                                                                    <span class="text-2xl font-bold"><?= $currency_prefix ?> <?php echo esc_html($level['sale_price']); ?></span>
                                                                    <span class="text-gray-500 ml-2 line-through"><?= $currency_prefix ?> <?php echo esc_html($price); ?></span>
                                                                <?php else : ?>
                                                                    <span class="text-2xl font-bold"><?= $currency_prefix ?> <?php echo esc_html($price); ?></span>
                                                                <?php endif; ?>
                                                                <span class="text-gray-600 ml-1">/<?php echo esc_html($billing_label); ?></span>
                                                            </div>
                                                        </div>
                                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                                                        <?php 
                                                            // Use the correct variable name and handle null values
                                                            if (isset($$billing_frequency) && $$billing_frequency == 12) {
                                                                echo esc_html('Annually');
                                                            } else if ($price > 0) {
                                                                echo esc_html('Monthly');
                                                            } else {
                                                                echo esc_html('75% discount');
                                                            }
                                                            ?>
                                                        </span>
                                                    </div>
                                                    <div class="flex items-center gap-2 text-sm text-gray-600">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                            viewBox="0 0 24 24"
                                                            fill="none" stroke="currentColor" stroke-width="2"
                                                            stroke-linecap="round"
                                                            stroke-linejoin="round"
                                                            class="lucide lucide-check w-4 h-4 text-green-500">
                                                            <path d="M20 6 9 17l-5-5"></path>
                                                        </svg>
                                                        <span><?php echo esc_html($trial_text); ?></span>
                                                    </div>
                                                </div>
                                            </label>
                                    <?php endforeach;
                                    } else {
                                        echo "<p>No membership plans available.</p>";
                                    } ?>
                                    <div class="space-y-4">
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">

                                            <div>
                                                <label class="block text-sm font-medium mb-1" for="clinic-user_first_name"><?php echo esc_html_e("First Name",'kc-lang')?>
                                                    <span class="text-red-500">*</span>
                                                </label>
                                                <input id="clinic-user_first_name"
                                                    name="clinic_user_first_name"
                                                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-shadow"
                                                    placeholder="Enter your first name"
                                                    type="text">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium mb-1" for="clinic-user_last_name"><?php echo esc_html_e("Last Name",'kc-lang')?>
                                                    <span class="text-red-500">*</span>
                                                </label>
                                                <input id="clinic-user_last_name"
                                                    name="clinic_user_last_name"
                                                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-shadow"
                                                    placeholder="Enter your last name"
                                                    type="text">
                                            </div>
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium mb-1" for="clinic-name">Clinic Name
                                                <span class="text-red-500">*</span>
                                            </label>
                                            <input id="clinic-name"
                                                name="clinic_name"
                                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-shadow"
                                                placeholder="Enter your clinic name"
                                                type="text">
                                        </div>
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div>
                                                <label class="block text-sm font-medium mb-1" for="email">
                                                    Email Address <span class="text-red-500">*</span>
                                                </label>
                                                <input id="email"
                                                    name="clinic_email"
                                                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-shadow"
                                                    placeholder="<EMAIL>"

                                                    type="email" />
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium mb-1" for="phone">
                                                    Phone Number
                                                    <span class="text-red-500">*</span>
                                                </label>
                                                <input id="phone"
                                                    name="clinic_phone"
                                                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-shadow"
                                                    placeholder="+****************"
                                                    type="tel" />
                                            </div>
                                        </div>
                                        <div class="relative">
                                            <label class="block text-sm font-medium mb-1" for="password">
                                                Create
                                                Password
                                                <span class="text-red-500">*</span>
                                            </label>
                                            <div class="relative">
                                                <input id="password"
                                                    name="clinic_password"
                                                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-shadow"
                                                    placeholder="Enter your password"
                                                    type="password">
                                                <button type="button"
                                                    class="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="2"
                                                        stroke-linecap="round" stroke-linejoin="round"
                                                        class="lucide lucide-eye">
                                                        <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                                                        <circle cx="12" cy="12" r="3"></circle>
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="flex items-start gap-3">
                                            <input id="terms" class="mt-1" name="terms"
                                                type="checkbox">
                                            <label for="terms"
                                                class="text-sm text-gray-600">I
                                                agree to the <a href="#" class="text-purple-600 hover:text-purple-700">Terms
                                                    of
                                                    Service</a> and <a href="#"
                                                    class="text-purple-600 hover:text-purple-700">Privacy
                                                    Policy</a></label>
                                        </div>
                                    </div>
                                    <button type="submit"
                                        class="relative flex items-center justify-center w-full px-4 py-2 text-white bg-purple-600 rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2">
                                        <span class="buttonText">Complete Registration</span>
                                        <svg
                                            class="hidden  loader w-5 h-5 ml-2 animate-spin text-white"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24">
                                            <circle
                                                class="opacity-25"
                                                cx="12"
                                                cy="12"
                                                r="10"
                                                stroke="currentColor"
                                                stroke-width="4"></circle>
                                            <path
                                                class="opacity-75"
                                                fill="currentColor"
                                                d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"></path>
                                        </svg>
                                    </button>
                                    <p class="text-center text-gray-600">Already have an account? <a href="/ehr/login"
                                            class="text-purple-600 hover:text-purple-700 font-medium">Sign
                                            in instead</a></p>
                                </form>
                            </div>
                            <div class="border-t border-gray-200 pt-6 mt-8">
                                <h4
                                    class="text-sm font-semibold text-gray-900 mb-4">Included with your
                                    registration:</h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div class="flex items-start gap-3">
                                        <div class="rounded-lg bg-purple-100 p-2 mt-1">
                                            <svg class="w-4 h-4 text-purple-600" viewBox="0 0 24 24" fill="none"
                                                stroke="currentColor" stroke-width="2">
                                                <path d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <h5 class="font-medium text-gray-900">AI-Powered Analytics</h5>
                                            <p class="text-sm text-gray-600">Advanced insights into your practice
                                                performance</p>
                                        </div>
                                    </div>
                                    <div class="flex items-start gap-3">
                                        <div class="rounded-lg bg-blue-100 p-2 mt-1">
                                            <svg class="w-4 h-4 text-blue-600" viewBox="0 0 24 24" fill="none"
                                                stroke="currentColor" stroke-width="2">
                                                <path d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <h5 class="font-medium text-gray-900">Smart Security</h5>
                                            <p class="text-sm text-gray-600">Enterprise-grade protection for your data</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-6 flex items-center justify-center gap-2 text-sm text-gray-600">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                    class="lucide lucide-circle-alert w-4 h-4">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <line x1="12" x2="12" y1="8" y2="12"></line>
                                    <line x1="12" x2="12.01" y1="16" y2="16"></line>
                                </svg>
                                <span>Need help? <a href="https://help.medroid.ai/" target="_blank" class="text-purple-600 hover:text-purple-700">Contact our support team</a></span>
                            </div>
                        </div>
                        <div class="mt-8 text-center">
                            <div class="inline-flex items-center gap-2 text-sm text-gray-600">
                                <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>Secure payment processing</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-step hidden">
                    <div class="max-w-xl mx-auto p-6 bg-white rounded-lg shadow-sm">
                        <div class="mb-8">
                            <h1 class="text-2xl font-semibold mb-2">Clinic Details</h1>
                        </div>
                        <form class="space-y-6 clinic_details">
                            <div class="space-y-6">
                                <div><label class="block text-sm font-medium mb-2">Number of Additional Doctors <span
                                            class="text-red-500">*</span></label>
                                    <div class="flex items-center space-x-4 counter-widget">
                                        <button type="button" class="p-2 border rounded-lg decrement-btn">-</button>
                                        <span class="text-lg font-medium counter-value">0</span>
                                        <input type="hidden" class="doctor_count" value="0" name="doctor_count">
                                        <button type="button" class="p-2 border rounded-lg increment-btn">+</button>
                                    </div>

                                    <div class="mt-2 flex items-start space-x-2 text-sm text-gray-600">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round" class="lucide lucide-circle-alert w-4 h-4 mt-0.5">
                                            <circle cx="12" cy="12" r="10"></circle>
                                            <line x1="12" x2="12" y1="8" y2="12"></line>
                                            <line x1="12" x2="12.01" y1="16" y2="16"></line>
                                        </svg>
                                        <p>First doctor is included in the base plan. Additional doctors are <?= $currency_prefix ?>250/month
                                            each.</p>
                                    </div>
                                </div>

                                <button type="submit"
                                    class="relative flex items-center justify-center w-full px-4 py-2 text-white bg-purple-600 rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2">
                                    <span class="buttonText">Continue to Payment</span>
                                    <svg
                                        class="hidden  loader w-5 h-5 ml-2 animate-spin text-white"
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24">
                                        <circle
                                            class="opacity-25"
                                            cx="12"
                                            cy="12"
                                            r="10"
                                            stroke="currentColor"
                                            stroke-width="4"></circle>
                                        <path
                                            class="opacity-75"
                                            fill="currentColor"
                                            d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"></path>
                                    </svg>
                                </button>
                                <div class="mt-8 pt-6 border-t">
                                    <h3 class="font-medium mb-4">Included with your clinic
                                        setup:</h3>
                                    <div class="space-y-4">
                                        <div class="flex items-start space-x-3">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24"
                                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                stroke-linejoin="round"
                                                class="lucide lucide-stethoscope w-5 h-5 text-purple-600 mt-1">
                                                <path d="M4.8 2.3A.3.3 0 1 0 5 2H4a2 2 0 0 0-2 2v5a6 6 0 0 0 6 6v0a6 6 0 0 0 6-6V4a2 2 0 0 0-2-2h-1a.2.2 0 1 0 .3.3"></path>
                                                <path d="M8 15v1a6 6 0 0 0 6 6v0a6 6 0 0 0 6-6v-4"></path>
                                                <circle cx="20" cy="10" r="2"></circle>
                                            </svg>
                                            <div>
                                                <h4 class="font-medium">Advanced Scheduling</h4>
                                                <p class="text-sm text-gray-600">Smart appointment management for multiple
                                                    doctors</p>
                                            </div>
                                        </div>
                                        <div class="flex items-start space-x-3">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24"
                                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                stroke-linejoin="round"
                                                class="lucide lucide-users w-5 h-5 text-purple-600 mt-1">
                                                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                                                <circle cx="9" cy="7" r="4"></circle>
                                                <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                                                <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                                            </svg>
                                            <div>
                                                <h4 class="font-medium">AI Scribe</h4>
                                                <p class="text-sm text-gray-600">Listens to the appointment and intelligently generates letters & codes</p>
                                            </div>
                                        </div>
                                        <div class="flex items-start space-x-3">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24"
                                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                stroke-linejoin="round"
                                                class="lucide lucide-calendar w-5 h-5 text-purple-600 mt-1">
                                                <path d="M8 2v4"></path>
                                                <path d="M16 2v4"></path>
                                                <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                                                <path d="M3 10h18"></path>
                                            </svg>
                                            <div>
                                                <h4 class="font-medium">Booking Widget</h4>
                                                <p class="text-sm text-gray-600">User friendly interface for your patients to book appointments on your website</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex items-center justify-center space-x-2 text-sm text-purple-600">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-message-circle w-4 h-4">
                                        <path d="M7.9 20A9 9 0 1 0 4 16.1L2 22Z"></path>
                                    </svg>
                                    <span>Need help setting up your clinic?</span><a href="#"
                                        class="font-medium hover:underline">Contact
                                        our support team</a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            <?php
            endif;
            ?>
        </div>
    </div>
</div>