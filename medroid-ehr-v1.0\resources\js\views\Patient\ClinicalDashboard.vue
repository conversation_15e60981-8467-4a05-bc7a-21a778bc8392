<template>
  <div class="p-6 bg-white min-h-screen">
    <!-- Header Section -->
    <header class="mb-6">
      <div class="flex justify-between items-start">
        <div>
          <h1 class="text-2xl font-semibold text-gray-800 mb-2">
            {{ patientDetails.patient_name }}
          </h1>
          <p class="text-gray-500">
            {{ patientMetaData.dob }}
            <template v-if="patientDetails.age"
              >| {{ patientDetails.age }}</template
            >
            <template v-if="patientDetails.gender"
              >| {{ patientDetails.gender }}</template
            >
            <template v-if="patientDetails.phone"
              >• {{ patientDetails.phone }}</template
            >
          </p>
        </div>
        <button
          class="p-2 hover:bg-gray-50 rounded-full transition-colors"
          @click="handleEdit"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="w-5 h-5 text-gray-400"
          >
            <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"></path>
          </svg>
        </button>
      </div>

      <!-- Allergies Alert -->
      <div
        v-if="encounterData.allergies?.length"
        class="mt-4 flex items-center bg-red-50 text-red-600 px-4 py-2 rounded-lg"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="w-4 h-4 mr-2"
        >
          <circle cx="12" cy="12" r="10" />
          <line x1="12" x2="12" y1="8" y2="12" />
          <line x1="12" x2="12.01" y1="16" y2="16" />
        </svg>
        <span class="font-medium">
          Allergies: {{ encounterData.allergies.join(", ") }}
        </span>
      </div>
    </header>

    <!-- Navigation Tabs -->
    <nav class="flex border-b overflow-x-auto">
      <button
        v-for="tab in tabs"
        :key="tab.id"
        @click="setActiveTab(tab.id)"
        class="px-4 py-2 text-sm whitespace-nowrap"
        :class="[
          currentTab === tab.id
            ? 'text-blue-600 border-b-2 border-blue-600'
            : 'text-gray-500 hover:text-gray-700',
        ]"
      >
        {{ tab.label }}
      </button>
    </nav>

    <!-- <div v-if="isLoading" class="flex justify-center items-center h-64">
      <div
        class="animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent"
      ></div>
    </div> -->

    <!-- Tab Content -->
    <keep-alive>
      <component
        :is="currentTabComponent"
        :encounter-id="encounterId"
        :patient-id="patientId"
        :isEncounterTemp="isEncounterTemp"
        :encounter-data="encounterData"
        :tab-content="currentTab"
        @update:allergies="updateAllergies"
        @loading="handleTabLoading"
      />
    </keep-alive>

    <EditPatientModal
      v-if="showEditPatientModal"
      :showEditPatientModal="showEditPatientModal"
      :patient-details="patientDetails"
      :patient-meta-data="patientMetaData"
      :is-encounter-temp="isEncounterTemp"
      @update:showEditPatientModal="showEditPatientModal = $event"
      @patient-updated="handlePatientUpdated"
    />
  </div>
</template>

<script>
import { post, get } from "../../config/request";
import { displayMessage, displayErrorMessage } from "../../utils/message";
import SummaryTab from "../../components/AppointmentDashboard/SummaryTab.vue";
import ConsultationsTab from "../../components/AppointmentDashboard/ConsultationsTab.vue";
import PrescriptionsTab from "../../components/AppointmentDashboard/PrescriptionsTab.vue";
import DocumentsTab from "../../components/AppointmentDashboard/DocumentsTab.vue";
import EditPatientModal from "../../components/Patient/EditPatientModal.vue";

export default {
  name: "ClinicalDashboard",
  components: {
    SummaryTab,
    ConsultationsTab,
    PrescriptionsTab,
    DocumentsTab,
    EditPatientModal,
  },

  data() {
    return {
      isLoading: false,
      encounterId: 0,
      patientId: 0,
      patientDetails: {},
      encounterData: {},
      currentTab: "summary",
      isEncounterTemp: false,
      showEditPatientModal: false,
      patientMetaData: {}, // This will store patient metadata
      tabs: [
        { id: "summary", label: "Summary", component: SummaryTab },
        {
          id: "consultations",
          label: "Consultations",
          component: ConsultationsTab,
        },
        {
          id: "prescriptions",
          label: "Prescriptions",
          component: PrescriptionsTab,
        },
        { id: "documents", label: "Documents", component: DocumentsTab },
      ],
    };
  },

  mounted() {
    this.init();
  },

  computed: {
    currentTabComponent() {
      const tab = this.tabs.find((t) => t.id === this.currentTab);
      return tab?.component || SummaryTab;
    },
  },

  methods: {
    setActiveTab(tabId) {
      if (this.currentTab === tabId) return;
      this.isLoading = true;
      this.currentTab = tabId;
    },

    updateAllergies(allergies) {
      this.$set(this.encounterData, "allergies", allergies || []);
    },

    async init() {
      try {
        if (this.$route?.query?.isEncounterTemp !== undefined) {
          this.isEncounterTemp = this.$route.query.isEncounterTemp == 1;
        }
        this.patientId = this.$route.params.patient_id || 0;

        if (this.patientId) {
          this.getBasicDetails();
        } else {
          displayErrorMessage("Error initializing dashboard.");
        }
      } catch (error) {
        console.error("Error initializing dashboard:", error);
      }
    },

    handleTabLoading(isLoading) {
      this.isLoading = isLoading;
    },

    async loadTabs() {
      try {
        const response = await get("get_encounter_tabs", {
          encounter_id: this.encounterId,
        });

        if (response?.data?.status) {
          this.tabs = response.data.data.map((tab) => ({
            id: tab.id,
            label: this.getTabTitle(tab.type),
            component: this.getTabComponent(tab.type),
          }));
        } else {
          displayErrorMessage("Failed to load tabs.");
        }
      } catch (error) {
        console.error("Error loading tabs:", error);
        displayErrorMessage("Failed to load tabs.");
      }
    },

    getTabTitle(type) {
      const titles = {
        summary: "Summary",
        // Add other tab titles here
      };
      return titles[type] || "Unknown Tab";
    },

    getTabComponent(type) {
      const components = {
        summary: SummaryTab,
      };
      return components[type] || SummaryTab;
    },

    getBasicDetails() {
      get("patient_details", {
        id: this.patientId,
      })
        .then((data) => {
          if (data.data.status !== undefined && data.data.status === true) {
            this.showCustomField = true;
            console.log("data.data", data.data);
            this.patientDetails = data.data.data.patientDetails;
            this.patientMetaData = data.data.data.patientMetaData;
            
            this.hideClinicalDetailsToPatient =
              (data.data.hideInPatient == "true" ||
                data.data.hideInPatient == true) &&
              this.getUserRole === "patient"
                ? true
                : false;
            if (!this.hideClinicalDetailsToPatient) {
              // this.getMedicalRecords(this.encounterId);
            }

            this.encounterData.allergies = [];
          } else if (data?.data?.status === false) {
            this.$router.push({ name: "/" });
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },

    handleEdit() {
      this.showEditPatientModal = true;
    },

    handlePatientUpdated(updatedDetails) {
      // Update the local patient details
      this.patientDetails = { ...this.patientDetails, ...updatedDetails };

      // Show success message
      this.$swal.fire({
        icon: "success",
        title: "Success",
        text: "Patient details updated successfully",
      });
    },
  },
};
</script>
