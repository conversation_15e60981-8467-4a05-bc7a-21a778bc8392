<template>
  <div>
    <form ref="contactForm" @submit.prevent="handleSubmit">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="space-y-1">
            <label for="name" class="block text-sm font-medium text-gray-700">Name <span class="text-red-500">*</span></label>
            <input
              type="text"
              id="name"
              v-model="contact.name"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
              :class="{ 'border-red-500 ring-1 ring-red-500': validationErrors.name }"
              required
            />
            <p v-if="validationErrors.name" class="text-xs text-red-500">
              {{ validationErrors.name }}
            </p>
          </div>

          <div class="space-y-1">
            <label for="type" class="block text-sm font-medium text-gray-700">Type <span class="text-red-500">*</span></label>
            <select
              id="type"
              v-model="contact.type"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
              :class="{ 'border-red-500 ring-1 ring-red-500': validationErrors.type }"
              required
            >
              <option value="general">General</option>
              <option value="clinic">Clinic</option>
              <option value="doctor">Doctor</option>
              <option value="pharmacy">Pharmacy</option>
            </select>
            <p v-if="validationErrors.type" class="text-xs text-red-500">
              {{ validationErrors.type }}
            </p>
          </div>

          <div class="space-y-1">
            <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
            <input
              type="email"
              id="email"
              v-model="contact.email"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
              :class="{ 'border-red-500 ring-1 ring-red-500': validationErrors.email }"
            />
            <p v-if="validationErrors.email" class="text-xs text-red-500">
              {{ validationErrors.email }}
            </p>
          </div>

          <div class="space-y-1">
            <label for="phone" class="block text-sm font-medium text-gray-700">Phone</label>
            <input
              type="text"
              id="phone"
              v-model="contact.phone"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
              :class="{ 'border-red-500 ring-1 ring-red-500': validationErrors.phone }"
            />
            <p v-if="validationErrors.phone" class="text-xs text-red-500">
              {{ validationErrors.phone }}
            </p>
          </div>

          <div class="space-y-1 md:col-span-2">
            <label for="address" class="block text-sm font-medium text-gray-700">Address</label>
            <textarea
              id="address"
              v-model="contact.address"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
              :class="{ 'border-red-500 ring-1 ring-red-500': validationErrors.address }"
              rows="2"
            ></textarea>
            <p v-if="validationErrors.address" class="text-xs text-red-500">
              {{ validationErrors.address }}
            </p>
          </div>

          <div class="space-y-1">
            <label for="city" class="block text-sm font-medium text-gray-700">City</label>
            <input
              type="text"
              id="city"
              v-model="contact.city"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
              :class="{ 'border-red-500 ring-1 ring-red-500': validationErrors.city }"
            />
            <p v-if="validationErrors.city" class="text-xs text-red-500">
              {{ validationErrors.city }}
            </p>
          </div>

          <div class="space-y-1">
            <label for="state" class="block text-sm font-medium text-gray-700">State</label>
            <input
              type="text"
              id="state"
              v-model="contact.state"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
              :class="{ 'border-red-500 ring-1 ring-red-500': validationErrors.state }"
            />
            <p v-if="validationErrors.state" class="text-xs text-red-500">
              {{ validationErrors.state }}
            </p>
          </div>

          <div class="space-y-1">
            <label for="postal_code" class="block text-sm font-medium text-gray-700">Postal Code</label>
            <input
              type="text"
              id="postal_code"
              v-model="contact.postal_code"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
              :class="{ 'border-red-500 ring-1 ring-red-500': validationErrors.postal_code }"
            />
            <p v-if="validationErrors.postal_code" class="text-xs text-red-500">
              {{ validationErrors.postal_code }}
            </p>
          </div>

          <div class="space-y-1">
            <label for="country" class="block text-sm font-medium text-gray-700">Country</label>
            <input
              type="text"
              id="country"
              v-model="contact.country"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
              :class="{ 'border-red-500 ring-1 ring-red-500': validationErrors.country }"
            />
            <p v-if="validationErrors.country" class="text-xs text-red-500">
              {{ validationErrors.country }}
            </p>
          </div>

          <div class="space-y-1 md:col-span-2">
            <label for="clinic_id" class="block text-sm font-medium text-gray-700">Associate with Clinic</label>
            <select
              id="clinic_id"
              v-model="contact.clinic_id"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
              :class="{ 'border-red-500 ring-1 ring-red-500': validationErrors.clinic_id }"
              :disabled="currentUserRole !== 'administrator'"
            >
              <option :value="null" v-if="currentUserRole === 'administrator'">Global Contact (visible to all)</option>
              <option v-for="clinic in clinicList" :key="clinic.id" :value="clinic.id">
                {{ clinic.name }}
              </option>
            </select>
            <p v-if="validationErrors.clinic_id" class="text-xs text-red-500">
              {{ validationErrors.clinic_id }}
            </p>
            <p class="text-xs text-gray-500 mt-1" v-if="currentUserRole === 'administrator'">
              Global contacts are visible to all clinics. Clinic-specific contacts are only visible to the selected clinic.
            </p>
            <p class="text-xs text-gray-500 mt-1" v-else-if="currentUserRole === 'kiviCare_clinic_admin'">
              Contacts will be visible to all staff members in your clinic.
            </p>
            <p class="text-xs text-gray-500 mt-1" v-else-if="currentUserRole === 'kiviCare_doctor'">
              Contacts will be visible to all staff members in your clinic.
            </p>
          </div>

          <div class="space-y-1 md:col-span-2">
            <label for="notes" class="block text-sm font-medium text-gray-700">Notes</label>
            <textarea
              id="notes"
              v-model="contact.notes"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
              :class="{ 'border-red-500 ring-1 ring-red-500': validationErrors.notes }"
              rows="3"
            ></textarea>
            <p v-if="validationErrors.notes" class="text-xs text-red-500">
              {{ validationErrors.notes }}
            </p>
          </div>

          <div class="md:col-span-2">
            <div class="flex items-center">
              <button 
                type="button"
                @click="contact.status = !contact.status" 
                class="relative inline-flex h-6 w-11 items-center rounded-full" 
                :class="contact.status ? 'bg-black' : 'bg-gray-300'"
              >
                <span 
                  class="inline-block h-4 w-4 transform rounded-full bg-white transition"
                  :class="contact.status ? 'translate-x-6' : 'translate-x-1'"
                >
                </span>
              </button>
              <span class="ml-3 text-sm font-medium text-gray-700">
                {{ contact.status ? 'Active' : 'Inactive' }}
              </span>
            </div>
          </div>
        </div>
      </form>

    <!-- Form buttons -->
    <div class="mt-6 flex justify-end space-x-3">
      <button 
        type="button"
        @click="closeModal" 
        class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200"
      >
        Cancel
      </button>
      <button 
        type="button"
        :disabled="isSaving" 
        @click="handleSubmit"
        class="flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 disabled:opacity-50"
      >
        <span v-if="isSaving" class="mr-2">
          <svg class="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </span>
        {{ contactId ? 'Update' : 'Save' }}
      </button>
    </div>
  </div>
</template>

<script>
import { get, post } from '../../config/request'
import { displayMessage, displayErrorMessage } from '../../utils/message'

export default {
    name: 'ContactCreate',
    props: {
        contactId: {
            type: [Number, String],
            required: false,
            default: null
        },
        selectedContactData: {
            type: Object,
            required: false,
            default: null
        }
    },
    data() {
        return {
            contact: this.defaultContactData(),
            validationErrors: {},
            isSaving: false,
            clinicList: [],
            isAdmin: false
        }
    },
    created() {
        // Check if user is admin or clinic admin
        this.isAdmin = ['administrator', 'kiviCare_clinic_admin'].includes(this.currentUserRole)
        
        if (this.contactId) {
            this.loadContactData()
        } else if (this.selectedContactData) {
            this.contact = { ...this.defaultContactData(), ...this.selectedContactData }
        }
        
        // For admin, load all clinics. For clinic admin, auto-select their clinic
        if (this.currentUserRole === 'administrator') {
            this.getClinicList()
        } else if (this.currentUserRole === 'kiviCare_clinic_admin') {
            this.getCurrentUserClinic()
        } else if (this.currentUserRole === 'kiviCare_doctor') {
            // Doctor can add contacts, but they're automatically associated with their clinic
            this.getDoctorClinic()
        }
    },
    computed: {
        currentUserRole() {
            return this.$store.state.userData.user_role || ''
        }
    },
    methods: {
        defaultContactData() {
            return {
                name: '',
                type: 'general',
                email: '',
                phone: '',
                address: '',
                city: '',
                state: '',
                postal_code: '',
                country: '',
                clinic_id: null,
                notes: '',
                status: true
            }
        },
        loadContactData() {
            get('contact_edit', { id: this.contactId })
                .then(response => {
                    if (response.data.status) {
                        this.contact = { ...this.defaultContactData(), ...response.data.data }
                        // Convert status to boolean
                        this.contact.status = this.contact.status === '1' || this.contact.status === 1 || this.contact.status === true
                    } else {
                        displayErrorMessage(response.data.message)
                    }
                })
                .catch(error => {
                    displayErrorMessage(error)
                })
        },
        getClinicList() {
            get('contact_clinic_list')
                .then(response => {
                    if (response.data.status) {
                        this.clinicList = response.data.data
                    } else {
                        this.clinicList = []
                    }
                })
                .catch(error => {
                    displayErrorMessage(error)
                    this.clinicList = []
                })
        },
        
        getCurrentUserClinic() {
            // For clinic admin, get their clinic ID and set it by default
            // We'll use the user's clinic_id from store if available
            const userData = this.$store.state.userData.user
            if (userData && userData.clinic_id) {
                this.contact.clinic_id = userData.clinic_id
                
                // Also get clinic name for display
                this.getClinicList() 
            }
        },
        
        getDoctorClinic() {
            // For doctors, get their associated clinic
            const userData = this.$store.state.userData.user
            if (userData && userData.clinic_id) {
                this.contact.clinic_id = userData.clinic_id
                
                // Load this clinic data just to have the name available
                get('contact_clinic_list')
                    .then(response => {
                        if (response.data.status) {
                            this.clinicList = response.data.data.filter(clinic => 
                                clinic.id === userData.clinic_id
                            )
                        }
                    })
            }
        },
        validateForm() {
            this.validationErrors = {}
            let isValid = true
            
            if (!this.contact.name.trim()) {
                this.validationErrors.name = this.$t('contacts.name_required')
                isValid = false
            }
            
            if (!this.contact.type) {
                this.validationErrors.type = this.$t('contacts.type_required')
                isValid = false
            }
            
            if (this.contact.email && !this.isValidEmail(this.contact.email)) {
                this.validationErrors.email = this.$t('contacts.invalid_email')
                isValid = false
            }
            
            if (!this.contact.email && !this.contact.phone) {
                this.validationErrors.email = this.$t('contacts.email_or_phone_required')
                this.validationErrors.phone = this.$t('contacts.email_or_phone_required')
                isValid = false
            }
            
            return isValid
        },
        isValidEmail(email) {
            const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
            return re.test(String(email).toLowerCase())
        },
        handleSubmit() {
            if (!this.validateForm()) {
                return
            }
            
            this.isSaving = true
            
            // Convert status to 0/1 for API
            const contactData = {
                ...this.contact,
                status: this.contact.status ? 1 : 0
            }
            
            // Log the data being sent for debugging
            console.log('Saving contact data:', contactData);
            
            post('contact_save', contactData)
                .then(response => {
                    console.log('Contact save response:', response);
                    if (response.data.status) {
                        displayMessage(this.contactId 
                            ? 'Contact updated successfully' 
                            : 'Contact added successfully'
                        )
                        this.$emit('refresh-contacts')
                        this.closeModal()
                    } else {
                        displayErrorMessage(response.data.message)
                    }
                })
                .catch(error => {
                    console.error('Contact save error:', error);
                    displayErrorMessage(error)
                })
                .finally(() => {
                    this.isSaving = false
                })
        },
        closeModal() {
            this.contact = this.defaultContactData()
            this.validationErrors = {}
            this.$emit('close-modal')
        }
    }
}
</script>