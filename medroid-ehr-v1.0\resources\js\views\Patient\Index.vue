<template>
  <div class="min-h-screen p-6 bg-gradient-to-br from-pink-50 to-purple-50">
    <!-- Header Section -->
    <div class="mb-8 flex justify-between items-center">
      <div class="flex items-center gap-4">
        <router-link :to="{ path: '/' }"
          class="flex items-center gap-2 px-4 py-2 bg-black text-sm text-white rounded-lg shadow-sm hover:bg-gray-800">
          <i class="fa fa-arrow-left"></i>
          <span>Back</span>
        </router-link>
        <h1 class="text-2xl font-semibold text-gray-800">
          {{ formTranslation.patient.patients_lists }}
        </h1>
      </div>
      <div class="flex gap-2">

        <!-- data import module -->
        <module-data-import v-if="
          userData.addOns.kiviPro &&
          kcCheckPermission('patient_add') &&
          kivicareCompareVersion(requireProVersion, userData.pro_version)" ref="module_data_import"
          :required-data="importRequiredData" :module-name="formTranslation.common.patient" module-type="patient"
          @reloadList="getPatientList"></module-data-import>

        <!-- data export module -->
        <module-data-export v-if="kcCheckPermission('patient_export')" :module-data="patientList.data"
          :module-name="formTranslation.dashboard.patients" module-type="patient">
          <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="7 10 12 15 17 10"></polyline>
            <line x1="12" x2="12" y1="15" y2="3"></line>
          </svg>
          {{ formTranslation.common.import }}
        </module-data-export>

        <button v-if="kcCheckPermission('patient_add')"
          class="flex items-center gap-2 px-4 py-2 bg-black text-sm text-white rounded-lg hover:bg-gray-800"
          @click="handlePatientAddModel">
          <i class="fa fa-plus"></i>
          {{ formTranslation.patient.add_patient }}
        </button>
      </div>
    </div>

    <!-- Search Bar -->
    <div class="relative mb-6">
      <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
      <input v-model="serverParams.searchTerm" @input="globalFilter({ searchTerm: serverParams.searchTerm })"
        placeholder="Search patients..."
        class="w-full pl-10 pr-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400" />
    </div>

    <!-- Filters Grid -->
    <div class="grid grid-cols-1 md:grid-cols-6 lg:grid-cols-6 gap-4 mb-6">
      <!-- ID Filter -->
      <div class="relative">
        <input v-model="serverParams.columnFilters.uid" @input="debouncedColumnFilter" :placeholder="p_uid
          ? formTranslation.patient.unique_id
          : formTranslation.common.id
          "
          class="w-full px-4 py-2 pr-8 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400" />
        <button v-if="serverParams.columnFilters.uid" @click="clearFilter('uid')"
          class="absolute right-3 top-1/4 -translate-y-1/2 text-gray-400 hover:text-gray-600">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- Name Filter -->
      <div class="relative">
        <input v-model="serverParams.columnFilters.display_name" @input="debouncedColumnFilter"
          placeholder="Filter by name"
          class="w-full px-4 py-2 pr-8 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400" />
        <button v-if="serverParams.columnFilters.display_name" @click="clearFilter('display_name')"
          class="absolute right-3 top-1/4 -translate-y-1/2 text-gray-400 hover:text-gray-600">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- Email Filter -->
      <div class="relative">
        <input v-model="serverParams.columnFilters.user_email" @input="debouncedColumnFilter"
          placeholder="Filter by email"
          class="w-full px-4 py-2 pr-8 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400" />
        <button v-if="serverParams.columnFilters.user_email" @click="clearFilter('user_email')"
          class="absolute right-3 top-1/4 -translate-y-1/2 text-gray-400 hover:text-gray-600">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- Clinic Filter -->
      <div class="relative">
        <select v-model="serverParams.columnFilters.clinic_name" @change="debouncedColumnFilter"
          class="w-full px-4 py-2 pr-8 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400 appearance-none">
          <option value="">All Clinics</option>
          <option v-for="clinic in filterClinic" :key="clinic.value" :value="clinic.value">
            {{ clinic.text }}
          </option>
        </select>
        <button v-if="serverParams.columnFilters.clinic_name" @click="clearFilter('clinic_name')"
          class="absolute right-3 top-1/4 -translate-y-1/2 text-gray-400 hover:text-gray-600">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- Status Filter -->
      <div class="relative">
        <select v-model="serverParams.columnFilters.user_status" @change="debouncedColumnFilter"
          class="w-full px-4 py-2 pr-8 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400 appearance-none">
          <option value="" selected>All Status</option>
          <option value="0">Active</option>
          <option value="1">Inactive</option>
        </select>
        <button v-if="serverParams.columnFilters.user_status" @click="clearFilter('user_status')"
          class="absolute right-3 top-1/4 -translate-y-1/2 text-gray-400 hover:text-gray-600">
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>

    <!-- Bulk Actions Section -->
    <div v-if="selectedRows.length > 0" class="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-4">
          <span class="text-sm font-medium text-gray-700">
            {{ selectedRows.length }} patient{{ selectedRows.length > 1 ? 's' : '' }} selected
          </span>
          <div class="flex gap-2">
            <button @click="confirmBulkDelete" 
              class="px-3 py-1 bg-red-600 text-white rounded-md text-sm hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed">
              Delete Selected
            </button>
          </div>
        </div>
        <button @click="selectedRows = []" class="text-sm text-gray-500 hover:text-gray-700">
          Clear selection
        </button>
      </div>
    </div>

    <!-- Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-x-auto">
      <table class="w-full">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              <input type="checkbox" class="rounded border-gray-300" @change="selectAll" :checked="allSelected" />
            </th>
            <th v-for="column in patientList.column" :key="column.field"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              @click="handleSort(column.field)">
              {{ column.label }}
            </th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
          <tr v-for="(row, index) in patientList.data" :key="row.ID" class="hover:bg-gray-50" :class="{'bg-blue-50': selectedRows.some(selected => selected.ID === row.ID)}">
            <td class="px-6 py-4 whitespace-nowrap">
              <input type="checkbox" class="rounded border-gray-300" v-model="selectedRows" :value="row" @change="handleRowSelection" />
            </td>
            <td class="px-6 py-4 whitespace-nowrap">{{ row.u_id }}</td>
            <td class="px-6 py-4 whitespace-nowrap">
              <router-link v-if="kcCheckPermission('patient_edit')"
                :to="{ name: 'patient-profile-view', params: { id: row.ID } }"
                class="text-purple-600 hover:text-purple-900">
                {{ row.display_name }}
              </router-link>
              <span v-else>{{ row.display_name }}</span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">{{ row.clinic_name }}</td>
            <td class="px-6 py-4 whitespace-nowrap">{{ row.user_email }}</td>
            <td class="px-4 py-3">
              <div class="relative actions-dropdown">
                <button @click="showActionsMenu = row.ID"
                  class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring h-8 px-3 text-xs bg-black text-white hover:bg-gray-800">
                  Actions
                </button>

                <!-- Actions Dropdown -->
                <div v-if="showActionsMenu === row.ID"
                  class="absolute right-0 mt-2 w-48 rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-10">
                  <!-- Appointments -->
                  <router-link v-if="kcCheckPermission('appointment_list')" :to="{
                    name: 'patient-appointment-list',
                    params: { patient_id: row.ID },
                  }" class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    <i class="fas fa-calendar-week w-4 h-4 mr-2"></i>
                    {{ formTranslation.appointments.appointments }}
                  </router-link>

                  <!-- Consultations -->
                  <router-link v-if="kcCheckPermission('patient_encounter_list')" :to="{
                    name: 'patient-encounter',
                    params: { patient_id: row.ID },
                  }" class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    <i class="fa fa-calendar-check w-4 h-4 mr-2"></i>
                    {{ formTranslation.patient_encounter.encounters }}
                  </router-link>

                  <!-- Medical Reports -->
                  <router-link v-if="
                    userData.addOns.kiviPro &&
                    kcCheckPermission('patient_report')
                  " :to="{
                    name: 'patient-medical-report_id',
                    params: { patient_id: row.ID },
                  }" class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    <i class="fa fa-file w-4 h-4 mr-2"></i>
                    {{ formTranslation.reports.reports }}
                  </router-link>

                  <!-- Resend Credentials -->
                  <button v-if="kcCheckPermission('patient_edit')" @click="resendRequest(row.ID)"
                    class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    <i class="fa fa-paper-plane w-4 h-4 mr-2"></i>
                    {{ formTranslation.receptionist.resend_credential }}
                  </button>

                  <!-- Custom Forms -->
                  <template v-if="
                    userData.addOns.kiviPro &&
                    customFormCondition(row, custom_form_data)
                  ">
                    <button v-for="(custom_form_data, key) in row.custom_forms" :key="key"
                      @click="customFormOpen(row, custom_form_data)"
                      class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      <i :class="[
                        custom_form_data.name?.icon || 'fas fa-book-medical',
                        'w-4 h-4 mr-2',
                      ]"></i>
                      {{ custom_form_data.name?.text || "" }}
                    </button>
                  </template>

                  <!-- Delete Patient -->
                  <button v-if="kcCheckPermission('patient_delete')" @click="deletePatientData(index + 1)"
                    class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    <i class="fa fa-trash w-4 h-4 mr-2 text-red-500"></i>
                    {{ formTranslation.clinic_schedule.dt_lbl_dlt }}
                  </button>
                </div>
              </div>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- Pagination -->
      <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
        <div class="flex items-center gap-2">
          <span class="text-sm text-gray-700">Rows per page:</span>
          <select v-model="serverParams.perPage"
            @change="onPerPageChange({ currentPerPage: serverParams.perPage, currentPage: serverParams.page })"
            class="border border-gray-300 rounded-md text-sm p-1">
            <option>10</option>
            <option>25</option>
            <option>50</option>
          </select>
        </div>
        <div class="flex items-center gap-4">
          <span class="text-sm text-gray-700">
            Page {{ serverParams.page }} of
            {{ Math.ceil(totalRows / serverParams.perPage) }}
          </span>
          <div class="flex gap-2">
            <button class="p-1 rounded hover:bg-gray-100 disabled:opacity-50" :disabled="serverParams.page === 1"
              @click="onPageChange({ currentPage: serverParams.page - 1 })">
              <i class="fa fa-chevron-left text-gray-600"></i>
            </button>
            <button class="p-1 rounded hover:bg-gray-100 disabled:opacity-50" :disabled="serverParams.page >= Math.ceil(totalRows / serverParams.perPage)
              " @click="onPageChange({ currentPage: serverParams.page + 1 })">
              <i class="fa fa-chevron-right text-gray-600"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Modals and Additional Components -->
    <CustomForm v-if="patientCustomFormModal" :data="patientCustomFormData" :viewMode="patientCustomFormViewMode"
      :customFormModal="patientCustomFormModal" @closeModal="patientCustomFormModal = false" />

    <module-data-import v-if="showImportModal" :is-import-modal-open="showImportModal" ref="moduleDataImport"
      @update:isModalOpen="showImportModal = $event" @close="closeImportModal" @reloadList="getPatientList"
      :required-data="importRequiredData" :module-name="formTranslation.common.patient" module-type="patient" />

    <AddPatientModal :show="showAddPatientModal" :editMode="false" @close="showAddPatientModal = false"
      @patient-saved="handlePatientSaved" />
  </div>
</template>

<script>
import { post, get } from "../../config/request";
import CustomForm from "../CustomForm/Form.vue";
import AddPatientModal from "../../components/Patient/AddPatientModal";

export default {
  components: { CustomForm, AddPatientModal },
  data() {
    return {
      pageLoader: true,
      showAddPatientModal: false,
      patientList: {
        column: [],
        data: [],
      },
      p_uid: 0,
      showActionsMenu: null,
      totalRows: false,
      selectedRows: [], // Array to store selected row IDs
      selectAllChecked: false, // For the select all checkbox state
      serverParams: {
        columnFilters: {
          uid: "",
          display_name: "",
          user_email: "",
          clinic_name: "",
          user_status: "",
        },
        sort: [{ field: "uid", type: "asc" }],
        page: 1,
        perPage: 10,
        searchTerm: "",
      },
      oldServerParams: {
        columnFilters: {},
        searchTerm: "",
        perPage: 10,
      },
      filterClinic: [],
      debouncedColumnFilter: null,
      debouncedGlobalSearch: null,
      showImportModal: false,
      patientCustomFormData: {},
      patientCustomFormModal: false,
      patientCustomFormViewMode: false,
      importRequiredData: [
        { label: "First Name", value: "first_name" },
        { label: "Last Name", value: "last_name" },
        { label: "Email", value: "email" },
        { label: "Country Calling Code", value: "country_calling_code" },
        { label: "Country Code", value: "country_code" },
        { label: "Contact", value: "contact" },
        { label: "Gender", value: "gender" },
      ],
    };
  },
  created() {
    this.debouncedColumnFilter = _.debounce(this.handleColumnFilter, 300);
    this.debouncedGlobalSearch = _.debounce(this.handleGlobalSearch, 300);
  },
  mounted() {
    this.init();
    document.addEventListener("click", this.closeDropdown);
  },
  beforeDestroy() {
    document.removeEventListener("click", this.closeDropdown);
  },
  methods: {
    init: function () {
      this.getUniqueSetting();
      this.patientList = this.defaultPatientList();
      this.globalCheckboxApplyData = this.defaultGlobalCheckboxApplyData();
      this.globalCheckboxApplyDataActions =
        this.defaultGlobalCheckboxApplyDataActions();

      // Initialize clinics immediately
      this.filterClinic = this.clinics.map((clinic) => ({
        value: clinic.id,
        text: clinic.label,
      }));

      this.getPatientList();
      this.getDynamicTranslation();
    },
    defaultPatientList: function () {
      return {
        column: [
          {
            field: "uid",
            label: this.p_uid
              ? this.formTranslation.patient.unique_id
              : this.formTranslation.common.id,
            sortable: !this.p_uid,
            width: "100px",
            filterOptions: {
              enabled: true,
              placeholder: this.p_uid
                ? this.formTranslation.patient.unique_id
                : this.formTranslation.common.id,
              filterValue: "",
            },
          },
          {
            label: this.formTranslation.patient.dt_lbl_name,
            field: "display_name",
            filterOptions: {
              enabled: true,
              placeholder: this.formTranslation.patient.dt_plh_name_filter,
              filterValue: "",
            },
          },
          {
            field: "clinic_name",
            label: this.formTranslation.patient.clinic,
            sortable: false,
            filterOptions: {
              enabled:
                this.userData && this.userData.addOns.kiviPro &&
                ["administrator", "doctor"].includes(this.getUserRole()),
              filterValue: "",
              filterDropdownItems: this.filterClinic,
            },
          },
          {
            label: this.formTranslation.patient.dt_lbl_email,
            field: "user_email",
            filterOptions: {
              enabled: true,
              placeholder: this.formTranslation.patient.dt_plh_email_fltr,
              filterValue: "",
            },
          },
          // {
          //   label: this.formTranslation.patient.dt_lbl_mobile_number,
          //   field: 'mobile_number',
          //   sortable: false,
          //   width: '200px',
          //   filterOptions: {
          //     enabled: true,
          //     placeholder: this.formTranslation.patient.dt_plh_mobile_fltr_number,
          //     filterValue: '',
          //   },
          // },
          // {
          //   label: this.formTranslation.patient.dt_lbl_registered,
          //   field: 'user_registered',
          //   filterOptions: {
          //     enabled: false,
          //     placeholder: this.formTranslation.patient.dt_plh_date,
          //     filterValue: '',
          //   },
          // },
          // {
          //   field: 'user_status',
          //   label: this.formTranslation.service.dt_lbl_status,
          //   filterOptions: {
          //     enabled: true, // enable filter for this column
          //     placeholder: this.formTranslation.static_data.dt_lbl_plh_sr_fltr_status,
          //     filterDropdownItems: [
          //       { value: '0', text: this.formTranslation.common.active },
          //       { value: '1', text: this.formTranslation.common.inactive }
          //     ],
          //     filterValue: '',
          //   },
          //   html: true
          // },
          {
            label: this.formTranslation.patient.dt_lbl_action,
            field: "action",
            sortable: false,
            html: true,
          },
        ],
        data: [],
      };
    },
    closeDropdown(e) {
      if (!e.target.closest(".actions-dropdown")) {
        this.showActionsMenu = null;
      }
    },
    getPatientList: function () {
      console.log('Fetching patient list with params:', this.serverParams);

      // Set a fallback timeout in case the request hangs
      const timeout = setTimeout(() => {
        if (this.pageLoader) {
          console.log('Patient list request timed out, resetting loading state');
          this.pageLoader = false;
          displayErrorMessage('Request timed out, please try again');
        }
      }, 10000);

      get("patient_list", this.serverParams)
        .then((data) => {
          clearTimeout(timeout);
          this.pageLoader = false;
          console.log('Patient list response:', data.data);

          if (data.data.status !== undefined && data.data.status === true) {
            this.patientList.data = data.data.data;
            this.totalRows = data.data.total_rows;

            // Add a fallback in case data is empty but should not be
            if (!this.patientList.data || this.patientList.data.length === 0) {
              console.log('Patient list is empty, checking if this is correct...');
            }
          } else {
            this.patientList.data = [];
            this.totalRows = 0;
            console.warn('Patient list request returned non-success status:', data.data);
          }
        })
        .catch((error) => {
          clearTimeout(timeout);
          this.pageLoader = false;
          console.error('Error fetching patient list:', error);

          // Set empty data as fallback
          this.patientList.data = [];
          this.totalRows = 0;

          // Show error message
          const errorMessage = error.response?.data?.message || 'Failed to load patient list';
          displayErrorMessage(errorMessage);
        });
    },
    clearFilter(filterName) {
      this.serverParams.columnFilters[filterName] = "";
      this.handleColumnFilter();
    },
    handleColumnFilter() {
      const hasFilters = Object.values(this.serverParams.columnFilters).some(
        (value) => !!value
      );
      const hadFilters = Object.values(this.oldServerParams.columnFilters).some(
        (value) => !!value
      );

      if (hasFilters || hadFilters) {
        this.oldServerParams.columnFilters = {
          ...this.serverParams.columnFilters,
        };
        this.updateParams({
          columnFilters: this.serverParams.columnFilters,
          page: 1,
        });
      }
    },

    handleGlobalSearch() {
      if (this.oldServerParams.searchTerm === this.serverParams.searchTerm) {
        return;
      }
      this.oldServerParams.searchTerm = this.serverParams.searchTerm;
      this.updateParams({
        searchTerm: this.serverParams.searchTerm,
        page: 1,
      });
    },

    openImportModal() {
      this.showImportModal = true;
    },

    closeImportModal() {
      this.showImportModal = false;
    },

    handleSort(field) {
      const column = this.patientList.column.find((col) => col.field === field);
      if (column && column.sortable === false) return;

      let type = "asc";
      if (this.serverParams.sort?.[0]?.field === field) {
        type = this.serverParams.sort[0].type === "asc" ? "desc" : "asc";
      }

      this.updateParams({
        sort: [
          {
            field,
            type,
          },
        ],
      });
    },

    defaultGlobalCheckboxApplyData() {
      return {
        action_perform: "delete",
        module: "patient",
        data: [],
      };
    },

    selectAll() {
      this.selectedRows = this.selectAllChecked
        ? []
        : [...this.patientList.data];
      this.handleRowSelection();
    },
    handleRowSelection() {
      // Update select all checkbox state
      this.selectAllChecked = this.allSelected;

      // Emit selected rows for parent component
      this.$emit("selected-rows-change", this.selectedRows);
      
      // Update global checkbox data for bulk operations
      this.globalCheckboxApplyData.data = this.selectedRows.map(row => row.ID);
    },
    
    confirmBulkDelete() {
      if (this.selectedRows.length === 0) {
        return;
      }
      
      this.$swal.fire({
        title: this.formTranslation.clinic_schedule.dt_are_you_sure,
        text: `Are you sure you want to delete ${this.selectedRows.length} patient${this.selectedRows.length > 1 ? 's' : ''}? This action cannot be undone.`,
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#d33",
        cancelButtonColor: "#3085d6",
        confirmButtonText: this.formTranslation.common.yes,
        cancelButtonText: this.formTranslation.common.cancel,
      }).then((result) => {
        if (result.isConfirmed) {
          this.bulkDeletePatients();
        }
      });
    },
    
    bulkDeletePatients() {
      // Show loading indicator
      this.pageLoader = true;
      
      const selectedIds = this.selectedRows.map(row => row.ID);
      console.log('Selected patient IDs for deletion:', selectedIds);
      
      // Track completion of all operations
      let completedOperations = 0;
      let successCount = 0;
      let errorCount = 0;
      const totalOperations = selectedIds.length;
      
      // Function to handle when all operations are complete
      const checkCompletion = () => {
        completedOperations++;
        
        if (completedOperations === totalOperations) {
          this.pageLoader = false;
          
          // Show success message
          this.$swal.fire({
            icon: successCount > 0 ? 'success' : 'error',
            title: successCount > 0 
              ? `Successfully deleted ${successCount} patient${successCount > 1 ? 's' : ''}${errorCount > 0 ? ` (${errorCount} failed)` : ''}`
              : 'Failed to delete patients',
            showConfirmButton: false,
            timer: 2000
          });
          
          // Clear selection
          this.selectedRows = [];
          
          // Refresh data
          this.getPatientList();
        }
      };
      
      // Process each selected item
      selectedIds.forEach(id => {
        // Use the same API as single delete
        get("patient_delete", { id })
          .then(response => {
            console.log('Delete response for ID', id, ':', response.data);
            if (response.data.status === true) {
              successCount++;
            } else {
              errorCount++;
            }
            checkCompletion();
          })
          .catch(error => {
            console.error('Error deleting patient ID', id, ':', error);
            errorCount++;
            checkCompletion();
          });
      });
    },
    onPageChange(params) {
      this.updateParams({ page: params.currentPage });
    },

    defaultGlobalCheckboxApplyDataActions: function () {
      return [
        {
          value: "active",
          label: this.formTranslation.service.dt_active,
        },
        {
          value: "inactive",
          label: this.formTranslation.service.dt_inactive,
        },
        {
          value: "resend_credential",
          label: this.formTranslation.receptionist.resend_credential,
        },
        {
          value: "delete",
          label: this.formTranslation.clinic_schedule.dt_lbl_dlt,
        },
      ];
    },

    confirmDelete() {
      let content = "";
      if (this.globalCheckboxApplyData.action_perform === "delete") {
        content = this.formTranslation.common.py_delete;
      } else if (
        this.globalCheckboxApplyData.action_perform === "resend_credential"
      ) {
        content = this.formTranslation.common.py_resend_credential;
      } else if (
        this.globalCheckboxApplyData.action_perform === "active" ||
        this.globalCheckboxApplyData.action_perform === "inactive"
      ) {
        content = this.formTranslation.common.py_status;
      }

      this.$swal
        .fire({
          title: this.formTranslation.clinic_schedule.dt_are_you_sure,
          text: content,
          icon: "warning",
          showCancelButton: true,
          confirmButtonColor: "#d33",
          cancelButtonColor: "#3085d6",
          confirmButtonText: this.formTranslation.common.yes,
          cancelButtonText: this.formTranslation.common.cancel,
        })
        .then((result) => {
          if (result.isConfirmed) {
            this.globalCheckboxApply();
          }
        });
    },

    globalCheckboxApply() {
      this.pageLoader = true;
      post("module_wise_multiple_data_update", this.globalCheckboxApplyData)
        .then((data) => {
          this.pageLoader = false;
          if (data.data.status !== undefined && data.data.status === true) {
            displayMessage(data.data.message);
            this.getPatientList();
          } else {
            displayErrorMessage(data.data.message);
          }
        })
        .catch((error) => {
          this.pageLoader = true;
          console.log(error);
        });
    },
    deletePatientData: function (index) {
      if (this.patientList.data[index - 1] !== undefined) {
        let ele = $("#user_delete_" + index);

        this.$swal
          .fire({
            title: this.formTranslation.clinic_schedule.dt_are_you_sure,
            text: this.formTranslation.common.action_delete_appointment_patient,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#d33",
            cancelButtonColor: "#3085d6",
            confirmButtonText: this.formTranslation.common.yes,
            cancelButtonText: this.formTranslation.common.cancel,
          })
          .then((result) => {
            if (result.isConfirmed) {
              // Disable button and show loading spinner
              ele.prop("disabled", true);
              $(ele).find("i").removeClass("fa fa-trash");
              $(ele).find("i").addClass("fa fa-sync fa-spin");

              // Delete patient
              get("patient_delete", {
                id: this.patientList.data[index - 1].ID,
              })
                .then((data) => {
                  // Reset button state
                  ele.prop("disabled", false);
                  $(ele).find("i").removeClass("fa fa-sync fa-spin");
                  $(ele).find("i").addClass("fa fa-trash");

                  // Check if deletion was successful
                  if (
                    data.data.status !== undefined &&
                    data.data.status === true
                  ) {
                    this.patientList.data.splice(index - 1, 1);

                    // Show success message using Sweet Alert
                    this.$swal.fire({
                      icon: "success",
                      title: data.data.message,
                      showConfirmButton: false,
                      timer: 1500,
                    });
                  }
                })
                .catch((error) => {
                  // Reset button state
                  ele.prop("disabled", false);
                  $(ele).find("i").removeClass("fa fa-sync fa-spin");
                  $(ele).find("i").addClass("fa fa-trash");

                  // Show error message using Sweet Alert
                  this.$swal.fire({
                    icon: "error",
                    title: this.formTranslation.common.internal_server_error,
                    text: error.toString(),
                  });
                });
            }
          });
      }
    },
    resendRequest: function (id) {
      var element = $("#resend_" + id).find("i");
      element.removeClass("fa fa-paper-plane ");
      element.addClass("fa fa-spinner fa-spin");
      post("resend_credential", { id: id })
        .then((data) => {
          element.removeClass("fa fa-spinner fa-spin");
          element.addClass("fa fa-paper-plane");
          if (data.data.status !== undefined && data.data.status === true) {
            displayMessage(data.data.message);
          } else {
            displayErrorMessage(data.data.message);
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    getDynamicTranslation: function () {
      this.$store.state.staticDataModule.langTranslateData;
    },
    getUniqueSetting: function () {
      if (
        this.userData.unquie_id_status !== undefined &&
        this.userData.unquie_id_status == true
      ) {
        this.p_uid = this.userData.unquie_id_status;
      } else {
        this.p_uid = false;
      }
    },
    updateParams(newProps) {
      this.serverParams = Object.assign({}, this.serverParams, newProps);
      this.getPatientList();
    },

    onPerPageChange(params) {
      if (this.oldServerParams.perPage === params.currentPerPage) {
        return;
      }
      this.oldServerParams.perPage = params.currentPerPage;
      this.updateParams({
        perPage: params.currentPerPage,
        page: params.currentPage,
      });
    },

    globalFilter: _.debounce(function (params) {
      if (this.oldServerParams.searchTerm === params.searchTerm) {
        return;
      }
      this.oldServerParams.searchTerm = params.searchTerm;
      this.updateParams({
        searchTerm: params.searchTerm,
        perPage: this.serverParams.perPage,
        page: 1,
      });
    }, 300),

    getInitials(name) {
      if (name !== undefined && name !== "" && name !== null) {
        const patient_name = name.split(" ");
        const initials = patient_name.map((patient_name) =>
          patient_name.charAt(0).toUpperCase()
        );
        return initials.join("");
      } else {
        return " - ";
      }
    },
    customFormOpen(props, custom_form_data) {
      this.patientCustomFormData = custom_form_data;
      this.patientCustomFormData.module_id = props.ID;
      this.patientCustomFormViewMode = false;
      this.patientCustomFormModal = true;
    },
    customFormCondition(props, custom_form_data) {
      return (
        props.custom_forms &&
        props.custom_forms.length &&
        (custom_form_data.clinic_ids.length === 0 ||
          custom_form_data.clinic_ids.some((value) =>
            props.clinic_id.includes(value)
          ))
      );
    },
    handlePatientSaved() {
      this.getPatientList(); // Refresh the patient list
    },
    handlePatientAddModel() {
      // Simply show the add patient modal without checking limits
      this.showAddPatientModal = true;
    }
  },
  computed: {
    allSelected() {
      return (
        this.patientList.data.length > 0 &&
        this.selectedRows.length === this.patientList.data.length
      );
    },
    patientListExport() {
      return "Patient List - " + moment().format("YYYY-MM-DD");
    },
    getSpeciality: function () {
      return (salut) => {
        if (salut !== undefined && salut.length > 0) {
          let specialties = "";
          salut.map(function (spec, index) {
            specialties +=
              salut.length === index + 1 ? spec.label : spec.label + ", ";
            return spec;
          });
          return specialties;
        }
        return " - ";
      };
    },
    userData() {
      return this.$store.state.userDataModule.user;
    },
    clinics() {
      return this.$store.state.clinic;
    },
    // formTranslation: function () {
    //   return this.$store.state.staticDataModule.langTranslateData ;
    // }
  },
  watch: {
    selectedRows(newVal) {
      this.selectAllChecked = newVal.length === this.patientList.data.length;
    },
  },
};
</script>
<style>
#printPatientData .vgt-table thead th {
  vertical-align: middle;
}

@media (max-width: 576px) {
  #printPatientData .vgt-compact td:before {
    width: 42%;
    padding-left: 0;
  }
}
</style>
