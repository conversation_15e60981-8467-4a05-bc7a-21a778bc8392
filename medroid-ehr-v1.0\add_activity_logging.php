<?php
// Temporary script to add activity logging to various controllers
// After you run this, you can remove this file

// Load WordPress
require_once(dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-load.php');

// Add patient activity logging
add_action('kc_patient_save', function($patient_id) {
    // Log patient creation
    kcLogActivity(
        get_current_user_id(),
        'patient_created',
        sprintf(__('New patient #%d created', 'kc-lang'), $patient_id),
        [
            'patient_id' => $patient_id,
            'resource_id' => $patient_id,
            'resource_type' => 'patient',
            'clinic_id' => kcGetDefaultClinicId()
        ]
    );
});

add_action('kc_patient_update', function($patient_id) {
    // Log patient update
    kcLogActivity(
        get_current_user_id(),
        'patient_updated',
        sprintf(__('Patient #%d updated', 'kc-lang'), $patient_id),
        [
            'patient_id' => $patient_id,
            'resource_id' => $patient_id,
            'resource_type' => 'patient',
            'clinic_id' => kcGetDefaultClinicId()
        ]
    );
});

add_action('kc_patient_delete', function($patient_id) {
    // Log patient deletion
    kcLogActivity(
        get_current_user_id(),
        'patient_deleted',
        sprintf(__('Patient #%d deleted', 'kc-lang'), $patient_id),
        [
            'patient_id' => $patient_id,
            'resource_id' => $patient_id,
            'resource_type' => 'patient',
            'clinic_id' => kcGetDefaultClinicId()
        ]
    );
});

// Add consultation/encounter activity logging
add_action('kc_encounter_save', function($encounter_id) {
    // Get encounter details
    global $wpdb;
    $encounter = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}kc_patient_encounters WHERE id = {$encounter_id}");
    
    if ($encounter) {
        // Log encounter creation
        kcLogActivity(
            get_current_user_id(),
            'encounter_created',
            sprintf(__('Consultation #%d created for patient #%d', 'kc-lang'), $encounter_id, $encounter->patient_id),
            [
                'patient_id' => $encounter->patient_id,
                'resource_id' => $encounter_id,
                'resource_type' => 'encounter',
                'clinic_id' => $encounter->clinic_id
            ]
        );
    }
});

add_action('kc_encounter_update', function($encounter_id) {
    // Get encounter details
    global $wpdb;
    $encounter = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}kc_patient_encounters WHERE id = {$encounter_id}");
    
    if ($encounter) {
        // Log encounter update
        kcLogActivity(
            get_current_user_id(),
            'encounter_updated',
            sprintf(__('Consultation updated for patient', 'kc-lang'), $encounter_id, $encounter->patient_id),
            [
                'patient_id' => $encounter->patient_id,
                'resource_id' => $encounter_id,
                'resource_type' => 'encounter',
                'clinic_id' => $encounter->clinic_id
            ]
        );
    }
});

// Add service activity logging
add_action('kc_service_save', function($service_id) {
    // Get service details
    global $wpdb;
    $service = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}kc_services WHERE id = {$service_id}");
    
    if ($service) {
        // Log service creation
        kcLogActivity(
            get_current_user_id(),
            'service_created',
            sprintf(__('Service #%d created: %s', 'kc-lang'), $service_id, $service->name),
            [
                'resource_id' => $service_id,
                'resource_type' => 'service',
                'clinic_id' => $service->clinic_id
            ]
        );
    }
});

add_action('kc_service_update', function($service_id) {
    // Get service details
    global $wpdb;
    $service = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}kc_services WHERE id = {$service_id}");
    
    if ($service) {
        // Log service update
        kcLogActivity(
            get_current_user_id(),
            'service_updated',
            sprintf(__('Service #%d updated: %s', 'kc-lang'), $service_id, $service->name),
            [
                'resource_id' => $service_id,
                'resource_type' => 'service',
                'clinic_id' => $service->clinic_id
            ]
        );
    }
});

add_action('kc_service_delete', function($service_id, $service = null) {
    // Log service deletion
    kcLogActivity(
        get_current_user_id(),
        'service_deleted',
        sprintf(__('Service #%d deleted', 'kc-lang'), $service_id),
        [
            'resource_id' => $service_id,
            'resource_type' => 'service',
            'clinic_id' => isset($service->clinic_id) ? $service->clinic_id : kcGetDefaultClinicId()
        ]
    );
}, 10, 2);

// Add prescription activity logging
add_action('kc_prescription_save', function($prescription_id) {
    // Get prescription details
    global $wpdb;
    $prescription = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}kc_prescription WHERE id = {$prescription_id}");
    
    if ($prescription) {
        // Log prescription creation
        kcLogActivity(
            get_current_user_id(),
            'prescription_created',
            sprintf(__('Prescription #%d created for patient #%d', 'kc-lang'), $prescription_id, $prescription->patient_id),
            [
                'patient_id' => $prescription->patient_id,
                'resource_id' => $prescription_id,
                'resource_type' => 'prescription',
                'clinic_id' => isset($prescription->clinic_id) ? $prescription->clinic_id : kcGetDefaultClinicId()
            ]
        );
    }
});

add_action('kc_prescription_update', function($prescription_id) {
    // Get prescription details
    global $wpdb;
    $prescription = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}kc_prescription WHERE id = {$prescription_id}");
    
    if ($prescription) {
        // Log prescription update
        kcLogActivity(
            get_current_user_id(),
            'prescription_updated',
            sprintf(__('Prescription #%d updated for patient #%d', 'kc-lang'), $prescription_id, $prescription->patient_id),
            [
                'patient_id' => $prescription->patient_id,
                'resource_id' => $prescription_id,
                'resource_type' => 'prescription',
                'clinic_id' => isset($prescription->clinic_id) ? $prescription->clinic_id : kcGetDefaultClinicId()
            ]
        );
    }
});

add_action('kc_prescription_download', function($prescription_id, $patient_id = null) {
    // Log prescription download
    kcLogActivity(
        get_current_user_id(),
        'prescription_downloaded',
        sprintf(__('Prescription #%d downloaded', 'kc-lang'), $prescription_id),
        [
            'patient_id' => $patient_id,
            'resource_id' => $prescription_id,
            'resource_type' => 'prescription'
        ]
    );
}, 10, 2);

// Add billing activity logging
add_action('kc_bill_save', function($bill_id) {
    // Get bill details
    global $wpdb;
    $bill = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}kc_bills WHERE id = {$bill_id}");
    
    if ($bill) {
        // Log bill creation
        kcLogActivity(
            get_current_user_id(),
            'bill_generated',
            sprintf(__('Bill #%d generated for patient #%d', 'kc-lang'), $bill_id, $bill->patient_id),
            [
                'patient_id' => $bill->patient_id,
                'resource_id' => $bill_id,
                'resource_type' => 'bill',
                'clinic_id' => $bill->clinic_id
            ]
        );
    }
});

add_action('kc_bill_update', function($bill_id) {
    // Get bill details
    global $wpdb;
    $bill = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}kc_bills WHERE id = {$bill_id}");
    
    if ($bill) {
        // Log bill update
        kcLogActivity(
            get_current_user_id(),
            'bill_updated',
            sprintf(__('Bill #%d updated for patient #%d', 'kc-lang'), $bill_id, $bill->patient_id),
            [
                'patient_id' => $bill->patient_id,
                'resource_id' => $bill_id,
                'resource_type' => 'bill',
                'clinic_id' => $bill->clinic_id
            ]
        );
    }
});

add_action('kc_bill_status_change', function($bill_id, $status) {
    // Get bill details
    global $wpdb;
    $bill = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}kc_bills WHERE id = {$bill_id}");
    
    if ($bill) {
        // Log bill status change
        kcLogActivity(
            get_current_user_id(),
            'bill_updated',
            sprintf(__('Bill #%d status changed to %s', 'kc-lang'), $bill_id, $status),
            [
                'patient_id' => $bill->patient_id,
                'resource_id' => $bill_id,
                'resource_type' => 'bill',
                'clinic_id' => $bill->clinic_id
            ]
        );
    }
}, 10, 2);

// Add doctor session activity logging
add_action('kc_session_save', function($session_id) {
    // Get session details
    global $wpdb;
    $session = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}kc_clinic_sessions WHERE id = {$session_id}");
    
    if ($session) {
        // Log session creation
        kcLogActivity(
            get_current_user_id(),
            'session_created',
            sprintf(__('Doctor session #%d created', 'kc-lang'), $session_id),
            [
                'resource_id' => $session_id,
                'resource_type' => 'session',
                'clinic_id' => isset($session->clinic_id) ? $session->clinic_id : null
            ]
        );
    }
});

add_action('kc_session_update', function($session_id) {
    // Get session details
    global $wpdb;
    $session = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}kc_clinic_sessions WHERE id = {$session_id}");
    
    if ($session) {
        // Log session update
        kcLogActivity(
            get_current_user_id(),
            'session_updated',
            sprintf(__('Doctor session #%d updated', 'kc-lang'), $session_id),
            [
                'resource_id' => $session_id,
                'resource_type' => 'session',
                'clinic_id' => isset($session->clinic_id) ? $session->clinic_id : null
            ]
        );
    }
});

add_action('kc_session_delete', function($session_id) {
    // Log session deletion
    kcLogActivity(
        get_current_user_id(),
        'session_deleted',
        sprintf(__('Doctor session #%d deleted', 'kc-lang'), $session_id),
        [
            'resource_id' => $session_id,
            'resource_type' => 'session'
        ]
    );
});

// Add consultation sharing activity logging
add_action('kc_consultation_shared', function($encounter_id, $recipient = '') {
    // Get encounter details
    global $wpdb;
    $encounter = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}kc_patient_encounters WHERE id = {$encounter_id}");
    
    if ($encounter) {
        // Log consultation sharing
        kcLogActivity(
            get_current_user_id(),
            'consultation_shared',
            sprintf(__('Consultation #%d shared%s', 'kc-lang'), $encounter_id, $recipient ? ' with ' . $recipient : ''),
            [
                'patient_id' => $encounter->patient_id,
                'resource_id' => $encounter_id,
                'resource_type' => 'encounter',
                'clinic_id' => $encounter->clinic_id
            ]
        );
    }
}, 10, 2);

// Add absence activity logging
add_action('kc_absence_created', function($doctor_id, $clinic_id, $date) {
    // Log absence creation
    kcLogActivity(
        get_current_user_id(),
        'absence_created',
        sprintf(__('Absence created for doctor #%d on %s', 'kc-lang'), $doctor_id, $date),
        [
            'resource_id' => $doctor_id,
            'resource_type' => 'absence',
            'clinic_id' => $clinic_id
        ]
    );
}, 10, 3);

add_action('kc_absence_deleted', function($doctor_id, $clinic_id, $date) {
    // Log absence deletion
    kcLogActivity(
        get_current_user_id(),
        'absence_deleted',
        sprintf(__('Absence deleted for doctor #%d on %s', 'kc-lang'), $doctor_id, $date),
        [
            'resource_id' => $doctor_id,
            'resource_type' => 'absence',
            'clinic_id' => $clinic_id
        ]
    );
}, 10, 3);

// Inject activity logging into specific class methods
add_action('init', function() {
    // Patient controller save method
    add_action('wp_ajax_ajax_post', function() {
        if (isset($_REQUEST['route_name']) && $_REQUEST['route_name'] === 'patient_save') {
            add_action('wp_loaded', function() {
                // This will execute after the patient controller's save method
                error_log('Patient save hook triggered');
            }, 999);
        }
    }, 1);
    
    // Patient controller delete method
    add_action('wp_ajax_ajax_post', function() {
        if (isset($_REQUEST['route_name']) && $_REQUEST['route_name'] === 'patient_delete') {
            add_action('wp_loaded', function() {
                // This will execute after the patient controller's delete method
                error_log('Patient delete hook triggered');
            }, 999);
        }
    }, 1);
    
    // Encounter controller save method
    add_action('wp_ajax_ajax_post', function() {
        if (isset($_REQUEST['route_name']) && $_REQUEST['route_name'] === 'encounter_save') {
            add_action('wp_loaded', function() {
                // This will execute after the encounter controller's save method
                error_log('Encounter save hook triggered');
            }, 999);
        }
    }, 1);
    
    // Service controller save/update methods
    add_action('wp_ajax_ajax_post', function() {
        if (isset($_REQUEST['route_name']) && $_REQUEST['route_name'] === 'service_save') {
            add_action('wp_loaded', function() {
                // This will execute after the service controller's save method
                error_log('Service save hook triggered');
            }, 999);
        }
    }, 1);
    
    // Service controller delete method
    add_action('wp_ajax_ajax_post', function() {
        if (isset($_REQUEST['route_name']) && $_REQUEST['route_name'] === 'service_delete') {
            add_action('wp_loaded', function() {
                // This will execute after the service controller's delete method
                error_log('Service delete hook triggered');
            }, 999);
        }
    }, 1);
});

// Register all the action hooks
function register_activity_logging_hooks() {
    // Define the hooks to add
    $hooks_to_add = [
        'kc_patient_save',
        'kc_patient_update',
        'kc_patient_delete',
        'kc_encounter_save',
        'kc_encounter_update',
        'kc_service_save',
        'kc_service_update',
        'kc_service_delete',
        'kc_prescription_save',
        'kc_prescription_update',
        'kc_prescription_download',
        'kc_bill_save',
        'kc_bill_update',
        'kc_bill_status_change',
        'kc_session_save',
        'kc_session_update',
        'kc_session_delete',
        'kc_consultation_shared',
        'kc_absence_created',
        'kc_absence_deleted'
    ];
    
    // Add direct controller method hooks
    add_filter('kc_after_patient_save', function($response, $data) {
        $user_id = !empty($data['ID']) ? $data['ID'] : $response['data'];
        
        if (!empty($data['ID'])) {
            // Log patient update activity
            kcLogActivity(
                get_current_user_id(),
                'patient_updated',
                sprintf(__('Patient #%d updated', 'kc-lang'), $user_id),
                [
                    'patient_id' => $user_id,
                    'resource_id' => $user_id,
                    'resource_type' => 'patient',
                    'clinic_id' => !empty($data['clinic_id']) ? $data['clinic_id'] : null
                ]
            );
        } else {
            // Log patient creation activity
            kcLogActivity(
                get_current_user_id(),
                'patient_created',
                sprintf(__('New patient #%d created', 'kc-lang'), $user_id),
                [
                    'patient_id' => $user_id,
                    'resource_id' => $user_id,
                    'resource_type' => 'patient',
                    'clinic_id' => !empty($data['clinic_id']) ? $data['clinic_id'] : null
                ]
            );
        }
        
        return $response;
    }, 10, 2);
    
    add_filter('kc_after_patient_delete', function($response, $id) {
        // Log patient deletion activity
        kcLogActivity(
            get_current_user_id(),
            'patient_deleted',
            sprintf(__('Patient #%d deleted', 'kc-lang'), $id),
            [
                'patient_id' => $id,
                'resource_id' => $id,
                'resource_type' => 'patient'
            ]
        );
        
        return $response;
    }, 10, 2);
    
    add_filter('kc_after_encounter_save', function($response, $data) {
        $encounter_id = !empty($data['id']) ? $data['id'] : $response['data'];
        
        if (!empty($data['id'])) {
            // Log encounter update activity
            kcLogActivity(
                get_current_user_id(),
                'encounter_updated',
                sprintf(__('Consultation #%d updated', 'kc-lang'), $encounter_id),
                [
                    'patient_id' => $data['patient_id'],
                    'resource_id' => $encounter_id,
                    'resource_type' => 'encounter',
                    'clinic_id' => !empty($data['clinic_id']) ? $data['clinic_id'] : null
                ]
            );
        } else {
            // Log encounter creation activity
            kcLogActivity(
                get_current_user_id(),
                'encounter_created',
                sprintf(__('New consultation #%d created', 'kc-lang'), $encounter_id),
                [
                    'patient_id' => $data['patient_id'],
                    'resource_id' => $encounter_id,
                    'resource_type' => 'encounter',
                    'clinic_id' => !empty($data['clinic_id']) ? $data['clinic_id'] : null
                ]
            );
        }
        
        return $response;
    }, 10, 2);
    
    // Service creation/update hooks
    add_filter('kc_after_service_save', function($response, $data) {
        $service_id = !empty($data['id']) ? $data['id'] : $response['data'];
        
        if (!empty($data['id'])) {
            // Log service update activity
            kcLogActivity(
                get_current_user_id(),
                'service_updated',
                sprintf(__('Service #%d updated: %s', 'kc-lang'), $service_id, $data['name']),
                [
                    'resource_id' => $service_id,
                    'resource_type' => 'service',
                    'clinic_id' => !empty($data['clinic_id']) ? $data['clinic_id'] : null
                ]
            );
        } else {
            // Log service creation activity
            kcLogActivity(
                get_current_user_id(),
                'service_created',
                sprintf(__('New service #%d created: %s', 'kc-lang'), $service_id, $data['name']),
                [
                    'resource_id' => $service_id,
                    'resource_type' => 'service',
                    'clinic_id' => !empty($data['clinic_id']) ? $data['clinic_id'] : null
                ]
            );
        }
        
        return $response;
    }, 10, 2);
    
    // Service deletion hook
    add_filter('kc_after_service_delete', function($response, $id) {
        // Log service deletion activity
        kcLogActivity(
            get_current_user_id(),
            'service_deleted',
            sprintf(__('Service #%d deleted', 'kc-lang'), $id),
            [
                'resource_id' => $id,
                'resource_type' => 'service'
            ]
        );
        
        return $response;
    }, 10, 2);
    
    // Prescription hooks
    add_filter('kc_after_prescription_save', function($response, $data) {
        $prescription_id = !empty($data['id']) ? $data['id'] : $response['data'];
        
        if (!empty($data['id'])) {
            // Log prescription update activity
            kcLogActivity(
                get_current_user_id(),
                'prescription_updated',
                sprintf(__('Prescription #%d updated', 'kc-lang'), $prescription_id),
                [
                    'patient_id' => $data['patient_id'],
                    'resource_id' => $prescription_id,
                    'resource_type' => 'prescription',
                    'clinic_id' => !empty($data['clinic_id']) ? $data['clinic_id'] : null
                ]
            );
        } else {
            // Log prescription creation activity
            kcLogActivity(
                get_current_user_id(),
                'prescription_created',
                sprintf(__('New prescription #%d created', 'kc-lang'), $prescription_id),
                [
                    'patient_id' => $data['patient_id'],
                    'resource_id' => $prescription_id,
                    'resource_type' => 'prescription',
                    'clinic_id' => !empty($data['clinic_id']) ? $data['clinic_id'] : null
                ]
            );
        }
        
        return $response;
    }, 10, 2);
    
    // Prescription download hook
    add_action('kc_prescription_download_action', function($prescription_id, $patient_id = null) {
        // Log prescription download
        kcLogActivity(
            get_current_user_id(),
            'prescription_downloaded',
            sprintf(__('Prescription #%d downloaded', 'kc-lang'), $prescription_id),
            [
                'patient_id' => $patient_id,
                'resource_id' => $prescription_id,
                'resource_type' => 'prescription'
            ]
        );
    }, 10, 2);
    
    // Consultation share hook
    add_action('kc_after_consultation_share', function($encounter_id, $recipient = '') {
        global $wpdb;
        $encounter = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}kc_patient_encounters WHERE id = {$encounter_id}");
        
        if ($encounter) {
            // Log consultation sharing
            kcLogActivity(
                get_current_user_id(),
                'consultation_shared',
                sprintf(__('Consultation #%d shared%s', 'kc-lang'), $encounter_id, $recipient ? ' with ' . $recipient : ''),
                [
                    'patient_id' => $encounter->patient_id,
                    'resource_id' => $encounter_id,
                    'resource_type' => 'encounter',
                    'clinic_id' => $encounter->clinic_id
                ]
            );
        }
    }, 10, 2);
    
    // Bill hooks
    add_filter('kc_after_bill_save', function($response, $data) {
        $bill_id = !empty($data['id']) ? $data['id'] : $response['data'];
        
        if (!empty($data['id'])) {
            // Log bill update activity
            kcLogActivity(
                get_current_user_id(),
                'bill_updated',
                sprintf(__('Bill #%d updated', 'kc-lang'), $bill_id),
                [
                    'patient_id' => $data['patient_id'],
                    'resource_id' => $bill_id,
                    'resource_type' => 'bill',
                    'clinic_id' => !empty($data['clinic_id']) ? $data['clinic_id'] : null
                ]
            );
        } else {
            // Log bill creation activity
            kcLogActivity(
                get_current_user_id(),
                'bill_generated',
                sprintf(__('New bill #%d generated', 'kc-lang'), $bill_id),
                [
                    'patient_id' => $data['patient_id'],
                    'resource_id' => $bill_id,
                    'resource_type' => 'bill',
                    'clinic_id' => !empty($data['clinic_id']) ? $data['clinic_id'] : null
                ]
            );
        }
        
        return $response;
    }, 10, 2);
    
    // Bill status change hook
    add_action('kc_bill_status_change_action', function($bill_id, $status) {
        global $wpdb;
        $bill = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}kc_bills WHERE id = {$bill_id}");
        
        if ($bill) {
            // Log bill status change
            kcLogActivity(
                get_current_user_id(),
                'bill_updated',
                sprintf(__('Bill #%d status changed to %s', 'kc-lang'), $bill_id, $status),
                [
                    'patient_id' => $bill->patient_id,
                    'resource_id' => $bill_id,
                    'resource_type' => 'bill',
                    'clinic_id' => $bill->clinic_id
                ]
            );
        }
    }, 10, 2);
    
    // Doctor session hooks
    add_filter('kc_after_doctor_session_save', function($response, $data) {
        $session_id = !empty($data['id']) ? $data['id'] : $response['data'];
        
        if (!empty($data['id'])) {
            // Log session update activity
            kcLogActivity(
                get_current_user_id(),
                'session_updated',
                sprintf(__('Doctor session #%d updated', 'kc-lang'), $session_id),
                [
                    'resource_id' => $session_id,
                    'resource_type' => 'session',
                    'clinic_id' => !empty($data['clinic_id']) ? $data['clinic_id'] : null
                ]
            );
        } else {
            // Log session creation activity
            kcLogActivity(
                get_current_user_id(),
                'session_created',
                sprintf(__('New doctor session #%d created', 'kc-lang'), $session_id),
                [
                    'resource_id' => $session_id,
                    'resource_type' => 'session',
                    'clinic_id' => !empty($data['clinic_id']) ? $data['clinic_id'] : null
                ]
            );
        }
        
        return $response;
    }, 10, 2);
    
    // Absence hooks
    add_action('kc_doctor_absence_created', function($doctor_id, $clinic_id, $date) {
        // Log absence creation
        kcLogActivity(
            get_current_user_id(),
            'absence_created',
            sprintf(__('Absence created for doctor #%d on %s', 'kc-lang'), $doctor_id, $date),
            [
                'resource_id' => $doctor_id,
                'resource_type' => 'absence',
                'clinic_id' => $clinic_id
            ]
        );
    }, 10, 3);
    
    // Consultation download hook
    add_action('kc_consultation_download', function($encounter_id, $patient_id = null) {
        global $wpdb;
        $encounter = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}kc_patient_encounters WHERE id = {$encounter_id}");
        
        if ($encounter) {
            // Log consultation download
            kcLogActivity(
                get_current_user_id(),
                'file_downloaded',
                sprintf(__('Consultation #%d downloaded', 'kc-lang'), $encounter_id),
                [
                    'patient_id' => $encounter->patient_id,
                    'resource_id' => $encounter_id,
                    'resource_type' => 'encounter',
                    'clinic_id' => $encounter->clinic_id
                ]
            );
        }
    }, 10, 2);
    
    // Modify all controllers to call the appropriate hooks
    add_action('init', function() {
        // Find all controller files 
        $controllers_path = dirname(__FILE__) . '/app/controllers/';
        $controllers = glob($controllers_path . 'KC*Controller.php');
        
        foreach ($controllers as $controller_file) {
            $content = file_get_contents($controller_file);
            
            // Skip files that already have logging
            if (strpos($content, 'kcLogActivity') !== false) {
                continue;
            }
            
            $controller_name = basename($controller_file, '.php');
            
            // Add hooks based on controller type
            if ($controller_name === 'KCPatientController') {
                // Patient controller hooks are already added above
            } else if ($controller_name === 'KCServiceController') {
                // Service controller hooks are already added above
            }
            
            // More specific controllers could be added here
        }
    });
    
    // Log that we've registered all hooks
    error_log('Activity logging hooks registered: ' . implode(', ', $hooks_to_add));
    
    echo '<h1>Activity Logging Hooks Registered</h1>';
    echo '<p>The following hooks have been registered for activity logging:</p>';
    echo '<ul>';
    foreach ($hooks_to_add as $hook) {
        echo '<li>' . esc_html($hook) . '</li>';
    }
    echo '</ul>';
    echo '<p>You can now <a href="/wp-admin/admin.php?page=dashboard">return to the dashboard</a>.</p>';
}

// Register all hooks
register_activity_logging_hooks();