import {get, post} from "../config/request";
import {disabledButton, enabledButton} from "./helper";
export const helperModuleTableColumns = async (endpoint, module_name, params = {}) => {
  try {
    const serverParams = Object.assign({},params);
    const response = await get(endpoint,serverParams);
    return response?.data?.status && response.data.status === true && response?.data?.data ? response.data.data : null
  } catch (error) {
    console.error(error);
    displayErrorMessage(window?.__kivicarelang?.common?.internal_server_error);
    return null;
  }
};

export const helperModuleTableData = async (endpoint, params = {}) => {
  const data = {
    rows:[],
    totalRows:0,
    response:[]
  };
  try {
    const serverParams = Object.assign({},params);
    const response = await get(endpoint,serverParams);
    if(response?.data?.status && response.data.status === true && response?.data?.data ){
      data.rows = response.data.data;
      data.totalRows = response.data.total_rows;
      data.response = response.data;
    }
    return data;
  } catch (error) {
    console.error(error);
    displayErrorMessage(window?.__kivicarelang?.common?.internal_server_error);
    return data;
  }
};

export const helperModuleCloneData = async (endpoint, params = {}) => {
  const id = params.id;
  const ele = $(`#${params.clone_ele}${id}`);
  const eleIconClass = 'fa fa-clone';
  disabledButton(ele,eleIconClass);
  try {
    const serverParams = Object.assign({},params);
    const response = await post(endpoint,serverParams);
    enabledButton(ele,eleIconClass);
    if(response?.data?.status && response.data.status === true){
      displayMessage(response.data.message);
      return true;
    }else{
      displayErrorMessage(response.data.message);
      return false
    }
  } catch (error) {
    enabledButton(ele,eleIconClass);
    displayErrorMessage(window?.__kivicarelang?.common?.internal_server_error);
    console.error(error);
    return false;
  }
}
