<template>
  <div class="min-h-screen bg-gradient-to-br from-pink-50 to-purple-50 py-4">
    <!-- Header Section -->
    <div class="mb-8 flex justify-between items-center">
      <div class="flex items-center gap-4">
        <button @click="$router.back()"
          class="flex items-center gap-2 px-4 py-2 bg-black text-sm text-white rounded-lg shadow-sm hover:bg-gray-800">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
            <path d="m12 19-7-7 7-7"></path>
            <path d="M19 12H5"></path>
          </svg>
          <span>Back</span>
        </button>
        <h1 class="text-2xl font-semibold text-gray-800">
          {{ formTranslation.static_data.listing_data }}
        </h1>
      </div>
      <div class="flex gap-3">
        <module-data-export v-if="kcCheckPermission('static_data_export')" :module-data="staticDataList.data"
          :module-name="formTranslation.static_data.listing_data" module-type="listing_data">
          <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="7 10 12 15 17 10"></polyline>
            <line x1="12" x2="12" y1="15" y2="3"></line>
          </svg>
          {{ formTranslation.common.import }}
        </module-data-export>
        <module-data-import v-if="
          userData.addOns.kiviPro &&
          kcCheckPermission('static_data_add') &&
          kivicareCompareVersion(requireProVersion, userData.pro_version)
        " ref="module_data_import" @reloadList="getStaticData" :required-data="[
          { label: formTranslation.static_data.dt_lbl_name, value: 'name' },
          { label: formTranslation.static_data.dt_lbl_type, value: 'type' },
        ]" :module-name="formTranslation.static_data.listing_data" module-type="static_data">
        </module-data-import>
        <button v-if="kcCheckPermission('static_data_add')"
          class="px-4 py-2 bg-black text-sm text-white rounded-lg hover:bg-gray-800 flex items-center gap-2"
          @click="handleStaticDataForm({})">
          <i v-if="!visible" class="fas fa-plus"></i>
          <i v-else class="fas fa-minus"></i>
          {{ visible ? formTranslation.common.close_form_btn : formTranslation.static_data.add_list_data_btn }}
        </button>
      </div>
    </div>

    <!-- Create Forms -->
    <Create v-if="showAddForm" :static-id="staticId" @getStaticData="getStaticData" @closeForm="closeForm">
    </Create>
    <Create v-if="showEditForm && staticId" :static-id="staticId" @getStaticData="getStaticData" @closeForm="closeForm">
    </Create>

    <!-- Table Section -->
    <!-- Table Section -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <!-- Search Bar -->
      <div class="p-4 border-b border-gray-200">
        <div class="relative">
          <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
          <input type="text" @input="globalFilter"
            class="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-400 focus:border-transparent"
            :placeholder="formTranslation.common.search_listing_data_global_placeholder" />
        </div>
      </div>

      <!-- Loader -->
      <div v-show="pageLoader" class="relative p-6">
        <div class="absolute inset-0 bg-white bg-opacity-50 flex items-center justify-center">
          <loader-component-2></loader-component-2>
        </div>
      </div>

      <!-- Table -->
      <div class="overflow-x-auto" id="printStaticData">
        <table class="w-full border-collapse">
          <!-- Table Header -->
          <thead>
            <tr class="bg-gray-50 border-b border-gray-200">
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ formTranslation.common.id }}
                <button @click="onSortChange({ field: 'id', type: sortDirection('id') })" class="ml-1">
                  <i class="fas" :class="getSortIconClass('id')"></i>
                </button>
              </th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ formTranslation.static_data.dt_lbl_name }}
                <button @click="onSortChange({ field: 'label', type: sortDirection('label') })" class="ml-1">
                  <i class="fas" :class="getSortIconClass('label')"></i>
                </button>
              </th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ formTranslation.static_data.dt_lbl_type }}
                <button @click="onSortChange({ field: 'type', type: sortDirection('type') })" class="ml-1">
                  <i class="fas" :class="getSortIconClass('type')"></i>
                </button>
              </th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ formTranslation.static_data.dt_lbl_status }}
                <button @click="onSortChange({ field: 'status', type: sortDirection('status') })" class="ml-1">
                  <i class="fas" :class="getSortIconClass('status')"></i>
                </button>
              </th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ formTranslation.static_data.dt_lbl_action }}
              </th>
            </tr>
            <!-- Filter Row -->
            <tr class="bg-white border-b border-gray-200">
              <th class="px-4 py-2">
                <input v-model="staticDataList.serverParams.columnFilters.id"
                  @input="onColumnFilter({ columnFilters: staticDataList.serverParams.columnFilters })"
                  class="w-full px-2 py-1 text-sm border border-gray-200 rounded"
                  :placeholder="formTranslation.common.id" />
              </th>
              <th class="px-4 py-2">
                <input v-model="staticDataList.serverParams.columnFilters.label"
                  @input="onColumnFilter({ columnFilters: staticDataList.serverParams.columnFilters })"
                  class="w-full px-2 py-1 text-sm border border-gray-200 rounded"
                  :placeholder="formTranslation.static_data.dt_lbl_plh_fltr_name" />
              </th>
              <th class="px-4 py-2">
                <select v-model="staticDataList.serverParams.columnFilters.type"
                  @change="onColumnFilter({ columnFilters: staticDataList.serverParams.columnFilters })"
                  class="w-full px-2 py-1 text-sm border border-gray-200 rounded">
                  <option value="">{{ formTranslation.static_data.dt_lbl_plh_fltr_type }}</option>
                  <option value="specialization">{{ formTranslation.doctor.dt_lbl_specialties }}</option>
                  <option value="service_type">{{ formTranslation.widgets.service_type }}</option>
                  <option value="prescription_medicine">{{ formTranslation.patient_encounter.prescription }}</option>
                  <option value="clinical_problems">{{ formTranslation.encounter_dashboard.problems }}</option>
                  <option value="clinical_observations">{{ formTranslation.encounter_dashboard.observation }}</option>
                </select>
              </th>
              <th class="px-4 py-2">
                <select v-model="staticDataList.serverParams.columnFilters.status"
                  @change="onColumnFilter({ columnFilters: staticDataList.serverParams.columnFilters })"
                  class="w-full px-2 py-1 text-sm border border-gray-200 rounded">
                  <option value="">{{ formTranslation.static_data.dt_lbl_plh_sr_fltr_status }}</option>
                  <option value="1">{{ formTranslation.common.active }}</option>
                  <option value="0">{{ formTranslation.common.inactive }}</option>
                </select>
              </th>
              <th class="px-4 py-2"></th>
            </tr>
          </thead>

          <!-- Table Body -->
          <tbody>
            <!-- Empty State -->
            <tr v-if="staticDataList.data.length === 0">
              <td colspan="5" class="px-4 py-8 text-red-500 text-center border-b border-gray-200">
                {{ formTranslation.common.no_data_found }}
              </td>
            </tr>

            <!-- Data Rows -->
            <tr v-for="(row, index) in staticDataList.data" :key="row.id"
              class="hover:bg-gray-50 border-b border-gray-200 transition-colors">
              <td class="px-4 py-3 text-sm text-gray-500">{{ row.id }}</td>
              <td class="px-4 py-3 text-sm">{{ row.label }}</td>
              <td class="px-4 py-3 text-sm">{{ row.type }}</td>
              <td class="px-4 py-3 text-sm">
                <div class="flex items-center space-x-2">
                  <!-- Toggle button -->
                  <toggle-switch v-if="kcCheckPermission('static_data_edit')" :value="row.status === '1' ? 'on' : 'off'"
                    @input="(value) => {
                      // Update the local state immediately for the toggle animation
                      row.status = value === 'on' ? '1' : '0';
                      // Then call the status change function
                      changeModuleValueStatus({
                        module_type: 'static_data',
                        id: row.id,
                        value: row.status,
                      });
                    }" on-value="on" off-value="off" />

                  <span v-if="row.status == '1'"
                    class="px-2 py-1 text-xs font-semibold text-green-800 bg-green-100 rounded-full">
                    {{ formTranslation.common.active }}
                  </span>
                  <span v-else class="px-2 py-1 text-xs font-semibold text-red-800 bg-red-100 rounded-full">
                    {{ formTranslation.common.inactive }}
                  </span>
                </div>
              </td>
              <td class="px-4 py-3 text-sm">
                <div class="flex space-x-2">
                  <button v-if="kcCheckPermission('static_data_edit')" @click="editStaticData(row, row.id)"
                    class="p-1 hover:bg-gray-100 rounded" :title="formTranslation.clinic_schedule.dt_lbl_edit">
                    <i class="fa fa-pen-alt text-gray-600"></i>
                  </button>
                  <button v-if="getUserRole() == 'administrator' && kcCheckPermission('static_data_delete')"
                    @click="deleteListingData(row, index + 1)" class="p-1 hover:bg-gray-100 rounded"
                    :title="formTranslation.clinic_schedule.dt_lbl_dlt">
                    <i class="fa fa-trash text-red-500"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200">
        <div class="flex items-center gap-2">
          <span class="text-sm text-gray-700">Rows per page:</span>
          <select @change="onPerPageChange" v-model="staticDataList.serverParams.perPage"
            class="border border-gray-300 rounded-md text-sm p-1">
            <option>10</option>
            <option>25</option>
            <option>50</option>
          </select>
        </div>
        <div class="flex items-center gap-4">
          <span class="text-sm text-gray-700">
            Page {{ staticDataList.serverParams.page }} of {{ Math.ceil(totalRows / staticDataList.serverParams.perPage)
            }}
          </span>
          <div class="flex gap-2">
            <button @click="onPageChange(staticDataList.serverParams.page - 1)"
              :disabled="staticDataList.serverParams.page === 1"
              class="p-1 rounded hover:bg-gray-100 disabled:opacity-50">
              <i class="fas fa-chevron-left"></i>
            </button>
            <button @click="onPageChange(staticDataList.serverParams.page + 1)"
              :disabled="staticDataList.serverParams.page === Math.ceil(totalRows / staticDataList.serverParams.perPage)"
              class="p-1 rounded hover:bg-gray-100 disabled:opacity-50">
              <i class="fas fa-chevron-right"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { post, get } from "../../config/request";
import Create from "./Create";
import _ from "lodash";
import moment from "moment";

export default {
  data() {
    return {
      visible: false,
      request_status: "off",
      showEditForm: false,
      showAddForm: false,
      staticId: -1,
      pageLoader: true,
      staticDataList: {
        column: [],
        data: [],
        serverParams: {
          columnFilters: {},
          sort: [
            {
              field: "",
              type: "",
            },
          ],
          page: 1,
          perPage: 10,
          searchTerm: "",
        },
      },
      oldServerParams: {
        columnFilters: {},
        searchTerm: "",
        perPage: 10,
      },
      totalRows: 0,
      requireProVersion: "2.0.0"
    };
  },
  components: { Create },
  mounted() {
    if (["patient"].includes(this.getUserRole())) {
      this.$router.push({ name: "403" });
    }
    this.init();
    this.getModule();
  },
  methods: {
    init() {
      this.staticDataList = this.defaultStaticDataList();
      this.getStaticData();
      if (
        !this.$store.state.staticDataModule.static_data.static_data_types !== undefined
      ) {
        this.$store.dispatch("staticDataModule/fetchStaticData", {
          type: "static_data_types",
        });
      }
    },
    defaultStaticDataList() {
      return {
        column: [
          {
            field: "id",
            label: this.formTranslation.common.id,
            width: "100px",
            filterOptions: {
              enabled: true,
              placeholder: this.formTranslation.common.id,
              filterValue: "",
            },
          },
          {
            field: "label",
            label: this.formTranslation.static_data.dt_lbl_name,
            filterOptions: {
              enabled: true,
              placeholder:
                this.formTranslation.static_data.dt_lbl_plh_fltr_name,
              filterValue: "",
            },
          },
          {
            field: "type",
            label: this.formTranslation.static_data.dt_lbl_type,
            filterOptions: {
              enabled: true,
              placeholder:
                this.formTranslation.static_data.dt_lbl_plh_fltr_type,
              filterValue: "",
              filterDropdownItems: [
                {
                  value: "specialization",
                  text: this.formTranslation.doctor.dt_lbl_specialties,
                },
                {
                  value: "service_type",
                  text: this.formTranslation.widgets.service_type,
                },
                {
                  value: "prescription_medicine",
                  text: this.formTranslation.patient_encounter.prescription,
                },
                {
                  value: "clinical_problems",
                  text: this.formTranslation.encounter_dashboard.problems,
                },
                {
                  value: "clinical_observations",
                  text: this.formTranslation.encounter_dashboard.observation,
                },
              ],
            },
          },
          {
            field: "status",
            label: this.formTranslation.static_data.dt_lbl_status,
            width: "150px",
            filterOptions: {
              enabled: true,
              placeholder:
                this.formTranslation.static_data.dt_lbl_plh_sr_fltr_status,
              filterValue: "",
              filterDropdownItems: [
                { value: "1", text: this.formTranslation.common.active },
                { value: "0", text: this.formTranslation.common.inactive },
              ],
            },
            html: true,
          },
          {
            field: "actions",
            sortable: false,
            label: this.formTranslation.static_data.dt_lbl_action,
            html: true,
          },
        ],
        data: [],
        serverParams: {
          columnFilters: {},
          sort: [
            {
              field: "",
              type: "",
            },
          ],
          page: 1,
          perPage: 10,
          searchTerm: "",
        },
      };
    },
    getStaticData() {
      this.pageLoader = true;
      get("static_data_list", this.staticDataList.serverParams)
        .then((response) => {
          if (response.data.status !== undefined && response.data.status === true) {
            this.pageLoader = false;
            this.staticDataList.data = response.data.data;
            this.totalRows = response.data.total_rows;
          } else {
            this.pageLoader = false;
            this.staticDataList.data = [];
            this.totalRows = 0;
          }
        })
        .catch((error) => {
          console.log(error);
          this.displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },

    displayErrorMessage(message) {
      this.$swal.fire({
        icon: "error",
        title: "Error",
        text: message,
        timer: 1500,
        showConfirmButton: false,
      });
    },

    deleteListingData(data, index) {
      this.$swal
        .fire({
          title: this.formTranslation.clinic_schedule.dt_are_you_sure,
          text: this.formTranslation.common.reset_appointment_slot,
          icon: "warning",
          showCancelButton: true,
          confirmButtonColor: "#dc3545", // Bootstrap danger color
          cancelButtonColor: "#6c757d", // Bootstrap secondary color
          confirmButtonText: this.formTranslation.common.yes,
          cancelButtonText: this.formTranslation.common.cancel,
        })
        .then((result) => {
          if (result.isConfirmed) {
            post("static_data_delete", { id: data.id })
              .then((response) => {
                if (
                  response.data.status !== undefined &&
                  response.data.status === true
                ) {
                  this.getStaticData(); // Refresh the data instead of manually splicing
                  this.$swal.fire({
                    icon: "success",
                    title: "Success",
                    text: response.data.message,
                    timer: 1500,
                    showConfirmButton: false,
                  });
                } else {
                  this.$swal.fire({
                    icon: "error",
                    title: "Error",
                    text: response.data.message,
                    timer: 1500,
                    showConfirmButton: false,
                  });
                }
              })
              .catch((error) => {
                console.log(error);
                this.$swal.fire({
                  icon: "error",
                  title: "Error",
                  text: this.formTranslation.common.internal_server_error,
                  timer: 1500,
                  showConfirmButton: false,
                });
              });
          }
        });
    },

    updateParams(newProps) {
      this.staticDataList.serverParams = Object.assign({}, this.staticDataList.serverParams, newProps);
      this.getStaticData();
    },

    onPageChange(page) {
      this.updateParams({ page: page });
    },

    onPerPageChange(event) {
      const perPage = parseInt(event.target.value);
      if (this.oldServerParams.perPage === perPage) {
        return;
      }
      this.oldServerParams.perPage = perPage;
      this.updateParams({
        perPage: perPage,
        page: 1,
      });
    },

    sortDirection(field) {
  // Check current sort status
  const currentSort = this.staticDataList.serverParams.sort[0];
  
  // If already sorting by this field, toggle direction
  if (currentSort && currentSort.field === field) {
    return currentSort.type === 'asc' ? 'desc' : 'asc';
  }
  
  // Default to ascending sort
  return 'asc';
},

getSortIconClass(field) {
  const currentSort = this.staticDataList.serverParams.sort[0];
  
  // If not sorting by this field, show default icon
  if (!currentSort || currentSort.field !== field) {
    return 'fa-sort';
  }
  
  // Show appropriate sort direction icon
  return currentSort.type === 'asc' ? 'fa-sort-up' : 'fa-sort-down';
},

    onSortChange(params) {
      this.updateParams({
        sort: params,
      });
    },

    globalFilter: _.debounce(function (event) {
      const searchTerm = event.target.value;
      if (this.oldServerParams.searchTerm === searchTerm) {
        return;
      }
      this.oldServerParams.searchTerm = searchTerm;
      this.updateParams({
        searchTerm: searchTerm,
        page: 1,
      });
    }, 300),

    onColumnFilter: _.debounce(function (params) {
      let emptyValue = true;
      let emptyValue2 = true;

      Object.values(params.columnFilters).forEach(value => {
        if (value) {
          emptyValue = false;
        }
      });

      Object.values(this.oldServerParams.columnFilters).forEach(value => {
        if (value) {
          emptyValue2 = false;
        }
      });

      if (!emptyValue || !emptyValue2) {
        this.oldServerParams.columnFilters = Object.assign(
          {},
          params.columnFilters
        );
        this.updateParams({
          columnFilters: params.columnFilters,
          page: 1,
        });
      }
    }, 300),

    editStaticData(data, id) {
      this.staticId = id;
      this.showAddForm = false;
      this.showEditForm = true;
      this.visible = true;
      window.scroll({ top: 0, behavior: "smooth" });
    },

    handleStaticDataForm() {
      if (!this.showAddForm) {
        this.visible = true;
        this.showAddForm = true;
        this.showEditForm = false;
      } else {
        this.visible = false;
        this.showAddForm = false;
        this.showEditForm = false;
      }
    },

    closeForm() {
      this.visible = false;
      this.staticId = -1;
      this.showAddForm = false;
      this.showEditForm = false;
    },

    getModule() {
      if (
        window.request_data &&
        window.request_data.link_show_hide !== undefined &&
        window.request_data.link_show_hide !== ""
      ) {
        this.request_status = window.request_data.link_show_hide;
      }
    },

    changeModuleValueStatus(data) {
      post("change_module_status", data)
        .then(response => {
          if (response.data.status) {
            this.$swal.fire({
              icon: "success",
              title: "Success",
              text: response.data.message,
              timer: 1500,
              showConfirmButton: false,
            });
          } else {
            this.$swal.fire({
              icon: "error",
              title: "Error",
              text: response.data.message,
              timer: 1500,
              showConfirmButton: false,
            });
          }
        })
        .catch(error => {
          console.error(error);
          this.$swal.fire({
            icon: "error",
            title: "Error",
            text: this.formTranslation.common.internal_server_error,
            timer: 1500,
            showConfirmButton: false,
          });
        });
    },

    kivicareCompareVersion(required, current) {
      if (!required || !current) return false;

      const requiredParts = required.split('.').map(Number);
      const currentParts = current.split('.').map(Number);

      for (let i = 0; i < requiredParts.length; i++) {
        if (currentParts[i] === undefined) return false;
        if (currentParts[i] > requiredParts[i]) return true;
        if (currentParts[i] < requiredParts[i]) return false;
      }

      return true;
    }
  },
  computed: {
    ListingDataExport() {
      return "Listing Data - " + moment().format("YYYY-MM-DD");
    },
    userData() {
      return this.$store.state.userDataModule.user;
    },
    formTranslation() {
      return this.$store.state.staticDataModule.langTranslateData;
    }
  }
};
</script>
