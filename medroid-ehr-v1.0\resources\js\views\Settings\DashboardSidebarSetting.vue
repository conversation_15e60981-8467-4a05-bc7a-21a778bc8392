<template>
  <div class="w-full">
    <div class="flex flex-col">
      <div class="w-full">
        <!-- Replace b-overlay with custom overlay -->
        <div class="relative" v-if="userData.addOns.kiviPro != true">
          <div class="absolute inset-0 bg-white bg-opacity-50 z-10">
            <overlay-message addon_type="pro"></overlay-message>
          </div>
        </div>
        
        <form id="doctorDataForm" class="w-full" :novalidate="true">
          <div class="bg-white rounded-lg shadow-lg">
            <!-- Header -->
            <div class="px-6 py-4 border-b">
              <div class="flex justify-between items-center">
                <div class="flex-grow">
                  <h2 class="text-xl font-semibold">
                    {{ formTranslation.common.dashboard_sidebar_setting }}
                    <a v-if="request_status == 'off'" 
                       href="https://apps.medroid.ai/docs/product/kivicare/clinic-admin/settings/#dashboard-sidebar" 
                       target="_blank"
                       class="ml-2 text-gray-500 hover:text-gray-700">
                      <i class="fa fa-question-circle"></i>
                    </a>
                  </h2>
                </div>
              </div>
            </div>

            <!-- Loader -->
            <div v-if="isLoading" class="p-4">
              <loader-component-2></loader-component-2>
            </div>

            <!-- Content -->
            <div v-else class="p-4">
              <div class="space-y-4">
                <!-- Accordion Items -->
                <div v-for="(head, headIndex) in allUserList" 
                     :key="headIndex" 
                     class="border rounded-lg">
                  
                  <!-- Accordion Header -->
                  <div class="border-b">
                    <button type="button" @click="mainAccordian(head.id)"
                            class="w-full px-4 py-3 text-left flex items-center hover:bg-gray-50 focus:outline-none">
                      <span class="flex-grow font-medium">{{ head.label }}</span>
                      <span class="transform transition-transform duration-200"
                            :class="{'rotate-180': selectedMainAccrodionId === head.id}">
                        <i class="fa fa-chevron-down"></i>
                      </span>
                    </button>
                  </div>

                  <!-- Accordion Content -->
                  <div v-show="selectedMainAccrodionId === head.id"
                       class="p-4">
                    <table class="min-w-full divide-y divide-gray-200">
                      <thead>
                        <tr>
                          <th class="w-12"></th>
                          <th class="px-4 py-2 text-left text-sm font-medium text-gray-700">
                            {{formTranslation.common.label}}
                          </th>
                          <th class="px-4 py-2 text-left text-sm font-medium text-gray-700">
                            {{formTranslation.common.url}}
                          </th>
                          <th class="px-4 py-2 text-left text-sm font-medium text-gray-700">
                            {{formTranslation.common.icon}}
                            <a v-if="request_status == 'off'" 
                               href="https://fontawesome.com/v5/search" 
                               target="_blank"
                               class="ml-2 text-gray-500 hover:text-gray-700">
                              <i class="fa fa-question-circle"></i>
                            </a>
                          </th>
                          <th class="px-4 py-2 text-left text-sm font-medium text-gray-700">
                            {{formTranslation.doctor.dt_lbl_actions}}
                          </th>
                        </tr>
                      </thead>
                      <draggable v-model="dashboardSidebarData[head.id]" 
                                tag="tbody"
                                :options="{ animation:1000, handle:'.my_handle:not(.exclude-this-item)'}"
                                class="divide-y divide-gray-200">
                        <tr v-for="(itemValue,itemkey) in dashboardSidebarData[head.id]" 
                            :key="itemkey"
                            class="draggable hover:bg-gray-50">
                          <td class="my_handle px-4 py-2">
                            <i class="fa fa-align-justify cursor-move text-gray-500"></i>
                          </td>
                          <td class="px-4 py-2">
                            <input type="text" 
                                   v-model="itemValue.label" 
                                   :disabled="!itemValue.custom"
                                   class="w-full px-3 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500">
                            <div :id="'labelValid'+head.id+itemkey" 
                                 class="hidden mt-1 text-sm text-red-600">
                              {{formTranslation.clinic_schedule.module_type_required}}
                            </div>
                          </td>
                          <td class="px-4 py-2">
                            <input v-if="itemValue.type === 'href'" 
                                   type="url" 
                                   v-model="itemValue.link"
                                   class="w-full px-3 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500">
                            <input v-else 
                                   type="url" 
                                   :value="routeUrl(itemValue.link)" 
                                   disabled
                                   class="w-full px-3 py-2 border rounded-md bg-gray-100">
                          </td>
                          <td class="px-4 py-2">
                            <input type="text" 
                                   v-model="itemValue.iconClass"
                                   class="w-full px-3 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500">
                          </td>
                          <td class="px-4 py-2">
                            <button v-if="itemValue.custom" 
                                    @click.prevent="deleteDashboardSidebarData(head.id,itemkey)"
                                    class="p-2 text-red-600 hover:text-red-800 focus:outline-none">
                              <i class="fa fa-trash"></i>
                            </button>
                          </td>
                        </tr>
                      </draggable>
                    </table>

                    <!-- Action Buttons -->
                    <div class="flex justify-end space-x-4 mt-4">
                      <button type="button" @click="addNewDashboardSidebarData(head.id)"
                              class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        <i class="fa fa-plus mr-2"></i>Add New
                      </button>
                      <button :id="'sidebarButton'+head.id"
                              @click="saveDashboardSidebarData(head.id)"
                              class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        <i class="fa fa-save mr-2"></i>{{formTranslation.common.save}}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import {get,post} from "../../config/request";
import draggable from 'vuedraggable';

export default {
  name: "DashboardSidebarSetting",
  components:{
    draggable
  },
  data:() => {
    return {
      isLoading:false,
      request_status: window.request_data.link_show_hide ? window.request_data.link_show_hide : 'off',
      allUserList:[],
      selectedMainAccrodionId:'administrator',
      dashboardSidebarData:[]
    }
  },
  mounted() {
    if(!['administrator'].includes(this.getUserRole())) {
      this.$router.push({ name: "403"})
    }
    this.allUserList = [
      {id:'administrator',label:'Administrator'},
      {id:'clinic_admin',label:this.formTranslation.clinic.clinic},
      {id:'receptionist',label:this.formTranslation.clinic.receptionist},
      {id:'doctor',label:this.formTranslation.common.doctors},
      {id:'patient',label:this.formTranslation.dashboard.patients},
    ];
    this.getSidebarData();
  },
  methods:{
    mainAccordian(value){
      this.selectedMainAccrodionId = this.selectedMainAccrodionId === value ? '' : value;
    },
    getSidebarData(){
      this.isLoading = true;
      get('get_dashbaord_sidebar_data', {})
        .then((response) => {
          this.isLoading = false;
          if (response.data.status !== undefined && response.data.status === true) {
            this.dashboardSidebarData = response.data.data;
          }
        })
        .catch((error) => {
          this.isLoading = false;
          console.log(error);
        })
    },
    saveDashboardSidebarData(type){
      let sidebar_data_valid = true;
      document.querySelectorAll('.invalid-feedback').forEach(el => {
        el.parentNode.classList.add('hidden');
      });
      
      this.dashboardSidebarData[type].forEach((index, key)=> {
        if(!(index.label) || !(index.link)){
          document.getElementById('urlValid'+type+key).parentNode.classList.remove('hidden');
          document.getElementById('labelValid'+type+key).parentNode.classList.remove('hidden');          
          sidebar_data_valid = false;
        }
      })
      
      if(!(sidebar_data_valid)){
        return;
      }

      const button = document.getElementById('sidebarButton' + type);
      button.disabled = true;
      button.innerHTML = `<i class='fa fa-sync fa-spin mr-2'></i>${this.formTranslation.common.loading}`;

      post('save_dashbaord_sidebar_data', {data:this.dashboardSidebarData[type],type:type})
        .then((response) => {
          button.disabled = false;
          button.innerHTML = `<i class='fa fa-save mr-2'></i>${this.formTranslation.common.save}`;
          
          if (response.data.status !== undefined && response.data.status === true) {
            this.$store.dispatch("userDataModule/fetchUserData", {});
            displayMessage(response.data.message);
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          button.disabled = false;
          button.innerHTML = `<i class='fa fa-save mr-2'></i>${this.formTranslation.common.save}`;
          console.log(error);
        })
    },
    addNewDashboardSidebarData(type){
      this.dashboardSidebarData[type].push({
        label: '',
        type: 'href',
        link: '',
        iconClass: '',
        routeClass: '',
        show: true,
        custom: true
      })
    },
    deleteDashboardSidebarData(type,index){
      this.dashboardSidebarData[type].splice(index, 1);
    },
    routeUrl(name){
      let props = this.$router.resolve({
        name: name
      });
      return props.href ? window.request_data.menu_url + props.href : '#'
    }
  },
  computed: {
    userData() {
      return this.$store.state.userDataModule.user;
    }
  }
}
</script>