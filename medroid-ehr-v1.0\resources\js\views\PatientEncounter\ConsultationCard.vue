# ConsultationCard.vue
<template>
  <div class="bg-white text-card-foreground rounded-xl border shadow overflow-hidden hover:shadow-md transition-shadow">
    <div class="p-6">
      <div class="p-4">
        <!-- Header Section -->
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 text-purple-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5"></path>
                <rect x="2" y="6" width="14" height="12" rx="2"></rect>
              </svg>
            </div>
            <div>
              <h3 class="font-semibold text-lg">{{ consultation.doctor_name }}</h3>
              <p class="text-gray-600">{{ consultation.specialty }}</p>
              <div class="flex items-center space-x-2 mt-1">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M8 2v4"></path>
                  <path d="M16 2v4"></path>
                  <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                  <path d="M3 10h18"></path>
                </svg>
                <span class="text-sm text-gray-600">{{ formatDate(consultation.encounter_date) }}</span>
                <span class="text-sm text-gray-400">•</span>
                <span :class="[
                  'text-sm px-2 py-0.5 rounded-full',
                  consultation.status === '1' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'
                ]">
                  {{ consultation.status === '1' ? 'Completed' : 'Scheduled' }}
                </span>
              </div>
            </div>
          </div>
          <div class="mt-4 md:mt-0 flex items-center space-x-3">
            <button 
              @click="redirectToDashboard"
              class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
            View Details
            </button>
          </div>
        </div>

      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ConsultationCard',
  
  props: {
    consultation: {
      type: Object,
      required: true,
      default: () => ({
        doctor_name: '',
        specialty: '',
        encounter_date: '',
        status: '',
        notes: 'Patient reports improved sleep patterns. BP readings stable at 120/80. Continue current medication regimen.',
        prescriptions: ['Lisinopril 10mg', 'Vitamin D3'],
        attachments: ['Blood Work Results', 'BP Log'],
        rating: 5
      })
    }
  },

  data() {
    return {
      showDetails: false
    }
  },

  methods: {
    formatDate(date) {
      if (!date) return '';
      return new Date(date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    },
    redirectToDashboard() {
      this.$router.push(`/patient-encounter/dashboard/${this.consultation.id}`);
    }
  }
}
</script>