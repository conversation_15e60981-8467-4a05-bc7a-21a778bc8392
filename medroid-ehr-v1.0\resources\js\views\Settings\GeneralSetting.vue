<template>
  <div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto">
      <!-- Loading State -->
      <div v-if="formLoader" class="flex justify-center items-center min-h-[400px]">
        <loader-component-2></loader-component-2>
      </div>

      <div v-else class="p-6 space-y-8">
        <!-- Settings Header -->
        <div class="border-b border-gray-200 pb-4">
          <div class="flex items-center justify-between">
            <h2 class="text-2xl font-semibold text-gray-900">
              {{ formTranslation.common.settings }}
              <a v-if="request_status == 'off'"
                href="https://apps.medroid.ai/docs/product/kivicare/clinic-admin/settings/#general-setting"
                target="_blank" class="ml-2 text-blue-600 hover:text-blue-700">
                <i class="fa fa-question-circle"></i>
              </a>
            </h2>
          </div>
        </div>

        <!-- Request Features Form -->
        <form v-if="request_status == 'off' || showOption" @submit.prevent="handleSubmit" class="space-y-6">
          <div class="flex items-center space-x-3">
            <toggle-switch :value="request_status" @input="(value) => (request_status = value)"
              :label="formTranslation.common.hide_request_features" on-value="on" off-value="off" />
          </div>

          <!-- Remove Features Option -->
          <div v-if="request_status == 'on' && showOption" class="flex items-center space-x-3">
            <toggle-switch :value="request_status" @input="(value) => (request_status = value)"
              @change="removeFeatureRequest" :label="formTranslation.common.remove_request_features" on-value="on"
              off-value="off" />
          </div>

          <div class="flex justify-end">
            <button type="submit" :disabled="isSubmited"
              class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md shadow-sm disabled:opacity-50">
              <i class="fa fa-save mr-2"></i>
              {{ formTranslation.common.save }}
            </button>
          </div>
        </form>

        <!-- Clinic Currency Section -->
        <div
          class="relative bg-white rounded-xl shadow-lg border border-gray-100 p-8 transition-all duration-300 hover:shadow-xl overflow-hidden">
          <!-- Decorative Background Elements -->
          <div class="absolute top-0 right-0 w-40 h-40 bg-blue-50 rounded-full -mr-20 -mt-20 opacity-70"></div>
          <div class="absolute bottom-0 left-0 w-32 h-32 bg-blue-50 rounded-full -ml-16 -mb-16 opacity-70"></div>

          <!-- Section Header -->
          <div class="relative z-10 flex items-center space-x-4 mb-8">
            <div
              class="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-md transform transition-transform duration-300 hover:scale-105">
              <i class="fa fa-dollar-sign text-white text-xl"></i>
            </div>
            <div>
              <h2 class="text-2xl font-bold text-gray-900 mb-1">
                {{ formTranslation.common.clinic_currency }}
              </h2>
              <p class="text-sm text-gray-500">
                Configure your clinic's currency display settings
              </p>
            </div>
          </div>

          <form @submit.prevent="handleGeneralSettingSubmit" class="relative z-10 space-y-8">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
              <!-- Currency Prefix Input Card -->
              <div
                class="bg-gray-50 p-6 rounded-xl border border-gray-200 transition-all duration-300 hover:border-blue-200 hover:shadow-md">
                <div class="flex items-start space-x-3 mb-4">
                  <div class="p-2 bg-blue-100 rounded-lg">
                    <i class="fa fa-tag text-blue-600"></i>
                  </div>
                  <div>
                    <label class="block font-semibold text-gray-900 mb-1">
                      {{ formTranslation.clinic.product_country }}
                    </label>
                    <p class="text-xs text-gray-500">
                      Enter country (e.g., US, UK)
                    </p>
                  </div>
                </div>

                <div class="relative mt-2">
                  <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fa fa-dollar-sign text-gray-400"></i>
                  </div>
                  <input type="text" v-model="productData.country"
                    class="w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                    placeholder="Enter country" />
                </div>
              </div>
            </div>

            <!-- Save Button -->
            <div class="flex justify-end pt-4">
              <button type="submit" :disabled="isSubmitedClinic"
                class="group relative inline-flex items-center px-8 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white text-sm font-semibold rounded-xl shadow-md transition-all duration-300 hover:shadow-lg hover:from-blue-700 hover:to-blue-800 disabled:opacity-50 disabled:cursor-not-allowed">
                <i :class="isSubmitedClinic
                    ? 'fa fa-spinner animate-spin'
                    : 'fa fa-save transition-transform duration-200 group-hover:scale-110'
                  " class="mr-2"></i>
                {{
                  isSubmitedClinic
                    ? formTranslation.common.loading
                    : formTranslation.common.save
                }}
              </button>
            </div>
          </form>

          <form @submit.prevent="handleCliniSubmit" class="relative z-10 space-y-8">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
              <!-- Currency Prefix Input Card -->
              <div
                class="bg-gray-50 p-6 rounded-xl border border-gray-200 transition-all duration-300 hover:border-blue-200 hover:shadow-md">
                <div class="flex items-start space-x-3 mb-4">
                  <div class="p-2 bg-blue-100 rounded-lg">
                    <i class="fa fa-tag text-blue-600"></i>
                  </div>
                  <div>
                    <label class="block font-semibold text-gray-900 mb-1">
                      {{ formTranslation.clinic.currency_prefix }}
                    </label>
                    <p class="text-xs text-gray-500">
                      Enter currency symbol (e.g., $, €, £)
                    </p>
                  </div>
                </div>

                <div class="relative mt-2">
                  <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fa fa-dollar-sign text-gray-400"></i>
                  </div>
                  <input type="text" v-model="clinicData.currency_prefix"
                    class="w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                    placeholder="Enter prefix" />
                </div>
              </div>

              <!-- Currency Postfix Input Card -->
              <div
                class="bg-gray-50 p-6 rounded-xl border border-gray-200 transition-all duration-300 hover:border-blue-200 hover:shadow-md">
                <div class="flex items-start space-x-3 mb-4">
                  <div class="p-2 bg-green-100 rounded-lg">
                    <i class="fa fa-tags text-green-600"></i>
                  </div>
                  <div>
                    <label class="block font-semibold text-gray-900 mb-1">
                      {{ formTranslation.clinic.currency_postfix }}
                    </label>
                    <p class="text-xs text-gray-500">
                      Enter currency code (e.g., USD, EUR)
                    </p>
                  </div>
                </div>

                <div class="relative mt-2">
                  <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fa fa-money-bill-alt text-gray-400"></i>
                  </div>
                  <input type="text" v-model="clinicData.currency_postfix"
                    class="w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                    placeholder="Enter postfix" />
                </div>
              </div>
            </div>

            <!-- Preview Section (Optional) -->
            <div v-if="clinicData.currency_prefix || clinicData.currency_postfix"
              class="bg-blue-50 p-4 rounded-lg border border-blue-100">
              <div class="flex items-center">
                <i class="fa fa-eye text-blue-600 mr-2"></i>
                <span class="text-sm text-blue-800">Preview: </span>
                <span class="ml-2 font-medium text-blue-900">
                  {{ clinicData.currency_prefix }}100{{
                    clinicData.currency_postfix
                  }}
                </span>
              </div>
            </div>

            <!-- Save Button -->
            <div class="flex justify-end pt-4">
              <button type="submit" :disabled="isSubmitedClinic"
                class="group relative inline-flex items-center px-8 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white text-sm font-semibold rounded-xl shadow-md transition-all duration-300 hover:shadow-lg hover:from-blue-700 hover:to-blue-800 disabled:opacity-50 disabled:cursor-not-allowed">
                <i :class="isSubmitedClinic
                    ? 'fa fa-spinner animate-spin'
                    : 'fa fa-save transition-transform duration-200 group-hover:scale-110'
                  " class="mr-2"></i>
                {{
                  isSubmitedClinic
                    ? formTranslation.common.loading
                    : formTranslation.common.save
                }}
              </button>
            </div>
          </form>
        </div>

        <!-- Country Code Section -->
        <div
          class="relative bg-white rounded-xl shadow-lg border border-gray-100 p-8 transition-all duration-300 hover:shadow-xl overflow-hidden">
          <!-- Decorative Background Elements -->
          <div class="absolute top-0 right-0 w-40 h-40 bg-blue-50 rounded-full -mr-20 -mt-20 opacity-70"></div>
          <div class="absolute bottom-0 left-0 w-32 h-32 bg-blue-50 rounded-full -ml-16 -mb-16 opacity-70"></div>

          <!-- Section Header -->
          <div class="relative z-10 flex items-center space-x-4 mb-8">
            <div
              class="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-md transform transition-transform duration-300 hover:scale-105">
              <i class="fa fa-globe text-white text-xl"></i>
            </div>
            <div>
              <h2 class="text-2xl font-bold text-gray-900 mb-1">
                {{ formTranslation.common.default_country_code }}
              </h2>
              <p class="text-sm text-gray-500">
                Configure your regional phone settings
              </p>
            </div>
          </div>

          <form @submit.prevent="handleCountryCodeSubmit" class="relative z-10 space-y-8">
            <div class="max-w-md">
              <!-- Input Section -->
              <div
                class="bg-gray-50 p-6 rounded-xl border border-gray-200 transition-all duration-300 hover:border-blue-200 hover:shadow-md">
                <label class="block text-sm font-semibold text-gray-700 mb-3 flex items-center">
                  <i class="fa fa-phone-alt text-blue-500 mr-2"></i>
                  {{ formTranslation.common.select_country_code }}
                </label>

                <div
                  class="relative bg-white rounded-lg shadow-sm transition-all duration-200 focus-within:ring-2 focus-within:ring-blue-200">
                  <VuePhoneNumberInput id="country_code" :default-country-code="defaultCountryCode" v-model="contact"
                    @update="countryCodeUpdateHandaler" no-example clearable class="w-full" />
                </div>

                <!-- Selected Country Info -->
                <div v-if="countryCodeData.countrycode"
                  class="mt-4 flex items-center text-sm text-gray-600 bg-blue-50 p-3 rounded-lg">
                  <i class="fa fa-info-circle text-blue-500 mr-2"></i>
                  <span>Selected Country:
                    <span class="font-semibold">{{
                      countryCodeData.countrycode
                    }}</span></span>
                  <span class="mx-2 text-gray-400">|</span>
                  <span>Code:
                    <span class="font-semibold">+{{ countryCodeData.countryCallingCode }}</span></span>
                </div>
              </div>
            </div>

            <!-- Save Button -->
            <div class="flex justify-end pt-4">
              <button type="submit" :disabled="isSubmitedCountryCode"
                class="group relative inline-flex items-center px-8 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white text-sm font-semibold rounded-xl shadow-md transition-all duration-300 hover:shadow-lg hover:from-blue-700 hover:to-blue-800 disabled:opacity-50 disabled:cursor-not-allowed">
                <i class="fa fa-save mr-2 transition-transform duration-200 group-hover:scale-110"></i>
                {{ formTranslation.common.save }}
                <span v-if="isSubmitedCountryCode" class="ml-2">
                  <i class="fa fa-spinner animate-spin"></i>
                </span>
              </button>
            </div>
          </form>
        </div>

        <!-- Doctor Registration Prefix section removed - now handled at the doctor level -->

        <!-- Registration Shortcode Settings -->
        <div
          class="relative bg-white rounded-xl shadow-lg border border-gray-100 p-8 transition-all duration-300 hover:shadow-xl overflow-hidden">
          <!-- Decorative Background Elements -->
          <div class="absolute top-0 right-0 w-40 h-40 bg-blue-50 rounded-full -mr-20 -mt-20 opacity-70"></div>
          <div class="absolute bottom-0 left-0 w-32 h-32 bg-blue-50 rounded-full -ml-16 -mb-16 opacity-70"></div>

          <!-- Section Header -->
          <div class="relative z-10 flex items-center space-x-4 mb-8">
            <div
              class="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-md transform transition-transform duration-300 hover:scale-105">
              <i class="fa fa-user-plus text-white text-xl"></i>
            </div>
            <div>
              <h2 class="text-2xl font-bold text-gray-900 mb-1">
                {{ formTranslation.common.registration_shortcode_setting }}
              </h2>
              <p class="text-sm text-gray-500">
                Configure registration settings for different user roles
              </p>
            </div>
          </div>

          <form @submit.prevent="handleUserRegistrationShortcodeSettingSubmit" class="relative z-10 space-y-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <!-- Doctor Register -->
              <div
                class="bg-gray-50 p-6 rounded-xl border border-gray-200 transition-all duration-300 hover:border-blue-200 hover:shadow-md">
                <div class="flex items-start space-x-3 mb-4">
                  <div class="p-2 bg-blue-100 rounded-lg">
                    <i class="fa fa-user-md text-blue-600"></i>
                  </div>
                  <div>
                    <h3 class="font-semibold text-gray-900 mb-1">
                      Doctor Registration
                    </h3>
                    <p class="text-xs text-gray-500">
                      Set default registration status
                    </p>
                  </div>
                </div>

                <div class="space-y-4">
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium mb-2" :class="userRegistrationShortcodeSetting.status.doctor === 'on'
                      ? 'bg-green-100 text-green-800 border border-green-200'
                      : 'bg-red-100 text-red-800 border border-red-200'
                    ">
                    <i class="fa" :class="userRegistrationShortcodeSetting.status.doctor === 'on'
                        ? 'fa-check-circle mr-1'
                        : 'fa-times-circle mr-1'
                      "></i>
                    {{
                      userRegistrationShortcodeSetting.status.doctor === "on"
                        ? formTranslation.common.active
                        : formTranslation.common.inactive
                    }}
                  </span>

                  <toggle-switch :value="userRegistrationShortcodeSetting.status.doctor" @input="
                    (value) =>
                      (userRegistrationShortcodeSetting.status.doctor = value)
                  " on-value="on" off-value="off">
                    <span class="text-sm font-medium text-gray-700">
                      {{
                        formTranslation.common
                          .default_status_when_doctor_register
                      }}
                    </span>
                  </toggle-switch>
                </div>
              </div>

              <!-- Receptionist Register -->
              <div
                class="bg-gray-50 p-6 rounded-xl border border-gray-200 transition-all duration-300 hover:border-blue-200 hover:shadow-md">
                <div class="flex items-start space-x-3 mb-4">
                  <div class="p-2 bg-purple-100 rounded-lg">
                    <i class="fa fa-user-tie text-purple-600"></i>
                  </div>
                  <div>
                    <h3 class="font-semibold text-gray-900 mb-1">
                      Receptionist Registration
                    </h3>
                    <p class="text-xs text-gray-500">
                      Set default registration status
                    </p>
                  </div>
                </div>

                <div class="space-y-4">
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium mb-2" :class="userRegistrationShortcodeSetting.status.receptionist ===
                      'on'
                      ? 'bg-green-100 text-green-800 border border-green-200'
                      : 'bg-red-100 text-red-800 border border-red-200'
                    ">
                    <i class="fa" :class="userRegistrationShortcodeSetting.status.receptionist ===
                        'on'
                        ? 'fa-check-circle mr-1'
                        : 'fa-times-circle mr-1'
                      "></i>
                    {{
                      userRegistrationShortcodeSetting.status.receptionist ===
                        "on"
                        ? formTranslation.common.active
                        : formTranslation.common.inactive
                    }}
                  </span>

                  <toggle-switch :value="userRegistrationShortcodeSetting.status.receptionist
                    " @input="
                      (value) =>
                      (userRegistrationShortcodeSetting.status.receptionist =
                        value)
                    " on-value="on" off-value="off">
                    <span class="text-sm font-medium text-gray-700">
                      {{
                        formTranslation.common
                          .default_status_when_receptionist_register
                      }}
                    </span>
                  </toggle-switch>
                </div>
              </div>

              <!-- Patient Register -->
              <div
                class="bg-gray-50 p-6 rounded-xl border border-gray-200 transition-all duration-300 hover:border-blue-200 hover:shadow-md">
                <div class="flex items-start space-x-3 mb-4">
                  <div class="p-2 bg-indigo-100 rounded-lg">
                    <i class="fa fa-user text-indigo-600"></i>
                  </div>
                  <div>
                    <h3 class="font-semibold text-gray-900 mb-1">
                      Patient Registration
                    </h3>
                    <p class="text-xs text-gray-500">
                      Set default registration status
                    </p>
                  </div>
                </div>

                <div class="space-y-4">
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium mb-2" :class="userRegistrationShortcodeSetting.status.patient === 'on'
                      ? 'bg-green-100 text-green-800 border border-green-200'
                      : 'bg-red-100 text-red-800 border border-red-200'
                    ">
                    <i class="fa" :class="userRegistrationShortcodeSetting.status.patient === 'on'
                        ? 'fa-check-circle mr-1'
                        : 'fa-times-circle mr-1'
                      "></i>
                    {{
                      userRegistrationShortcodeSetting.status.patient === "on"
                        ? formTranslation.common.active
                        : formTranslation.common.inactive
                    }}
                  </span>

                  <toggle-switch :value="userRegistrationShortcodeSetting.status.patient" @input="
                    (value) =>
                    (userRegistrationShortcodeSetting.status.patient =
                      value)
                  " on-value="on" off-value="off">
                    <span class="text-sm font-medium text-gray-700">
                      {{
                        formTranslation.common
                          .default_status_when_patient_register
                      }}
                    </span>
                  </toggle-switch>
                </div>
              </div>
            </div>

            <!-- Save Button -->
            <div class="flex justify-end pt-4">
              <button type="submit" :disabled="userRegistrationShortcodeSettingLoading"
                class="group relative inline-flex items-center px-8 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white text-sm font-semibold rounded-xl shadow-md transition-all duration-300 hover:shadow-lg hover:from-blue-700 hover:to-blue-800 disabled:opacity-50 disabled:cursor-not-allowed">
                <i :class="userRegistrationShortcodeSettingLoading
                    ? 'fa fa-spinner animate-spin'
                    : 'fa fa-save transition-transform duration-200 group-hover:scale-110'
                  " class="mr-2"></i>
                {{
                  userRegistrationShortcodeSettingLoading
                    ? formTranslation.common.loading
                    : formTranslation.common.save
                }}
              </button>
            </div>
          </form>
        </div>

        <!-- Google reCAPTCHA Section -->
        <div
          class="relative bg-white rounded-xl shadow-lg border border-gray-100 p-8 transition-all duration-300 hover:shadow-xl overflow-hidden">
          <!-- Decorative Background Elements -->
          <div class="absolute top-0 right-0 w-40 h-40 bg-purple-50 rounded-full -mr-20 -mt-20 opacity-70"></div>
          <div class="absolute bottom-0 left-0 w-32 h-32 bg-purple-50 rounded-full -ml-16 -mb-16 opacity-70"></div>

          <!-- Section Header -->
          <div class="relative z-10 flex items-center space-x-4 mb-8">
            <div
              class="p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow-md transform transition-transform duration-300 hover:scale-105">
              <i class="fa fa-shield-alt text-white text-xl"></i>
            </div>
            <div>
              <h2 class="text-2xl font-bold text-gray-900 mb-1">
                {{ formTranslation.common.google_recaptcha }}
              </h2>
              <p class="text-sm text-gray-500">
                Protect your forms from spam and abuse
              </p>
            </div>
          </div>

          <form @submit.prevent="handleCaptchaSubmit" class="relative z-10 space-y-8">
            <div
              class="bg-gray-50 p-6 rounded-xl border border-gray-200 transition-all duration-300 hover:border-purple-200 hover:shadow-md">
              <div class="flex items-start space-x-3 mb-4">
                <div class="p-2 bg-purple-100 rounded-lg">
                  <i class="fa fa-toggle-on text-purple-600"></i>
                </div>
                <div>
                  <toggle-switch :value="googleCaptcha.status" @input="(value) => (googleCaptcha.status = value)"
                    @change="handleCaptchaSubmit" :label="formTranslation.common.enable_google_recaptcha" on-value="on"
                    off-value="off" />
                </div>
              </div>

              <div v-if="googleCaptcha.status == 'on'" class="space-y-6 mt-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <!-- Site Key Input Card -->
                  <div
                    class="bg-white p-6 rounded-xl border border-gray-200 transition-all duration-300 hover:border-purple-200 hover:shadow-md">
                    <div class="flex items-start space-x-3 mb-4">
                      <div class="p-2 bg-purple-100 rounded-lg">
                        <i class="fa fa-key text-purple-600"></i>
                      </div>
                      <div>
                        <label class="block font-semibold text-gray-900 mb-1">
                          {{ formTranslation.common.site_key }}
                        </label>
                        <p class="text-xs text-gray-500">
                          Enter your reCAPTCHA site key
                        </p>
                      </div>
                    </div>

                    <div class="relative mt-2">
                      <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fa fa-globe text-gray-400"></i>
                      </div>
                      <input type="text" v-model="googleCaptcha.site_key"
                        class="w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 bg-white focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
                        placeholder="Enter site key" />
                    </div>
                  </div>

                  <!-- Secret Key Input Card -->
                  <div
                    class="bg-white p-6 rounded-xl border border-gray-200 transition-all duration-300 hover:border-purple-200 hover:shadow-md">
                    <div class="flex items-start space-x-3 mb-4">
                      <div class="p-2 bg-purple-100 rounded-lg">
                        <i class="fa fa-lock text-purple-600"></i>
                      </div>
                      <div>
                        <label class="block font-semibold text-gray-900 mb-1">
                          {{ formTranslation.common.secret_key }}
                        </label>
                        <p class="text-xs text-gray-500">
                          Enter your reCAPTCHA secret key
                        </p>
                      </div>
                    </div>

                    <div class="relative mt-2">
                      <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fa fa-key text-gray-400"></i>
                      </div>
                      <input type="text" v-model="googleCaptcha.secret_key"
                        class="w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 bg-white focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
                        placeholder="Enter secret key" />
                    </div>
                  </div>
                </div>

                <!-- Reference Link Card -->
                <div class="bg-purple-50 p-4 rounded-lg border border-purple-100">
                  <div class="flex items-center space-x-2">
                    <i class="fa fa-external-link-alt text-purple-600"></i>
                    <a href="https://www.google.com/recaptcha/admin" target="_blank"
                      class="text-purple-600 hover:text-purple-700 font-medium transition-colors duration-200">
                      {{ formTranslation.common.google_recaptcha_refer_link }}
                    </a>
                  </div>
                </div>

                <!-- Save Button -->
                <div class="flex justify-end pt-4">
                  <button type="submit" :disabled="googleRecaptchaLoading"
                    class="group relative inline-flex items-center px-8 py-3 bg-gradient-to-r from-purple-600 to-purple-700 text-white text-sm font-semibold rounded-xl shadow-md transition-all duration-300 hover:shadow-lg hover:from-purple-700 hover:to-purple-800 disabled:opacity-50 disabled:cursor-not-allowed">
                    <i :class="googleRecaptchaLoading
                        ? 'fa fa-spinner animate-spin'
                        : 'fa fa-save transition-transform duration-200 group-hover:scale-110'
                      " class="mr-2"></i>
                    {{
                      googleRecaptchaLoading
                        ? formTranslation.common.loading
                        : formTranslation.common.save
                    }}
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
        <!-- Login Redirect Section -->
        <div class="relative bg-white rounded-3xl shadow-sm overflow-hidden">
          <!-- Decorative Corner Elements -->
          <div class="absolute top-0 right-0 w-40 h-40 bg-blue-50 rounded-full -mr-20 -mt-20 opacity-70"></div>
          <div class="absolute bottom-0 left-0 w-32 h-32 bg-blue-50 rounded-full -ml-16 -mb-16 opacity-70"></div>

          <div class="p-8 relative z-10">
            <!-- Header Section -->
            <div class="flex items-center gap-4 mb-8">
              <div class="w-12 h-12 bg-violet-100 rounded-2xl flex items-center justify-center">
                <svg class="w-6 h-6 text-violet-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                    d="M13 9l3 3m0 0l-3 3m3-3H8m13 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h2 class="text-xl font-semibold text-gray-900">
                  Login Redirect
                </h2>
                <p class="text-gray-500 text-sm">
                  Configure redirect URLs for different user roles after login
                </p>
              </div>
            </div>

            <form @submit.prevent="handleLoginRedirectSubmit">
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- Clinic Admin Card -->
                <div class="bg-gray-50 rounded-2xl p-6">
                  <div class="flex items-center gap-3 mb-4">
                    <div class="w-10 h-10 bg-violet-100 rounded-xl flex items-center justify-center">
                      <svg class="w-5 h-5 text-violet-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                          d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                      </svg>
                    </div>
                    <div>
                      <div class="font-medium text-gray-900">Clinic</div>
                      <div class="text-sm text-gray-500">
                        Clinic admin redirect URL
                      </div>
                    </div>
                  </div>
                  <div class="relative">
                    <div class="absolute inset-y-0 left-3 flex items-center">
                      <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                          d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                      </svg>
                    </div>
                    <input type="url" v-model="login_redirect.clinic_admin"
                      class="w-full pl-9 pr-4 py-2.5 bg-white rounded-xl border border-gray-200 focus:ring-2 focus:ring-violet-500 focus:border-violet-500"
                      placeholder="http://medroid-ai.local" />
                  </div>
                </div>

                <!-- Doctor Card -->
                <div class="bg-gray-50 rounded-2xl p-6">
                  <div class="flex items-center gap-3 mb-4">
                    <div class="w-10 h-10 bg-violet-100 rounded-xl flex items-center justify-center">
                      <svg class="w-5 h-5 text-violet-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                          d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <div class="font-medium text-gray-900">Doctor</div>
                      <div class="text-sm text-gray-500">
                        Doctor redirect URL
                      </div>
                    </div>
                  </div>
                  <div class="relative">
                    <div class="absolute inset-y-0 left-3 flex items-center">
                      <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                          d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                      </svg>
                    </div>
                    <input type="url" v-model="login_redirect.doctor"
                      class="w-full pl-9 pr-4 py-2.5 bg-white rounded-xl border border-gray-200 focus:ring-2 focus:ring-violet-500 focus:border-violet-500"
                      placeholder="http://medroid-ai.local" />
                  </div>
                </div>

                <!-- Admin Staff Card -->
                <div class="bg-gray-50 rounded-2xl p-6">
                  <div class="flex items-center gap-3 mb-4">
                    <div class="w-10 h-10 bg-violet-100 rounded-xl flex items-center justify-center">
                      <svg class="w-5 h-5 text-violet-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                          d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                    </div>
                    <div>
                      <div class="font-medium text-gray-900">Admin Staff</div>
                      <div class="text-sm text-gray-500">
                        Receptionist redirect URL
                      </div>
                    </div>
                  </div>
                  <div class="relative">
                    <div class="absolute inset-y-0 left-3 flex items-center">
                      <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                          d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                      </svg>
                    </div>
                    <input type="url" v-model="login_redirect.receptionist"
                      class="w-full pl-9 pr-4 py-2.5 bg-white rounded-xl border border-gray-200 focus:ring-2 focus:ring-violet-500 focus:border-violet-500"
                      placeholder="http://medroid-ai.local" />
                  </div>
                </div>

                <!-- Patient Card -->
                <div class="bg-gray-50 rounded-2xl p-6">
                  <div class="flex items-center gap-3 mb-4">
                    <div class="w-10 h-10 bg-violet-100 rounded-xl flex items-center justify-center">
                      <svg class="w-5 h-5 text-violet-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </div>
                    <div>
                      <div class="font-medium text-gray-900">Patient</div>
                      <div class="text-sm text-gray-500">
                        Patient redirect URL
                      </div>
                    </div>
                  </div>
                  <div class="relative">
                    <div class="absolute inset-y-0 left-3 flex items-center">
                      <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                          d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                      </svg>
                    </div>
                    <input type="url" v-model="login_redirect.patient"
                      class="w-full pl-9 pr-4 py-2.5 bg-white rounded-xl border border-gray-200 focus:ring-2 focus:ring-violet-500 focus:border-violet-500"
                      placeholder="http://medroid-ai.local" />
                  </div>
                </div>
              </div>

              <!-- Save Button -->
              <div class="flex justify-end mt-6">
                <button type="submit" :disabled="loginRedirectSettingLoading"
                  class="inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl font-medium hover:from-blue-700 hover:to-blue-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-sm hover:shadow">
                  <svg v-if="loginRedirectSettingLoading" class="animate-spin -ml-1 mr-2 h-5 w-5 text-white"
                    xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                    </path>
                  </svg>
                  <svg v-else class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                  {{
                    loginRedirectSettingLoading
                      ? formTranslation.common.loading
                      : formTranslation.common.save
                  }}
                </button>
              </div>
            </form>
          </div>
        </div>

        <!-- Logout Redirect Section -->
        <div class="relative bg-white rounded-3xl shadow-sm overflow-hidden">
          <!-- Decorative Corner Elements -->
          <div class="absolute top-0 right-0 w-40 h-40 bg-blue-50 rounded-full -mr-20 -mt-20 opacity-70"></div>
          <div class="absolute bottom-0 left-0 w-32 h-32 bg-blue-50 rounded-full -ml-16 -mb-16 opacity-70"></div>

          <div class="p-8 relative z-10">
            <!-- Header Section -->
            <div class="flex items-center gap-4 mb-8">
              <div class="w-12 h-12 bg-violet-100 rounded-2xl flex items-center justify-center">
                <svg class="w-6 h-6 text-violet-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                    d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
              </div>
              <div>
                <h2 class="text-xl font-semibold text-gray-900">
                  {{ formTranslation.common.logout_redirect }}
                </h2>
                <p class="text-gray-500 text-sm">
                  Configure redirect URLs for different user roles after logout
                </p>
              </div>
            </div>

            <form @submit.prevent="handleLogoutRedirectSubmit">
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- Clinic Admin Card -->
                <div class="bg-gray-50 rounded-2xl p-6">
                  <div class="flex items-center gap-3 mb-4">
                    <div class="w-10 h-10 bg-violet-100 rounded-xl flex items-center justify-center">
                      <svg class="w-5 h-5 text-violet-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                          d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                      </svg>
                    </div>
                    <div>
                      <div class="font-medium text-gray-900">Clinic</div>
                      <div class="text-sm text-gray-500">
                        Clinic admin logout URL
                      </div>
                    </div>
                  </div>
                  <div class="relative">
                    <div class="absolute inset-y-0 left-3 flex items-center">
                      <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                          d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                      </svg>
                    </div>
                    <input type="url" v-model="logout_redirect.clinic_admin"
                      class="w-full pl-9 pr-4 py-2.5 bg-white rounded-xl border border-gray-200 focus:ring-2 focus:ring-violet-500 focus:border-violet-500"
                      placeholder="http://medroid-ai.local" />
                  </div>
                </div>

                <!-- Doctor Card -->
                <div class="bg-gray-50 rounded-2xl p-6">
                  <div class="flex items-center gap-3 mb-4">
                    <div class="w-10 h-10 bg-violet-100 rounded-xl flex items-center justify-center">
                      <svg class="w-5 h-5 text-violet-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                          d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <div class="font-medium text-gray-900">Doctor</div>
                      <div class="text-sm text-gray-500">Doctor logout URL</div>
                    </div>
                  </div>
                  <div class="relative">
                    <div class="absolute inset-y-0 left-3 flex items-center">
                      <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                          d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                      </svg>
                    </div>
                    <input type="url" v-model="logout_redirect.doctor"
                      class="w-full pl-9 pr-4 py-2.5 bg-white rounded-xl border border-gray-200 focus:ring-2 focus:ring-violet-500 focus:border-violet-500"
                      placeholder="http://medroid-ai.local" />
                  </div>
                </div>

                <!-- Receptionist Card -->
                <div class="bg-gray-50 rounded-2xl p-6">
                  <div class="flex items-center gap-3 mb-4">
                    <div class="w-10 h-10 bg-violet-100 rounded-xl flex items-center justify-center">
                      <svg class="w-5 h-5 text-violet-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                          d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                    </div>
                    <div>
                      <div class="font-medium text-gray-900">Admin Staff</div>
                      <div class="text-sm text-gray-500">
                        Receptionist logout URL
                      </div>
                    </div>
                  </div>
                  <div class="relative">
                    <div class="absolute inset-y-0 left-3 flex items-center">
                      <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                          d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                      </svg>
                    </div>
                    <input type="url" v-model="logout_redirect.receptionist"
                      class="w-full pl-9 pr-4 py-2.5 bg-white rounded-xl border border-gray-200 focus:ring-2 focus:ring-violet-500 focus:border-violet-500"
                      placeholder="http://medroid-ai.local" />
                  </div>
                </div>

                <!-- Patient Card -->
                <div class="bg-gray-50 rounded-2xl p-6">
                  <div class="flex items-center gap-3 mb-4">
                    <div class="w-10 h-10 bg-violet-100 rounded-xl flex items-center justify-center">
                      <svg class="w-5 h-5 text-violet-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </div>
                    <div>
                      <div class="font-medium text-gray-900">Patient</div>
                      <div class="text-sm text-gray-500">
                        Patient logout URL
                      </div>
                    </div>
                  </div>
                  <div class="relative">
                    <div class="absolute inset-y-0 left-3 flex items-center">
                      <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                          d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                      </svg>
                    </div>
                    <input type="url" v-model="logout_redirect.patient"
                      class="w-full pl-9 pr-4 py-2.5 bg-white rounded-xl border border-gray-200 focus:ring-2 focus:ring-violet-500 focus:border-violet-500"
                      placeholder="http://medroid-ai.local" />
                  </div>
                </div>
              </div>

              <!-- Save Button -->
              <div class="flex justify-end mt-6">
                <button type="submit" :disabled="redirectSettingLoading"
                  class="inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl font-medium hover:from-blue-700 hover:to-blue-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-sm hover:shadow">
                  <svg v-if="redirectSettingLoading" class="animate-spin -ml-1 mr-2 h-5 w-5 text-white"
                    xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                    </path>
                  </svg>
                  <svg v-else class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                  {{
                    redirectSettingLoading
                      ? formTranslation.common.loading
                      : formTranslation.common.save
                  }}
                </button>
              </div>
            </form>
          </div>
        </div>
        <!-- FullCalendar Section -->
        <div class="relative bg-white rounded-3xl shadow-sm overflow-hidden">
          <!-- Decorative Corner Elements -->
          <div class="absolute top-0 right-0 w-40 h-40 bg-blue-50 rounded-full -mr-20 -mt-20 opacity-70"></div>
          <div class="absolute bottom-0 left-0 w-32 h-32 bg-blue-50 rounded-full -ml-16 -mb-16 opacity-70"></div>

          <div class="p-8 relative z-10">
            <!-- Header Section -->
            <div class="flex items-center gap-4 mb-8">
              <div class="w-12 h-12 bg-violet-100 rounded-2xl flex items-center justify-center">
                <svg class="w-6 h-6 text-violet-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <div>
                <h2 class="text-xl font-semibold text-gray-900">
                  {{ formTranslation.common.fullcalendar_setting }}
                </h2>
                <p class="text-gray-500 text-sm">
                  Configure your FullCalendar license settings
                </p>
              </div>
            </div>

            <form @submit.prevent="handleFullCalendarFormSubmit">
              <div class="bg-gray-50 rounded-2xl p-6 max-w-2xl">
                <div class="flex items-center gap-3 mb-4">
                  <div class="w-10 h-10 bg-violet-100 rounded-xl flex items-center justify-center">
                    <svg class="w-5 h-5 text-violet-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                        d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                    </svg>
                  </div>
                  <div>
                    <label class="block font-medium text-gray-900">
                      {{ formTranslation.common.fullcalendar_license_key }}
                    </label>
                    <p class="text-sm text-gray-500">
                      Enter your FullCalendar license key
                    </p>
                  </div>
                </div>

                <div class="relative">
                  <div class="absolute inset-y-0 left-3 flex items-center">
                    <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                        d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                    </svg>
                  </div>
                  <input type="text" v-model="fullcalendar_key"
                    class="w-full pl-9 pr-4 py-2.5 bg-white rounded-xl border border-gray-200 focus:ring-2 focus:ring-violet-500 focus:border-violet-500"
                    :class="{
                      'border-red-300 focus:ring-red-500 focus:border-red-500':
                        isFullcalendarFormKeyEmpty,
                    }" placeholder="Enter license key" />
                  <div v-if="isFullcalendarFormKeyEmpty" class="mt-2 flex items-center text-sm text-red-600">
                    <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                    {{ formTranslation.common.full_calender_validation }}
                  </div>
                </div>
              </div>

              <!-- Save Button -->
              <div class="flex justify-end mt-6">
                <button type="submit" :disabled="fullcalendarFormLoading"
                  class="inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl font-medium hover:from-blue-700 hover:to-blue-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-sm hover:shadow">
                  <svg v-if="fullcalendarFormLoading" class="animate-spin -ml-1 mr-2 h-5 w-5 text-white"
                    xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                    </path>
                  </svg>
                  <svg v-else class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                  {{
                    fullcalendarFormLoading
                      ? formTranslation.common.loading
                      : formTranslation.common.save
                  }}
                </button>
              </div>
            </form>
          </div>
        </div>

        <!-- Consultation Settings Section -->
        <div class="relative bg-white rounded-3xl shadow-sm overflow-hidden">
          <!-- Decorative Corner Elements -->
          <div class="absolute top-0 right-0 w-40 h-40 bg-blue-50 rounded-full -mr-20 -mt-20 opacity-70"></div>
          <div class="absolute bottom-0 left-0 w-32 h-32 bg-blue-50 rounded-full -ml-16 -mb-16 opacity-70"></div>

          <div class="p-8 relative z-10">
            <!-- Header Section -->
            <div class="flex items-center gap-4 mb-8">
              <div class="w-12 h-12 bg-violet-100 rounded-2xl flex items-center justify-center">
                <svg class="w-6 h-6 text-violet-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                    d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                </svg>
              </div>
              <div>
                <h2 class="text-xl font-semibold text-gray-900">
                  {{ $t("common.encounter_setting") }}
                </h2>
                <p class="text-gray-500 text-sm">
                  Configure encounter editing permissions and settings
                </p>
              </div>
            </div>

            <form @submit.prevent="handleEncounterFormSubmit">
              <div class="bg-gray-50 rounded-2xl p-6">
                <div class="flex items-center gap-3 mb-4">
                  <div class="w-10 h-10 bg-violet-100 rounded-xl flex items-center justify-center">
                    <svg class="w-5 h-5 text-violet-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </div>
                  <div class="flex-grow">
                    <toggle-switch :value="encounterSettingsData.encounter_edit_after_close_status
                      " @input="
                        (value) =>
                        (encounterSettingsData.encounter_edit_after_close_status =
                          value)
                      " :label="$t('common.allow_encounter_edit_after_close')" on-value="on" off-value="off" />
                    <p class="text-sm text-gray-500 mt-1">
                      Allow editing of encounters after they have been closed
                    </p>
                  </div>
                </div>
              </div>

              <!-- Save Button -->
              <div class="flex justify-end mt-6">
                <button type="submit" :disabled="isSubmitedEncounter"
                  class="inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl font-medium hover:from-blue-700 hover:to-blue-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-sm hover:shadow">
                  <svg v-if="isSubmitedEncounter" class="animate-spin -ml-1 mr-2 h-5 w-5 text-white"
                    xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                    </path>
                  </svg>
                  <svg v-else class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                  {{
                    isSubmitedEncounter
                      ? formTranslation.common.loading
                      : formTranslation.common.save
                  }}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import VuePhoneNumberInput from "vue-phone-number-input";
import "vue-phone-number-input/dist/vue-phone-number-input.css";
import { post, get } from "../../config/request";
import { maxValue, minValue, required } from "vuelidate/lib/validators";
import ModalPopup from "../../components/Modal/Index";
export default {
  name: "generalSetting",
  components: {
    ModalPopup,
    VuePhoneNumberInput,
  },
  data: () => {
    return {
      request_status: "yes",
      remove_request_status: "off",
      showOption: true,
      isSubmited: false,
      isSubmitedClinic: false,
      isSubmitedCountryCode: false,
      isSubmitedReset: false,
      isSubmitedEncounter: false,
      productData: {
        country: "",
      },
      clinicData: {
        currency_prefix: "",
        currency_postfix: "",
        // decimal_point:{

        // }
      },
      googleRecaptchaLoading: false,
      googleCaptcha: {
        site_key: "",
        secret_key: "",
        status: "off",
      },
      redirectSettingLoading: false,
      loginRedirectSettingLoading: false,
      logout_redirect: {
        clinic_admin: "",
        patient: "",
        receptionist: "",
        doctor: "",
      },
      login_redirect: {
        clinic_admin: "",
        patient: "",
        receptionist: "",
        doctor: "",
      },
      datat: {},
      formLoader: true,
      decimals: [
        { id: 0, label: "100" },
        { id: 1, label: "100.0" },
        { id: 2, label: "100.00" },
        { id: 3, label: "100.000" },
        { id: 4, label: "100.0000" },
      ],
      fullcalendarFormLoading: false,
      isFullcalendarFormKeyEmpty: false,
      fullcalendar_key: "",
      dateFormatLoading: false,
      dateFormat: "D-MM-YYYY",
      dateFormatOutput: "",
      reset_status: "off",
      resetPluginModel: false,
      resetPluginData: {
        resetAppointmentEncounterStatus: "off",
        resetDoctorStatus: "off",
        resetReceptionistStatus: "off",
        resetPatientStatus: "off",
        resetRevenueStatus: "off",
        // resetClinicStatus:'off',
        resetAllDataStatus: "off",
      },
      userRegistrationShortcodeSetting: {
        status: {
          doctor: "off",
          receptionist: "off",
          patient: "on",
        },
        user_role: {
          kiviCare_doctor: "on",
          kiviCare_receptionist: "on",
          kiviCare_patient: "on",
        },
      },
      userRegistrationShortcodeSettingLoading: false,
      countryCodeData: {
        countrycode: "",
        countryCallingCode: "",
      },
      contact: "00",
      defaultCountryCode: null,
      userRegistrationFormSetting_status: "on",
      encounterSettingsData: {
        encounter_edit_after_close_status: "off",
      },
      userRegistrationFormSettingLoading: false,
    };
  },
  mounted() {
    if (!["administrator"].includes(this.getUserRole())) {
      this.$router.push({ name: "403" });
    }
    this.getRequestHelper();
    this.getAllSetting();
    this.getModule();
  },
  methods: {
    validateCheckboxes(userRole) {
      const user_roles = this.userRegistrationShortcodeSetting.user_role;

      const { kiviCare_doctor, kiviCare_receptionist, kiviCare_patient } =
        this.userRegistrationShortcodeSetting.user_role;

      if (
        kiviCare_doctor === "off" &&
        kiviCare_receptionist === "off" &&
        kiviCare_patient === "off"
      ) {
        user_roles[userRole] = "on";
        this.userRegistrationShortcodeSetting.user_role = user_roles;
      }
    },
    countryCodeUpdateHandaler: function (val) {
      this.countryCodeData.countrycode = val.countryCode;
      this.countryCodeData.countryCallingCode = val.countryCallingCode;
    },
    handleSubmit() {
      var element = $("#btn-general-submit").find("i");
      element.removeClass("fa fa-save ");
      element.addClass("fa fa-spinner fa-spin");
      this.isSubmited = true;
      post("save_request_helper_status", {
        request_status: this.request_status,
        remove_request_status: this.remove_request_status,
      })
        .then((response) => {
          this.isSubmited = false;
          element.removeClass("fa fa-spinner fa-spin");
          element.addClass("fa fa-save");
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.showOption = false;
            if (response.data.data == "off") {
              this.request_status = response.data.data;
            }
            setTimeout(() => {
              window.location.reload();
            }, 500);
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    handleUserRegistrationShortcodeSettingSubmit() {
      this.userRegistrationShortcodeSettingLoading = true;
      post("save_registration_shortcode_setting", {
        data: this.userRegistrationShortcodeSetting,
      })
        .then((response) => {
          this.userRegistrationShortcodeSettingLoading = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          this.userRegistrationShortcodeSettingLoading = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    handleDateFormatSubmit() {
      this.dateFormatLoading = true;
      post("save_date_format", { data: this.dateFormat })
        .then((response) => {
          this.dateFormatLoading = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.$store.commit("FETCH_DATE_FORMAT", { data: this.dateFormat });
            displayMessage(response.data.message);
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          this.dateFormatLoading = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    handleLogoutRedirectSubmit() {
      this.redirectSettingLoading = true;
      post("save_logout_redirect_setting", { data: this.logout_redirect })
        .then((response) => {
          this.redirectSettingLoading = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.$store.dispatch("logout_redirect_url", {
              data: Object.assign({}, this.logout_redirect),
            });
            displayMessage(response.data.message);
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          this.redirectSettingLoading = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    handleLoginRedirectSubmit() {
      this.loginRedirectSettingLoading = true;
      post("save_login_redirect_setting", { data: this.login_redirect })
        .then((response) => {
          this.loginRedirectSettingLoading = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          this.loginRedirectSettingLoading = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    handleFullCalendarFormSubmit() {
      if (this.fullcalendar_key.length == 0) {
        this.isFullcalendarFormKeyEmpty = true;
        return;
      }
      this.isFullcalendarFormKeyEmpty = this.fullcalendar_key.length == 0;
      this.fullcalendarFormLoading = true;
      post("save_fullcalendar_setting", {
        fullcalendar_key: this.fullcalendar_key,
      })
        .then((response) => {
          this.fullcalendarFormLoading = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          this.fullcalendarFormLoading = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    handleRegistrationFormSetting() {
      this.userRegistrationFormSettingLoading = true;
      post("save_registration_form_setting", {
        status: this.userRegistrationFormSetting_status,
      })
        .then((response) => {
          this.userRegistrationFormSettingLoading = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          this.userRegistrationFormSettingLoading = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    handleCaptchaSubmit() {
      this.googleRecaptchaLoading = true;
      post("save_google_recaptcha_setting", this.googleCaptcha)
        .then((response) => {
          this.googleRecaptchaLoading = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          this.googleRecaptchaLoading = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    
    handleGeneralSettingSubmit: function () {
      var element = $("#btn-product-submit").find("i");
      element.removeClass("fa fa-save ");
      element.addClass("fa fa-spinner fa-spin");
      this.isSubmitedClinic = true;
      post("save_product_detail", { product_data: this.productData })
        .then((response) => {
          this.isSubmitedClinic = false;
          element.removeClass("fa fa-spinner fa-spin");
          element.addClass("fa fa-save");
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },

    handleCliniSubmit: function () {
      var element = $("#btn-clinic-submit").find("i");
      element.removeClass("fa fa-save ");
      element.addClass("fa fa-spinner fa-spin");
      this.isSubmitedClinic = true;
      post("save_clinic_currency", { clinic_data: this.clinicData })
        .then((response) => {
          this.isSubmitedClinic = false;
          element.removeClass("fa fa-spinner fa-spin");
          element.addClass("fa fa-save");
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    handleCountryCodeSubmit: function () {
      var element = $("#btn-country-code-submit").find("i");
      element.removeClass("fa fa-save ");
      element.addClass("fa fa-spinner fa-spin");
      this.isSubmitedCountryCode = true;
      post("save_country_code", { CountryCode: this.countryCodeData })
        .then((response) => {
          this.isSubmitedCountryCode = false;
          element.removeClass("fa fa-spinner fa-spin");
          element.addClass("fa fa-save");
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },

    handleEncounterFormSubmit() {
      this.isSubmitedEncounter = true;
      post("save_encounter_setting", this.encounterSettingsData)
        .then((response) => {
          this.isSubmitedEncounter = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          this.isSubmitedEncounter = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    getRequestHelper: function () {
      if (
        window.request_data.link_show_hide !== undefined &&
        window.request_data.link_show_hide !== ""
      ) {
        this.request_status = window.request_data.link_show_hide;
        if (this.request_status == "on") {
          this.showOption = false;
        }
      }
      this.formLoader = false;
    },
    getAllSetting: function () {
      this.formLoader = true;
      get("get_all_general_setting", {})
        .then((response) => {
          this.formLoader = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            // this.datat =response.data.data.decimal_point
            this.clinicData = response.data.data;
            this.productData = response.data.productData;
            this.countryCodeData = response.data.countryCodeData;
            if (
              response.data.countryCodeData.countryCallingCode !== "" &&
              response.data.countryCodeData.countryCallingCode !== undefined
            ) {
              this.defaultCountryCode =
                response.data.countryCodeData.countrycode;
            }
            if (response.data.captcha_data !== undefined) {
              this.googleCaptcha = response.data.captcha_data;
            }
            if (response.data.logout_redirect !== undefined) {
              this.logout_redirect = response.data.logout_redirect;
            }
            if (response.data.fullcalendar !== undefined) {
              this.fullcalendar_key = response.data.fullcalendar;
            }
            if (response.data.date_format !== undefined) {
              this.dateFormat = response.data.date_format;
            }
            if (response.data.login_redirect !== undefined) {
              this.login_redirect = response.data.login_redirect;
            }
            if (response.data.userRegistrationShortcodeSetting !== undefined) {
              this.userRegistrationShortcodeSetting =
                response.data.userRegistrationShortcodeSetting;
            }
            if (response.data.userRegistrationFormSetting !== undefined) {
              this.userRegistrationFormSetting_status =
                response.data.userRegistrationFormSetting;
            }
            if (response.data.encounter_settings !== undefined) {
              this.encounterSettingsData = response.data.encounter_settings;
            }
            this.formatDateValue();
          }
        })
        .catch((error) => {
          this.formLoader = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    removeFeatureRequest(status) {
      if (status == "on") {
        this.$swal
          .fire({
            title: this.formTranslation.common.remove_links,
            text: "Are you sure want to remove all hard links permanently ?",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#dc3545", // Bootstrap danger color
            cancelButtonColor: "#6c757d", // Bootstrap secondary color
            confirmButtonText: this.formTranslation.common.yes,
            cancelButtonText: this.formTranslation.common.cancel,
          })
          .then((result) => {
            if (result.isConfirmed) {
              this.handleSubmit();
            }
          });
      }
    },
    closeResetPluginModel() {
      this.resetPluginModel = false;
      this.resetPluginData.resetAppointmentEncounterStatus = "";
      this.resetPluginData.resetDoctorStatus = "";
      this.resetPluginData.resetReceptionistStatus = "";
      this.resetPluginData.resetPatientStatus = "";
      this.resetPluginData.resetRevenueStatus = "";
      this.resetPluginData.resetAllDataStatus = "";
    },
    handleResetPluginForm() {
      const element = $("#btn-reset-submit").find("i");
      const content_msg =
        this.resetPluginData.resetDoctorStatus == "on" ||
          this.resetPluginData.resetPatientStatus == "on"
          ? this.formTranslation.common.action_reset_plugin_user_data
          : this.formTranslation.common.action_reset_plugin_data;

      this.$swal
        .fire({
          title: this.formTranslation.clinic_schedule.dt_are_you_sure,
          text: content_msg,
          icon: "warning",
          showCancelButton: true,
          confirmButtonColor: "#dc3545", // Bootstrap danger color
          cancelButtonColor: "#6c757d", // Bootstrap secondary color
          confirmButtonText: this.formTranslation.common.yes,
          cancelButtonText: this.formTranslation.common.cancel,
        })
        .then((result) => {
          if (result.isConfirmed) {
            this.isSubmitedReset = true;
            element.removeClass("fa fa-save");
            element.addClass("fa fa-spinner fa-spin");

            post("reset_plugin_data", {
              reset_plugin_data: this.resetPluginData,
            })
              .then((response) => {
                element.removeClass("fa fa-spinner fa-spin");
                element.addClass("fa fa-save");
                this.isSubmitedReset = false;

                if (
                  response.data.status !== undefined &&
                  response.data.status === true
                ) {
                  this.resetPluginModel = false;

                  this.$swal
                    .fire({
                      icon: "success",
                      title: "Success",
                      text: response.data.message,
                      timer: 1500,
                      showConfirmButton: false,
                    })
                    .then(() => {
                      if (
                        response.data.reset_all !== undefined &&
                        response.data.reset_all === true
                      ) {
                        window.location.href =
                          window.request_data.homePage +
                          "/wp-admin/plugins.php";
                      } else {
                        location.reload();
                      }
                    });
                } else {
                  this.resetPluginModel = false;
                  this.$swal.fire({
                    icon: "error",
                    title: "Error",
                    text: response.data.message,
                    timer: 1500,
                    showConfirmButton: false,
                  });
                }
              })
              .catch((error) => {
                console.log(error);
                this.$swal.fire({
                  icon: "error",
                  title: "Error",
                  text: response.data.message,
                  timer: 1500,
                  showConfirmButton: false,
                });
              });
          }
        });
    },
    formatDateValue() {
      this.dateFormatOutput = window.moment
        .utc(new Date())
        .format(this.dateFormat);
    },
    getModule: function () {
      if (
        window.request_data.link_show_hide !== undefined &&
        window.request_data.link_show_hide !== ""
      ) {
        this.request_status = window.request_data.link_show_hide;
      }
    },
  },
  computed: {},
  watch: {
    dateFormat: function () {
      this.formatDateValue();
    },
  },
};
</script>

<style>
.form-group.country-code-inline-box #country_code .flex-1 {
  display: none;
}

#user_registration .custom-switch .custom-control-label::before {
  left: -2.5rem;
}

#user_registration .custom-switch .custom-control-label::after {
  left: calc(-2.5rem + 2px);
}
</style>
