<template>
  <div>
    <div class="max-w-4xl mx-auto bg-white p-8">
      <div class="flex items-center justify-between mb-8 pb-6 border-b-2 border-black">
        <div class="items-center gap-4">
          <img v-if="clinicData.logo" :src="clinicData.logo" :alt="clinicData.name" class="h-full object-contain"
            style="width: 80px" />
          <!-- Fallback if no logo -->
          <div v-else class="p-3 rounded">
            <h4 class="text-2xl font-bold">x-Logo-x</h4>
          </div>
          <div>
            <p class="text-xs font-medium text-[#000000]">PRESCRIPTION</p>
            <p class="text-sm text-[#000000]">
              Ref: RX-{{ new Date().getFullYear() }}-{{
                encounterId?.toString().padStart(3, "0")
              }}
            </p>
          </div>
        </div>
        <div class="text-center flex-1">
          <h2 class="text-xl font-bold text-[#000000]">
            {{ clinicData.name }}
          </h2>
          <div class="text-sm text-[#000000] space-y-1">
            <p>{{ clinicData.address }}, {{ clinicData.city }}</p>
            <p>Tel: {{ clinicData.phone }}</p>
            <p class="text-xs">{{ clinicData.website }}</p>
          </div>
        </div>
        <div class="text-right space-y-1">
          <div class="flex items-center justify-end gap-2 text-sm text-[#000000]">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
              class="lucide lucide-calendar w-4 h-4">
              <path d="M8 2v4"></path>
              <path d="M16 2v4"></path>
              <rect width="18" height="18" x="3" y="4" rx="2"></rect>
              <path d="M3 10h18"></path>
            </svg>
            <p>{{ currentDate }}</p>
          </div>
          <div class="flex items-center justify-end gap-2 text-sm text-[#000000]">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
              class="lucide lucide-clock w-4 h-4">
              <circle cx="12" cy="12" r="10"></circle>
              <polyline points="12 6 12 12 16 14"></polyline>
            </svg>
            <p>{{ currentTime }}</p>
          </div>
        </div>
      </div>
      <div class="bg-gray-50 rounded-lg p-4 mb-6">
        <div class="flex items-center gap-2 mb-3">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
            class="lucide lucide-user w-5 h-5">
            <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
            <circle cx="12" cy="7" r="4"></circle>
          </svg>
          <h3 class="font-bold text-[#000000]">Patient Details</h3>
        </div>
        <div class="grid grid-cols-2 gap-4">
          <div class="space-y-1">
            <p class="text-sm text-[#000000]">
              <strong>Name:</strong> {{ patientData.name }}
            </p>
            <p class="text-sm text-[#000000]">
              <strong>Date of Birth:</strong> {{ patientData.dob }}
            </p>
            <p class="text-sm text-[#000000]">
              <strong>NHS Number:</strong>
              {{ patientData.nhs }}
            </p>
          </div>
          <div class="space-y-1">
            <p class="text-sm text-[#000000]"><strong>Address:</strong></p>
            <p class="text-sm text-[#000000]">{{ formattedAddress }}</p>
            <p class="text-sm text-[#000000]">
              <strong>Patient ID:</strong>
              {{ patientData.unique_id }}
            </p>
          </div>
        </div>
      </div>
      <div class="mb-8">
        <h3 class="font-bold mb-4 text-[#000000] flex items-center gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
            class="lucide lucide-circle-alert w-5 h-5">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" x2="12" y1="8" y2="12"></line>
            <line x1="12" x2="12.01" y1="16" y2="16"></line>
          </svg>Prescribed Medications
        </h3>
        <div class="space-y-6">
          <div v-for="prescription in formattedPrescriptions" :key="prescription.number" class="border rounded-lg p-4">
            <div class="flex justify-between items-start mb-3">
              <h4 class="font-medium text-[#000000]">
                {{ prescription.number }}. {{ prescription.name }}
              </h4>
              <span class="px-2 py-1 bg-blue-50 text-blue-700 rounded text-xs">{{ prescription.route }}</span>
            </div>
            <div class="grid grid-cols-2 gap-4 mb-4">
              <div class="space-y-2">
                <p class="text-sm text-[#000000]">
                  <strong>Dose:</strong> {{ prescription.dose }}
                </p>
                <p class="text-sm text-[#000000]">
                  <strong>Frequency:</strong> {{ prescription.frequency }}
                </p>
                <p class="text-sm text-[#000000]">
                  <strong>Duration:</strong> {{ prescription.duration }}
                </p>
                <p class="text-sm text-[#000000]">
                  <strong>Quantity:</strong> {{ prescription.quantity }} units
                </p>
              </div>
              <div class="space-y-2">
                <div class="bg-yellow-50 p-2 rounded">
                  <p class="text-sm font-medium text-yellow-800">
                    Special Instructions:
                  </p>
                  <p class="text-sm text-yellow-800">
                    {{ prescription.specialInstructions }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="grid grid-cols-2 mt-12 pt-6 border-t-2 border-black">
        <div>
          <!-- Update doctor details -->
          <div class="space-y-1">
            <p class="font-bold text-[#000000]">Dr. {{ doctorData.name }}</p>
            <p class="text-sm text-[#000000]">
              {{ doctorData.registration_prefix || 'SET PREFIX' }} No: {{ doctorData.gmc_number }}
            </p>
            <p class="text-sm text-[#000000]">{{ doctorData.designation }}</p>
          </div>
          <div class="mt-4">
            <!-- <img
              src="/api/placeholder/200/100"
              alt="Digital Signature"
              class="max-w-[200px]"
            /> -->
            <img v-if="doctorData.doctor_signature" :src="doctorData.doctor_signature" :alt="doctorData.name"
              class="h-full object-contain" style="width: 200px" />
            <!-- Fallback if no logo -->
            <div v-else class="bg-blue-600 text-white p-3 rounded">
              <p class="font-bold">Upload Signature from "Edit Profile"</p>
            </div>
            <p class="text-xs text-[#000000] mt-1">
              Digitally signed on {{ currentDate }}, {{ currentTime }}
            </p>
          </div>
        </div>
      </div>
      <div class="mt-8 pt-4 border-t border-gray-200">
        <div class="flex justify-between items-center">
          <div>
            <p class="text-xs text-[#000000]">
              In case of emergency, contact NHS 111 or 999 or your local
              emergency services
            </p>
          </div>
          <div class="flex gap-2 relative">
            <!-- Main Button that toggles dropdown -->
            <button @click="
              isPrescriptionActionDropdownOpen =
              !isPrescriptionActionDropdownOpen
              " class="px-4 py-2 bg-black text-white rounded text-sm hover:bg-gray-800 flex items-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-download w-4 h-4">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                <polyline points="7 10 12 15 17 10"></polyline>
                <line x1="12" x2="12" y1="15" y2="3"></line>
              </svg>
              Actions
              <!-- Add a chevron icon -->
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
                <polyline points="6 9 12 15 18 9"></polyline>
              </svg>
            </button>

            <!-- Dropdown Menu -->
            <div v-if="isPrescriptionActionDropdownOpen"
              class="absolute right-0 bottom-12 w-48 bg-white rounded-md shadow-lg border ring-1 ring-black ring-opacity-5 z-50">
              <div class="py-1">
                <button @click="downloadPDF"
                  class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                    <polyline points="14 2 14 8 20 8"></polyline>
                    <line x1="16" y1="13" x2="8" y2="13"></line>
                    <line x1="16" y1="17" x2="8" y2="17"></line>
                    <polyline points="10 9 9 9 8 9"></polyline>
                  </svg>
                  Download as PDF
                </button>
                <button @click="sendPrescriptionToEmail"
                  class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                    <polyline points="14 2 14 8 20 8"></polyline>
                    <line x1="16" y1="13" x2="8" y2="13"></line>
                    <line x1="16" y1="17" x2="8" y2="17"></line>
                  </svg>
                  Send Email
                </button>
                <!-- <button
                  @click="printPrescription"
                  class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="w-4 h-4"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <polyline points="6 9 6 2 18 2 18 9"></polyline>
                    <path
                      d="M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2"
                    ></path>
                    <rect x="6" y="14" width="12" height="8"></rect>
                  </svg>
                  Print Prescription
                </button> -->
                <button @click="showSignatureRXModal = true"
                  class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2">
                    <polyline points="6 9 6 2 18 2 18 9"></polyline>
                    <path d="M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2"></path>
                    <rect x="6" y="14" width="12" height="8"></rect>
                  </svg>
                  RX (Signature Rx)
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- SignatureRX Modal -->
    <div v-show="showSignatureRXModal" class="srx-modal-overlay">
      <div class="srx-modal">
        <!-- Header -->
        <div class="srx-modal-header">
          <h3>SignatureRX Authentication</h3>
          <button @click="showSignatureRXModal = false" class="srx-close-btn">×</button>
        </div>

        <!-- Body -->
        <div class="srx-modal-body">
          <!-- Info box -->
          <div class="srx-info-box">
            <p>Please enter your 6-digit secure PIN to authenticate with SignatureRX.</p>
          </div>

          <!-- PIN input -->
          <div class="srx-form-group">
            <label for="srx-pin">Secure PIN</label>
            <input type="password" id="srx-pin" v-model="signatureRXPin" class="srx-input"
              placeholder="Enter your 6-digit PIN" maxlength="6" />
            <p class="srx-help-text">This PIN will be used to authenticate your prescription with SignatureRX.</p>
          </div>

          <!-- Buttons -->
          <div class="srx-button-group">
            <button type="button" @click="showSignatureRXModal = false" class="srx-btn srx-btn-cancel">
              Cancel
            </button>
            <button type="button" @click="sendToSignatureRX" :disabled="signatureRXLoading"
              class="srx-btn srx-btn-send">
              <span v-if="signatureRXLoading">
                <svg class="srx-spinner" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="srx-spinner-circle" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
                  </circle>
                  <path class="srx-spinner-path" fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                  </path>
                </svg>
                Sending...
              </span>
              <span v-else>Send Prescription</span>
            </button>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import { getBasicDetails } from "../../services/appointment.js";
import { post, get } from "../../config/request";
import moment from "moment";
import html2pdf from "html2pdf.js";

export default {
  name: "PatientPrescription",

  data() {
    return {
      encounterId: null,
      isPrescriptionActionDropdownOpen: false,
      isEncounterTemp: false,
      encounterData: {},
      currentDate: "",
      currentTime: "",
      patientData: {
        id: "",
        name: "",
        dob: "",
        nhs: "",
        unique_id: "",
        address: "",
        city: "",
        postcode: "",
        mobile: "",
      },
      clinicData: {
        name: "",
        address: "",
        city: "",
        state: "",
        country: "",
        phone: "",
        website: "",
        logo: "",
      },
      doctorData: {
        name: "",
        npi_number: "",
        designation: "",
        doctor_signature: "",
      },
      prescriptionList: [],
      currentDateTime: moment().format("DD/MM/YYYY, h:mm:ss A"),
      // SignatureRX related fields
      showSignatureRXModal: false,
      signatureRXPin: '',
      signatureRXLoading: false,
    };
  },

  computed: {
    formattedPrescriptions() {
      return this.prescriptionList.map((prescription, index) => ({
        number: index + 1,
        name: prescription.name.label,
        route: prescription.route || "Oral",
        dose: prescription.dose,
        frequency: prescription.frequency,
        duration: `${prescription.duration} days`,
        quantity: this.calculateQuantity(prescription),
        specialInstructions: prescription.instruction || "-",
      }));
    },

    formattedAddress() {
      const parts = [
        this.patientData.address,
        this.patientData.city,
        this.patientData.postcode,
      ].filter(Boolean);
      return parts.join(", ");
    },
  },

  mounted() {
    this.init();
  },

  methods: {
    async init() {
      try {
        if (this.$route?.query?.isEncounterTemp !== undefined) {
          this.isEncounterTemp = this.$route.query.isEncounterTemp == 1;
        }
        this.encounterId = this.$route.params.encounter_id || 0;

        if (this.encounterId) {
          this.updateDateTime();
          await Promise.all([
            this.fetchBasicDetails(),
            this.getPrescriptionList(),
          ]);
        } else {
          displayErrorMessage("Error initializing prescription.");
        }
      } catch (error) {
        console.error("Error initializing prescription:", error);
      }
    },

    handleClickOutside(event) {
      const dropdown = this.$el.querySelector(".dropdown");
      if (!event.target.closest(".dropdown")) {
        this.isDropdownOpen = false;
      }
    },

    async fetchBasicDetails() {
      try {
        const result = await getBasicDetails(
          this.encounterId,
          this.isEncounterTemp
        );

        if (result.status) {
          // Set patient data
          this.patientData = {
            id: result.patientDetails.patient_id,
            name: result.patientDetails.patient_name,
            dob: result.patientMetaData.dob,
            nhs: result.patientMetaData.nhs,
            unique_id: result.patientDetails.patient_unique_id,
            address: result.patientMetaData.address,
            city: result.patientMetaData.city,
            postcode: result.patientMetaData.postal_code,
            mobile: result.patientMetaData.mobile_number,
          };

          // Set clinic data
          this.clinicData = {
            name: result.patientDetails.clinic_name,
            address: result.patientDetails.clinic_address,
            city: result.patientDetails.clinic_city,
            state: result.patientDetails.clinic_state,
            country: result.patientDetails.clinic_country,
            phone: result.patientDetails.clinic_telephone_no,
            website: result.patientDetails.clinic_website,
            logo: result.patientDetails.clinic_logo,
          };

          // Set doctor data
          this.doctorData = {
            name: result.patientDetails.doctor_name,
            doctor_signature: result.patientDetails.doctor_signature,
            gmc_number: result.patientDetails.doctor_gmc_no,
            registration_prefix: result.patientDetails.doctor_registration_prefix,
            designation: result.patientDetails.doctor_specialties
              ?.map((specialty) => specialty.label)
              .join(", "),
          };
        }
      } catch (error) {
        console.error("Error fetching basic details:", error);
      }
    },

    getPrescriptionList() {
      return get("prescription_list", {
        encounter_id: this.encounterId,
      })
        .then((response) => {
          if (response.data?.status === true) {
            this.prescriptionList = response.data.data;
          }
        })
        .catch((error) => {
          console.error("Error fetching prescriptions:", error);
          if (typeof displayErrorMessage === "function") {
            displayErrorMessage("Failed to fetch prescriptions");
          }
        });
    },

    calculateQuantity(prescription) {
      // Basic quantity calculation based on duration and frequency
      const frequencyMap = {
        "Once daily": 1,
        "Twice daily": 2,
        "Three times daily": 3,
        "Four times daily": 4,
        "Every 4 hours": 6,
        "Every 6 hours": 4,
        "Every 8 hours": 3,
      };

      const frequency = frequencyMap[prescription.frequency] || 1;
      const duration = parseInt(prescription.duration) || 1;

      return frequency * duration;
    },

    async downloadPDF() {
      try {

        this.logDocumentDownload(this.encounterId);

        // Close the dropdown
        this.isPrescriptionActionDropdownOpen = false;

        // Get the main prescription container
        const element = this.$el.querySelector(".max-w-4xl");

        // Hide the action buttons before generating PDF
        const actionButtons = element.querySelector(".flex.gap-2.relative");
        if (actionButtons) {
          actionButtons.style.display = "none";
        }

        const options = {
          filename: `prescription_${this.encounterId}.pdf`,
          image: { type: "jpeg", quality: 0.98 },
          html2canvas: {
            scale: 2,
            useCORS: true,
            logging: false,
            letterRendering: true,
            allowTaint: true,
          },
          jsPDF: {
            unit: "mm",
            format: "a4",
            orientation: "portrait",
            compress: true,
          },
          margin: [10, 10, 10, 10],
        };

        // Generate PDF
        await html2pdf().set(options).from(element).save();

        // Show action buttons again
        if (actionButtons) {
          actionButtons.style.display = "";
        }

        // Show success message if you have a toast system
        if (this.$bvToast) {
          this.$bvToast.toast("PDF downloaded successfully", {
            title: "Success",
            variant: "success",
            solid: true,
          });
        }
      } catch (error) {
        console.error("Error generating PDF:", error);
        // Show error message if you have a toast system
        if (this.$bvToast) {
          this.$bvToast.toast("Failed to generate PDF. Please try again.", {
            title: "Error",
            variant: "danger",
            solid: true,
          });
        }
      }
    },

    logDocumentDownload(encounterId) {
      const user_id = this.getUserId();
      
      if (!user_id) {
        console.warn('Unable to log activity: User ID not found');
        return;
      }

      // Prepare activity data
      const activity_type = 'document_prescription';
      const activity_description = `Downloaded prescription: ${encounterId}`;
      const additional_data = {
        encounter_id: encounterId,
        downloaded_at: new Date().toISOString()
      };

      // Log the activity using the logActivity endpoint
      post('log_activity', {
        user_id,
        activity_type,
        activity_description,
        additional_data
      }).catch(error => {
        console.error('Failed to log document download activity:', error);
      });
    },

    async confirmAction(title, message) {
      const result = await this.$swal.fire({
        title: title,
        text: message,
        icon: "question",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes",
        cancelButtonText: "No",
      });
      return result.isConfirmed;
    },

    async downloadPDF_delete() {
      if (this.isButtonDisabled) return;

      const confirmed = await this.confirmAction(
        "Download PDF",
        "Do you want to download this prescription as PDF?"
      );

      if (!confirmed) return;

      this.isButtonDisabled = true;
      this.iconClass = "fa fa-spinner fa-spin";

      try {
        // First get the HTML content
        const htmlResponse = await get("get_encounter_print", {
          encounter_id: this.encounterId,
        });

        if (!htmlResponse?.data?.status || !htmlResponse?.data?.data) {
          throw new Error(
            htmlResponse?.data?.message || "Failed to generate print data"
          );
        }

        // Create HTML content with proper styling
        const htmlContent = `
            <!DOCTYPE html>
            <html>
                <head>
                    <title>Prescription #${this.encounterId}</title>
                    <style>
                        @page { 
                            margin: 2cm;
                            size: A4;
                        }
                        body { 
                            font-family: Arial, sans-serif;
                            line-height: 1.6;
                            color: #000000;
                        }
                        .print-container { width: 100%; }
                        .header { text-align: center; margin-bottom: 20px; }
                        .patient-info { margin-bottom: 20px; }
                        .section { margin-bottom: 15px; }
                        table { width: 100%; border-collapse: collapse; }
                        th, td { padding: 8px; border: 1px solid #ddd; }
                        .signature { margin-top: 50px; text-align: right; }
                        
                        /* Additional styles for modern look */
                        .rounded-lg { border-radius: 0.5rem; }
                        .bg-gray-50 { background-color: #F9FAFB; }
                        .border-black { border-color: #000000; }
                        .space-y-1 > * + * { margin-top: 0.25rem; }
                        .grid { display: grid; }
                        .grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
                        .gap-4 { gap: 1rem; }
                    </style>
                </head>
                <body>${htmlResponse.data.data}</body>
            </html>
        `;

        // Now generate PDF using the styled HTML
        const pdfResponse = await post("generate_prescription_pdf", {
          encounter_id: this.encounterId,
          html_content: htmlContent,
        });

        if (pdfResponse?.data?.status === true) {
          // Create and trigger download
          const link = document.createElement("a");
          link.href = pdfResponse.data.pdf_url;
          link.download = `prescription_${this.encounterId}.pdf`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          // Show success message
          this.$bvToast.toast("PDF downloaded successfully", {
            title: "Success",
            variant: "success",
            solid: true,
          });
        } else {
          throw new Error(
            pdfResponse?.data?.message || "Failed to generate PDF"
          );
        }
      } catch (error) {
        console.error("PDF generation error:", error);
        this.$bvToast.toast("Failed to generate PDF. Please try again.", {
          title: "Error",
          variant: "danger",
          solid: true,
        });
      } finally {
        this.isButtonDisabled = false;
        this.iconClass = "fa fa-download";
      }
    },

    updateDateTime() {
      const now = new Date();
      // Format date as M/D/YYYY
      this.currentDate = now.toLocaleDateString("en-US", {
        month: "numeric",
        day: "numeric",
        year: "numeric",
      });
      // Format time as H:MM:SS AM/PM
      this.currentTime = now.toLocaleTimeString("en-US", {
        hour: "numeric",
        minute: "2-digit",
        second: "2-digit",
        hour12: true,
      });
    },

    printPrescription() {
      window.print();
    },

    async sendPrescriptionToEmail() {
      try {
        // Close the dropdown
        this.isPrescriptionActionDropdownOpen = false;

        if (!this.encounterId) {
          throw new Error("No encounter ID found");
        }

        // Ask for confirmation using SweetAlert2
        const result = await this.$swal.fire({
          title: "Send Prescription",
          text: "Are you sure you want to send this prescription via email?",
          icon: "question",
          showCancelButton: true,
          confirmButtonColor: "#3085d6",
          cancelButtonColor: "#d33",
          confirmButtonText: "Yes, send it!",
          cancelButtonText: "Cancel",
        });

        // If user confirms
        if (result.isConfirmed) {
          // Show loading state
          this.$swal.fire({
            title: "Sending...",
            text: "Please wait while we send the prescription",
            allowOutsideClick: false,
            allowEscapeKey: false,
            allowEnterKey: false,
            showConfirmButton: false,
            didOpen: () => {
              this.$swal.showLoading();
            },
          });

          // Send the email with just encounter_id
          const response = await get("prescription_mail", {
            encounter_id: this.encounterId,
          });

          // Close loading state
          this.$swal.close();

          if (response.data?.status === true) {
            // Show success message
            this.$swal.fire({
              title: "Success!",
              text: response.data.message || "Prescription sent successfully",
              icon: "success",
              confirmButtonColor: "#3085d6",
            });
          } else {
            throw new Error(response.data?.message || "Failed to send email");
          }
        }
      } catch (error) {
        console.error("Error sending prescription email:", error);
        // Show error message
        this.$swal.fire({
          title: "Error!",
          text:
            error.message ||
            "Failed to send prescription email. Please try again.",
          icon: "error",
          confirmButtonColor: "#3085d6",
        });
      }
    },

    async sendToSignatureRX() {
      try {
        console.log("Starting SignatureRX send process");
        console.log("Encounter ID:", this.encounterId);
        console.log("PIN length:", this.signatureRXPin ? this.signatureRXPin.length : 0);

        if (!this.encounterId) {
          throw new Error("No encounter ID found");
        }

        if (!this.signatureRXPin || this.signatureRXPin.length !== 6) {
          throw new Error("Please enter a valid 6-digit secure PIN");
        }

        this.signatureRXLoading = true;
        console.log("Setting loading state, preparing to send data to API");

        // Send prescription to SignatureRX
        console.log("Sending data to API:", {
          encounter_id: this.encounterId,
          secure_pin: "******" // Don't log the actual PIN
        });

        const response = await post("signaturerx_send_prescription", {
          encounter_id: this.encounterId,
          secure_pin: this.signatureRXPin
        });

        console.log("API Response:", response);

        // Process response
        if (response.data?.status === true) {
          console.log("Success response from API");
          // Close modal
          this.showSignatureRXModal = false;
          this.signatureRXPin = '';

          // Show success message
          this.$swal.fire({
            title: "Success!",
            text: response.data.message || "Prescription sent to SignatureRX successfully",
            icon: "success",
            confirmButtonColor: "#3085d6",
          });
        } else {
          console.log("Error response from API:", response.data);
          throw new Error(response.data?.message || "Failed to send prescription to SignatureRX");
        }
      } catch (error) {
        console.error("Error sending to SignatureRX:", error);

        // Show error message
        this.$swal.fire({
          title: "Error!",
          text: error.message || "Failed to send prescription to SignatureRX. Please try again.",
          icon: "error",
          confirmButtonColor: "#3085d6",
        });
      } finally {
        console.log("Resetting loading state");
        this.signatureRXLoading = false;
      }
    },
  },
};
</script>

<style scoped>
.srx-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.srx-modal {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 500px;
}

.srx-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.srx-modal-header h3 {
  font-size: 18px;
  font-weight: 500;
  margin: 0;
}

.srx-close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6B7280;
}

.srx-modal-body {
  padding: 24px;
}

.srx-info-box {
  background-color: #EFF6FF;
  color: #1E40AF;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.srx-info-box p {
  margin: 0;
  font-size: 14px;
}

.srx-form-group {
  margin-bottom: 16px;
}

.srx-form-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 4px;
}

.srx-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #D1D5DB;
  border-radius: 6px;
  font-size: 14px;
}

.srx-help-text {
  margin-top: 4px;
  font-size: 12px;
  color: #6B7280;
}

.srx-button-group {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.srx-btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.srx-btn-cancel {
  background-color: white;
  border: 1px solid #D1D5DB;
  color: #374151;
}

.srx-btn-send {
  background-color: black;
  border: 1px solid transparent;
  color: white;
  display: flex;
  align-items: center;
}

.srx-btn-send:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.srx-spinner {
  animation: srx-spin 1s linear infinite;
  margin-right: 8px;
  height: 16px;
  width: 16px;
}

.srx-spinner-circle {
  opacity: 0.25;
}

.srx-spinner-path {
  opacity: 0.75;
}

@keyframes srx-spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}
</style>
