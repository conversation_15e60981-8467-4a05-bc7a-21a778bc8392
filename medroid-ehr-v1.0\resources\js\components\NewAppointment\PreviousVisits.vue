<template>
  <div class="bg-white border rounded p-3 mb-3">
    <!-- Header -->
    <div class="flex justify-between items-center mb-3">
      <h2 class="font-medium flex items-center gap-2">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="lucide lucide-repeat w-4 h-4"
        >
          <path d="m17 2 4 4-4 4"></path>
          <path d="M3 11v-1a4 4 0 0 1 4-4h14"></path>
          <path d="m7 22-4-4 4-4"></path>
          <path d="M21 13v1a4 4 0 0 1-4 4H3"></path>
        </svg>
        Previous Visits
      </h2>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="text-center py-4 text-gray-500">
      Loading...
    </div>

    <!-- Content -->
    <template v-else>
      <div class="flex flex-col gap-2">
        <div
          v-for="encounter in limitedEncounters"
          :key="encounter.id"
          class="p-2 border rounded text-sm"
        >
          <div class="flex justify-between mb-1">
            <span class="font-medium">{{ encounter.description || 'General Visit' }}</span>
            <span class="text-gray-500">{{ formatDate(encounter.encounter_date) }}</span>
          </div>
          <div class="text-gray-600">
            <p>Doctor: {{ encounter.doctor_name }}</p>
            <template v-if="encounter.medical_history">
              <div v-if="encounter.medical_history.problem">
                <span class="font-medium">Problems: </span>
                {{ encounter.medical_history.problem.map(p => p.title).join(', ') }}
              </div>
            </template>
          </div>
        </div>

        <!-- No Data State -->
        <div v-if="encounters.length === 0" class="text-center text-gray-500 py-2">
          No previous visits found
        </div>
      </div>

      <!-- View More Button -->
      <div v-if="encounters.length > 2" class="mt-3 text-center">
        <button 
          @click="showAllEncounters = true"
          class="text-sm text-blue-500 hover:text-blue-600"
        >
          View More
        </button>
      </div>
    </template>

    <!-- Modal/Popup for All Encounters -->
    <div 
      v-if="showAllEncounters"
      class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-start justify-center overflow-y-auto pt-8"
    >
      <div class="bg-white rounded-lg w-full max-w-3xl max-h-[80vh] overflow-hidden">
        <!-- Modal Header -->
        <div class="p-4 border-b flex justify-between items-center">
          <h3 class="font-medium text-lg">Previous Visits History</h3>
          <button 
            @click="showAllEncounters = false"
            class="text-gray-500 hover:text-gray-700"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="w-5 h-5"
            >
              <path d="M18 6 6 18"></path>
              <path d="m6 6 12 12"></path>
            </svg>
          </button>
        </div>

        <!-- Modal Content -->
        <div class="p-4 overflow-y-auto max-h-[calc(80vh-8rem)]">
          <div class="flex flex-col gap-3">
            <div
              v-for="encounter in encounters"
              :key="encounter.id"
              class="p-3 border rounded"
            >
              <div class="flex justify-between mb-2">
                <span class="font-medium">{{ encounter.description || 'General Visit' }}</span>
                <span class="text-gray-500">{{ formatDate(encounter.encounter_date) }}</span>
              </div>
              <div class="text-gray-600">
                <p class="mb-2">Doctor: {{ encounter.doctor_name }}</p>
                <template v-if="encounter.medical_history">
                  <div v-if="encounter.medical_history.problem" class="mb-2">
                    <span class="font-medium">Problems: </span>
                    {{ encounter.medical_history.problem.map(p => p.title).join(', ') }}
                  </div>
                  <div v-if="encounter.medical_history.observation" class="mb-2">
                    <span class="font-medium">Observations: </span>
                    {{ encounter.medical_history.observation.map(o => o.title).join(', ') }}
                  </div>
                  <div v-if="encounter.medical_history.note">
                    <span class="font-medium">Notes: </span>
                    <div class="ml-4 mt-1">
                      <div v-for="(note, index) in encounter.medical_history.note" :key="index" class="mb-1">
                        - {{ note.title }}
                      </div>
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { get } from "../../config/request";
import { formatDistanceToNow, parseISO } from 'date-fns';

export default {
  name: "PreviousVisits",

  props: {
    patientId: {
      type: [String, Number],
      required: true
    }
  },

  data() {
    return {
      loading: false,
      encounters: [],
      showAllEncounters: false
    };
  },

  computed: {
    limitedEncounters() {
      return this.encounters.slice(0, 2);
    }
  },

  watch: {
    patientId: {
      immediate: true,
      handler(newId) {
        if (newId) {
          this.fetchEncounters();
        }
      }
    }
  },

  methods: {
    async fetchEncounters() {
      if (!this.patientId) return;

      try {
        this.loading = true;
        const response = await get('get_encounter_list_by_patient_id', {
          login_id: this.patientId
        });

        if (!response?.data?.status) {
          throw new Error(response?.data?.message || 'Failed to fetch encounters');
        }

        this.encounters = response.data.data.sort((a, b) => 
          new Date(b.encounter_date) - new Date(a.encounter_date)
        );

      } catch (error) {
        console.error('Error fetching encounters:', error);
        if (this.$toast) {
          this.$toast.error(error.message || 'Failed to fetch previous visits');
        }
      } finally {
        this.loading = false;
      }
    },

    formatDate(dateString) {
      try {
        return formatDistanceToNow(parseISO(dateString), { addSuffix: true });
      } catch (error) {
        return dateString;
      }
    }
  }
};
</script>