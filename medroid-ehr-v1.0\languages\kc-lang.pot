# Copyright (C) 2024 medroid
# This file is distributed under the same license as the KiviCare - Clinic & Patient Management System (EHR) plugin.
msgid ""
msgstr ""
"Project-Id-Version: KiviCare - Clinic & Patient Management System (EHR) 3.6.3\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/kivicare-clinic-management-system\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2024-10-23T11:30:18+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.9.0\n"
"X-Domain: kc-lang\n"

#. Plugin Name of the plugin
msgid "KiviCare - Clinic & Patient Management System (EHR)"
msgstr ""

#. Plugin URI of the plugin
msgid "https://medroid.ai"
msgstr ""

#. Description of the plugin
msgid "KiviCare is an impressive clinic and patient management plugin (EHR)."
msgstr ""

#. Author of the plugin
msgid "medroid"
msgstr ""

#. Author URI of the plugin
msgid "http://medroid.ai/"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:107
#: app/baseClasses/bookAppointment/bookAppointment.php:233
#: resources/assets/lang/temp.php:179
msgid "Logout"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:230
#: app/baseClasses/KCRoutesHandler.php:67
#: app/baseClasses/KCRoutesHandler.php:125
#: app/baseClasses/registerLogin/registerLogin.php:347
#: app/baseClasses/registerLogin/registerLogin.php:374
msgid "Route not found"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:231
#: app/baseClasses/registerLogin/registerLogin.php:353
#: app/baseClasses/registerLogin/registerLogin.php:380
#: app/baseClasses/registerLogin/registerLogin.php:477
#: app/baseClasses/registerLogin/registerLogin.php:511
#: resources/assets/lang/temp.php:407
#: resources/assets/lang/temp.php:1076
msgid "Internal server error"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:232
msgid "Loading...."
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:234
#: app/baseClasses/bookAppointment/components/detail-info/tab-panel.php:19
#: app/baseClasses/registerLogin/registerLogin.php:50
#: resources/assets/lang/temp.php:230
#: resources/assets/lang/temp.php:1785
msgid "Login"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:235
#: app/baseClasses/bookAppointment/components/detail-info/tab-panel.php:15
#: app/baseClasses/registerLogin/registerLogin.php:56
#: resources/assets/lang/temp.php:1786
#: utils/kc_helpers.php:6497
msgid "Register"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:236
#: app/baseClasses/bookAppointment/components/clinic/tab-panel.php:3
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:87
#: app/baseClasses/registerLogin/registerLogin.php:184
#: resources/assets/lang/temp.php:741
#: resources/assets/lang/temp.php:825
#: resources/assets/lang/temp.php:869
#: resources/assets/lang/temp.php:1012
#: resources/assets/lang/temp.php:1683
msgid "Select Clinic"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:237
#: app/baseClasses/bookAppointment/components/doctor/tab-panel.php:3
#: resources/assets/lang/temp.php:1065
#: resources/assets/lang/temp.php:1466
msgid "Select Doctor"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:238
#: resources/assets/lang/temp.php:1701
msgid "Select Category"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:239
#: app/baseClasses/bookAppointment/components/date-time/tab-panel.php:3
#: app/baseClasses/bookAppointment/components/date-time/tab.php:2
#: resources/assets/lang/temp.php:1675
#: utils/kc_helpers.php:5106
msgid "Select Date and Time"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:240
msgid "Select Payment Mode"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:241
#: app/baseClasses/bookAppointment/components/date-time/tab-panel.php:41
msgid "Please Select Date"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:242
msgid "No doctor Available"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:243
msgid "No clinic Available"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:244
msgid "No service Available"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:245
#: app/controllers/KCElementorController.php:270
#: app/controllers/KCElementorController.php:535
#: resources/assets/lang/temp.php:1771
#: utils/kc_helpers.php:6506
#: utils/kc_helpers.php:6513
msgid "Next"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:246
#: resources/assets/lang/temp.php:1695
#: utils/kc_helpers.php:6508
#: utils/kc_helpers.php:6511
msgid "Confirm"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:250
#: resources/assets/lang/temp.php:1832
msgid "Sun"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:251
#: resources/assets/lang/temp.php:1826
msgid "Mon"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:252
#: resources/assets/lang/temp.php:1827
msgid "Tue"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:253
#: resources/assets/lang/temp.php:1828
msgid "Wed"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:254
#: resources/assets/lang/temp.php:1829
msgid "Thu"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:255
#: resources/assets/lang/temp.php:1830
msgid "Fri"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:256
#: resources/assets/lang/temp.php:1831
msgid "Sat"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:259
#: app/controllers/KCHomeController.php:482
msgid "Sunday"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:260
#: app/controllers/KCHomeController.php:476
msgid "Monday"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:261
#: app/controllers/KCHomeController.php:477
msgid "Tuesday"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:262
#: app/controllers/KCHomeController.php:478
msgid "Wednesday"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:263
#: app/controllers/KCHomeController.php:479
msgid "Thursday"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:264
#: app/controllers/KCHomeController.php:480
msgid "Friday"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:265
#: app/controllers/KCHomeController.php:481
msgid "Saturday"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:270
#: resources/assets/lang/temp.php:1849
msgid "Jan"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:271
#: resources/assets/lang/temp.php:1850
msgid "Feb"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:272
#: resources/assets/lang/temp.php:1851
msgid "Mar"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:273
#: resources/assets/lang/temp.php:1852
msgid "Apr"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:274
#: app/baseClasses/bookAppointment/bookAppointment.php:288
#: resources/assets/lang/temp.php:1839
#: resources/assets/lang/temp.php:1853
#: utils/kc_helpers.php:630
msgid "May"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:275
#: resources/assets/lang/temp.php:1854
msgid "Jun"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:276
#: resources/assets/lang/temp.php:1855
msgid "Jul"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:277
#: resources/assets/lang/temp.php:1856
msgid "Aug"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:278
#: resources/assets/lang/temp.php:1857
msgid "Sep"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:279
#: resources/assets/lang/temp.php:1858
msgid "Oct"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:280
#: resources/assets/lang/temp.php:1859
msgid "Nov"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:281
#: resources/assets/lang/temp.php:1860
msgid "Dec"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:284
#: resources/assets/lang/temp.php:1835
#: utils/kc_helpers.php:626
msgid "January"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:285
#: resources/assets/lang/temp.php:1836
#: utils/kc_helpers.php:627
msgid "February"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:286
#: resources/assets/lang/temp.php:1837
#: utils/kc_helpers.php:628
msgid "March"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:287
#: resources/assets/lang/temp.php:1838
#: utils/kc_helpers.php:629
msgid "April"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:289
#: resources/assets/lang/temp.php:1840
#: utils/kc_helpers.php:631
msgid "June"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:290
#: resources/assets/lang/temp.php:1841
#: utils/kc_helpers.php:632
msgid "July"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:291
#: resources/assets/lang/temp.php:1842
#: utils/kc_helpers.php:633
msgid "August"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:292
#: resources/assets/lang/temp.php:1843
#: utils/kc_helpers.php:634
msgid "September"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:293
#: resources/assets/lang/temp.php:1844
#: utils/kc_helpers.php:635
msgid "October"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:294
#: resources/assets/lang/temp.php:1845
#: utils/kc_helpers.php:636
msgid "November"
msgstr ""

#: app/baseClasses/bookAppointment/bookAppointment.php:295
#: resources/assets/lang/temp.php:1846
#: utils/kc_helpers.php:637
msgid "December"
msgstr ""

#: app/baseClasses/bookAppointment/components/category/tab-panel.php:3
msgid "Select Service"
msgstr ""

#: app/baseClasses/bookAppointment/components/category/tab-panel.php:14
#: app/baseClasses/bookAppointment/components/clinic/tab-panel.php:14
#: app/baseClasses/bookAppointment/components/doctor/tab-panel.php:14
msgid "Search...."
msgstr ""

#: app/baseClasses/bookAppointment/components/category/tab.php:2
msgid "Doctor Services"
msgstr ""

#: app/baseClasses/bookAppointment/components/category/tab.php:3
#: resources/assets/lang/temp.php:1674
msgid "Please select a service from below options"
msgstr ""

#: app/baseClasses/bookAppointment/components/clinic/tab.php:2
#: resources/assets/lang/temp.php:1682
#: utils/kc_helpers.php:5103
msgid "Choose a Clinic"
msgstr ""

#: app/baseClasses/bookAppointment/components/clinic/tab.php:3
#: resources/assets/lang/temp.php:1684
msgid "Please select a Clinic you want to visit"
msgstr ""

#: app/baseClasses/bookAppointment/components/confirm-pay/tab-panel.php:2
msgid "Payment Selection"
msgstr ""

#: app/baseClasses/bookAppointment/components/confirm/tab-panel.php:2
#: resources/assets/lang/temp.php:1680
msgid "Confirmation Detail"
msgstr ""

#: app/baseClasses/bookAppointment/components/confirm/tab.php:2
#: resources/assets/lang/temp.php:1679
#: utils/kc_helpers.php:5109
msgid "Confirmation"
msgstr ""

#: app/baseClasses/bookAppointment/components/confirm/tab.php:3
#: resources/assets/lang/temp.php:1681
msgid "Confirm your booking"
msgstr ""

#: app/baseClasses/bookAppointment/components/confirmed.php:27
msgid "Your Appointment is Booked Sucessfully!"
msgstr ""

#: app/baseClasses/bookAppointment/components/confirmed.php:30
msgid "Please check your email for verification"
msgstr ""

#: app/baseClasses/bookAppointment/components/confirmed.php:34
msgid "Verification email will receive after payment complete"
msgstr ""

#: app/baseClasses/bookAppointment/components/confirmed.php:42
msgid "Book More Appointments"
msgstr ""

#: app/baseClasses/bookAppointment/components/confirmed.php:49
#: resources/assets/lang/temp.php:1709
msgid "Print Detail"
msgstr ""

#: app/baseClasses/bookAppointment/components/confirmed.php:54
msgid "Add To Calendar"
msgstr ""

#: app/baseClasses/bookAppointment/components/date-time/tab-panel.php:27
msgid "Time Zone: "
msgstr ""

#: app/baseClasses/bookAppointment/components/date-time/tab-panel.php:37
#: resources/assets/lang/temp.php:1687
msgid "Available time slots"
msgstr ""

#: app/baseClasses/bookAppointment/components/date-time/tab-panel.php:50
msgid "Select doctor session is not available with selected clinic, please select other doctor or other clinic"
msgstr ""

#: app/baseClasses/bookAppointment/components/date-time/tab.php:3
#: resources/assets/lang/temp.php:1676
msgid "Select date to see a timeline of available slots"
msgstr ""

#: app/baseClasses/bookAppointment/components/detail-info/tab-panel.php:8
#: resources/assets/lang/temp.php:1688
msgid "Enter Details"
msgstr ""

#: app/baseClasses/bookAppointment/components/detail-info/tab-panel.php:36
#: app/baseClasses/registerLogin/registerLogin.php:81
#: resources/assets/lang/temp.php:213
msgid "First Name"
msgstr ""

#: app/baseClasses/bookAppointment/components/detail-info/tab-panel.php:39
#: app/baseClasses/registerLogin/registerLogin.php:84
#: resources/assets/lang/temp.php:215
msgid "Enter your first name"
msgstr ""

#: app/baseClasses/bookAppointment/components/detail-info/tab-panel.php:42
#: app/baseClasses/registerLogin/registerLogin.php:89
#: resources/assets/lang/temp.php:219
msgid "Last Name"
msgstr ""

#: app/baseClasses/bookAppointment/components/detail-info/tab-panel.php:45
#: app/baseClasses/registerLogin/registerLogin.php:92
#: resources/assets/lang/temp.php:220
msgid "Enter your last name"
msgstr ""

#: app/baseClasses/bookAppointment/components/detail-info/tab-panel.php:48
#: app/baseClasses/registerLogin/registerLogin.php:96
#: app/controllers/KCBookAppointmentWidgetController.php:729
#: app/controllers/KCBookAppointmentWidgetController.php:1268
#: app/controllers/KCBookAppointmentWidgetController.php:1304
#: resources/assets/lang/temp.php:222
#: resources/assets/lang/temp.php:700
#: resources/assets/lang/temp.php:756
#: resources/assets/lang/temp.php:792
#: resources/assets/lang/temp.php:834
#: resources/assets/lang/temp.php:906
#: resources/assets/lang/temp.php:1257
#: resources/assets/lang/temp.php:1686
msgid "Email"
msgstr ""

#: app/baseClasses/bookAppointment/components/detail-info/tab-panel.php:51
#: app/baseClasses/registerLogin/registerLogin.php:99
#: resources/assets/lang/temp.php:223
msgid "Enter your email"
msgstr ""

#: app/baseClasses/bookAppointment/components/detail-info/tab-panel.php:54
#: app/baseClasses/registerLogin/registerLogin.php:103
#: app/controllers/KCBookAppointmentWidgetController.php:1275
#: app/controllers/KCBookAppointmentWidgetController.php:1291
#: resources/assets/lang/temp.php:256
#: resources/assets/lang/temp.php:1685
msgid "Contact"
msgstr ""

#: app/baseClasses/bookAppointment/components/detail-info/tab-panel.php:77
#: app/baseClasses/registerLogin/registerLogin.php:128
msgid "Enter your country code"
msgstr ""

#: app/baseClasses/bookAppointment/components/detail-info/tab-panel.php:83
#: app/baseClasses/registerLogin/registerLogin.php:133
#: resources/assets/lang/temp.php:257
msgid "Enter your contact number"
msgstr ""

#: app/baseClasses/bookAppointment/components/detail-info/tab-panel.php:89
#: app/baseClasses/registerLogin/registerLogin.php:138
#: resources/assets/lang/temp.php:187
msgid "Gender"
msgstr ""

#: app/baseClasses/bookAppointment/components/detail-info/tab-panel.php:94
#: app/baseClasses/registerLogin/registerLogin.php:144
#: app/controllers/KCPatientBillController.php:387
#: resources/assets/lang/temp.php:189
msgid "Male"
msgstr ""

#: app/baseClasses/bookAppointment/components/detail-info/tab-panel.php:98
#: app/baseClasses/registerLogin/registerLogin.php:148
#: app/controllers/KCPatientBillController.php:389
#: resources/assets/lang/temp.php:190
msgid "Female"
msgstr ""

#: app/baseClasses/bookAppointment/components/detail-info/tab-panel.php:102
#: app/baseClasses/KCDeactivate.php:74
#: app/baseClasses/registerLogin/registerLogin.php:152
#: resources/assets/lang/temp.php:191
msgid "Other"
msgstr ""

#: app/baseClasses/bookAppointment/components/detail-info/tab-panel.php:124
#: app/baseClasses/registerLogin/registerLogin.php:215
#: resources/assets/lang/temp.php:1225
msgid "Username or Email"
msgstr ""

#: app/baseClasses/bookAppointment/components/detail-info/tab-panel.php:127
#: app/baseClasses/registerLogin/registerLogin.php:218
msgid "Enter your username or email"
msgstr ""

#: app/baseClasses/bookAppointment/components/detail-info/tab-panel.php:130
#: app/baseClasses/registerLogin/registerLogin.php:222
#: resources/assets/lang/temp.php:225
msgid "Password"
msgstr ""

#: app/baseClasses/bookAppointment/components/detail-info/tab-panel.php:140
#: app/baseClasses/registerLogin/registerLogin.php:236
msgid "Forgot Password ?"
msgstr ""

#: app/baseClasses/bookAppointment/components/detail-info/tab.php:2
#: resources/assets/lang/temp.php:1677
#: utils/kc_helpers.php:5108
msgid "User Detail Information"
msgstr ""

#: app/baseClasses/bookAppointment/components/detail-info/tab.php:3
#: resources/assets/lang/temp.php:1678
msgid "Please provide you contact details"
msgstr ""

#: app/baseClasses/bookAppointment/components/doctor/tab.php:2
#: resources/assets/lang/temp.php:1671
#: utils/kc_helpers.php:5104
msgid "Choose Your Doctor"
msgstr ""

#: app/baseClasses/bookAppointment/components/doctor/tab.php:3
#: resources/assets/lang/temp.php:1672
msgid "pick a specific Doctor to perform your service"
msgstr ""

#: app/baseClasses/bookAppointment/components/file-uploads-custom/tab-panel.php:3
msgid "More About Appointment"
msgstr ""

#: app/baseClasses/bookAppointment/components/file-uploads-custom/tab-panel.php:15
msgid "Appointment Descriptions"
msgstr ""

#: app/baseClasses/bookAppointment/components/file-uploads-custom/tab-panel.php:19
msgid "Enter Appointment Descriptions"
msgstr ""

#: app/baseClasses/bookAppointment/components/file-uploads-custom/tab-panel.php:30
#: resources/assets/lang/temp.php:738
msgid "Add Medical Report"
msgstr ""

#: app/baseClasses/bookAppointment/components/file-uploads-custom/tab-panel.php:33
msgid "Add Your Medical Report"
msgstr ""

#: app/baseClasses/bookAppointment/components/file-uploads-custom/tab.php:2
msgid "Appointment Extra Data"
msgstr ""

#: app/baseClasses/bookAppointment/components/file-uploads-custom/tab.php:3
msgid "Upload file and description about appointment"
msgstr ""

#: app/baseClasses/bookAppointment/components/payment-error/tab-panel.php:5
msgid "Payment Transaction Failed. Please, try again."
msgstr ""

#: app/baseClasses/bookAppointment/components/payment-error/tab-panel.php:12
msgid "Try Again"
msgstr ""

#: app/baseClasses/KCActivate.php:346
#: resources/assets/lang/temp.php:11
#: resources/assets/lang/temp.php:1518
msgid "Medroid (EHR)"
msgstr ""

#: app/baseClasses/KCActivate.php:478
msgid "Go Pro"
msgstr ""

#: app/baseClasses/KCActivate.php:868
msgid "Login has been disabled. please contact you system administrator. "
msgstr ""

#: app/baseClasses/KCActivate.php:1486
msgid "Custom kivicaresms Posts"
msgstr ""

#: app/baseClasses/KCActivate.php:1504
msgid "Custom kivicaremail Posts"
msgstr ""

#: app/baseClasses/KCActivate.php:1523
msgid "Custom kivicare Google Event Posts"
msgstr ""

#: app/baseClasses/KCActivate.php:1542
msgid "Custom kivicare Google Meet Event Posts"
msgstr ""

#: app/baseClasses/KCActivate.php:1661
msgid "This User account is Not Activated"
msgstr ""

#: app/baseClasses/KCActivate.php:1738
msgid "Kivicare"
msgstr ""

#: app/baseClasses/KCActivate.php:1765
msgctxt "Page slug"
msgid "appointment"
msgstr ""

#: app/baseClasses/KCActivate.php:1766
msgctxt "Page title"
msgid "Appointment"
msgstr ""

#: app/baseClasses/KCActivate.php:1771
msgctxt "Page slug"
msgid "patient-dashboard"
msgstr ""

#: app/baseClasses/KCActivate.php:1772
msgctxt "Page title"
msgid "Patient Dashboard"
msgstr ""

#: app/baseClasses/KCActivate.php:1777
msgctxt "Page slug"
msgid "register-login"
msgstr ""

#: app/baseClasses/KCActivate.php:1778
msgctxt "Page title"
msgid "Register Login user"
msgstr ""

#: app/baseClasses/KCActivate.php:1857
msgid "Appointment Booking Page"
msgstr ""

#: app/baseClasses/KCActivate.php:1861
msgid "Patient Dashboard Page"
msgstr ""

#: app/baseClasses/KCActivate.php:1865
msgid "User Register Login Page"
msgstr ""

#: app/baseClasses/KCActivate.php:1885
msgid "Service ID"
msgstr ""

#: app/baseClasses/KCActivate.php:1887
msgid "Appointment ID"
msgstr ""

#: app/baseClasses/KCActivate.php:2100
#: resources/assets/lang/temp.php:1136
#: utils/kc_helpers.php:6882
#: utils/kc_helpers.php:6889
#: utils/kc_helpers.php:7026
#: utils/kc_helpers.php:7142
#: utils/kc_helpers.php:7149
#: utils/kc_helpers.php:7230
#: utils/kc_helpers.php:7237
#: utils/kc_helpers.php:7310
msgid "Encounters"
msgstr ""

#: app/baseClasses/KCActivate.php:2107
#: resources/assets/lang/temp.php:241
#: utils/kc_helpers.php:6896
#: utils/kc_helpers.php:7040
#: utils/kc_helpers.php:7156
#: utils/kc_helpers.php:7244
#: utils/kc_helpers.php:7421
msgid "Encounter Templates"
msgstr ""

#: app/baseClasses/KCBase.php:37
#: app/controllers/KCPatientMedicalHistoryController.php:56
#: utils/kc_helpers.php:7380
msgid "You don't have permission to access"
msgstr ""

#: app/baseClasses/KCDeactivate.php:49
msgid "I no longer need the plugin"
msgstr ""

#: app/baseClasses/KCDeactivate.php:53
msgid "I found a better plugin"
msgstr ""

#: app/baseClasses/KCDeactivate.php:54
msgid "Please share which plugin"
msgstr ""

#: app/baseClasses/KCDeactivate.php:57
msgid "I couldn't get the plugin to work"
msgstr ""

#: app/baseClasses/KCDeactivate.php:61
msgid "It's a temporary deactivation"
msgstr ""

#: app/baseClasses/KCDeactivate.php:65
msgid "I have kivicare Pro"
msgstr ""

#: app/baseClasses/KCDeactivate.php:67
msgid "Note : kivicare is a Mandatory plugin for PRO version to work"
msgstr ""

#: app/baseClasses/KCDeactivate.php:70
msgid "Not able To customize"
msgstr ""

#: app/baseClasses/KCDeactivate.php:71
msgid "Please share the where you need customization"
msgstr ""

#: app/baseClasses/KCDeactivate.php:75
msgid "Please share the reason"
msgstr ""

#: app/baseClasses/KCDeactivate.php:84
msgid "Quick Feedback"
msgstr ""

#: app/baseClasses/KCDeactivate.php:89
msgid "If you have a moment, please share why you are deactivating Kivicare:"
msgstr ""

#: app/baseClasses/KCDeactivate.php:106
msgid "Submitting"
msgstr ""

#: app/baseClasses/KCDeactivate.php:109
msgid "Submit & Deactivate"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:47
msgid "Kivicare Clinic List"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:62
msgid "Kivicare Clinic Card"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:69
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:134
msgid "Enable Filter"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:80
msgid "Clinic per page "
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:92
msgid "Enable Exclude Clinic"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:100
msgid "Exclude Clinic"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:115
msgid "Hide Space Between Clinics"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:125
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:165
msgid "Profile Image"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:147
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:185
msgid "Card style"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:156
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:194
msgid "Card Background"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:166
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:204
msgid "Card Box Shadow"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:175
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:213
msgid "Card Border"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:183
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:221
msgid "Card Border Radius"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:197
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:246
msgid "Image style"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:209
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:258
msgid "Image Height"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:223
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:272
msgid "Image width"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:238
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:287
msgid "Image Border"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:252
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:301
msgid "Image Border Radius"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:266
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:315
msgid "Image Border style"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:272
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:321
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:461
#: utils/kc_helpers.php:3853
#: utils/kc_helpers.php:3928
#: utils/kc_helpers.php:4059
#: utils/kc_helpers.php:4134
msgid "solid"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:273
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:322
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:462
#: utils/kc_helpers.php:3854
#: utils/kc_helpers.php:3929
#: utils/kc_helpers.php:4060
#: utils/kc_helpers.php:4135
msgid "dashed"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:274
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:323
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:463
#: utils/kc_helpers.php:3855
#: utils/kc_helpers.php:3930
#: utils/kc_helpers.php:4061
#: utils/kc_helpers.php:4136
msgid "dotted"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:275
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:324
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:464
#: utils/kc_helpers.php:3856
#: utils/kc_helpers.php:3931
#: utils/kc_helpers.php:4062
#: utils/kc_helpers.php:4137
msgid "double"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:276
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:325
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:465
#: utils/kc_helpers.php:3857
#: utils/kc_helpers.php:3932
#: utils/kc_helpers.php:4063
#: utils/kc_helpers.php:4138
msgid "groove"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:277
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:326
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:466
#: utils/kc_helpers.php:3858
#: utils/kc_helpers.php:3933
#: utils/kc_helpers.php:4064
#: utils/kc_helpers.php:4139
msgid "ridge"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:278
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:327
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:467
#: utils/kc_helpers.php:3859
#: utils/kc_helpers.php:3934
#: utils/kc_helpers.php:4065
#: utils/kc_helpers.php:4140
msgid "inset"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:279
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:328
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:468
#: utils/kc_helpers.php:3860
#: utils/kc_helpers.php:3935
#: utils/kc_helpers.php:4066
#: utils/kc_helpers.php:4141
msgid "outset"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:280
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:329
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:469
#: utils/kc_helpers.php:3861
#: utils/kc_helpers.php:3936
#: utils/kc_helpers.php:4067
#: utils/kc_helpers.php:4142
msgid "none"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:281
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:330
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:470
#: utils/kc_helpers.php:3862
#: utils/kc_helpers.php:3937
#: utils/kc_helpers.php:4068
#: utils/kc_helpers.php:4143
msgid "hidden"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:292
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:341
msgid "Image Border Color"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:316
msgid "Administrator style"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:327
#: app/baseClasses/KCElementor/KCElementorClinicCard.php:483
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:629
msgid "Label Color"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:342
#: app/baseClasses/KCElementor/KCElementorClinicCard.php:498
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:644
msgid "Label Typography"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:354
#: app/baseClasses/KCElementor/KCElementorClinicCard.php:510
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:656
msgid "Label Margin"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:369
#: app/baseClasses/KCElementor/KCElementorClinicCard.php:525
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:671
msgid "Label Padding"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:384
#: app/baseClasses/KCElementor/KCElementorClinicCard.php:540
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:686
msgid "Label Alignment"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:387
#: app/baseClasses/KCElementor/KCElementorClinicCard.php:543
#: app/baseClasses/KCElementor/KCElementorClinicCard.php:619
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:689
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:770
msgid "Left"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:388
#: app/baseClasses/KCElementor/KCElementorClinicCard.php:544
#: app/baseClasses/KCElementor/KCElementorClinicCard.php:620
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:690
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:771
msgid "Center"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:389
#: app/baseClasses/KCElementor/KCElementorClinicCard.php:545
#: app/baseClasses/KCElementor/KCElementorClinicCard.php:621
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:691
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:772
msgid "Right"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:404
msgid "Book appointment Button style"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:419
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:567
msgid " ID"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:454
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:597
msgid "label "
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:472
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:618
msgid " style"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:559
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:706
msgid "Value Color"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:574
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:723
msgid "Value Typography"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:586
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:735
msgid "Value Margin"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:601
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:751
msgid "Value Padding"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:616
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:767
msgid "Value Alignment"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:655
msgid "Filter by  Speciality "
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:686
msgid "Filter by  Service"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:703
msgid "Search clinic by name,email and contact no"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:704
msgid "Filter Clinic"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:746
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:901
msgid "Please select any filter value"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:784
#: app/baseClasses/KCElementor/KCElementorClinicCard.php:826
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:940
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:995
msgid "fail"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:801
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:914
#: app/baseClasses/popupBookAppointment/bookAppointment.php:26
#: resources/assets/lang/temp.php:111
msgid "Loading..."
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicCard.php:817
#: app/baseClasses/KCElementor/KCElementorClinicCard.php:824
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:931
#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:938
#: app/baseClasses/popupBookAppointment/bookAppointment.php:7
#: app/baseClasses/popupBookAppointment/bookAppointment.php:40
#: app/baseClasses/popupBookAppointment/bookAppointment.php:48
#: app/baseClasses/popupBookAppointment/bookAppointment.php:68
#: app/controllers/KCElementorController.php:249
#: app/controllers/KCElementorController.php:517
#: resources/assets/lang/temp.php:968
#: resources/assets/lang/temp.php:1742
msgid "Book Appointment"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:28
#: app/baseClasses/WidgetHandler.php:138
#: app/baseClasses/WidgetHandler.php:237
msgid "Google Recaptcha Data Not found"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:62
msgid "KiviCare Doctor List"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:80
msgid "Kivicare Clinic Wise Doctor"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:101
msgid "Specific Doctor add"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:116
msgid "Select Doctors"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:145
msgid "Doctor per page "
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:155
msgid "Hide Space Between Doctors"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:233
msgid "Left Side Color"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:364
msgid "Session Container style"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:375
msgid "Container Height"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:389
msgid "Cell Height"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:403
msgid "Cell width"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:418
msgid "Cell Background"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:427
msgid "Cell Border"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:441
msgid "Cell Border Radius"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:455
msgid "Cell Border style"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:481
msgid "Cell Border Color"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:495
msgid "Title Font Color"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:510
msgid "Title Font Typography"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:522
#: utils/kc_helpers.php:3818
#: utils/kc_helpers.php:3893
#: utils/kc_helpers.php:4024
#: utils/kc_helpers.php:4099
msgid "Font Color"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:537
#: utils/kc_helpers.php:3789
#: utils/kc_helpers.php:3995
msgid "Font Typography"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:551
msgid "Appointment Book Button style"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:796
#: app/controllers/KCElementorController.php:56
#: resources/assets/lang/temp.php:726
#: resources/assets/lang/temp.php:928
msgid "No Doctor Found"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:823
msgid "Filter by  Speciality"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:841
msgid "Filter by  Service "
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:856
msgid "Search doctor by name,email and ID"
msgstr ""

#: app/baseClasses/KCElementor/KCElementorClinicWiseDoctor.php:857
msgid "Filter Doctor"
msgstr ""

#: app/baseClasses/KCModel.php:146
msgid "Values for IN query must be an array."
msgstr ""

#: app/baseClasses/KCRoutesHandler.php:36
#: app/baseClasses/KCRoutesHandler.php:50
#: app/baseClasses/KCRoutesHandler.php:113
msgid "Method is not allowed"
msgstr ""

#: app/baseClasses/KCRoutesHandler.php:60
#: app/baseClasses/KCRoutesHandler.php:118
msgid "Invalid nonce in request"
msgstr ""

#: app/baseClasses/KCRoutesHandler.php:154
msgid "Controller Class not found "
msgstr ""

#: app/baseClasses/registerLogin/registerLogin.php:41
msgid "Note:- Click Here To Enable Google V3 captcha"
msgstr ""

#: app/baseClasses/registerLogin/registerLogin.php:55
msgid "active"
msgstr ""

#: app/baseClasses/registerLogin/registerLogin.php:161
msgid "Select Role"
msgstr ""

#: app/baseClasses/registerLogin/registerLogin.php:233
msgid "Remember Me"
msgstr ""

#: app/baseClasses/WidgetHandler.php:15
msgid "Current user can not view the widget. Please open this page in incognito mode or use another browser."
msgstr ""

#: app/baseClasses/WidgetHandler.php:58
#: app/baseClasses/WidgetHandler.php:147
msgid "Selected clinic not available"
msgstr ""

#: app/baseClasses/WidgetHandler.php:65
#: app/baseClasses/WidgetHandler.php:155
msgid "No clinic available"
msgstr ""

#: app/baseClasses/WidgetHandler.php:75
#: app/baseClasses/WidgetHandler.php:169
msgid "Please Provide clinic id"
msgstr ""

#: app/baseClasses/WidgetHandler.php:79
#: app/baseClasses/WidgetHandler.php:173
msgid "Select Doctor Not available"
msgstr ""

#: app/baseClasses/WidgetHandler.php:86
#: app/baseClasses/WidgetHandler.php:181
msgid "No Doctor  available"
msgstr ""

#: app/baseClasses/WidgetHandler.php:194
msgid "Select Service Not available"
msgstr ""

#: app/baseClasses/WidgetHandler.php:201
msgid "No service  available"
msgstr ""

#: app/baseClasses/WidgetHandler.php:252
#: app/baseClasses/WidgetHandler.php:262
#: resources/assets/lang/temp.php:212
#: resources/assets/lang/temp.php:987
#: resources/assets/lang/temp.php:997
msgid "Patient"
msgstr ""

#: app/baseClasses/WidgetHandler.php:254
#: app/baseClasses/WidgetHandler.php:263
#: app/controllers/KCBookAppointmentWidgetController.php:745
#: app/controllers/KCBookAppointmentWidgetController.php:1138
#: resources/assets/lang/temp.php:209
#: resources/assets/lang/temp.php:1114
#: resources/assets/lang/temp.php:1268
#: resources/assets/lang/temp.php:1655
msgid "Doctor"
msgstr ""

#: app/baseClasses/WidgetHandler.php:256
#: app/baseClasses/WidgetHandler.php:264
#: resources/assets/lang/temp.php:210
#: resources/assets/lang/temp.php:857
#: utils/kc_helpers.php:6926
#: utils/kc_helpers.php:7063
msgid "Receptionist"
msgstr ""

#: app/controllers/api/KCApiHomeController.php:47
#: app/controllers/KCStaticDataController.php:299
msgid "Datatype not found"
msgstr ""

#: app/controllers/api/KCApiHomeController.php:134
#: app/controllers/KCStaticDataController.php:625
msgid "Datatype found"
msgstr ""

#: app/controllers/api/KCApiHomeController.php:152
#: app/controllers/KCDoctorController.php:173
#: app/controllers/KCSetupController.php:468
#: app/controllers/KCSetupController.php:486
msgid "No doctors found"
msgstr ""

#: app/controllers/api/KCApiHomeController.php:182
#: app/controllers/KCDoctorController.php:234
msgid "Doctors list"
msgstr ""

#: app/controllers/api/KCAuthController.php:67
msgid "Incorrect username and password"
msgstr ""

#: app/controllers/api/KCAuthController.php:77
#: app/controllers/KCAuthController.php:71
#: app/controllers/KCAuthController.php:148
#: app/controllers/KCAuthController.php:399
msgid "Logged in successfully"
msgstr ""

#: app/controllers/api/KCAuthController.php:135
msgid "Register successfully completed"
msgstr ""

#: app/controllers/api/KCAuthController.php:139
msgid "Registration not successfully completed"
msgstr ""

#: app/controllers/api/KCBookAppointmentController.php:57
msgid "Please Sign in to book appointment"
msgstr ""

#: app/controllers/api/KCBookAppointmentController.php:61
msgid "User must be a Patient to book appointment"
msgstr ""

#: app/controllers/api/KCBookAppointmentController.php:88
msgid "Appointment booked successfully"
msgstr ""

#: app/controllers/api/KCPatientAppointmentBooking.php:52
#: app/controllers/KCAppointmentController.php:281
#: app/controllers/KCPatientEncounterController.php:252
#: resources/assets/lang/temp.php:958
msgid "Status is required"
msgstr ""

#: app/controllers/api/KCPatientAppointmentBooking.php:53
#: app/controllers/KCAppointmentController.php:282
#: app/controllers/KCPatientEncounterController.php:253
#: resources/assets/lang/temp.php:985
#: resources/assets/lang/temp.php:1309
msgid "Patient is required"
msgstr ""

#: app/controllers/api/KCPatientAppointmentBooking.php:54
#: app/controllers/KCAppointmentController.php:283
#: app/controllers/KCAppointmentController.php:809
#: resources/assets/lang/temp.php:461
msgid "Clinic is required"
msgstr ""

#: app/controllers/api/KCPatientAppointmentBooking.php:55
#: app/controllers/KCAppointmentController.php:284
#: app/controllers/KCAppointmentController.php:810
#: app/controllers/KCPatientEncounterController.php:254
#: resources/assets/lang/temp.php:953
msgid "Doctor is required"
msgstr ""

#: app/controllers/api/KCPatientAppointmentBooking.php:88
msgid "Appointment has been added successfully"
msgstr ""

#: app/controllers/KCAppointmentController.php:148
msgid "No appointments found"
msgstr ""

#: app/controllers/KCAppointmentController.php:257
msgid "Appointment list"
msgstr ""

#: app/controllers/KCAppointmentController.php:374
#: app/controllers/KCAppointmentController.php:713
msgid "This Appointment can not be edited."
msgstr ""

#: app/controllers/KCAppointmentController.php:397
msgid "Appointment has been updated successfully"
msgstr ""

#: app/controllers/KCAppointmentController.php:432
msgid "Appointment Already Booked For This Time Slot."
msgstr ""

#: app/controllers/KCAppointmentController.php:443
#: app/controllers/KCAppointmentController.php:623
msgid "Appointment booking Failed. Please try again."
msgstr ""

#: app/controllers/KCAppointmentController.php:450
msgid "Appointment is Successfully booked."
msgstr ""

#: app/controllers/KCAppointmentController.php:511
#: app/controllers/KCBookAppointmentWidgetController.php:308
msgid "Failed to generate Video Meeting."
msgstr ""

#: app/controllers/KCAppointmentController.php:590
#: app/controllers/KCBookAppointmentWidgetController.php:397
msgid "Failed to create razorpay payment link"
msgstr ""

#: app/controllers/KCAppointmentController.php:644
#: app/controllers/KCAppointmentController.php:662
#: app/controllers/KCAppointmentController.php:1265
#: app/controllers/KCAppointmentController.php:1275
#: app/controllers/KCClinicController.php:412
#: app/controllers/KCClinicController.php:443
#: app/controllers/KCClinicController.php:474
#: app/controllers/KCClinicScheduleController.php:255
#: app/controllers/KCClinicScheduleController.php:303
#: app/controllers/KCClinicScheduleController.php:332
#: app/controllers/KCClinicScheduleController.php:355
#: app/controllers/KCCustomFieldController.php:198
#: app/controllers/KCCustomFieldController.php:206
#: app/controllers/KCCustomFieldController.php:265
#: app/controllers/KCCustomFieldController.php:310
#: app/controllers/KCDoctorController.php:514
#: app/controllers/KCDoctorController.php:651
#: app/controllers/KCDoctorController.php:682
#: app/controllers/KCDoctorController.php:740
#: app/controllers/KCPatientBillController.php:257
#: app/controllers/KCPatientBillController.php:312
#: app/controllers/KCPatientBillController.php:473
#: app/controllers/KCPatientBillController.php:523
#: app/controllers/KCPatientBillController.php:571
#: app/controllers/KCPatientController.php:499
#: app/controllers/KCPatientController.php:592
#: app/controllers/KCPatientController.php:622
#: app/controllers/KCPatientController.php:669
#: app/controllers/KCPatientEncounterController.php:323
#: app/controllers/KCPatientEncounterController.php:385
#: app/controllers/KCPatientEncounterController.php:420
#: app/controllers/KCPatientEncounterController.php:463
#: app/controllers/KCPatientEncounterController.php:638
#: app/controllers/KCPatientMedicalHistoryController.php:162
#: app/controllers/KCPatientMedicalRecordsController.php:167
#: app/controllers/KCPatientMedicalRecordsController.php:222
#: app/controllers/KCPatientMedicalRecordsController.php:252
#: app/controllers/KCPatientPrescriptionController.php:171
#: app/controllers/KCPatientPrescriptionController.php:201
#: app/controllers/KCPatientPrescriptionController.php:229
#: app/controllers/KCReceptionistController.php:382
#: app/controllers/KCReceptionistController.php:470
#: app/controllers/KCReceptionistController.php:499
#: app/controllers/KCReceptionistController.php:524
#: app/controllers/KCServiceController.php:579
#: app/controllers/KCServiceController.php:676
#: app/controllers/KCServiceController.php:702
#: app/controllers/KCStaticDataController.php:181
#: app/controllers/KCStaticDataController.php:214
#: app/controllers/KCStaticDataController.php:242
#: app/controllers/KCStaticDataController.php:258
#: app/controllers/KCTaxController.php:119
#: app/controllers/KCTaxController.php:165
msgid "Data not found"
msgstr ""

#: app/controllers/KCAppointmentController.php:659
msgid "Appointment is deleted successfully"
msgstr ""

#: app/controllers/KCAppointmentController.php:766
msgid "Appointment status is updated successfully"
msgstr ""

#: app/controllers/KCAppointmentController.php:827
msgid "Appointment slots"
msgstr ""

#: app/controllers/KCAppointmentController.php:1166
#: app/controllers/KCAppointmentController.php:1239
#: resources/assets/lang/temp.php:943
#: utils/kc_helpers.php:6875
#: utils/kc_helpers.php:7019
#: utils/kc_helpers.php:7135
#: utils/kc_helpers.php:7223
#: utils/kc_helpers.php:7303
msgid "Appointments"
msgstr ""

#: app/controllers/KCAppointmentController.php:1222
msgid "No appointment found"
msgstr ""

#: app/controllers/KCAppointmentController.php:1272
msgid "Selected Appointment is deleted successfully"
msgstr ""

#: app/controllers/KCAppointmentController.php:1312
msgid "Failed to upload Medical report.File Type not supported"
msgstr ""

#: app/controllers/KCAppointmentController.php:1342
#: app/controllers/KCAppointmentController.php:1376
msgid "Failed to upload Medical report."
msgstr ""

#: app/controllers/KCAppointmentController.php:1353
#: app/controllers/KCBookAppointmentWidgetController.php:803
msgid "Uploaded files"
msgstr ""

#: app/controllers/KCAppointmentController.php:1376
msgid "Medical report uploaded successfully."
msgstr ""

#: app/controllers/KCAppointmentController.php:1411
msgid "Appointment Date."
msgstr ""

#: app/controllers/KCAppointmentSettingController.php:33
#: app/controllers/KCAppointmentSettingController.php:89
#: app/controllers/KCAppointmentSettingController.php:109
#: app/controllers/KCAppointmentSettingController.php:162
#: app/controllers/KCAppointmentSettingController.php:233
msgid "Failed to update"
msgstr ""

#: app/controllers/KCAppointmentSettingController.php:39
msgid "Pre or Post Book Days Must Be Greater than Zero "
msgstr ""

#: app/controllers/KCAppointmentSettingController.php:46
msgid "Appointment restrict days saved successfully"
msgstr ""

#: app/controllers/KCAppointmentSettingController.php:95
msgid "File Upload Setting Saved."
msgstr ""

#: app/controllers/KCAppointmentSettingController.php:118
msgid "Email Appointment Reminder Setting Saved"
msgstr ""

#: app/controllers/KCAppointmentSettingController.php:166
msgid "Appointment Time Format Saved"
msgstr ""

#: app/controllers/KCAppointmentSettingController.php:184
msgid "Appointment Description status changed successfully."
msgstr ""

#: app/controllers/KCAppointmentSettingController.php:210
msgid "Appointment Patient Info visibility status changed successfully."
msgstr ""

#: app/controllers/KCAppointmentSettingController.php:240
msgid "Appointment Cancellation Buffer Setting Saved"
msgstr ""

#: app/controllers/KCAuthController.php:61
#: app/controllers/KCAuthController.php:126
msgid "User not found user must be a patient."
msgstr ""

#: app/controllers/KCAuthController.php:193
#: app/controllers/KCAuthController.php:442
msgid "Invalid country code"
msgstr ""

#: app/controllers/KCAuthController.php:207
#: app/controllers/KCAuthController.php:478
msgid "Email already exists. Please use a different email."
msgstr ""

#: app/controllers/KCAuthController.php:297
msgid "Patient registration successful. Check your email for login credentials."
msgstr ""

#: app/controllers/KCAuthController.php:300
msgid "Patient registration failed. Please try again."
msgstr ""

#: app/controllers/KCAuthController.php:334
#: app/controllers/KCHomeController.php:51
msgid "Logout successful."
msgstr ""

#: app/controllers/KCAuthController.php:376
msgid "Login user is not kivicare user"
msgstr ""

#: app/controllers/KCAuthController.php:498
msgid "Clinic id is not proper. Please contact admin."
msgstr ""

#: app/controllers/KCAuthController.php:648
msgid "User registration successfully. Check your email for login credentials."
msgstr ""

#: app/controllers/KCAuthController.php:651
msgid "User registration not success."
msgstr ""

#: app/controllers/KCAuthController.php:695
msgid "User id not found"
msgstr ""

#: app/controllers/KCAuthController.php:730
msgid "User Verified Successfully"
msgstr ""

#: app/controllers/KCBookAppointmentWidgetController.php:124
#: app/controllers/KCBookAppointmentWidgetController.php:132
msgid "Doctor details"
msgstr ""

#: app/controllers/KCBookAppointmentWidgetController.php:158
msgid "Time slots"
msgstr ""

#: app/controllers/KCBookAppointmentWidgetController.php:177
msgid "Doctor is not available for this date"
msgstr ""

#: app/controllers/KCBookAppointmentWidgetController.php:194
msgid "Sign in to book appointment"
msgstr ""

#: app/controllers/KCBookAppointmentWidgetController.php:200
msgid "User must be patient to book appointment"
msgstr ""

#: app/controllers/KCBookAppointmentWidgetController.php:274
msgid "Appointment has been booked successfully"
msgstr ""

#: app/controllers/KCBookAppointmentWidgetController.php:277
msgid "Appointment booking failed."
msgstr ""

#: app/controllers/KCBookAppointmentWidgetController.php:521
msgid "No Doctor Available For This Clinic"
msgstr ""

#: app/controllers/KCBookAppointmentWidgetController.php:691
msgid "Clinic info"
msgstr ""

#: app/controllers/KCBookAppointmentWidgetController.php:708
msgid "Patient info"
msgstr ""

#: app/controllers/KCBookAppointmentWidgetController.php:713
#: resources/assets/lang/temp.php:208
#: resources/assets/lang/temp.php:697
#: resources/assets/lang/temp.php:752
#: resources/assets/lang/temp.php:789
#: resources/assets/lang/temp.php:813
#: resources/assets/lang/temp.php:832
#: resources/assets/lang/temp.php:902
#: resources/assets/lang/temp.php:1069
#: resources/assets/lang/temp.php:1165
#: utils/kc_helpers.php:6082
#: utils/kc_helpers.php:7527
msgid "Name"
msgstr ""

#: app/controllers/KCBookAppointmentWidgetController.php:721
#: resources/assets/lang/temp.php:1694
msgid "Number"
msgstr ""

#: app/controllers/KCBookAppointmentWidgetController.php:742
#: app/controllers/KCBookAppointmentWidgetController.php:1135
#: resources/assets/lang/temp.php:1690
msgid "Appointment summary"
msgstr ""

#: app/controllers/KCBookAppointmentWidgetController.php:749
#: app/controllers/KCBookAppointmentWidgetController.php:1142
msgid "Date "
msgstr ""

#: app/controllers/KCBookAppointmentWidgetController.php:753
#: app/controllers/KCBookAppointmentWidgetController.php:1146
msgid "Time "
msgstr ""

#: app/controllers/KCBookAppointmentWidgetController.php:757
#: app/controllers/KCBookAppointmentWidgetController.php:1150
#: resources/assets/lang/temp.php:193
#: resources/assets/lang/temp.php:1329
#: resources/assets/lang/temp.php:1333
#: utils/kc_helpers.php:5966
#: utils/kc_helpers.php:6933
#: utils/kc_helpers.php:7070
#: utils/kc_helpers.php:7179
#: utils/kc_helpers.php:7260
msgid "Services"
msgstr ""

#: app/controllers/KCBookAppointmentWidgetController.php:777
#: app/controllers/KCBookAppointmentWidgetController.php:1169
msgid "Taxes"
msgstr ""

#: app/controllers/KCBookAppointmentWidgetController.php:835
msgid "Other info"
msgstr ""

#: app/controllers/KCBookAppointmentWidgetController.php:841
#: resources/assets/lang/temp.php:944
#: resources/assets/lang/temp.php:1006
msgid "Description"
msgstr ""

#: app/controllers/KCBookAppointmentWidgetController.php:884
msgid "confirm page details"
msgstr ""

#: app/controllers/KCBookAppointmentWidgetController.php:986
#: app/controllers/KCPatientEncounterController.php:759
msgid " Days"
msgstr ""

#: app/controllers/KCBookAppointmentWidgetController.php:988
#: app/controllers/KCPatientEncounterController.php:761
msgid " Months"
msgstr ""

#: app/controllers/KCBookAppointmentWidgetController.php:990
#: app/controllers/KCPatientEncounterController.php:763
msgid " Years"
msgstr ""

#: app/controllers/KCBookAppointmentWidgetController.php:1106
msgid "Select Payment"
msgstr ""

#: app/controllers/KCBookAppointmentWidgetController.php:1248
msgid "Exp : "
msgstr ""

#: app/controllers/KCBookAppointmentWidgetController.php:1248
msgid "yr"
msgstr ""

#: app/controllers/KCBookAppointmentWidgetController.php:1325
#: resources/assets/lang/temp.php:1693
msgid "Total Price"
msgstr ""

#: app/controllers/KCClinicController.php:143
msgid "No clinics found"
msgstr ""

#: app/controllers/KCClinicController.php:150
msgid "Clinic list"
msgstr ""

#: app/controllers/KCClinicController.php:229
msgid " clinic "
msgstr ""

#: app/controllers/KCClinicController.php:229
msgid " clinic admin"
msgstr ""

#: app/controllers/KCClinicController.php:232
msgid "There already exists an clinic or clinic admin registered with this email address,please use other email address for "
msgstr ""

#: app/controllers/KCClinicController.php:300
msgid "Clinic id is required to update "
msgstr ""

#: app/controllers/KCClinicController.php:330
#: app/controllers/KCSetupController.php:83
msgid "Clinic saved successfully"
msgstr ""

#: app/controllers/KCClinicController.php:407
msgid "Clinic data"
msgstr ""

#: app/controllers/KCClinicController.php:449
#: app/controllers/KCHomeController.php:968
msgid "You can not delete the default clinic."
msgstr ""

#: app/controllers/KCClinicController.php:471
msgid "Clinic has been deleted successfully"
msgstr ""

#: app/controllers/KCClinicController.php:515
msgid "Kivicare Pro is not activated"
msgstr ""

#: app/controllers/KCClinicScheduleController.php:144
msgid "No holidays found"
msgstr ""

#: app/controllers/KCClinicScheduleController.php:151
msgid "Schedule list"
msgstr ""

#: app/controllers/KCClinicScheduleController.php:203
msgid "Doctor holiday scheduled successfully."
msgstr ""

#: app/controllers/KCClinicScheduleController.php:205
msgid "Clinic holiday scheduled successfully."
msgstr ""

#: app/controllers/KCClinicScheduleController.php:209
#: app/controllers/KCClinicScheduleController.php:228
msgid "Doctor already has holiday scheduled."
msgstr ""

#: app/controllers/KCClinicScheduleController.php:212
#: app/controllers/KCClinicScheduleController.php:231
msgid "Clinic already has holiday scheduled."
msgstr ""

#: app/controllers/KCClinicScheduleController.php:222
msgid "Doctor holiday schedule updated successfully."
msgstr ""

#: app/controllers/KCClinicScheduleController.php:224
msgid "Clinic holiday schedule updated successfully."
msgstr ""

#: app/controllers/KCClinicScheduleController.php:299
#: app/controllers/KCStaticDataController.php:210
msgid "Static data"
msgstr ""

#: app/controllers/KCClinicScheduleController.php:352
msgid "Clinic schedule deleted successfully"
msgstr ""

#: app/controllers/KCClinicScheduleController.php:388
#: app/controllers/KCHomeController.php:813
#: app/controllers/KCHomeController.php:852
msgid "Terms & Condition saved successfully"
msgstr ""

#: app/controllers/KCClinicSessionController.php:40
#: app/controllers/KCClinicSessionController.php:69
msgid "No clinic session list found"
msgstr ""

#: app/controllers/KCClinicSessionController.php:48
#: app/controllers/KCClinicSessionController.php:80
msgid "Clinic session list"
msgstr ""

#: app/controllers/KCClinicSessionController.php:161
#: resources/assets/lang/temp.php:1095
msgid "Selected Doctor is already added in another session"
msgstr ""

#: app/controllers/KCClinicSessionController.php:221
msgid "Failed to save clinic session. Please try again."
msgstr ""

#: app/controllers/KCClinicSessionController.php:229
#: resources/assets/lang/temp.php:1125
msgid "Doctor session saved successfully"
msgstr ""

#: app/controllers/KCClinicSessionController.php:258
msgid "Doctor session deleted successfully"
msgstr ""

#: app/controllers/KCCustomFieldController.php:94
msgid "No custom fields found"
msgstr ""

#: app/controllers/KCCustomFieldController.php:101
msgid "Custom fields records"
msgstr ""

#: app/controllers/KCCustomFieldController.php:151
msgid "Custom fields has been saved successfully"
msgstr ""

#: app/controllers/KCCustomFieldController.php:162
msgid "Custom fields has been updated successfully"
msgstr ""

#: app/controllers/KCCustomFieldController.php:230
#: resources/assets/lang/temp.php:244
#: resources/assets/lang/temp.php:842
#: resources/assets/lang/temp.php:1265
msgid "Active"
msgstr ""

#: app/controllers/KCCustomFieldController.php:230
#: resources/assets/lang/temp.php:245
msgid "Inactive"
msgstr ""

#: app/controllers/KCCustomFieldController.php:246
msgid "Custom field record"
msgstr ""

#: app/controllers/KCCustomFieldController.php:277
msgid "Custom field has been deleted successfully"
msgstr ""

#: app/controllers/KCCustomFieldController.php:282
msgid "Custom field delete failed"
msgstr ""

#: app/controllers/KCCustomFieldController.php:395
msgid "Custom fields"
msgstr ""

#: app/controllers/KCCustomFieldController.php:423
msgid "1MB"
msgstr ""

#: app/controllers/KCCustomFieldController.php:424
msgid "2MB"
msgstr ""

#: app/controllers/KCCustomFieldController.php:425
msgid "5MB"
msgstr ""

#: app/controllers/KCCustomFieldController.php:426
msgid "10MB"
msgstr ""

#: app/controllers/KCCustomFieldController.php:427
msgid "20MB"
msgstr ""

#: app/controllers/KCCustomFieldController.php:428
msgid "50MB"
msgstr ""

#: app/controllers/KCCustomFieldController.php:465
msgid "Current file upload size supported is limited to "
msgstr ""

#: app/controllers/KCCustomFieldController.php:465
#: utils/kc_helpers.php:855
msgid "MB"
msgstr ""

#: app/controllers/KCCustomFormController.php:51
msgid "Custom forms records"
msgstr ""

#: app/controllers/KCCustomFormController.php:70
#: app/controllers/KCCustomFormController.php:85
#: app/controllers/KCCustomFormController.php:100
#: app/controllers/KCCustomFormController.php:108
#: app/controllers/KCCustomFormController.php:136
#: app/controllers/KCPatientReportController.php:136
#: app/controllers/KCProSettingController.php:249
#: app/controllers/KCProSettingController.php:255
#: app/controllers/KCProSettingController.php:261
#: app/controllers/KCProSettingController.php:267
#: app/controllers/KCProSettingController.php:273
#: app/controllers/KCProSettingController.php:279
#: app/controllers/KCProSettingController.php:285
#: app/controllers/KCProSettingController.php:291
#: app/controllers/KCProSettingController.php:297
#: app/controllers/KCProSettingController.php:303
#: app/controllers/KCProSettingController.php:309
msgid "This API Is Only For KiviCare Pro"
msgstr ""

#: app/controllers/KCDashboardSidebarController.php:63
msgid "Dashboard sidebar data saved successfully"
msgstr ""

#: app/controllers/KCDashboardSidebarController.php:68
msgid "Data is not valid"
msgstr ""

#: app/controllers/KCDoctorController.php:367
#: app/controllers/KCSetupController.php:333
msgid "Doctor has been saved successfully"
msgstr ""

#: app/controllers/KCDoctorController.php:414
#: app/controllers/KCSetupController.php:358
msgid "Doctor updated successfully"
msgstr ""

#: app/controllers/KCDoctorController.php:482
#: app/controllers/KCSetupController.php:366
msgid "Failed to save Doctor data."
msgstr ""

#: app/controllers/KCDoctorController.php:737
msgid "Doctor has been deleted successfully"
msgstr ""

#: app/controllers/KCDoctorRatingController.php:43
msgid "Please use the latest Pro plugin"
msgstr ""

#: app/controllers/KCDoctorRatingController.php:50
#: app/controllers/KCDoctorRatingController.php:98
#: app/controllers/KCDoctorRatingController.php:125
#: app/controllers/KCPatientBillController.php:639
#: app/controllers/KCPatientBillController.php:665
#: app/controllers/KCPermissionController.php:47
#: app/controllers/KCPermissionController.php:70
#: app/controllers/KCProSettingController.php:341
msgid "Pro plugin not active"
msgstr ""

#: app/controllers/KCDoctorRatingController.php:90
#: app/controllers/KCDoctorRatingController.php:117
#: app/controllers/KCPatientBillController.php:632
#: app/controllers/KCPatientBillController.php:658
#: app/controllers/KCPermissionController.php:40
#: app/controllers/KCPermissionController.php:63
#: app/controllers/KCProSettingController.php:334
msgid "Please use latest Pro plugin"
msgstr ""

#: app/controllers/KCElementorController.php:34
#: app/controllers/KCElementorController.php:354
msgid "Setting data not found"
msgstr ""

#: app/controllers/KCElementorController.php:164
msgid "NA"
msgstr ""

#: app/controllers/KCElementorController.php:210
msgid "Morning : "
msgstr ""

#: app/controllers/KCElementorController.php:218
msgid "Evening : "
msgstr ""

#: app/controllers/KCElementorController.php:233
msgid "Morning : NA "
msgstr ""

#: app/controllers/KCElementorController.php:236
msgid "Evening : NA "
msgstr ""

#: app/controllers/KCElementorController.php:265
#: app/controllers/KCElementorController.php:530
#: resources/assets/lang/temp.php:1507
msgid "Previous"
msgstr ""

#: app/controllers/KCElementorController.php:369
msgid "No Clinic Found"
msgstr ""

#: app/controllers/KCGeneralSettingController.php:75
msgid "Google Recaptcha Setting Saved Successfully"
msgstr ""

#: app/controllers/KCGeneralSettingController.php:86
msgid "User Registration Form Setting Saved Successfully"
msgstr ""

#: app/controllers/KCGeneralSettingController.php:98
msgid "Fullcalendar Setting Saved"
msgstr ""

#: app/controllers/KCGeneralSettingController.php:110
msgid "Atleast One user role should by enable"
msgstr ""

#: app/controllers/KCGeneralSettingController.php:149
msgid "User Registration Shortcode Setting Saved"
msgstr ""

#: app/controllers/KCGeneralSettingController.php:178
msgid "Logout Redirect Setting Saved"
msgstr ""

#: app/controllers/KCGeneralSettingController.php:206
msgid "Login Redirect Setting Saved"
msgstr ""

#: app/controllers/KCGeneralSettingController.php:213
msgid "Failed To update Currency Setting"
msgstr ""

#: app/controllers/KCGeneralSettingController.php:224
msgid "Currency Setting Saved"
msgstr ""

#: app/controllers/KCGeneralSettingController.php:233
msgid "Failed To update Country Code Setting"
msgstr ""

#: app/controllers/KCGeneralSettingController.php:238
msgid "Country Code Setting Saved"
msgstr ""

#: app/controllers/KCGeneralSettingController.php:308
msgid "Encounter Setting Saved Successfully"
msgstr ""

#: app/controllers/KCGeneralSettingController.php:314
msgid "Failed To reset plugin data"
msgstr ""

#: app/controllers/KCGeneralSettingController.php:343
#: app/controllers/KCGeneralSettingController.php:368
#: app/controllers/KCGeneralSettingController.php:381
#: app/controllers/KCGeneralSettingController.php:403
#: app/controllers/KCGeneralSettingController.php:410
#: app/controllers/KCGeneralSettingController.php:486
msgid "Data deleted successfully"
msgstr ""

#: app/controllers/KCGeneralSettingController.php:515
#: app/controllers/KCHomeController.php:1235
msgid "This API Is Only For KiviCare API"
msgstr ""

#: app/controllers/KCHomeController.php:282
msgid "User data"
msgstr ""

#: app/controllers/KCHomeController.php:300
msgid "Password successfully changed"
msgstr ""

#: app/controllers/KCHomeController.php:304
msgid "Password change failed."
msgstr ""

#: app/controllers/KCHomeController.php:308
msgid "Current password is wrong!!"
msgstr ""

#: app/controllers/KCHomeController.php:374
msgid "doctor dashboard"
msgstr ""

#: app/controllers/KCHomeController.php:432
msgid "admin dashboard"
msgstr ""

#: app/controllers/KCHomeController.php:495
msgid "Monthly "
msgstr ""

#: app/controllers/KCHomeController.php:495
msgid "Weekly "
msgstr ""

#: app/controllers/KCHomeController.php:495
msgid " appointment"
msgstr ""

#: app/controllers/KCHomeController.php:550
msgid "Requested user not found"
msgstr ""

#: app/controllers/KCHomeController.php:645
msgid "Password Resend Successfully"
msgstr ""

#: app/controllers/KCHomeController.php:645
msgid "Password Resend Failed"
msgstr ""

#: app/controllers/KCHomeController.php:794
msgid "Status Changes Successfully"
msgstr ""

#: app/controllers/KCHomeController.php:836
msgid "country list"
msgstr ""

#: app/controllers/KCHomeController.php:914
msgid "Service status changed successfully"
msgstr ""

#: app/controllers/KCHomeController.php:926
msgid "service deleted successfully"
msgstr ""

#: app/controllers/KCHomeController.php:956
msgid "Clinic status changed successfully"
msgstr ""

#: app/controllers/KCHomeController.php:985
msgid "Clinic deleted successfully"
msgstr ""

#: app/controllers/KCHomeController.php:1015
msgid "Receptionists status changes successfully"
msgstr ""

#: app/controllers/KCHomeController.php:1024
msgid "Receptionists deleted successfully"
msgstr ""

#: app/controllers/KCHomeController.php:1056
msgid "Doctor status changed successfully"
msgstr ""

#: app/controllers/KCHomeController.php:1066
msgid "Doctor deleted successfully"
msgstr ""

#: app/controllers/KCHomeController.php:1098
msgid "Patient status changed successfully"
msgstr ""

#: app/controllers/KCHomeController.php:1107
#: app/controllers/KCPatientController.php:666
msgid "Patient deleted successfully"
msgstr ""

#: app/controllers/KCHomeController.php:1144
msgid "Patient encounter deleted successfully"
msgstr ""

#: app/controllers/KCHomeController.php:1161
msgid "Patient encounter template deleted successfully"
msgstr ""

#: app/controllers/KCHomeController.php:1183
msgid "Tax status changed successfully"
msgstr ""

#: app/controllers/KCHomeController.php:1190
msgid "Tax data not found"
msgstr ""

#: app/controllers/KCImportModuleDataController.php:98
#: app/controllers/KCPatientEncounterController.php:515
msgid "Encounter id not found"
msgstr ""

#: app/controllers/KCImportModuleDataController.php:114
msgid "Encounter id not valid"
msgstr ""

#: app/controllers/KCImportModuleDataController.php:122
msgid "Encounter already closed"
msgstr ""

#: app/controllers/KCLanguageController.php:71
msgid "Loco Translation setting saved successfully"
msgstr ""

#: app/controllers/KCLanguageController.php:76
msgid "Failed to update Loco Translation setting"
msgstr ""

#: app/controllers/KCLanguageController.php:87
#: app/controllers/KCLanguageController.php:98
msgid "Loco Translation setting data"
msgstr ""

#: app/controllers/KCModuleController.php:38
msgid "modules found."
msgstr ""

#: app/controllers/KCModuleController.php:42
msgid "modules not found."
msgstr ""

#: app/controllers/KCModuleController.php:183
msgid "Module configuration updated successfully"
msgstr ""

#: app/controllers/KCNotificationTemplateController.php:34
#: app/controllers/KCProSettingController.php:30
#: app/controllers/KCTaxController.php:34
msgid "Please update kivicare pro plugin"
msgstr ""

#: app/controllers/KCNotificationTemplateController.php:36
#: app/controllers/KCProSettingController.php:32
#: app/controllers/KCTaxController.php:36
msgid "Please install kivicare pro plugin"
msgstr ""

#: app/controllers/KCNotificationTemplateController.php:45
#: app/controllers/KCNotificationTemplateController.php:80
msgid "Patient Templates"
msgstr ""

#: app/controllers/KCNotificationTemplateController.php:46
#: app/controllers/KCNotificationTemplateController.php:81
msgid "Doctor Templates"
msgstr ""

#: app/controllers/KCNotificationTemplateController.php:47
#: app/controllers/KCNotificationTemplateController.php:82
msgid "Clinic Templates"
msgstr ""

#: app/controllers/KCNotificationTemplateController.php:48
#: app/controllers/KCNotificationTemplateController.php:83
msgid "Receptionist Templates"
msgstr ""

#: app/controllers/KCNotificationTemplateController.php:49
#: app/controllers/KCNotificationTemplateController.php:84
msgid "Common Templates"
msgstr ""

#: app/controllers/KCNotificationTemplateController.php:64
msgid "Email template saved successfully."
msgstr ""

#: app/controllers/KCNotificationTemplateController.php:101
msgid "Failed to send test "
msgstr ""

#: app/controllers/KCNotificationTemplateController.php:106
msgid "Failed to send test email. Please check your SMTP setup."
msgstr ""

#: app/controllers/KCNotificationTemplateController.php:109
msgid "Test email sent successfully."
msgstr ""

#: app/controllers/KCNotificationTemplateController.php:115
msgid "Failed to send test sms. Please check your Twillo sms setup."
msgstr ""

#: app/controllers/KCNotificationTemplateController.php:119
msgid "Test Sms sent successfully."
msgstr ""

#: app/controllers/KCNotificationTemplateController.php:127
msgid "Failed to send test sms. Please check your Twillo whatsapp setup."
msgstr ""

#: app/controllers/KCNotificationTemplateController.php:131
msgid "Test whatsapp sent successfully."
msgstr ""

#: app/controllers/KCPatientBillController.php:55
#: app/controllers/KCPatientEncounterController.php:486
#: app/controllers/KCPatientEncounterController.php:650
#: app/controllers/KCPatientMedicalHistoryController.php:47
#: app/controllers/KCPatientMedicalRecordsController.php:42
#: app/controllers/KCPatientPrescriptionController.php:42
msgid "Encounter not found"
msgstr ""

#: app/controllers/KCPatientBillController.php:69
msgid "No bill records found"
msgstr ""

#: app/controllers/KCPatientBillController.php:77
#: app/controllers/KCPatientMedicalRecordsController.php:84
#: app/controllers/KCPatientMedicalRecordsController.php:218
#: resources/assets/lang/temp.php:1193
msgid "Medical records"
msgstr ""

#: app/controllers/KCPatientBillController.php:116
#: app/controllers/KCPatientEncounterController.php:185
#: app/controllers/KCPatientMedicalHistoryController.php:117
#: app/controllers/KCPatientMedicalRecordsController.php:123
#: app/controllers/KCPatientPrescriptionController.php:115
msgid "No consultation found"
msgstr ""

#: app/controllers/KCPatientBillController.php:230
msgid "Bill generated successfully"
msgstr ""

#: app/controllers/KCPatientBillController.php:241
msgid "Encounter saved successfully"
msgstr ""

#: app/controllers/KCPatientBillController.php:434
msgid "Bill item"
msgstr ""

#: app/controllers/KCPatientBillController.php:441
msgid "Bill not found"
msgstr ""

#: app/controllers/KCPatientBillController.php:491
#: app/controllers/KCPatientBillController.php:539
msgid "Bill item deleted successfully"
msgstr ""

#: app/controllers/KCPatientBillController.php:494
#: app/controllers/KCPatientBillController.php:542
msgid "Failed to delete Bill item."
msgstr ""

#: app/controllers/KCPatientBillController.php:586
msgid "Payment status updated successfully"
msgstr ""

#: app/controllers/KCPatientBillController.php:589
msgid "Failed to update status"
msgstr ""

#: app/controllers/KCPatientBillController.php:614
msgid "link ready to access"
msgstr ""

#: app/controllers/KCPatientController.php:200
msgid "No patient found"
msgstr ""

#: app/controllers/KCPatientController.php:261
msgid "Patient list"
msgstr ""

#: app/controllers/KCPatientController.php:306
msgid "Patient Unique ID is required"
msgstr ""

#: app/controllers/KCPatientController.php:318
msgid "Patient Unique ID is already used"
msgstr ""

#: app/controllers/KCPatientController.php:407
msgid "Patient has been saved successfully"
msgstr ""

#: app/controllers/KCPatientController.php:438
msgid "Patient has been updated successfully"
msgstr ""

#: app/controllers/KCPatientController.php:466
msgid "Patient save operation has been failed"
msgstr ""

#: app/controllers/KCPatientController.php:706
msgid "Id Not Found"
msgstr ""

#: app/controllers/KCPatientController.php:745
#: app/controllers/KCPatientController.php:761
msgid "Patients Details"
msgstr ""

#: app/controllers/KCPatientDashboardWidget.php:32
msgid "User details"
msgstr ""

#: app/controllers/KCPatientEncounterController.php:53
msgid "Patient id not found"
msgstr ""

#: app/controllers/KCPatientEncounterController.php:204
msgid "Encounter list"
msgstr ""

#: app/controllers/KCPatientEncounterController.php:284
msgid "Patient encounter saved successfully"
msgstr ""

#: app/controllers/KCPatientEncounterController.php:294
msgid "Patient encounter has been updated successfully"
msgstr ""

#: app/controllers/KCPatientEncounterController.php:381
msgid "Encounter data"
msgstr ""

#: app/controllers/KCPatientEncounterController.php:434
msgid "Encounter has been deleted successfully"
msgstr ""

#: app/controllers/KCPatientEncounterController.php:437
msgid "Patient encounter delete failed"
msgstr ""

#: app/controllers/KCPatientEncounterController.php:479
#: resources/assets/lang/temp.php:1158
msgid "Encounter details"
msgstr ""

#: app/controllers/KCPatientEncounterController.php:531
#: app/controllers/KCPatientEncounterController.php:542
msgid "Encounter data has been saved"
msgstr ""

#: app/controllers/KCPatientEncounterController.php:664
msgid "Encounter status has been updated"
msgstr ""

#: app/controllers/KCPatientEncounterController.php:827
msgid "Invoice sent successfully"
msgstr ""

#: app/controllers/KCPatientEncounterController.php:827
msgid "Failed to send Invoice"
msgstr ""

#: app/controllers/KCPatientEncounterController.php:852
msgid "Encounter Id not found"
msgstr ""

#: app/controllers/KCPatientMedicalHistoryController.php:69
msgid "No medical history found"
msgstr ""

#: app/controllers/KCPatientMedicalHistoryController.php:80
msgid "Medical history"
msgstr ""

#: app/controllers/KCPatientMedicalHistoryController.php:144
msgid "Medical history saved successfully"
msgstr ""

#: app/controllers/KCPatientMedicalHistoryController.php:176
msgid "Problems deleted successfully"
msgstr ""

#: app/controllers/KCPatientMedicalHistoryController.php:178
msgid "Observations deleted successfully"
msgstr ""

#: app/controllers/KCPatientMedicalHistoryController.php:180
msgid "Notes deleted successfully"
msgstr ""

#: app/controllers/KCPatientMedicalHistoryController.php:189
msgid "Failed to delete Medical history."
msgstr ""

#: app/controllers/KCPatientMedicalRecordsController.php:77
msgid "No medical records found"
msgstr ""

#: app/controllers/KCPatientMedicalRecordsController.php:151
msgid "Medical record saved successfully"
msgstr ""

#: app/controllers/KCPatientMedicalRecordsController.php:264
msgid "Medical record deleted successfully"
msgstr ""

#: app/controllers/KCPatientMedicalRecordsController.php:267
msgid "Failed to delete Medical record"
msgstr ""

#: app/controllers/KCPatientPrescriptionController.php:70
#: resources/assets/lang/temp.php:1151
msgid "No prescription found"
msgstr ""

#: app/controllers/KCPatientPrescriptionController.php:78
msgid "Prescription records"
msgstr ""

#: app/controllers/KCPatientPrescriptionController.php:138
msgid "Prescription has been saved successfully"
msgstr ""

#: app/controllers/KCPatientPrescriptionController.php:143
msgid "Prescription has been updated successfully"
msgstr ""

#: app/controllers/KCPatientPrescriptionController.php:197
msgid "Prescription record"
msgstr ""

#: app/controllers/KCPatientPrescriptionController.php:245
msgid "Prescription has been deleted successfully"
msgstr ""

#: app/controllers/KCPatientPrescriptionController.php:248
msgid "Failed to delete Prescription"
msgstr ""

#: app/controllers/KCPatientPrescriptionController.php:270
#: app/controllers/KCPatientPrescriptionController.php:325
msgid "Failed to send email"
msgstr ""

#: app/controllers/KCPatientPrescriptionController.php:292
msgid "NAME"
msgstr ""

#: app/controllers/KCPatientPrescriptionController.php:293
msgid "FREQUENCY"
msgstr ""

#: app/controllers/KCPatientPrescriptionController.php:294
msgid "DAYS"
msgstr ""

#: app/controllers/KCPatientPrescriptionController.php:295
#: resources/assets/lang/temp.php:1149
#: utils/kc_helpers.php:6085
msgid "Instruction"
msgstr ""

#: app/controllers/KCPatientPrescriptionController.php:325
msgid "Prescription send to successfully."
msgstr ""

#: app/controllers/KCPatientReportController.php:93
#: app/controllers/KCPatientReportController.php:115
msgid "Failed to send report"
msgstr ""

#: app/controllers/KCPatientReportController.php:115
msgid "Report sent successfully"
msgstr ""

#: app/controllers/KCPatientReportController.php:127
msgid "you do not have permission to edit report"
msgstr ""

#: app/controllers/KCPatientUniqueIdController.php:45
msgid "Unique id setting saved successfully"
msgstr ""

#: app/controllers/KCPatientUniqueIdController.php:51
msgid "Failed to save Unique id settings."
msgstr ""

#: app/controllers/KCPaymentController.php:44
msgid "Woocommerce status can't change."
msgstr ""

#: app/controllers/KCPaymentController.php:53
#: app/controllers/KCPaymentController.php:107
#: app/controllers/KCPaymentController.php:212
msgid "Atleast One Payment Gateway should by enable"
msgstr ""

#: app/controllers/KCPaymentController.php:60
msgid "Woocommerce Plugin Is Not Active"
msgstr ""

#: app/controllers/KCPaymentController.php:69
#: app/controllers/KCPaymentController.php:75
#: app/controllers/KCPaymentController.php:82
msgid "Woocommerce change status."
msgstr ""

#: app/controllers/KCPaymentController.php:113
msgid "Paypal Setting Saved Successfully"
msgstr ""

#: app/controllers/KCPaymentController.php:119
msgid "Paypal Setting Update Failed"
msgstr ""

#: app/controllers/KCPaymentController.php:157
msgid "Failed to saved razorpay configurations"
msgstr ""

#: app/controllers/KCPaymentController.php:194
msgid "Failed to saved stripepay configurations"
msgstr ""

#: app/controllers/KCPaymentController.php:216
msgid "Local Payment Setting Saved Successfully"
msgstr ""

#: app/controllers/KCPaymentController.php:244
msgid "Woocommerce status."
msgstr ""

#: app/controllers/KCProSettingController.php:75
#: app/controllers/KCProSettingController.php:95
msgid "File no found"
msgstr ""

#: app/controllers/KCProSettingController.php:144
msgid "Filter Not Found"
msgstr ""

#: app/controllers/KCProSettingController.php:231
msgid "Setting Saved"
msgstr ""

#: app/controllers/KCProSettingController.php:242
msgid "CopyRight Text Saved Successfully"
msgstr ""

#: app/controllers/KCReceptionistController.php:118
#: app/controllers/KCSetupController.php:548
msgid "No receptionist found"
msgstr ""

#: app/controllers/KCReceptionistController.php:174
msgid "Receptionist list"
msgstr ""

#: app/controllers/KCReceptionistController.php:303
#: app/controllers/KCSetupController.php:742
msgid "Receptionist saved successfully"
msgstr ""

#: app/controllers/KCReceptionistController.php:334
msgid "Receptionist has been updated successfully"
msgstr ""

#: app/controllers/KCReceptionistController.php:349
#: app/controllers/KCSetupController.php:735
msgid " Failed to save Receptionist data"
msgstr ""

#: app/controllers/KCReceptionistController.php:521
msgid "Receptionist deleted successfully"
msgstr ""

#: app/controllers/KCReceptionistController.php:553
msgid "Email has been changed"
msgstr ""

#: app/controllers/KCReportController.php:71
msgid "Report Type."
msgstr ""

#: app/controllers/KCReportController.php:87
#: app/controllers/KCReportController.php:103
#: app/controllers/KCReportController.php:120
msgid "Clinic Revenue"
msgstr ""

#: app/controllers/KCReportController.php:136
msgid "doctor Appointments"
msgstr ""

#: app/controllers/KCReportController.php:151
msgid "Clinic Appointments"
msgstr ""

#: app/controllers/KCServiceController.php:306
#: app/controllers/KCStaticDataController.php:105
msgid "No services found"
msgstr ""

#: app/controllers/KCServiceController.php:313
#: app/controllers/KCStaticDataController.php:112
msgid "Service list"
msgstr ""

#: app/controllers/KCServiceController.php:428
#: app/controllers/KCServiceController.php:490
msgid "Same Service Already Exists,Please select Different category or service name"
msgstr ""

#: app/controllers/KCServiceController.php:467
msgid "Service saved successfully"
msgstr ""

#: app/controllers/KCServiceController.php:556
msgid "Service updated successfully"
msgstr ""

#: app/controllers/KCServiceController.php:648
msgid "yes"
msgstr ""

#: app/controllers/KCServiceController.php:671
msgid "Service data"
msgstr ""

#: app/controllers/KCServiceController.php:725
msgid "Service has been deleted successfully"
msgstr ""

#: app/controllers/KCServiceController.php:728
msgid "Failed to delete Service"
msgstr ""

#: app/controllers/KCServiceController.php:844
msgid "Base Price: "
msgstr ""

#: app/controllers/KCServiceController.php:857
msgid "Service Duration: "
msgstr ""

#: app/controllers/KCServiceController.php:859
msgid " min"
msgstr ""

#: app/controllers/KCSetupController.php:89
msgid "Clinic updated successfully"
msgstr ""

#: app/controllers/KCSetupController.php:206
msgid "Failed to save Clinic admin data"
msgstr ""

#: app/controllers/KCSetupController.php:214
msgid "Clinic Admin has been saved successfully"
msgstr ""

#: app/controllers/KCSetupController.php:439
msgid "Clinic session saved successfully"
msgstr ""

#: app/controllers/KCSetupController.php:604
msgid "Setup step found"
msgstr ""

#: app/controllers/KCSetupController.php:758
msgid "Clinic Setup steps is completed successfully."
msgstr ""

#: app/controllers/KCSetupController.php:764
msgid "Failed to complete Clinic setup."
msgstr ""

#: app/controllers/KCSetupController.php:787
msgid "Completed step."
msgstr ""

#: app/controllers/KCSetupController.php:855
msgid "Demo Service"
msgstr ""

#: app/controllers/KCStaticDataController.php:147
msgid "Listing data already exists."
msgstr ""

#: app/controllers/KCStaticDataController.php:156
msgid "Listing data saved successfully"
msgstr ""

#: app/controllers/KCStaticDataController.php:159
msgid "Listing data updated successfully"
msgstr ""

#: app/controllers/KCStaticDataController.php:255
msgid "Static data deleted successfully"
msgstr ""

#: app/controllers/KCTaxController.php:216
#: app/controllers/KCTaxController.php:276
msgid "required data missing"
msgstr ""

#: app/controllers/KCTaxController.php:269
msgid "encounter detail not found"
msgstr ""

#: app/controllers/KCWidgetSettingController.php:44
msgid "Widget Setting Saved Successfully"
msgstr ""

#: app/controllers/KCWidgetSettingController.php:49
msgid "Widget Setting Update Failed"
msgstr ""

#: app/controllers/KCWidgetSettingController.php:90
#: app/controllers/KCWidgetSettingController.php:97
msgid "Widget Setting data"
msgstr ""

#: app/controllers/KCWidgetSettingController.php:115
msgid "Widget logo updated"
msgstr ""

#: app/controllers/KCWidgetSettingController.php:121
msgid "Failed to update Widget logo"
msgstr ""

#: app/controllers/KCZoomTelemedController.php:86
msgid "Telemed key successfully saved."
msgstr ""

#: app/controllers/KCZoomTelemedController.php:91
msgid "Failed to save Telemed key, please check API key and API Secret"
msgstr ""

#: app/controllers/KCZoomTelemedController.php:116
msgid "Configuration data"
msgstr ""

#: app/controllers/KCZoomTelemedController.php:143
msgid "Video Conference Link Send"
msgstr ""

#: app/controllers/KCZoomTelemedController.php:149
msgid "Video Conference Link not Send"
msgstr ""

#: resources/assets/lang/temp.php:8
#: resources/assets/lang/temp.php:10
msgid "Choose Language"
msgstr ""

#: resources/assets/lang/temp.php:9
msgid "Form setting"
msgstr ""

#: resources/assets/lang/temp.php:12
msgid "Encounter body chart"
msgstr ""

#: resources/assets/lang/temp.php:13
msgid "Theme mode"
msgstr ""

#: resources/assets/lang/temp.php:14
msgid "Menu bar position"
msgstr ""

#: resources/assets/lang/temp.php:15
msgid "Menu items"
msgstr ""

#: resources/assets/lang/temp.php:16
msgid "Select menu items"
msgstr ""

#: resources/assets/lang/temp.php:17
msgid "Image template"
msgstr ""

#: resources/assets/lang/temp.php:18
msgid "Image"
msgstr ""

#: resources/assets/lang/temp.php:19
msgid "Default"
msgstr ""

#: resources/assets/lang/temp.php:20
msgid "Add new image"
msgstr ""

#: resources/assets/lang/temp.php:21
msgid "Replace"
msgstr ""

#: resources/assets/lang/temp.php:22
msgid "New"
msgstr ""

#: resources/assets/lang/temp.php:23
msgid "File"
msgstr ""

#: resources/assets/lang/temp.php:24
msgid "Body chart"
msgstr ""

#: resources/assets/lang/temp.php:25
msgid "Clone"
msgstr ""

#: resources/assets/lang/temp.php:26
msgid "Image Handling Preference (when image save after editing)"
msgstr ""

#: resources/assets/lang/temp.php:27
msgid "Press yes to delete body chart details."
msgstr ""

#: resources/assets/lang/temp.php:28
msgid "Press yes to delete image details"
msgstr ""

#: resources/assets/lang/temp.php:29
msgid "Create New image"
msgstr ""

#: resources/assets/lang/temp.php:30
msgid "Replace orginal image"
msgstr ""

#: resources/assets/lang/temp.php:31
msgid "The selected image is already set as the template. Please choose a different image."
msgstr ""

#: resources/assets/lang/temp.php:32
msgid "Note: The Stripe payment currency must be the same as the service price currency"
msgstr ""

#: resources/assets/lang/temp.php:33
msgid "Stripe payment"
msgstr ""

#: resources/assets/lang/temp.php:34
msgid "Publishable key"
msgstr ""

#: resources/assets/lang/temp.php:35
msgid "API secret key"
msgstr ""

#: resources/assets/lang/temp.php:36
msgid "Enter publishable key"
msgstr ""

#: resources/assets/lang/temp.php:37
msgid "Enter API secret key"
msgstr ""

#: resources/assets/lang/temp.php:38
msgid "Stripe Publishable key is required"
msgstr ""

#: resources/assets/lang/temp.php:39
msgid "Stripe currency is required"
msgstr ""

#: resources/assets/lang/temp.php:40
msgid "Tax Rate"
msgstr ""

#: resources/assets/lang/temp.php:41
msgid "Tax Value"
msgstr ""

#: resources/assets/lang/temp.php:42
msgid "Add Tax"
msgstr ""

#: resources/assets/lang/temp.php:43
msgid "New Tax"
msgstr ""

#: resources/assets/lang/temp.php:44
msgid "Note: To apply tax to all doctors, please leave the selection blank."
msgstr ""

#: resources/assets/lang/temp.php:45
msgid "Note: To apply tax to all services, please leave the selection blank."
msgstr ""

#: resources/assets/lang/temp.php:46
msgid "Note: To apply tax to all clinics, please leave the selection blank."
msgstr ""

#: resources/assets/lang/temp.php:47
msgid "Tax rate must be greater than zero"
msgstr ""

#: resources/assets/lang/temp.php:48
msgid "Custom notification"
msgstr ""

#: resources/assets/lang/temp.php:49
msgid "Custom Form"
msgstr ""

#: resources/assets/lang/temp.php:50
msgid "No Form Field Available"
msgstr ""

#: resources/assets/lang/temp.php:51
#: resources/assets/lang/temp.php:530
msgid "Please fill all required fields"
msgstr ""

#: resources/assets/lang/temp.php:52
msgid "Custom form list"
msgstr ""

#: resources/assets/lang/temp.php:53
msgid "Add form"
msgstr ""

#: resources/assets/lang/temp.php:54
msgid "Search by name,module type and id"
msgstr ""

#: resources/assets/lang/temp.php:55
msgid "Form title"
msgstr ""

#: resources/assets/lang/temp.php:56
msgid "Form title color"
msgstr ""

#: resources/assets/lang/temp.php:57
msgid "Form title alignment"
msgstr ""

#: resources/assets/lang/temp.php:58
msgid "Add field classes"
msgstr ""

#: resources/assets/lang/temp.php:59
msgid "Class"
msgstr ""

#: resources/assets/lang/temp.php:60
msgid "Heading title"
msgstr ""

#: resources/assets/lang/temp.php:61
msgid "Heading tag"
msgstr ""

#: resources/assets/lang/temp.php:62
msgid "Module type"
msgstr ""

#: resources/assets/lang/temp.php:63
msgid "Form icon"
msgstr ""

#: resources/assets/lang/temp.php:64
msgid "Enter font icon class"
msgstr ""

#: resources/assets/lang/temp.php:65
msgid "Show if Appointment status"
msgstr ""

#: resources/assets/lang/temp.php:66
msgid "Show in"
msgstr ""

#: resources/assets/lang/temp.php:67
msgid "Note: If encounter is selected, form will only be displayed if there has been an appointment encounter"
msgstr ""

#: resources/assets/lang/temp.php:68
msgid "Note: To show form to all clinics, please leave the selection blank."
msgstr ""

#: resources/assets/lang/temp.php:69
msgid "Note: To show form to all roles, please leave the selection blank."
msgstr ""

#: resources/assets/lang/temp.php:70
msgid "Please add form fields"
msgstr ""

#: resources/assets/lang/temp.php:71
msgid "Please select form status"
msgstr ""

#: resources/assets/lang/temp.php:72
msgid "Please select module type"
msgstr ""

#: resources/assets/lang/temp.php:73
msgid "Please enter field label"
msgstr ""

#: resources/assets/lang/temp.php:74
msgid "Please select field type"
msgstr ""

#: resources/assets/lang/temp.php:75
msgid "Please select file types"
msgstr ""

#: resources/assets/lang/temp.php:76
msgid "Please enter options"
msgstr ""

#: resources/assets/lang/temp.php:77
msgid "GET"
msgstr ""

#: resources/assets/lang/temp.php:78
msgid "POST"
msgstr ""

#: resources/assets/lang/temp.php:79
msgid "Headers"
msgstr ""

#: resources/assets/lang/temp.php:80
msgid "Enter key"
msgstr ""

#: resources/assets/lang/temp.php:81
msgid "Enter value"
msgstr ""

#: resources/assets/lang/temp.php:82
msgid "Value"
msgstr ""

#: resources/assets/lang/temp.php:83
msgid "Enabling Twilio WhatsApp will disable custom notification WhatsApp"
msgstr ""

#: resources/assets/lang/temp.php:84
msgid "Enabling Twilio SMS will disable custom notification SMS"
msgstr ""

#: resources/assets/lang/temp.php:85
msgid "Enabling custom notification WhatsApp will disable Twilio WhatsApp"
msgstr ""

#: resources/assets/lang/temp.php:86
msgid "Enabling custom notification SMS will disable Twillo SMS"
msgstr ""

#: resources/assets/lang/temp.php:87
msgid "Enable SMS"
msgstr ""

#: resources/assets/lang/temp.php:88
msgid "Enable WhatsApp"
msgstr ""

#: resources/assets/lang/temp.php:89
msgid "Add header"
msgstr ""

#: resources/assets/lang/temp.php:90
msgid "Add query parameter"
msgstr ""

#: resources/assets/lang/temp.php:91
msgid "Add dynamic key"
msgstr ""

#: resources/assets/lang/temp.php:92
msgid "Query Parameters"
msgstr ""

#: resources/assets/lang/temp.php:93
msgid "Send Request"
msgstr ""

#: resources/assets/lang/temp.php:94
msgid "Save custom notification"
msgstr ""

#: resources/assets/lang/temp.php:95
msgid "Enter name"
msgstr ""

#: resources/assets/lang/temp.php:96
msgid "Notification Type"
msgstr ""

#: resources/assets/lang/temp.php:97
msgid "SMS"
msgstr ""

#: resources/assets/lang/temp.php:98
msgid "Whatsapp"
msgstr ""

#: resources/assets/lang/temp.php:99
msgid "Status Code"
msgstr ""

#: resources/assets/lang/temp.php:100
msgid "Form"
msgstr ""

#: resources/assets/lang/temp.php:101
msgid "Response"
msgstr ""

#: resources/assets/lang/temp.php:102
msgid "Dynamic keys"
msgstr ""

#: resources/assets/lang/temp.php:103
msgid "Collections"
msgstr ""

#: resources/assets/lang/temp.php:104
#: utils/kc_helpers.php:6023
msgid "Tax"
msgstr ""

#: resources/assets/lang/temp.php:105
msgid "Tax Name"
msgstr ""

#: resources/assets/lang/temp.php:106
msgid "Tax List"
msgstr ""

#: resources/assets/lang/temp.php:107
msgid "Press yes to delete tax"
msgstr ""

#: resources/assets/lang/temp.php:108
msgid "No tax found"
msgstr ""

#: resources/assets/lang/temp.php:109
msgid "Search by name,status...."
msgstr ""

#: resources/assets/lang/temp.php:110
msgid "No Appointments Found"
msgstr ""

#: resources/assets/lang/temp.php:112
msgid "Cancel"
msgstr ""

#: resources/assets/lang/temp.php:113
msgid "Clear"
msgstr ""

#: resources/assets/lang/temp.php:114
msgid "Undo"
msgstr ""

#: resources/assets/lang/temp.php:115
msgid "Encounter ID"
msgstr ""

#: resources/assets/lang/temp.php:116
msgid "Total Rows"
msgstr ""

#: resources/assets/lang/temp.php:117
msgid "Rows selected"
msgstr ""

#: resources/assets/lang/temp.php:118
msgid "Apply"
msgstr ""

#: resources/assets/lang/temp.php:119
msgid "Total Rows Inserted"
msgstr ""

#: resources/assets/lang/temp.php:120
msgid "Import More File"
msgstr ""

#: resources/assets/lang/temp.php:121
msgid "Appointment Module"
msgstr ""

#: resources/assets/lang/temp.php:122
#: resources/assets/lang/temp.php:1156
msgid "Encounter Module"
msgstr ""

#: resources/assets/lang/temp.php:123
msgid "Patient Encounter Module"
msgstr ""

#: resources/assets/lang/temp.php:124
msgid "Encounter Template Module"
msgstr ""

#: resources/assets/lang/temp.php:125
msgid "Select allowed file type"
msgstr ""

#: resources/assets/lang/temp.php:126
msgid "To accept all type of file select all option from dropdown option"
msgstr ""

#: resources/assets/lang/temp.php:127
msgid "Clinical Detail Module"
msgstr ""

#: resources/assets/lang/temp.php:128
#: resources/assets/lang/temp.php:1216
msgid "Prescription Module"
msgstr ""

#: resources/assets/lang/temp.php:129
msgid "Clinic Module"
msgstr ""

#: resources/assets/lang/temp.php:130
msgid "Patient Module"
msgstr ""

#: resources/assets/lang/temp.php:131
msgid "Doctor Module"
msgstr ""

#: resources/assets/lang/temp.php:132
msgid "Receptionist Module"
msgstr ""

#: resources/assets/lang/temp.php:133
msgid "Service Module"
msgstr ""

#: resources/assets/lang/temp.php:134
msgid "Session Module"
msgstr ""

#: resources/assets/lang/temp.php:135
msgid "Billing Module"
msgstr ""

#: resources/assets/lang/temp.php:136
msgid "Holiday Module"
msgstr ""

#: resources/assets/lang/temp.php:137
msgid "Dashboard Module"
msgstr ""

#: resources/assets/lang/temp.php:138
msgid "Custom field Module"
msgstr ""

#: resources/assets/lang/temp.php:139
msgid "Static/Listing data Module"
msgstr ""

#: resources/assets/lang/temp.php:140
msgid "Other Module"
msgstr ""

#: resources/assets/lang/temp.php:141
msgid "Patient Report Module"
msgstr ""

#: resources/assets/lang/temp.php:142
msgid "Import"
msgstr ""

#: resources/assets/lang/temp.php:143
msgid "File type"
msgstr ""

#: resources/assets/lang/temp.php:144
msgid "Upload File"
msgstr ""

#: resources/assets/lang/temp.php:145
msgid "Clinic admin"
msgstr ""

#: resources/assets/lang/temp.php:146
msgid "Removed clinics will delete service and session data of this doctor and removed clinics."
msgstr ""

#: resources/assets/lang/temp.php:147
#: utils/kc_helpers.php:5281
#: utils/kc_helpers.php:6409
msgid "Razorpay"
msgstr ""

#: resources/assets/lang/temp.php:148
msgid "Note: The Razorpay currency must be the same as the service price currency"
msgstr ""

#: resources/assets/lang/temp.php:149
msgid "Send Notification when user register"
msgstr ""

#: resources/assets/lang/temp.php:150
msgid "Import data"
msgstr ""

#: resources/assets/lang/temp.php:151
msgid "Signature"
msgstr ""

#: resources/assets/lang/temp.php:152
msgid "Registration Shortcode Setting"
msgstr ""

#: resources/assets/lang/temp.php:153
msgid "Default status when doctor register"
msgstr ""

#: resources/assets/lang/temp.php:154
msgid "Default status when receptionist register"
msgstr ""

#: resources/assets/lang/temp.php:155
msgid "Default status when patient register"
msgstr ""

#: resources/assets/lang/temp.php:156
msgid "Please close the Encounter to checkout patient"
msgstr ""

#: resources/assets/lang/temp.php:157
msgid "Clinic Appointment Count"
msgstr ""

#: resources/assets/lang/temp.php:158
msgid "Doctor Appointment Count"
msgstr ""

#: resources/assets/lang/temp.php:159
msgid "SMS/Whatsapp"
msgstr ""

#: resources/assets/lang/temp.php:160
msgid "Note : If notification is enable, demo import will take time."
msgstr ""

#: resources/assets/lang/temp.php:161
msgid "Add Doctor Sessions"
msgstr ""

#: resources/assets/lang/temp.php:162
msgid "Detail"
msgstr ""

#: resources/assets/lang/temp.php:163
msgid "Please Share Your Experience"
msgstr ""

#: resources/assets/lang/temp.php:164
msgid "Not Verified"
msgstr ""

#: resources/assets/lang/temp.php:165
msgid "Verify"
msgstr ""

#: resources/assets/lang/temp.php:166
msgid "Verified"
msgstr ""

#: resources/assets/lang/temp.php:167
msgid "Create Selected Demo User"
msgstr ""

#: resources/assets/lang/temp.php:168
#: resources/assets/lang/temp.php:996
#: resources/assets/lang/temp.php:1000
#: resources/assets/lang/temp.php:1173
msgid "Date"
msgstr ""

#: resources/assets/lang/temp.php:169
msgid "Save Changes"
msgstr ""

#: resources/assets/lang/temp.php:170
msgid "Close"
msgstr ""

#: resources/assets/lang/temp.php:171
msgid "Closed"
msgstr ""

#: resources/assets/lang/temp.php:172
msgid "Test"
msgstr ""

#: resources/assets/lang/temp.php:173
msgid "-- Select option --"
msgstr ""

#: resources/assets/lang/temp.php:174
#: resources/assets/lang/temp.php:787
#: resources/assets/lang/temp.php:841
#: resources/assets/lang/temp.php:1264
msgid "All"
msgstr ""

#: resources/assets/lang/temp.php:175
msgid "Back To WordPress"
msgstr ""

#: resources/assets/lang/temp.php:176
msgid "Update"
msgstr ""

#: resources/assets/lang/temp.php:177
msgid "My Profile"
msgstr ""

#: resources/assets/lang/temp.php:178
#: resources/assets/lang/temp.php:1736
#: resources/assets/lang/temp.php:1744
msgid "Change Password"
msgstr ""

#: resources/assets/lang/temp.php:180
msgid "Choose file"
msgstr ""

#: resources/assets/lang/temp.php:181
msgid "No file Chosen"
msgstr ""

#: resources/assets/lang/temp.php:182
msgid "Full Screen"
msgstr ""

#: resources/assets/lang/temp.php:183
msgid "Please save your Zoom configuration"
msgstr ""

#: resources/assets/lang/temp.php:184
msgid "Zoom configuration link"
msgstr ""

#: resources/assets/lang/temp.php:185
msgid "DOB"
msgstr ""

#: resources/assets/lang/temp.php:186
msgid "Date of birth is required"
msgstr ""

#: resources/assets/lang/temp.php:188
msgid "Gender is required"
msgstr ""

#: resources/assets/lang/temp.php:192
#: resources/assets/lang/temp.php:1665
msgid "Service"
msgstr ""

#: resources/assets/lang/temp.php:194
#: resources/assets/lang/temp.php:370
#: resources/assets/lang/temp.php:1270
msgid "Add Service"
msgstr ""

#: resources/assets/lang/temp.php:195
msgid "Sr no"
msgstr ""

#: resources/assets/lang/temp.php:196
msgid "Item name"
msgstr ""

#: resources/assets/lang/temp.php:197
#: resources/assets/lang/temp.php:1330
msgid "Price"
msgstr ""

#: resources/assets/lang/temp.php:198
#: resources/assets/lang/temp.php:1331
msgid "Quantity"
msgstr ""

#: resources/assets/lang/temp.php:199
#: utils/kc_helpers.php:6015
#: utils/kc_helpers.php:6034
msgid "Total"
msgstr ""

#: resources/assets/lang/temp.php:200
#: utils/kc_helpers.php:5685
msgid "No records found"
msgstr ""

#: resources/assets/lang/temp.php:201
msgid "No record Selected"
msgstr ""

#: resources/assets/lang/temp.php:202
msgid "Note"
msgstr ""

#: resources/assets/lang/temp.php:203
msgid "Type and press enter to create new service"
msgstr ""

#: resources/assets/lang/temp.php:204
#: resources/assets/lang/temp.php:836
#: resources/assets/lang/temp.php:903
#: resources/assets/lang/temp.php:998
#: resources/assets/lang/temp.php:1004
#: resources/assets/lang/temp.php:1259
#: resources/assets/lang/temp.php:1335
#: resources/assets/lang/temp.php:1347
#: resources/assets/lang/temp.php:1576
msgid "Status"
msgstr ""

#: resources/assets/lang/temp.php:205
msgid "Change Status"
msgstr ""

#: resources/assets/lang/temp.php:206
#: resources/assets/lang/temp.php:706
#: resources/assets/lang/temp.php:764
#: resources/assets/lang/temp.php:791
#: resources/assets/lang/temp.php:837
#: resources/assets/lang/temp.php:913
#: resources/assets/lang/temp.php:1007
#: resources/assets/lang/temp.php:1075
#: resources/assets/lang/temp.php:1119
#: resources/assets/lang/temp.php:1175
#: resources/assets/lang/temp.php:1260
#: resources/assets/lang/temp.php:1342
#: resources/assets/lang/temp.php:1478
#: resources/assets/lang/temp.php:1578
#: utils/kc_helpers.php:7542
msgid "Action"
msgstr ""

#: resources/assets/lang/temp.php:207
#: resources/assets/lang/temp.php:1348
msgid "Title"
msgstr ""

#: resources/assets/lang/temp.php:211
#: resources/assets/lang/temp.php:1465
#: resources/assets/lang/temp.php:1648
#: utils/kc_helpers.php:6919
#: utils/kc_helpers.php:7056
#: utils/kc_helpers.php:7172
msgid "Doctors"
msgstr ""

#: resources/assets/lang/temp.php:214
msgid "Choose Image"
msgstr ""

#: resources/assets/lang/temp.php:216
msgid "Required."
msgstr ""

#: resources/assets/lang/temp.php:217
msgid "Invalid."
msgstr ""

#: resources/assets/lang/temp.php:218
msgid "First name is required."
msgstr ""

#: resources/assets/lang/temp.php:221
msgid "Last name is required."
msgstr ""

#: resources/assets/lang/temp.php:224
msgid "Email is required."
msgstr ""

#: resources/assets/lang/temp.php:226
msgid "Password is required."
msgstr ""

#: resources/assets/lang/temp.php:227
#: resources/assets/lang/temp.php:804
msgid "Repeat Password"
msgstr ""

#: resources/assets/lang/temp.php:228
msgid "Repeat Password is required."
msgstr ""

#: resources/assets/lang/temp.php:229
msgid "New password and confirm password does not match."
msgstr ""

#: resources/assets/lang/temp.php:231
msgid "Sign Up"
msgstr ""

#: resources/assets/lang/temp.php:232
msgid "No"
msgstr ""

#: resources/assets/lang/temp.php:233
#: resources/assets/lang/temp.php:1649
#: utils/kc_helpers.php:5459
msgid "Dr."
msgstr ""

#: resources/assets/lang/temp.php:234
msgid "Filters"
msgstr ""

#: resources/assets/lang/temp.php:235
msgid "Apply filters"
msgstr ""

#: resources/assets/lang/temp.php:236
msgid "Close filter"
msgstr ""

#: resources/assets/lang/temp.php:237
#: utils/kc_helpers.php:6523
msgid "Back"
msgstr ""

#: resources/assets/lang/temp.php:238
#: resources/assets/lang/temp.php:1317
msgid "Add Encounter Template"
msgstr ""

#: resources/assets/lang/temp.php:239
#: resources/assets/lang/temp.php:1185
msgid "Encounter Template"
msgstr ""

#: resources/assets/lang/temp.php:240
msgid "Patient Encounter"
msgstr ""

#: resources/assets/lang/temp.php:242
#: resources/assets/lang/temp.php:678
#: resources/assets/lang/temp.php:766
#: resources/assets/lang/temp.php:814
#: resources/assets/lang/temp.php:899
#: resources/assets/lang/temp.php:980
#: resources/assets/lang/temp.php:1160
#: resources/assets/lang/temp.php:1246
#: resources/assets/lang/temp.php:1499
msgid "Save"
msgstr ""

#: resources/assets/lang/temp.php:243
msgid "Invalid email format"
msgstr ""

#: resources/assets/lang/temp.php:246
msgid "Name is required"
msgstr ""

#: resources/assets/lang/temp.php:247
#: resources/assets/lang/temp.php:1585
msgid "Date is required"
msgstr ""

#: resources/assets/lang/temp.php:248
msgid "Email address"
msgstr ""

#: resources/assets/lang/temp.php:249
msgid "Contact information"
msgstr ""

#: resources/assets/lang/temp.php:250
#: utils/kc_helpers.php:6968
#: utils/kc_helpers.php:7105
#: utils/kc_helpers.php:7193
#: utils/kc_helpers.php:7274
msgid "Settings"
msgstr ""

#: resources/assets/lang/temp.php:251
msgid "Enable/Disable Modules"
msgstr ""

#: resources/assets/lang/temp.php:252
msgid "First name allows only alphabetic value (spaces are not allowed)"
msgstr ""

#: resources/assets/lang/temp.php:253
msgid "First name length should be between 2 to 15 character"
msgstr ""

#: resources/assets/lang/temp.php:254
msgid "Last name allows only alphabetic value (spaces are not allowed)"
msgstr ""

#: resources/assets/lang/temp.php:255
msgid "Last name length should be between 2 to 15 character"
msgstr ""

#: resources/assets/lang/temp.php:258
msgid "Contact is required."
msgstr ""

#: resources/assets/lang/temp.php:259
msgid "Contact number length should be between 4 to 15 digits"
msgstr ""

#: resources/assets/lang/temp.php:260
msgid "Invalid contact number format"
msgstr ""

#: resources/assets/lang/temp.php:261
msgid "Telemed"
msgstr ""

#: resources/assets/lang/temp.php:262
msgid "To"
msgstr ""

#: resources/assets/lang/temp.php:263
#: resources/assets/lang/temp.php:1001
msgid "Time"
msgstr ""

#: resources/assets/lang/temp.php:264
msgid "Contact no"
msgstr ""

#: resources/assets/lang/temp.php:265
msgid "Contact number is required"
msgstr ""

#: resources/assets/lang/temp.php:266
msgid "City"
msgstr ""

#: resources/assets/lang/temp.php:267
msgid "City is required"
msgstr ""

#: resources/assets/lang/temp.php:268
msgid "City name allows only alphabetic value"
msgstr ""

#: resources/assets/lang/temp.php:269
msgid "City maximum length should be 30 character"
msgstr ""

#: resources/assets/lang/temp.php:270
msgid "State"
msgstr ""

#: resources/assets/lang/temp.php:271
msgid "State name allows only alphabetic value"
msgstr ""

#: resources/assets/lang/temp.php:272
msgid "State maximum length should be 30 character"
msgstr ""

#: resources/assets/lang/temp.php:273
msgid "Country"
msgstr ""

#: resources/assets/lang/temp.php:274
msgid "Country is required"
msgstr ""

#: resources/assets/lang/temp.php:275
msgid "Country name allows only alphabetic value"
msgstr ""

#: resources/assets/lang/temp.php:276
msgid "Country maximum length should be 30 character"
msgstr ""

#: resources/assets/lang/temp.php:277
msgid "Address"
msgstr ""

#: resources/assets/lang/temp.php:278
msgid "Address is required"
msgstr ""

#: resources/assets/lang/temp.php:279
msgid "Postal code"
msgstr ""

#: resources/assets/lang/temp.php:280
msgid "Postal code is required"
msgstr ""

#: resources/assets/lang/temp.php:281
msgid "Invalid postal code format"
msgstr ""

#: resources/assets/lang/temp.php:282
msgid "Postal code should be maximum 12 digits"
msgstr ""

#: resources/assets/lang/temp.php:283
#: resources/assets/lang/temp.php:1743
msgid "Profile"
msgstr ""

#: resources/assets/lang/temp.php:284
msgid "Static Data"
msgstr ""

#: resources/assets/lang/temp.php:285
msgid "Handle Request"
msgstr ""

#: resources/assets/lang/temp.php:286
msgid "Other than this many more fine-tunings and tweaks are done. Please <NAME_EMAIL> if you face any issues with the update."
msgstr ""

#: resources/assets/lang/temp.php:287
msgid "Type option name and press enter to add new option"
msgstr ""

#: resources/assets/lang/temp.php:288
msgid "1) If you face any issue then try deactivating and activating the plugin or contact us."
msgstr ""

#: resources/assets/lang/temp.php:289
msgid "2) If you want to revert old version. Please install"
msgstr ""

#: resources/assets/lang/temp.php:290
msgid "wp-rollback"
msgstr ""

#: resources/assets/lang/temp.php:291
msgid "plugin."
msgstr ""

#: resources/assets/lang/temp.php:292
msgid "We will keep improving with your support! Thank You!"
msgstr ""

#: resources/assets/lang/temp.php:293
msgid "Currency Setting"
msgstr ""

#: resources/assets/lang/temp.php:294
msgid "I Understand"
msgstr ""

#: resources/assets/lang/temp.php:295
msgid "Important! Major Version update!! (V2.0.0)"
msgstr ""

#: resources/assets/lang/temp.php:296
msgid "Microsoft"
msgstr ""

#: resources/assets/lang/temp.php:297
msgid "Google"
msgstr ""

#: resources/assets/lang/temp.php:298
msgid "Outlook"
msgstr ""

#: resources/assets/lang/temp.php:299
msgid "Yahoo"
msgstr ""

#: resources/assets/lang/temp.php:300
msgid "Please read this below log before moving forward"
msgstr ""

#: resources/assets/lang/temp.php:301
msgid "Faced issues ?"
msgstr ""

#: resources/assets/lang/temp.php:302
msgid "If you face problems with this version and you want to continue with old version then please install and use"
msgstr ""

#: resources/assets/lang/temp.php:303
msgid "For smooth migration to new version check following Video guide"
msgstr ""

#: resources/assets/lang/temp.php:304
msgid "Kivicare Upgrade V2.0.0"
msgstr ""

#: resources/assets/lang/temp.php:305
msgid "Appointment Flow"
msgstr ""

#: resources/assets/lang/temp.php:306
msgid "Basic details"
msgstr ""

#: resources/assets/lang/temp.php:307
#: resources/assets/lang/temp.php:1009
#: resources/assets/lang/temp.php:1761
msgid "Close form"
msgstr ""

#: resources/assets/lang/temp.php:308
#: resources/assets/lang/temp.php:1497
msgid "Add"
msgstr ""

#: resources/assets/lang/temp.php:309
#: resources/assets/lang/temp.php:1081
msgid "Edit"
msgstr ""

#: resources/assets/lang/temp.php:310
msgid "url"
msgstr ""

#: resources/assets/lang/temp.php:311
msgid "Icon"
msgstr ""

#: resources/assets/lang/temp.php:312
msgid "Clinic Admin Email"
msgstr ""

#: resources/assets/lang/temp.php:313
msgid "Health Question"
msgstr ""

#: resources/assets/lang/temp.php:314
msgid "Patient Report Template"
msgstr ""

#: resources/assets/lang/temp.php:315
msgid "Disabled Patient Report Template"
msgstr ""

#: resources/assets/lang/temp.php:316
msgid "Patient Invoice Template"
msgstr ""

#: resources/assets/lang/temp.php:317
msgid "Disabled Patient Invoice Template"
msgstr ""

#: resources/assets/lang/temp.php:318
msgid "User Verified Acknowledgement notification Template"
msgstr ""

#: resources/assets/lang/temp.php:319
msgid "Disabled User Verified Template"
msgstr ""

#: resources/assets/lang/temp.php:320
msgid "New Admin User Registration Notification"
msgstr ""

#: resources/assets/lang/temp.php:321
msgid "Disabled Admin New User Register"
msgstr ""

#: resources/assets/lang/temp.php:322
msgid "Patient Prescription Notification Template"
msgstr ""

#: resources/assets/lang/temp.php:323
msgid "Disabled Patient Prescription Template"
msgstr ""

#: resources/assets/lang/temp.php:324
msgid "Patient  Appointment Reminder Notification Template"
msgstr ""

#: resources/assets/lang/temp.php:325
msgid "Disabled  Patient  Appointment ReminderTemplate"
msgstr ""

#: resources/assets/lang/temp.php:326
msgid "Patient  Appointment Reminder Notification Template for Doctor"
msgstr ""

#: resources/assets/lang/temp.php:327
msgid "Disabled  Patient  Appointment ReminderTemplate for Doctor"
msgstr ""

#: resources/assets/lang/temp.php:328
msgid "New Appointment Notification to Clinic"
msgstr ""

#: resources/assets/lang/temp.php:329
msgid "Disabled  Clinic Booked Appointment Template"
msgstr ""

#: resources/assets/lang/temp.php:330
msgid "New Appointment SMS Template"
msgstr ""

#: resources/assets/lang/temp.php:331
msgid "Disabled - Appointment Add SMS Template"
msgstr ""

#: resources/assets/lang/temp.php:332
msgid "Encounter Closed SMS Notify to User"
msgstr ""

#: resources/assets/lang/temp.php:333
msgid "Disabled - Encounter Close Add SMS Template"
msgstr ""

#: resources/assets/lang/temp.php:334
msgid "Kivicare Receptionist Registration"
msgstr ""

#: resources/assets/lang/temp.php:335
msgid "Disabled Kivicare Receptionist Register"
msgstr ""

#: resources/assets/lang/temp.php:336
msgid "Kivicare Doctor Registration"
msgstr ""

#: resources/assets/lang/temp.php:337
msgid "Disabled Kivicare Doctor Registration"
msgstr ""

#: resources/assets/lang/temp.php:338
msgid "Kivicare Book Appointment"
msgstr ""

#: resources/assets/lang/temp.php:339
msgid "Disabled Kivicare Book Appointment"
msgstr ""

#: resources/assets/lang/temp.php:340
msgid "Resend user credentials"
msgstr ""

#: resources/assets/lang/temp.php:341
msgid "Disabled Resend user credentials"
msgstr ""

#: resources/assets/lang/temp.php:342
msgid "Allow Cancel appointments"
msgstr ""

#: resources/assets/lang/temp.php:343
msgid "Disabled Cancel appointment"
msgstr ""

#: resources/assets/lang/temp.php:344
msgid "Registration Notification to Patient template"
msgstr ""

#: resources/assets/lang/temp.php:345
msgid "Disabled Patient Registration Template"
msgstr ""

#: resources/assets/lang/temp.php:346
msgid "Video conference appointment booking notification template"
msgstr ""

#: resources/assets/lang/temp.php:347
msgid "Disabled Video Conference appointment Template"
msgstr ""

#: resources/assets/lang/temp.php:348
msgid "New Appointment Notification to Doctor Template"
msgstr ""

#: resources/assets/lang/temp.php:349
msgid "Disabled Doctor Booked Appointment Template"
msgstr ""

#: resources/assets/lang/temp.php:350
msgid "New Zoom video appointment notification to doctor template"
msgstr ""

#: resources/assets/lang/temp.php:351
msgid "Doctor Zoom Video Conference appointment Template"
msgstr ""

#: resources/assets/lang/temp.php:352
msgid "New Google Meet video appointment notification to doctor template"
msgstr ""

#: resources/assets/lang/temp.php:353
#: resources/assets/lang/temp.php:355
msgid "Disabled Google Meet Video Conference appointment Template"
msgstr ""

#: resources/assets/lang/temp.php:354
msgid "New Google Meet video appointment email to patient template"
msgstr ""

#: resources/assets/lang/temp.php:356
msgid "Clinic Admin registration notification to user"
msgstr ""

#: resources/assets/lang/temp.php:357
msgid "Disabled Clinic Admin Registration"
msgstr ""

#: resources/assets/lang/temp.php:358
msgid "Payment pending notification to user template"
msgstr ""

#: resources/assets/lang/temp.php:359
msgid "Disabled Payment Pending Template"
msgstr ""

#: resources/assets/lang/temp.php:360
msgid "Patient clinic Check In notify template"
msgstr ""

#: resources/assets/lang/temp.php:361
msgid "Disabled Patient Clinic Check In  Template"
msgstr ""

#: resources/assets/lang/temp.php:362
#: resources/assets/lang/temp.php:1782
msgid "Google Event Template"
msgstr ""

#: resources/assets/lang/temp.php:363
#: resources/assets/lang/temp.php:989
msgid "Upcoming"
msgstr ""

#: resources/assets/lang/temp.php:364
msgid "Completed(check-out)"
msgstr ""

#: resources/assets/lang/temp.php:365
#: resources/assets/lang/temp.php:946
#: utils/kc_helpers.php:6252
msgid "Cancelled"
msgstr ""

#: resources/assets/lang/temp.php:366
msgid "Accepted"
msgstr ""

#: resources/assets/lang/temp.php:367
msgid "This action may delete your doctor's appointments, sessions, and holidays."
msgstr ""

#: resources/assets/lang/temp.php:368
msgid "This action may delete patient's appointment, history, and encounters."
msgstr ""

#: resources/assets/lang/temp.php:369
msgid "Actual Amount"
msgstr ""

#: resources/assets/lang/temp.php:371
msgid "Add Static Data"
msgstr ""

#: resources/assets/lang/temp.php:372
msgid "Add to Calendar"
msgstr ""

#: resources/assets/lang/temp.php:373
msgid "Add To Cart"
msgstr ""

#: resources/assets/lang/temp.php:374
msgid "Address info"
msgstr ""

#: resources/assets/lang/temp.php:375
msgid "Booking successful"
msgstr ""

#: resources/assets/lang/temp.php:376
msgid "All the appointment on selected date will be cancelled."
msgstr ""

#: resources/assets/lang/temp.php:377
#: resources/assets/lang/temp.php:699
#: resources/assets/lang/temp.php:754
#: resources/assets/lang/temp.php:851
#: resources/assets/lang/temp.php:905
#: resources/assets/lang/temp.php:1085
#: resources/assets/lang/temp.php:1654
#: utils/kc_helpers.php:6905
#: utils/kc_helpers.php:7331
msgid "Clinic"
msgstr ""

#: resources/assets/lang/temp.php:378
msgid "green"
msgstr ""

#: resources/assets/lang/temp.php:379
msgid "red"
msgstr ""

#: resources/assets/lang/temp.php:380
msgid "Confirm booking"
msgstr ""

#: resources/assets/lang/temp.php:381
msgid "You are connected with the google calender."
msgstr ""

#: resources/assets/lang/temp.php:382
msgid "You are connected with the google meet."
msgstr ""

#: resources/assets/lang/temp.php:383
msgid "Create Add"
msgstr ""

#: resources/assets/lang/temp.php:384
#: resources/assets/lang/temp.php:1200
#: resources/assets/lang/temp.php:1344
msgid "DATE"
msgstr ""

#: resources/assets/lang/temp.php:385
msgid "Deleting service may affects your existing appointments data."
msgstr ""

#: resources/assets/lang/temp.php:386
msgid "desc"
msgstr ""

#: resources/assets/lang/temp.php:387
msgid "Disconnect"
msgstr ""

#: resources/assets/lang/temp.php:388
msgid "Disconnected"
msgstr ""

#: resources/assets/lang/temp.php:389
msgid "Edit bill item"
msgstr ""

#: resources/assets/lang/temp.php:390
msgid "Edit Clinic"
msgstr ""

#: resources/assets/lang/temp.php:391
msgid "Edit encounter"
msgstr ""

#: resources/assets/lang/temp.php:392
msgid "Edit receptionist"
msgstr ""

#: resources/assets/lang/temp.php:393
msgid "Edit service"
msgstr ""

#: resources/assets/lang/temp.php:394
msgid "Edit Static Data"
msgstr ""

#: resources/assets/lang/temp.php:395
msgid "English"
msgstr ""

#: resources/assets/lang/temp.php:396
msgid "Please give permission to your language folder"
msgstr ""

#: resources/assets/lang/temp.php:397
msgid "Google Calendar"
msgstr ""

#: resources/assets/lang/temp.php:398
msgid "Google Calendar Client ID"
msgstr ""

#: resources/assets/lang/temp.php:399
msgid "Google Calendar Client Secret"
msgstr ""

#: resources/assets/lang/temp.php:400
msgid "Google Calendar Configuration"
msgstr ""

#: resources/assets/lang/temp.php:401
msgid "App Name"
msgstr ""

#: resources/assets/lang/temp.php:402
msgid "App Name Required"
msgstr ""

#: resources/assets/lang/temp.php:403
msgid "Google Calendar Integration"
msgstr ""

#: resources/assets/lang/temp.php:404
msgid "Guide to setup google calender."
msgstr ""

#: resources/assets/lang/temp.php:405
msgid "Important! Note "
msgstr ""

#: resources/assets/lang/temp.php:406
msgid "in resources."
msgstr ""

#: resources/assets/lang/temp.php:408
msgid "Key"
msgstr ""

#: resources/assets/lang/temp.php:409
msgid "KiviCare_lang folder"
msgstr ""

#: resources/assets/lang/temp.php:410
msgid "lang folder"
msgstr ""

#: resources/assets/lang/temp.php:411
msgid "in media uploads folder and"
msgstr ""

#: resources/assets/lang/temp.php:412
msgid "Monthly"
msgstr ""

#: resources/assets/lang/temp.php:413
msgid "Yearly"
msgstr ""

#: resources/assets/lang/temp.php:414
msgid "No Data Found"
msgstr ""

#: resources/assets/lang/temp.php:415
msgid "NOTES"
msgstr ""

#: resources/assets/lang/temp.php:417
msgid "Please connect with your Google Account to get appointments in Google Calendar automatically."
msgstr ""

#: resources/assets/lang/temp.php:418
msgid "Please connect your Google Account for Google Meet."
msgstr ""

#: resources/assets/lang/temp.php:419
msgid "Note: Please enable Google Meet from admin panel."
msgstr ""

#: resources/assets/lang/temp.php:420
msgid "Press yes to delete bill item"
msgstr ""

#: resources/assets/lang/temp.php:421
msgid "Press yes to delete static data"
msgstr ""

#: resources/assets/lang/temp.php:422
#: resources/assets/lang/temp.php:1487
msgid "Press yes to delete"
msgstr ""

#: resources/assets/lang/temp.php:423
msgid "Press yes to delete appointment"
msgstr ""

#: resources/assets/lang/temp.php:424
msgid "Press yes to cancel appointment"
msgstr ""

#: resources/assets/lang/temp.php:425
msgid "Press yes to delete clinic"
msgstr ""

#: resources/assets/lang/temp.php:426
msgid "Press yes to delete Report"
msgstr ""

#: resources/assets/lang/temp.php:427
msgid "Press yes to delete clinic session"
msgstr ""

#: resources/assets/lang/temp.php:428
msgid "Press yes to delete Field"
msgstr ""

#: resources/assets/lang/temp.php:429
msgid "Press yes to delete prescription"
msgstr ""

#: resources/assets/lang/temp.php:430
#: resources/assets/lang/temp.php:846
msgid "Press yes to delete receptionist"
msgstr ""

#: resources/assets/lang/temp.php:431
msgid "Receptionist List"
msgstr ""

#: resources/assets/lang/temp.php:432
msgid "Remove from Calender"
msgstr ""

#: resources/assets/lang/temp.php:433
msgid "Following action will reset your current appointment session slot."
msgstr ""

#: resources/assets/lang/temp.php:434
msgid "Save and Close Encounter"
msgstr ""

#: resources/assets/lang/temp.php:435
msgid "Save item"
msgstr ""

#: resources/assets/lang/temp.php:436
msgid "Save profile"
msgstr ""

#: resources/assets/lang/temp.php:437
msgid "Server Error"
msgstr ""

#: resources/assets/lang/temp.php:438
msgid "Setting for add event in calendar for patient."
msgstr ""

#: resources/assets/lang/temp.php:439
#: resources/assets/lang/temp.php:1199
#: resources/assets/lang/temp.php:1206
msgid "START DATE"
msgstr ""

#: resources/assets/lang/temp.php:440
msgid "Static Data List"
msgstr ""

#: resources/assets/lang/temp.php:441
#: resources/assets/lang/temp.php:1559
#: resources/assets/lang/temp.php:1584
msgid "Terms and Condition"
msgstr ""

#: resources/assets/lang/temp.php:442
#: resources/assets/lang/temp.php:1349
msgid "Total AMOUNT"
msgstr ""

#: resources/assets/lang/temp.php:443
msgid "Total Appointment"
msgstr ""

#: resources/assets/lang/temp.php:444
msgid "Press yes to update appointment status"
msgstr ""

#: resources/assets/lang/temp.php:445
msgid "Press yes to update payment status"
msgstr ""

#: resources/assets/lang/temp.php:446
msgid "Video Consultation"
msgstr ""

#: resources/assets/lang/temp.php:447
msgid "Weekly"
msgstr ""

#: resources/assets/lang/temp.php:448
msgid "Yes"
msgstr ""

#: resources/assets/lang/temp.php:449
msgid "CheckIn"
msgstr ""

#: resources/assets/lang/temp.php:450
msgid "Loco Translate"
msgstr ""

#: resources/assets/lang/temp.php:451
msgid "View"
msgstr ""

#: resources/assets/lang/temp.php:452
msgid "Selected Doctor or Clinic is not Available"
msgstr ""

#: resources/assets/lang/temp.php:453
#: utils/kc_helpers.php:6989
msgid "Request Features"
msgstr ""

#: resources/assets/lang/temp.php:454
msgid "Hide all utility links : Request features | Get Support | Documentation."
msgstr ""

#: resources/assets/lang/temp.php:455
msgid "Remove all utility links permanently : Request features | Get Support | Documentation."
msgstr ""

#: resources/assets/lang/temp.php:456
msgid "Remove Utility Links"
msgstr ""

#: resources/assets/lang/temp.php:457
msgid "Age"
msgstr ""

#: resources/assets/lang/temp.php:458
msgid "Clinic Admin Information"
msgstr ""

#: resources/assets/lang/temp.php:459
msgid "Clinic currency prefix and postfix "
msgstr ""

#: resources/assets/lang/temp.php:460
msgid "Enable Wordpress Logo"
msgstr ""

#: resources/assets/lang/temp.php:462
msgid "Google Recaptcha V3"
msgstr ""

#: resources/assets/lang/temp.php:463
msgid "Enable Google Recaptcha V3"
msgstr ""

#: resources/assets/lang/temp.php:464
msgid "Google Recaptcha Site Key"
msgstr ""

#: resources/assets/lang/temp.php:465
msgid "Google Recaptcha Secret Key"
msgstr ""

#: resources/assets/lang/temp.php:466
msgid "Please Click Here To Create New Site and a Secret Key"
msgstr ""

#: resources/assets/lang/temp.php:467
msgid "Fullcalendar Setting"
msgstr ""

#: resources/assets/lang/temp.php:468
msgid "License Key"
msgstr ""

#: resources/assets/lang/temp.php:469
msgid "Logout Redirect To"
msgstr ""

#: resources/assets/lang/temp.php:470
msgid "Allow multi selection while booking?"
msgstr ""

#: resources/assets/lang/temp.php:471
msgid "Payment Transaction Failed. Please Try Again"
msgstr ""

#: resources/assets/lang/temp.php:472
msgid "Appointment successfully booked, Please check your email for Confirmation Mail"
msgstr ""

#: resources/assets/lang/temp.php:473
msgid "More"
msgstr ""

#: resources/assets/lang/temp.php:474
msgid "Telemed Meeting Link"
msgstr ""

#: resources/assets/lang/temp.php:475
msgid "Qrcode"
msgstr ""

#: resources/assets/lang/temp.php:476
msgid "Copy"
msgstr ""

#: resources/assets/lang/temp.php:477
msgid "Remove"
msgstr ""

#: resources/assets/lang/temp.php:478
msgid "Send email"
msgstr ""

#: resources/assets/lang/temp.php:479
msgid "Refresh list"
msgstr ""

#: resources/assets/lang/temp.php:480
msgid "Shortcodes"
msgstr ""

#: resources/assets/lang/temp.php:481
msgid "Update payment status to paid"
msgstr ""

#: resources/assets/lang/temp.php:482
msgid "Please Click Here To Know more about date Format"
msgstr ""

#: resources/assets/lang/temp.php:483
msgid "Date Format Setting"
msgstr ""

#: resources/assets/lang/temp.php:484
msgid "Enter Custom Date Format "
msgstr ""

#: resources/assets/lang/temp.php:485
msgid "Export PDF"
msgstr ""

#: resources/assets/lang/temp.php:486
msgid "Select Doctor or Clinic is not Available"
msgstr ""

#: resources/assets/lang/temp.php:487
msgid "Test & Save"
msgstr ""

#: resources/assets/lang/temp.php:488
msgid "Search patient by ID,name, email and status(0 or 1)"
msgstr ""

#: resources/assets/lang/temp.php:489
msgid "Search doctor by ID,name, email and status(0 or 1)"
msgstr ""

#: resources/assets/lang/temp.php:490
msgid "Search receptionist by ID,name, email and status(0 or 1)"
msgstr ""

#: resources/assets/lang/temp.php:491
msgid "Search listing-data by name, type and status(0 or 1)"
msgstr ""

#: resources/assets/lang/temp.php:492
msgid "Delete All Data Of Kivicare Plugin"
msgstr ""

#: resources/assets/lang/temp.php:493
msgid "Search custom field data by id,field,type and status(0 or 1)"
msgstr ""

#: resources/assets/lang/temp.php:494
msgid "Search holiday data by id,schedule of,name"
msgstr ""

#: resources/assets/lang/temp.php:495
msgid "Search services data by id, doctor, name, category, price  and status(0 or 1)"
msgstr ""

#: resources/assets/lang/temp.php:496
msgid "Search bills data by id,encounter_id, clinic, doctor, patient, total amount, discount ,amount due  and status(0 or 1)"
msgstr ""

#: resources/assets/lang/temp.php:497
msgid "Search encounter data by id, doctor, clinic, patient, date  and status(0 or 1)"
msgstr ""

#: resources/assets/lang/temp.php:498
msgid "Search encounter Template data by id, name date and status(0 or 1)"
msgstr ""

#: resources/assets/lang/temp.php:499
msgid "Search clinic data by id, name, email, admin-email, speciality,address and status(0 or 1)"
msgstr ""

#: resources/assets/lang/temp.php:500
msgid "ID"
msgstr ""

#: resources/assets/lang/temp.php:501
msgid "Reset Plugin Data"
msgstr ""

#: resources/assets/lang/temp.php:502
msgid "Reset Appointments And Encounters"
msgstr ""

#: resources/assets/lang/temp.php:503
msgid "Reset Doctors"
msgstr ""

#: resources/assets/lang/temp.php:504
msgid "Reset Receptionists"
msgstr ""

#: resources/assets/lang/temp.php:505
msgid "Reset Patients"
msgstr ""

#: resources/assets/lang/temp.php:506
msgid "Reset Total Revenue"
msgstr ""

#: resources/assets/lang/temp.php:507
msgid "Reset Clinic"
msgstr ""

#: resources/assets/lang/temp.php:508
msgid "Delete All Data And Reset Plugin"
msgstr ""

#: resources/assets/lang/temp.php:509
msgid "This Will Delete All Data"
msgstr ""

#: resources/assets/lang/temp.php:510
msgid "This Will Also delete appointments and encounter related with the selected user type. Are you sure?"
msgstr ""

#: resources/assets/lang/temp.php:511
#: resources/assets/lang/temp.php:1374
msgid "Custom Field"
msgstr ""

#: resources/assets/lang/temp.php:512
msgid " Module"
msgstr ""

#: resources/assets/lang/temp.php:513
msgid " Label"
msgstr ""

#: resources/assets/lang/temp.php:514
msgid " Input Type"
msgstr ""

#: resources/assets/lang/temp.php:515
msgid " Options"
msgstr ""

#: resources/assets/lang/temp.php:516
msgid "Get Pro Now"
msgstr ""

#: resources/assets/lang/temp.php:517
#: resources/assets/lang/temp.php:1157
#: utils/kc_helpers.php:5738
msgid "Prescription"
msgstr ""

#: resources/assets/lang/temp.php:518
msgid "name"
msgstr ""

#: resources/assets/lang/temp.php:519
msgid "frequency"
msgstr ""

#: resources/assets/lang/temp.php:520
msgid "duration"
msgstr ""

#: resources/assets/lang/temp.php:521
msgid "Dashboard sidebar setting"
msgstr ""

#: resources/assets/lang/temp.php:522
msgid "Sidebar Setting"
msgstr ""

#: resources/assets/lang/temp.php:523
msgid "Country Code"
msgstr ""

#: resources/assets/lang/temp.php:524
msgid "Clinic Admin Country Code"
msgstr ""

#: resources/assets/lang/temp.php:525
msgid "Country Calling Code"
msgstr ""

#: resources/assets/lang/temp.php:526
msgid "Clinic Admin Country Calling Code"
msgstr ""

#: resources/assets/lang/temp.php:527
msgid "Default Country Code For Contact"
msgstr ""

#: resources/assets/lang/temp.php:528
msgid "Select Country Code"
msgstr ""

#: resources/assets/lang/temp.php:529
msgid "Required Fullcalendar License Key"
msgstr ""

#: resources/assets/lang/temp.php:531
msgid "Press yes to resend credential"
msgstr ""

#: resources/assets/lang/temp.php:532
msgid "Press yes to change status"
msgstr ""

#: resources/assets/lang/temp.php:533
msgid "Save and Close Checkout"
msgstr ""

#: resources/assets/lang/temp.php:534
msgid "User Registration Form Setting"
msgstr ""

#: resources/assets/lang/temp.php:535
msgid "Show \"Other\" option in gender"
msgstr ""

#: resources/assets/lang/temp.php:536
msgid "DISMISS"
msgstr ""

#: resources/assets/lang/temp.php:537
msgid "Encounter Setting"
msgstr ""

#: resources/assets/lang/temp.php:538
msgid "Allow Encounter Edit After Close"
msgstr ""

#: resources/assets/lang/temp.php:539
msgid "(Deprecated)"
msgstr ""

#: resources/assets/lang/temp.php:540
msgid "Please make sure your server has Email Server SMTP setup !"
msgstr ""

#: resources/assets/lang/temp.php:541
msgid "Click here to download sample file"
msgstr ""

#: resources/assets/lang/temp.php:542
msgid "Following field is required in"
msgstr ""

#: resources/assets/lang/temp.php:543
msgid "file"
msgstr ""

#: resources/assets/lang/temp.php:544
msgid "Documentation"
msgstr ""

#: resources/assets/lang/temp.php:545
#: utils/kc_helpers.php:6975
msgid "Get help"
msgstr ""

#: resources/assets/lang/temp.php:546
msgid "Get support"
msgstr ""

#: resources/assets/lang/temp.php:547
msgid "Telemed (Pro)"
msgstr ""

#: resources/assets/lang/temp.php:548
msgid "Woocommerce (Pro)"
msgstr ""

#: resources/assets/lang/temp.php:549
msgid "Google Calendar (Pro)"
msgstr ""

#: resources/assets/lang/temp.php:550
msgid "SMS (Pro)"
msgstr ""

#: resources/assets/lang/temp.php:551
msgid "Holiday"
msgstr ""

#: resources/assets/lang/temp.php:552
msgid "date(date should be less than current date)"
msgstr ""

#: resources/assets/lang/temp.php:553
msgid "Allowed User Role"
msgstr ""

#: resources/assets/lang/temp.php:554
msgid "Patient Role"
msgstr ""

#: resources/assets/lang/temp.php:555
msgid "Doctor Role"
msgstr ""

#: resources/assets/lang/temp.php:556
#: resources/assets/lang/temp.php:558
msgid "Receptionist Role"
msgstr ""

#: resources/assets/lang/temp.php:557
msgid "copied!"
msgstr ""

#: resources/assets/lang/temp.php:561
msgid "Paypal Settings"
msgstr ""

#: resources/assets/lang/temp.php:562
msgid "Paypal Account Settings"
msgstr ""

#: resources/assets/lang/temp.php:563
msgid "Paypal Status"
msgstr ""

#: resources/assets/lang/temp.php:564
msgid "Paypal Configration"
msgstr ""

#: resources/assets/lang/temp.php:565
msgid "Client ID"
msgstr ""

#: resources/assets/lang/temp.php:566
msgid "Client Secret"
msgstr ""

#: resources/assets/lang/temp.php:567
msgid "Mode"
msgstr ""

#: resources/assets/lang/temp.php:568
msgid "Enter Your Client Id"
msgstr ""

#: resources/assets/lang/temp.php:569
msgid "Enter Your Client Secret"
msgstr ""

#: resources/assets/lang/temp.php:570
msgid "Sandbox"
msgstr ""

#: resources/assets/lang/temp.php:571
msgid "Live"
msgstr ""

#: resources/assets/lang/temp.php:572
msgid "Payment mode is required"
msgstr ""

#: resources/assets/lang/temp.php:573
msgid "Client id is required"
msgstr ""

#: resources/assets/lang/temp.php:574
msgid "Client secret is required"
msgstr ""

#: resources/assets/lang/temp.php:575
msgid "Currency"
msgstr ""

#: resources/assets/lang/temp.php:576
msgid "Currency symbol"
msgstr ""

#: resources/assets/lang/temp.php:577
msgid "Currency format"
msgstr ""

#: resources/assets/lang/temp.php:578
msgid "Currency is required"
msgstr ""

#: resources/assets/lang/temp.php:579
msgid "Price number format"
msgstr ""

#: resources/assets/lang/temp.php:580
msgid "Price number of decimals"
msgstr ""

#: resources/assets/lang/temp.php:581
msgid "Note: The PayPal currency must be the same as the service price currency"
msgstr ""

#: resources/assets/lang/temp.php:584
#: utils/kc_helpers.php:6868
#: utils/kc_helpers.php:7012
#: utils/kc_helpers.php:7128
#: utils/kc_helpers.php:7216
#: utils/kc_helpers.php:7296
msgid "Dashboard"
msgstr ""

#: resources/assets/lang/temp.php:585
msgid "Total Patients"
msgstr ""

#: resources/assets/lang/temp.php:586
msgid "Total visited patients"
msgstr ""

#: resources/assets/lang/temp.php:587
msgid "Total Doctors"
msgstr ""

#: resources/assets/lang/temp.php:588
msgid "Total clinic doctors"
msgstr ""

#: resources/assets/lang/temp.php:589
msgid "Total Appointments"
msgstr ""

#: resources/assets/lang/temp.php:590
msgid "Total clinic appointments"
msgstr ""

#: resources/assets/lang/temp.php:591
#: resources/assets/lang/temp.php:1737
msgid "Latest appointments"
msgstr ""

#: resources/assets/lang/temp.php:592
msgid "Reload"
msgstr ""

#: resources/assets/lang/temp.php:593
msgid "View All"
msgstr ""

#: resources/assets/lang/temp.php:594
msgid "Weekly appointments"
msgstr ""

#: resources/assets/lang/temp.php:595
msgid "Weekly total appointments"
msgstr ""

#: resources/assets/lang/temp.php:596
msgid "Monthly appointments"
msgstr ""

#: resources/assets/lang/temp.php:597
msgid "Monthly total appointments"
msgstr ""

#: resources/assets/lang/temp.php:598
msgid "todays appointment list"
msgstr ""

#: resources/assets/lang/temp.php:599
msgid "All upcoming appointments"
msgstr ""

#: resources/assets/lang/temp.php:600
msgid "Total revenue"
msgstr ""

#: resources/assets/lang/temp.php:601
msgid "Total clinic revenue"
msgstr ""

#: resources/assets/lang/temp.php:602
msgid "Total generated revenue"
msgstr ""

#: resources/assets/lang/temp.php:603
msgid "Filter"
msgstr ""

#: resources/assets/lang/temp.php:604
msgid "Reset"
msgstr ""

#: resources/assets/lang/temp.php:605
msgid "Total Today Appointments"
msgstr ""

#: resources/assets/lang/temp.php:606
msgid "Total service"
msgstr ""

#: resources/assets/lang/temp.php:607
#: utils/kc_helpers.php:6912
#: utils/kc_helpers.php:7049
#: utils/kc_helpers.php:7165
#: utils/kc_helpers.php:7253
msgid "Patients"
msgstr ""

#: resources/assets/lang/temp.php:608
msgid "Medical dashboard"
msgstr ""

#: resources/assets/lang/temp.php:611
msgid "Doctor name"
msgstr ""

#: resources/assets/lang/temp.php:612
#: resources/assets/lang/temp.php:730
#: resources/assets/lang/temp.php:817
#: resources/assets/lang/temp.php:852
msgid " first name"
msgstr ""

#: resources/assets/lang/temp.php:613
#: resources/assets/lang/temp.php:731
#: resources/assets/lang/temp.php:818
#: resources/assets/lang/temp.php:853
msgid " last name"
msgstr ""

#: resources/assets/lang/temp.php:614
#: resources/assets/lang/temp.php:732
#: resources/assets/lang/temp.php:819
#: resources/assets/lang/temp.php:854
msgid " email"
msgstr ""

#: resources/assets/lang/temp.php:615
#: resources/assets/lang/temp.php:733
#: resources/assets/lang/temp.php:820
#: resources/assets/lang/temp.php:855
msgid " contact"
msgstr ""

#: resources/assets/lang/temp.php:616
#: resources/assets/lang/temp.php:734
#: resources/assets/lang/temp.php:821
#: resources/assets/lang/temp.php:856
msgid " gender"
msgstr ""

#: resources/assets/lang/temp.php:617
msgid " specilization"
msgstr ""

#: resources/assets/lang/temp.php:618
msgid "Doctor specialization is required"
msgstr ""

#: resources/assets/lang/temp.php:619
msgid "Experience(In Year)"
msgstr ""

#: resources/assets/lang/temp.php:620
msgid "Address details"
msgstr ""

#: resources/assets/lang/temp.php:621
msgid "Degree"
msgstr ""

#: resources/assets/lang/temp.php:622
msgid "Degree is required"
msgstr ""

#: resources/assets/lang/temp.php:623
msgid "Only alphanumeric characters are allowed for Degree"
msgstr ""

#: resources/assets/lang/temp.php:624
msgid "University"
msgstr ""

#: resources/assets/lang/temp.php:625
msgid "University is required"
msgstr ""

#: resources/assets/lang/temp.php:626
msgid "Only alphanumeric characters are allowed for University"
msgstr ""

#: resources/assets/lang/temp.php:627
msgid "Year"
msgstr ""

#: resources/assets/lang/temp.php:628
msgid "-- Select year --"
msgstr ""

#: resources/assets/lang/temp.php:629
msgid "Year is required"
msgstr ""

#: resources/assets/lang/temp.php:630
msgid "College/University"
msgstr ""

#: resources/assets/lang/temp.php:631
msgid "API key"
msgstr ""

#: resources/assets/lang/temp.php:632
msgid "API secret"
msgstr ""

#: resources/assets/lang/temp.php:633
msgid "API secret is required"
msgstr ""

#: resources/assets/lang/temp.php:634
msgid "API Key is required"
msgstr ""

#: resources/assets/lang/temp.php:635
msgid "Zoom Configuration guide"
msgstr ""

#: resources/assets/lang/temp.php:636
msgid "Step 1: Sign up or Sign in here"
msgstr ""

#: resources/assets/lang/temp.php:637
msgid "Step 2: Click/Hover on Develop button at the right in navigation bar and click on build app"
msgstr ""

#: resources/assets/lang/temp.php:638
msgid "Step 3: Select JWT and click Create"
msgstr ""

#: resources/assets/lang/temp.php:639
msgid "Step 3: Select OAuth and click Create"
msgstr ""

#: resources/assets/lang/temp.php:640
msgid "Step 4: Fill the mandatory information and In the App credentials tag you can see API key and API Secret."
msgstr ""

#: resources/assets/lang/temp.php:641
msgid "Step 4: Fill the mandatory information and In the App credentials tag you can see Client ID,Client secret And Redirect URL for OAuth."
msgstr ""

#: resources/assets/lang/temp.php:642
msgid "Step 5: Copy and Paste API key and API secret here and click on save button and you are ready to go."
msgstr ""

#: resources/assets/lang/temp.php:643
msgid "Step 5: Copy and Paste Client ID,Client secret And Redirect URL here and click on save button and you are ready to go."
msgstr ""

#: resources/assets/lang/temp.php:644
msgid "Other detail"
msgstr ""

#: resources/assets/lang/temp.php:645
msgid "Consultation Fees"
msgstr ""

#: resources/assets/lang/temp.php:646
msgid "Video consultation fees"
msgstr ""

#: resources/assets/lang/temp.php:647
msgid "Doctors fees is required"
msgstr ""

#: resources/assets/lang/temp.php:648
msgid "Zoom market Place portal"
msgstr ""

#: resources/assets/lang/temp.php:649
msgid "Create app"
msgstr ""

#: resources/assets/lang/temp.php:650
msgid "Doctors List"
msgstr ""

#: resources/assets/lang/temp.php:651
msgid "Other details"
msgstr ""

#: resources/assets/lang/temp.php:652
msgid "Extra detail"
msgstr ""

#: resources/assets/lang/temp.php:653
msgid "Add doctor"
msgstr ""

#: resources/assets/lang/temp.php:654
msgid "Edit doctor"
msgstr ""

#: resources/assets/lang/temp.php:655
#: resources/assets/lang/temp.php:778
msgid "Edit profile"
msgstr ""

#: resources/assets/lang/temp.php:656
msgid "Basic information"
msgstr ""

#: resources/assets/lang/temp.php:657
msgid "Basic Settings"
msgstr ""

#: resources/assets/lang/temp.php:658
#: resources/assets/lang/temp.php:1476
#: resources/assets/lang/temp.php:1574
msgid "Type"
msgstr ""

#: resources/assets/lang/temp.php:659
msgid "Type is required"
msgstr ""

#: resources/assets/lang/temp.php:660
msgid "Fees type"
msgstr ""

#: resources/assets/lang/temp.php:661
msgid "Range"
msgstr ""

#: resources/assets/lang/temp.php:662
msgid "Fixed"
msgstr ""

#: resources/assets/lang/temp.php:663
msgid "Fees"
msgstr ""

#: resources/assets/lang/temp.php:664
msgid "Fees type is required"
msgstr ""

#: resources/assets/lang/temp.php:665
msgid " Doctors fees is required"
msgstr ""

#: resources/assets/lang/temp.php:666
msgid "Doctors fees must be greater than zero"
msgstr ""

#: resources/assets/lang/temp.php:667
msgid "Doctors fees must be between 0 to 1000000000000000000"
msgstr ""

#: resources/assets/lang/temp.php:668
msgid "Doctors fees minimum fees and maximum fees are required"
msgstr ""

#: resources/assets/lang/temp.php:669
msgid "Doctors fees minimum fees and maximum fees must be greater than zero"
msgstr ""

#: resources/assets/lang/temp.php:670
msgid "Doctors to fees minimum value must be greater than from fees value."
msgstr ""

#: resources/assets/lang/temp.php:671
msgid "Doctors fees minimum fees and minimum fees must be between 0 to 1000000000000000000"
msgstr ""

#: resources/assets/lang/temp.php:672
msgid "Qualification Information"
msgstr ""

#: resources/assets/lang/temp.php:673
msgid "Qualification/Speciality details"
msgstr ""

#: resources/assets/lang/temp.php:674
msgid "Doctors working days and sessions"
msgstr ""

#: resources/assets/lang/temp.php:675
msgid "Working days"
msgstr ""

#: resources/assets/lang/temp.php:676
msgid "Added charges and doctor selection"
msgstr ""

#: resources/assets/lang/temp.php:677
msgid "Individual doctor field customization"
msgstr ""

#: resources/assets/lang/temp.php:679
#: resources/assets/lang/temp.php:742
#: resources/assets/lang/temp.php:812
#: resources/assets/lang/temp.php:896
#: resources/assets/lang/temp.php:1521
#: resources/assets/lang/temp.php:1724
msgid "Enter first name"
msgstr ""

#: resources/assets/lang/temp.php:680
#: resources/assets/lang/temp.php:743
#: resources/assets/lang/temp.php:815
#: resources/assets/lang/temp.php:1177
#: resources/assets/lang/temp.php:1522
#: resources/assets/lang/temp.php:1725
msgid "Enter last name"
msgstr ""

#: resources/assets/lang/temp.php:681
#: resources/assets/lang/temp.php:744
#: resources/assets/lang/temp.php:816
#: resources/assets/lang/temp.php:1523
#: resources/assets/lang/temp.php:1634
#: resources/assets/lang/temp.php:1726
msgid "Enter email"
msgstr ""

#: resources/assets/lang/temp.php:682
#: resources/assets/lang/temp.php:746
#: resources/assets/lang/temp.php:826
#: resources/assets/lang/temp.php:892
#: resources/assets/lang/temp.php:1531
#: resources/assets/lang/temp.php:1730
msgid "Enter address"
msgstr ""

#: resources/assets/lang/temp.php:683
#: resources/assets/lang/temp.php:749
#: resources/assets/lang/temp.php:918
#: resources/assets/lang/temp.php:1533
#: resources/assets/lang/temp.php:1733
msgid "Enter country"
msgstr ""

#: resources/assets/lang/temp.php:684
#: resources/assets/lang/temp.php:765
#: resources/assets/lang/temp.php:824
#: resources/assets/lang/temp.php:888
#: resources/assets/lang/temp.php:981
#: resources/assets/lang/temp.php:1107
#: resources/assets/lang/temp.php:1162
#: resources/assets/lang/temp.php:1196
#: resources/assets/lang/temp.php:1219
#: resources/assets/lang/temp.php:1350
#: resources/assets/lang/temp.php:1381
#: resources/assets/lang/temp.php:1537
#: resources/assets/lang/temp.php:1624
#: resources/assets/lang/temp.php:1727
#: resources/assets/lang/temp.php:1728
msgid "Search"
msgstr ""

#: resources/assets/lang/temp.php:685
#: resources/assets/lang/temp.php:767
#: resources/assets/lang/temp.php:1109
#: resources/assets/lang/temp.php:1161
#: resources/assets/lang/temp.php:1218
msgid "Select clinic"
msgstr ""

#: resources/assets/lang/temp.php:686
#: resources/assets/lang/temp.php:745
#: resources/assets/lang/temp.php:1635
#: resources/assets/lang/temp.php:1729
msgid "Enter contact"
msgstr ""

#: resources/assets/lang/temp.php:687
#: resources/assets/lang/temp.php:768
#: resources/assets/lang/temp.php:823
#: resources/assets/lang/temp.php:897
#: resources/assets/lang/temp.php:1525
msgid "welcome date"
msgstr ""

#: resources/assets/lang/temp.php:688
#: resources/assets/lang/temp.php:769
#: resources/assets/lang/temp.php:1536
msgid "Doctor specialization"
msgstr ""

#: resources/assets/lang/temp.php:689
#: resources/assets/lang/temp.php:770
msgid "Add Specialization"
msgstr ""

#: resources/assets/lang/temp.php:690
#: resources/assets/lang/temp.php:771
msgid "Enter experience"
msgstr ""

#: resources/assets/lang/temp.php:691
#: resources/assets/lang/temp.php:750
#: resources/assets/lang/temp.php:1734
msgid "Enter pin code"
msgstr ""

#: resources/assets/lang/temp.php:692
#: resources/assets/lang/temp.php:716
#: resources/assets/lang/temp.php:772
#: resources/assets/lang/temp.php:1541
msgid "Enter degree"
msgstr ""

#: resources/assets/lang/temp.php:693
#: resources/assets/lang/temp.php:717
#: resources/assets/lang/temp.php:773
#: resources/assets/lang/temp.php:1542
msgid "Enter university name"
msgstr ""

#: resources/assets/lang/temp.php:694
#: resources/assets/lang/temp.php:774
msgid "Enter your API key"
msgstr ""

#: resources/assets/lang/temp.php:695
#: resources/assets/lang/temp.php:775
msgid "Enter your API secret"
msgstr ""

#: resources/assets/lang/temp.php:696
#: resources/assets/lang/temp.php:751
#: resources/assets/lang/temp.php:788
#: resources/assets/lang/temp.php:831
#: resources/assets/lang/temp.php:901
#: resources/assets/lang/temp.php:999
#: resources/assets/lang/temp.php:1066
#: resources/assets/lang/temp.php:1113
#: resources/assets/lang/temp.php:1166
#: resources/assets/lang/temp.php:1473
#: resources/assets/lang/temp.php:1571
msgid "Sr."
msgstr ""

#: resources/assets/lang/temp.php:698
#: resources/assets/lang/temp.php:907
msgid "Filter doctor by name"
msgstr ""

#: resources/assets/lang/temp.php:701
msgid "Filter doctor by email"
msgstr ""

#: resources/assets/lang/temp.php:702
#: resources/assets/lang/temp.php:793
#: resources/assets/lang/temp.php:911
msgid "Mobile"
msgstr ""

#: resources/assets/lang/temp.php:703
msgid "Filter doctor by mobile no"
msgstr ""

#: resources/assets/lang/temp.php:704
#: resources/assets/lang/temp.php:794
#: resources/assets/lang/temp.php:912
#: resources/assets/lang/temp.php:1224
#: resources/assets/lang/temp.php:1528
msgid "Specialization"
msgstr ""

#: resources/assets/lang/temp.php:705
#: resources/assets/lang/temp.php:801
msgid "Filter doctor by specialties"
msgstr ""

#: resources/assets/lang/temp.php:707
msgid "Add Qualification"
msgstr ""

#: resources/assets/lang/temp.php:708
#: resources/assets/lang/temp.php:844
#: resources/assets/lang/temp.php:917
msgid "Clinic address"
msgstr ""

#: resources/assets/lang/temp.php:709
#: resources/assets/lang/temp.php:718
#: resources/assets/lang/temp.php:747
#: resources/assets/lang/temp.php:828
#: resources/assets/lang/temp.php:893
#: resources/assets/lang/temp.php:919
#: resources/assets/lang/temp.php:1532
#: resources/assets/lang/temp.php:1731
msgid "Enter city"
msgstr ""

#: resources/assets/lang/temp.php:710
#: resources/assets/lang/temp.php:748
#: resources/assets/lang/temp.php:829
#: resources/assets/lang/temp.php:1732
msgid "Enter state"
msgstr ""

#: resources/assets/lang/temp.php:711
#: resources/assets/lang/temp.php:827
#: resources/assets/lang/temp.php:894
msgid "Enter country name"
msgstr ""

#: resources/assets/lang/temp.php:712
#: resources/assets/lang/temp.php:830
#: resources/assets/lang/temp.php:895
#: resources/assets/lang/temp.php:920
#: resources/assets/lang/temp.php:1534
msgid "Enter postal code"
msgstr ""

#: resources/assets/lang/temp.php:713
#: resources/assets/lang/temp.php:1538
msgid "Enter fees"
msgstr ""

#: resources/assets/lang/temp.php:714
#: resources/assets/lang/temp.php:1539
msgid "Min price range"
msgstr ""

#: resources/assets/lang/temp.php:715
#: resources/assets/lang/temp.php:1540
msgid "Max price range"
msgstr ""

#: resources/assets/lang/temp.php:719
msgid "Add qualifiaction"
msgstr ""

#: resources/assets/lang/temp.php:720
msgid "Edit qualification"
msgstr ""

#: resources/assets/lang/temp.php:721
msgid "Google Calender is not connected please"
msgstr ""

#: resources/assets/lang/temp.php:722
msgid "Important! Note"
msgstr ""

#: resources/assets/lang/temp.php:723
msgid "Sign in"
msgstr ""

#: resources/assets/lang/temp.php:724
msgid "Sign in with Google."
msgstr ""

#: resources/assets/lang/temp.php:725
msgid "Save qualification"
msgstr ""

#: resources/assets/lang/temp.php:729
msgid " Patient name"
msgstr ""

#: resources/assets/lang/temp.php:735
msgid "Add patient"
msgstr ""

#: resources/assets/lang/temp.php:736
msgid "Patients Lists"
msgstr ""

#: resources/assets/lang/temp.php:737
msgid "Medical Report"
msgstr ""

#: resources/assets/lang/temp.php:739
msgid "Edit Medical Report"
msgstr ""

#: resources/assets/lang/temp.php:740
msgid "Upload Report"
msgstr ""

#: resources/assets/lang/temp.php:753
#: resources/assets/lang/temp.php:797
msgid "Filter patient by name"
msgstr ""

#: resources/assets/lang/temp.php:755
msgid "Filter clinic name"
msgstr ""

#: resources/assets/lang/temp.php:757
#: resources/assets/lang/temp.php:798
msgid "Filter patient by email"
msgstr ""

#: resources/assets/lang/temp.php:758
msgid "Mobile No."
msgstr ""

#: resources/assets/lang/temp.php:759
msgid "Filter patient by contact"
msgstr ""

#: resources/assets/lang/temp.php:760
#: resources/assets/lang/temp.php:796
#: resources/assets/lang/temp.php:1723
msgid "Blood Group"
msgstr ""

#: resources/assets/lang/temp.php:761
msgid "ARALL"
msgstr ""

#: resources/assets/lang/temp.php:762
msgid "Registered On."
msgstr ""

#: resources/assets/lang/temp.php:763
msgid "Filter patient by Reg. date"
msgstr ""

#: resources/assets/lang/temp.php:776
msgid "Edit Bill"
msgstr ""

#: resources/assets/lang/temp.php:777
msgid "Edit patient"
msgstr ""

#: resources/assets/lang/temp.php:779
#: resources/assets/lang/temp.php:1084
#: resources/assets/lang/temp.php:1127
#: resources/assets/lang/temp.php:1485
msgid "Export CSV"
msgstr ""

#: resources/assets/lang/temp.php:780
#: resources/assets/lang/temp.php:1083
#: resources/assets/lang/temp.php:1128
#: resources/assets/lang/temp.php:1486
msgid "Export Excel"
msgstr ""

#: resources/assets/lang/temp.php:781
msgid "Patient Unique ID"
msgstr ""

#: resources/assets/lang/temp.php:782
msgid "Unique ID"
msgstr ""

#: resources/assets/lang/temp.php:783
msgid "Postfix"
msgstr ""

#: resources/assets/lang/temp.php:784
msgid "Prefix"
msgstr ""

#: resources/assets/lang/temp.php:785
msgid "Patient unique Id Setting"
msgstr ""

#: resources/assets/lang/temp.php:786
#: resources/assets/lang/temp.php:1082
#: resources/assets/lang/temp.php:1284
#: resources/assets/lang/temp.php:1488
msgid "Print"
msgstr ""

#: resources/assets/lang/temp.php:790
#: resources/assets/lang/temp.php:833
#: resources/assets/lang/temp.php:1115
#: resources/assets/lang/temp.php:1169
msgid "Clinic Name"
msgstr ""

#: resources/assets/lang/temp.php:795
#: resources/assets/lang/temp.php:910
msgid "Registered ON"
msgstr ""

#: resources/assets/lang/temp.php:799
msgid "Filter patient by mobile no"
msgstr ""

#: resources/assets/lang/temp.php:800
msgid "Filter Patient by Date"
msgstr ""

#: resources/assets/lang/temp.php:802
msgid "Enter Username"
msgstr ""

#: resources/assets/lang/temp.php:803
msgid "Enter Password"
msgstr ""

#: resources/assets/lang/temp.php:805
msgid "Enter report name"
msgstr ""

#: resources/assets/lang/temp.php:806
#: resources/assets/lang/temp.php:1338
msgid "Enter title"
msgstr ""

#: resources/assets/lang/temp.php:807
msgid "Enter notes"
msgstr ""

#: resources/assets/lang/temp.php:808
msgid "Check Out or Check In Clinic"
msgstr ""

#: resources/assets/lang/temp.php:809
msgid "Show only numbers in patient unique id"
msgstr ""

#: resources/assets/lang/temp.php:822
#: resources/assets/lang/temp.php:915
msgid "Enter contact number"
msgstr ""

#: resources/assets/lang/temp.php:835
#: resources/assets/lang/temp.php:1258
msgid "Mobile No"
msgstr ""

#: resources/assets/lang/temp.php:838
msgid "Filter receptionist by name"
msgstr ""

#: resources/assets/lang/temp.php:839
msgid "Filter receptionist by email"
msgstr ""

#: resources/assets/lang/temp.php:840
msgid "Filter receptionist by mobile no"
msgstr ""

#: resources/assets/lang/temp.php:843
msgid "In Active"
msgstr ""

#: resources/assets/lang/temp.php:845
#: resources/assets/lang/temp.php:1644
msgid "Login user not found"
msgstr ""

#: resources/assets/lang/temp.php:847
msgid "Resend credential"
msgstr ""

#: resources/assets/lang/temp.php:848
msgid "Upload Proifle"
msgstr ""

#: resources/assets/lang/temp.php:858
msgid "Receptionists List"
msgstr ""

#: resources/assets/lang/temp.php:859
msgid "Add receptionist"
msgstr ""

#: resources/assets/lang/temp.php:860
msgid "Clinic name"
msgstr ""

#: resources/assets/lang/temp.php:861
msgid "Clinic information"
msgstr ""

#: resources/assets/lang/temp.php:862
msgid "Clinic Profile"
msgstr ""

#: resources/assets/lang/temp.php:863
msgid "Add clinic"
msgstr ""

#: resources/assets/lang/temp.php:864
msgid "Edit clinic"
msgstr ""

#: resources/assets/lang/temp.php:865
msgid "Admin Profile"
msgstr ""

#: resources/assets/lang/temp.php:866
msgid "Clinic Admin Detail"
msgstr ""

#: resources/assets/lang/temp.php:867
msgid "invalid clinic name format only allow alphabetic value"
msgstr ""

#: resources/assets/lang/temp.php:868
msgid "Clinic name length must be between 2 to 35 characters"
msgstr ""

#: resources/assets/lang/temp.php:870
msgid "Speciality"
msgstr ""

#: resources/assets/lang/temp.php:871
msgid "Specialties"
msgstr ""

#: resources/assets/lang/temp.php:872
msgid "Specialities"
msgstr ""

#: resources/assets/lang/temp.php:873
msgid "Note: Type and press enter to add new specialization"
msgstr ""

#: resources/assets/lang/temp.php:874
msgid "Clinic specialities is required"
msgstr ""

#: resources/assets/lang/temp.php:875
msgid "Currency prefix"
msgstr ""

#: resources/assets/lang/temp.php:876
msgid "Currency postfix"
msgstr ""

#: resources/assets/lang/temp.php:877
msgid "Currency decimals"
msgstr ""

#: resources/assets/lang/temp.php:878
msgid "Profile image"
msgstr ""

#: resources/assets/lang/temp.php:879
msgid "Edit profile image"
msgstr ""

#: resources/assets/lang/temp.php:880
msgid "Doctor record not found "
msgstr ""

#: resources/assets/lang/temp.php:881
msgid "Blood group"
msgstr ""

#: resources/assets/lang/temp.php:882
msgid "-- Select blood group --"
msgstr ""

#: resources/assets/lang/temp.php:883
msgid "Update Profile"
msgstr ""

#: resources/assets/lang/temp.php:884
msgid "Clinic List"
msgstr ""

#: resources/assets/lang/temp.php:885
#: resources/assets/lang/temp.php:925
#: resources/assets/lang/temp.php:1526
msgid "Enter clinic name"
msgstr ""

#: resources/assets/lang/temp.php:886
#: resources/assets/lang/temp.php:914
msgid "Enter email address"
msgstr ""

#: resources/assets/lang/temp.php:887
#: resources/assets/lang/temp.php:1524
msgid "Enter telephone number"
msgstr ""

#: resources/assets/lang/temp.php:889
#: resources/assets/lang/temp.php:916
#: resources/assets/lang/temp.php:1527
msgid "Clinic specialization"
msgstr ""

#: resources/assets/lang/temp.php:890
#: resources/assets/lang/temp.php:921
#: resources/assets/lang/temp.php:1529
msgid "Enter currency prefix"
msgstr ""

#: resources/assets/lang/temp.php:891
#: resources/assets/lang/temp.php:922
#: resources/assets/lang/temp.php:1530
msgid "Enter currency postfix"
msgstr ""

#: resources/assets/lang/temp.php:898
msgid "Clinic logo"
msgstr ""

#: resources/assets/lang/temp.php:900
msgid "Contact No"
msgstr ""

#: resources/assets/lang/temp.php:904
msgid "Filter clinic by name"
msgstr ""

#: resources/assets/lang/temp.php:908
msgid "Filter clinic by contact number"
msgstr ""

#: resources/assets/lang/temp.php:909
msgid "Filter clinic by specialties"
msgstr ""

#: resources/assets/lang/temp.php:923
msgid "currency decimals"
msgstr ""

#: resources/assets/lang/temp.php:924
msgid "Select decimal"
msgstr ""

#: resources/assets/lang/temp.php:926
#: resources/assets/lang/temp.php:1508
msgid "Add session details"
msgstr ""

#: resources/assets/lang/temp.php:927
msgid "Edit holiday"
msgstr ""

#: resources/assets/lang/temp.php:929
msgid "Edit Session Detail"
msgstr ""

#: resources/assets/lang/temp.php:930
msgid "Save Session Detail"
msgstr ""

#: resources/assets/lang/temp.php:931
msgid "Edit Clinic Profile"
msgstr ""

#: resources/assets/lang/temp.php:932
msgid "Record Not Found"
msgstr ""

#: resources/assets/lang/temp.php:933
msgid "Clinic Not Found"
msgstr ""

#: resources/assets/lang/temp.php:934
msgid "No Speciality Found"
msgstr ""

#: resources/assets/lang/temp.php:935
msgid "Clinic profile updated successfully"
msgstr ""

#: resources/assets/lang/temp.php:936
msgid "Clinic profile not updated successfully"
msgstr ""

#: resources/assets/lang/temp.php:939
#: resources/assets/lang/temp.php:1280
#: utils/kc_helpers.php:6055
msgid "Paid"
msgstr ""

#: resources/assets/lang/temp.php:940
#: resources/assets/lang/temp.php:1281
#: utils/kc_helpers.php:6055
msgid "Unpaid"
msgstr ""

#: resources/assets/lang/temp.php:941
msgid "Pending"
msgstr ""

#: resources/assets/lang/temp.php:942
msgid "Appointment"
msgstr ""

#: resources/assets/lang/temp.php:945
#: utils/kc_helpers.php:6255
msgid "Booked"
msgstr ""

#: resources/assets/lang/temp.php:947
msgid "Arrived"
msgstr ""

#: resources/assets/lang/temp.php:948
msgid "Check in"
msgstr ""

#: resources/assets/lang/temp.php:949
msgid "Reschedule Appointment"
msgstr ""

#: resources/assets/lang/temp.php:950
msgid "Check out"
msgstr ""

#: resources/assets/lang/temp.php:951
msgid "Start"
msgstr ""

#: resources/assets/lang/temp.php:952
msgid "Join"
msgstr ""

#: resources/assets/lang/temp.php:954
msgid "Visit type is required"
msgstr ""

#: resources/assets/lang/temp.php:955
#: resources/assets/lang/temp.php:1010
msgid "Appointment Date"
msgstr ""

#: resources/assets/lang/temp.php:956
msgid "Appointment date is required"
msgstr ""

#: resources/assets/lang/temp.php:957
#: resources/assets/lang/temp.php:1334
#: resources/assets/lang/temp.php:1472
#: resources/assets/lang/temp.php:1569
#: resources/assets/lang/temp.php:1570
msgid "Select status"
msgstr ""

#: resources/assets/lang/temp.php:959
msgid "Available Slot"
msgstr ""

#: resources/assets/lang/temp.php:960
#: resources/assets/lang/temp.php:1642
#: resources/assets/lang/temp.php:1643
msgid "Session"
msgstr ""

#: resources/assets/lang/temp.php:961
msgid "No time slots found"
msgstr ""

#: resources/assets/lang/temp.php:962
msgid "Time Slot required"
msgstr ""

#: resources/assets/lang/temp.php:963
msgid "Appointment details"
msgstr ""

#: resources/assets/lang/temp.php:964
msgid "Appointment type"
msgstr ""

#: resources/assets/lang/temp.php:965
msgid "Completed"
msgstr ""

#: resources/assets/lang/temp.php:966
msgid "Appointment Time"
msgstr ""

#: resources/assets/lang/temp.php:967
msgid " Appointment time is required."
msgstr ""

#: resources/assets/lang/temp.php:969
msgid "Todays Appointment"
msgstr ""

#: resources/assets/lang/temp.php:970
msgid "Tomorrows Appointment"
msgstr ""

#: resources/assets/lang/temp.php:971
msgid "Appointment Booking"
msgstr ""

#: resources/assets/lang/temp.php:972
msgid "Available Appointments On"
msgstr ""

#: resources/assets/lang/temp.php:973
msgid "Appointment visit type is required."
msgstr ""

#: resources/assets/lang/temp.php:974
#: utils/kc_helpers.php:6299
msgid "Appointment Detail"
msgstr ""

#: resources/assets/lang/temp.php:975
msgid "Save Appointment"
msgstr ""

#: resources/assets/lang/temp.php:976
#: resources/assets/lang/temp.php:1739
msgid "Appointment List"
msgstr ""

#: resources/assets/lang/temp.php:977
msgid "Add Review to doctor"
msgstr ""

#: resources/assets/lang/temp.php:978
msgid "Patient rating"
msgstr ""

#: resources/assets/lang/temp.php:979
msgid "Ratings"
msgstr ""

#: resources/assets/lang/temp.php:982
#: resources/assets/lang/temp.php:1163
#: resources/assets/lang/temp.php:1251
#: resources/assets/lang/temp.php:1252
#: resources/assets/lang/temp.php:1628
msgid "Select doctor"
msgstr ""

#: resources/assets/lang/temp.php:983
msgid "Visit type"
msgstr ""

#: resources/assets/lang/temp.php:984
#: resources/assets/lang/temp.php:1164
msgid "Patient type"
msgstr ""

#: resources/assets/lang/temp.php:986
msgid "Enter appointment description"
msgstr ""

#: resources/assets/lang/temp.php:988
msgid "all"
msgstr ""

#: resources/assets/lang/temp.php:990
msgid "past"
msgstr ""

#: resources/assets/lang/temp.php:991
#: resources/assets/lang/temp.php:1108
#: resources/assets/lang/temp.php:1543
msgid "Select session doctors"
msgstr ""

#: resources/assets/lang/temp.php:992
msgid "Select appointment type"
msgstr ""

#: resources/assets/lang/temp.php:993
msgid "Enter something"
msgstr ""

#: resources/assets/lang/temp.php:994
msgid "DOCTOR NAME"
msgstr ""

#: resources/assets/lang/temp.php:995
msgid "PATIENT NAME"
msgstr ""

#: resources/assets/lang/temp.php:1002
#: resources/assets/lang/temp.php:1171
msgid "Patient Name"
msgstr ""

#: resources/assets/lang/temp.php:1003
#: resources/assets/lang/temp.php:1167
msgid "Doctor Name"
msgstr ""

#: resources/assets/lang/temp.php:1005
msgid "Visit Type"
msgstr ""

#: resources/assets/lang/temp.php:1008
msgid "Add appointment"
msgstr ""

#: resources/assets/lang/temp.php:1011
msgid "Select Date"
msgstr ""

#: resources/assets/lang/temp.php:1013
msgid "Select Patient"
msgstr ""

#: resources/assets/lang/temp.php:1014
msgid "Appointment Setting"
msgstr ""

#: resources/assets/lang/temp.php:1015
msgid "Booking Restriction"
msgstr ""

#: resources/assets/lang/temp.php:1016
msgid "Restrict Advance Appointment Booking"
msgstr ""

#: resources/assets/lang/temp.php:1017
msgid "Appointment Cancellation Buffer"
msgstr ""

#: resources/assets/lang/temp.php:1018
msgid "To prevent appointments from getting canceled too close to the appointment time by patients, you can set a cancellation buffer."
msgstr ""

#: resources/assets/lang/temp.php:1019
msgid "Enable Cancellation Buffer"
msgstr ""

#: resources/assets/lang/temp.php:1020
#: resources/assets/lang/temp.php:1032
msgid "Select Hours"
msgstr ""

#: resources/assets/lang/temp.php:1021
msgid "Booking Close Before (in Days)"
msgstr ""

#: resources/assets/lang/temp.php:1022
msgid "Booking Open Before (in Days)"
msgstr ""

#: resources/assets/lang/temp.php:1023
msgid "Are you sure want save settings?"
msgstr ""

#: resources/assets/lang/temp.php:1024
msgid "Pre Appointment Restriction Days is Required"
msgstr ""

#: resources/assets/lang/temp.php:1025
msgid "Post Appointment Restriction Days is Required"
msgstr ""

#: resources/assets/lang/temp.php:1026
msgid "Pre Appointment Restriction Days must be greater than zero and less than 365 days"
msgstr ""

#: resources/assets/lang/temp.php:1027
msgid "Post Appointment Restriction Days must be greater than zero and less than 365 days"
msgstr ""

#: resources/assets/lang/temp.php:1028
msgid "Appointment File Upload Setting"
msgstr ""

#: resources/assets/lang/temp.php:1029
msgid "Appointment File Upload"
msgstr ""

#: resources/assets/lang/temp.php:1030
msgid "Appointment Reminder"
msgstr ""

#: resources/assets/lang/temp.php:1031
msgid "Email Reminder"
msgstr ""

#: resources/assets/lang/temp.php:1033
msgid "Sms Reminder (Twilio)"
msgstr ""

#: resources/assets/lang/temp.php:1034
msgid "Whatsapp Reminder (Twilio)"
msgstr ""

#: resources/assets/lang/temp.php:1035
msgid "For example, Booking Open Before: 60 days, Booking Close Before: 7 days, As consideration for the current date, The appointment booking opens 60 days ago and closed 7 days ago.  "
msgstr ""

#: resources/assets/lang/temp.php:1036
msgid "Medical Report Uploading......"
msgstr ""

#: resources/assets/lang/temp.php:1037
msgid "cron job will run in every 2 minutes and select the appointment in next select hours (Example if you select/save 06:00  cron will job collect all appointment of current date + 6 hours ) and send email/sms/whatsapps accordings to setting notification to patient only once a day."
msgstr ""

#: resources/assets/lang/temp.php:1038
msgid "Appointment Restriction Post Days Must Be Greater Than Pre Day"
msgstr ""

#: resources/assets/lang/temp.php:1039
msgid "Appointment Time Format"
msgstr ""

#: resources/assets/lang/temp.php:1040
msgid "Format Appointment Time in 24 Hours Format"
msgstr ""

#: resources/assets/lang/temp.php:1041
msgid "Disable multiple delete"
msgstr ""

#: resources/assets/lang/temp.php:1042
msgid "Enable multiple delete"
msgstr ""

#: resources/assets/lang/temp.php:1043
msgid "Delete selected appointment"
msgstr ""

#: resources/assets/lang/temp.php:1047
msgid "Start video Call"
msgstr ""

#: resources/assets/lang/temp.php:1048
msgid "Join video Call"
msgstr ""

#: resources/assets/lang/temp.php:1049
msgid "Resend Video Conference link"
msgstr ""

#: resources/assets/lang/temp.php:1050
msgid "view report"
msgstr ""

#: resources/assets/lang/temp.php:1051
msgid "Select Encounter Templates"
msgstr ""

#: resources/assets/lang/temp.php:1052
msgid "Allow Same Day Booking Only"
msgstr ""

#: resources/assets/lang/temp.php:1055
msgid "Schedule"
msgstr ""

#: resources/assets/lang/temp.php:1056
msgid "Holiday of"
msgstr ""

#: resources/assets/lang/temp.php:1057
msgid "Module type is required"
msgstr ""

#: resources/assets/lang/temp.php:1058
msgid "Schedule date"
msgstr ""

#: resources/assets/lang/temp.php:1059
msgid "Schedule date is required"
msgstr ""

#: resources/assets/lang/temp.php:1060
#: resources/assets/lang/temp.php:1079
msgid "Holiday List"
msgstr ""

#: resources/assets/lang/temp.php:1061
msgid "Select module type"
msgstr ""

#: resources/assets/lang/temp.php:1062
#: resources/assets/lang/temp.php:1464
msgid "Select module"
msgstr ""

#: resources/assets/lang/temp.php:1063
msgid "Select Schedule date"
msgstr ""

#: resources/assets/lang/temp.php:1064
msgid "doctors"
msgstr ""

#: resources/assets/lang/temp.php:1067
msgid "Schedule Of"
msgstr ""

#: resources/assets/lang/temp.php:1068
msgid "Filter Holiday by Schedule"
msgstr ""

#: resources/assets/lang/temp.php:1070
msgid "Filter Holiday by doctor"
msgstr ""

#: resources/assets/lang/temp.php:1071
msgid "From Date"
msgstr ""

#: resources/assets/lang/temp.php:1072
msgid "Filter Holiday by start date"
msgstr ""

#: resources/assets/lang/temp.php:1073
msgid "To Date"
msgstr ""

#: resources/assets/lang/temp.php:1074
msgid "Filter Holiday by end date"
msgstr ""

#: resources/assets/lang/temp.php:1077
#: resources/assets/lang/temp.php:1481
msgid "Are you sure ?"
msgstr ""

#: resources/assets/lang/temp.php:1078
msgid "Press yes to delete holiday"
msgstr ""

#: resources/assets/lang/temp.php:1080
msgid "Delete"
msgstr ""

#: resources/assets/lang/temp.php:1086
msgid "Add holiday"
msgstr ""

#: resources/assets/lang/temp.php:1087
#: resources/assets/lang/temp.php:1121
#: resources/assets/lang/temp.php:1271
msgid "close form"
msgstr ""

#: resources/assets/lang/temp.php:1088
msgid "Delete Current Appointment Session"
msgstr ""

#: resources/assets/lang/temp.php:1089
msgid "Schedule not found"
msgstr ""

#: resources/assets/lang/temp.php:1090
msgid "This action may delete your doctor's appointments, sessions and holidays."
msgstr ""

#: resources/assets/lang/temp.php:1093
#: utils/kc_helpers.php:6940
#: utils/kc_helpers.php:7077
msgid "Doctor Sessions"
msgstr ""

#: resources/assets/lang/temp.php:1094
msgid "Session doctor is required"
msgstr ""

#: resources/assets/lang/temp.php:1096
msgid "Week days"
msgstr ""

#: resources/assets/lang/temp.php:1097
msgid "Days is required"
msgstr ""

#: resources/assets/lang/temp.php:1098
msgid "Selected days already exist in the other session"
msgstr ""

#: resources/assets/lang/temp.php:1099
msgid "Morning session"
msgstr ""

#: resources/assets/lang/temp.php:1100
msgid "Start time is required"
msgstr ""

#: resources/assets/lang/temp.php:1101
msgid "Start time must be smaller than end time"
msgstr ""

#: resources/assets/lang/temp.php:1102
msgid "End time is required"
msgstr ""

#: resources/assets/lang/temp.php:1103
msgid "End time must be bigger than start time"
msgstr ""

#: resources/assets/lang/temp.php:1104
msgid "Evening session"
msgstr ""

#: resources/assets/lang/temp.php:1105
msgid "Start time must be smaller than first sessions end time"
msgstr ""

#: resources/assets/lang/temp.php:1106
msgid "This tab helps you to set sessions for individual Doctors"
msgstr ""

#: resources/assets/lang/temp.php:1110
msgid "Start time"
msgstr ""

#: resources/assets/lang/temp.php:1111
msgid "End time"
msgstr ""

#: resources/assets/lang/temp.php:1112
msgid "Filter doctor session by name"
msgstr ""

#: resources/assets/lang/temp.php:1116
#: resources/assets/lang/temp.php:1510
#: utils/kc_helpers.php:7539
msgid "Days"
msgstr ""

#: resources/assets/lang/temp.php:1117
msgid "Morning Session"
msgstr ""

#: resources/assets/lang/temp.php:1118
msgid "Evening Session"
msgstr ""

#: resources/assets/lang/temp.php:1120
msgid "Doctor Session"
msgstr ""

#: resources/assets/lang/temp.php:1122
msgid "Save Session"
msgstr ""

#: resources/assets/lang/temp.php:1123
msgid "Clinic Session List"
msgstr ""

#: resources/assets/lang/temp.php:1124
msgid "Doctor session not saved successfully"
msgstr ""

#: resources/assets/lang/temp.php:1126
msgid "Edit session"
msgstr ""

#: resources/assets/lang/temp.php:1129
msgid "No speciality Found"
msgstr ""

#: resources/assets/lang/temp.php:1130
msgid "Save session"
msgstr ""

#: resources/assets/lang/temp.php:1131
msgid "Clinic Profile Data Not Found"
msgstr ""

#: resources/assets/lang/temp.php:1135
msgid "Email To Patient"
msgstr ""

#: resources/assets/lang/temp.php:1137
msgid "Encounter List"
msgstr ""

#: resources/assets/lang/temp.php:1138
msgid "Encounter dashboard"
msgstr ""

#: resources/assets/lang/temp.php:1139
msgid "is required"
msgstr ""

#: resources/assets/lang/temp.php:1140
msgid "Note: Type and press enter to create new prescription"
msgstr ""

#: resources/assets/lang/temp.php:1141
msgid "Note: Type and press enter to create new problem"
msgstr ""

#: resources/assets/lang/temp.php:1142
msgid "Note: Type and press enter to create new observation"
msgstr ""

#: resources/assets/lang/temp.php:1143
msgid "Select Problem"
msgstr ""

#: resources/assets/lang/temp.php:1144
msgid "Select Observation"
msgstr ""

#: resources/assets/lang/temp.php:1145
#: utils/kc_helpers.php:6083
#: utils/kc_helpers.php:7533
msgid "Frequency"
msgstr ""

#: resources/assets/lang/temp.php:1146
msgid "Frequency is required"
msgstr ""

#: resources/assets/lang/temp.php:1147
msgid "Duration (In Days)"
msgstr ""

#: resources/assets/lang/temp.php:1148
msgid "Duration is required"
msgstr ""

#: resources/assets/lang/temp.php:1150
#: utils/kc_helpers.php:6084
#: utils/kc_helpers.php:7538
msgid "Duration"
msgstr ""

#: resources/assets/lang/temp.php:1152
msgid "No patient report found"
msgstr ""

#: resources/assets/lang/temp.php:1153
msgid "Add prescription"
msgstr ""

#: resources/assets/lang/temp.php:1154
msgid "Encounter Date"
msgstr ""

#: resources/assets/lang/temp.php:1155
msgid "Encounter date is required"
msgstr ""

#: resources/assets/lang/temp.php:1159
msgid "placeholder"
msgstr ""

#: resources/assets/lang/temp.php:1168
msgid "Filter Encounter by doctor"
msgstr ""

#: resources/assets/lang/temp.php:1170
msgid "Filter Encounter by clinic name"
msgstr ""

#: resources/assets/lang/temp.php:1172
msgid "Filter Encounter by patient name"
msgstr ""

#: resources/assets/lang/temp.php:1174
msgid "Filter Encounter by date"
msgstr ""

#: resources/assets/lang/temp.php:1176
msgid "Edit prescription"
msgstr ""

#: resources/assets/lang/temp.php:1178
msgid "Note: To close the encounter, invoice payment is mandatory"
msgstr ""

#: resources/assets/lang/temp.php:1179
msgid "Doctor Signature:"
msgstr ""

#: resources/assets/lang/temp.php:1180
msgid "Select Encounter Template"
msgstr ""

#: resources/assets/lang/temp.php:1183
msgid "Template Name"
msgstr ""

#: resources/assets/lang/temp.php:1184
msgid "Filter Template Name by name"
msgstr ""

#: resources/assets/lang/temp.php:1188
msgid "Problem type"
msgstr ""

#: resources/assets/lang/temp.php:1189
msgid "Problem start date is required"
msgstr ""

#: resources/assets/lang/temp.php:1190
msgid "Problem start date"
msgstr ""

#: resources/assets/lang/temp.php:1191
msgid "Problem end date"
msgstr ""

#: resources/assets/lang/temp.php:1192
msgid "Problem outcome"
msgstr ""

#: resources/assets/lang/temp.php:1194
msgid "Add medical problems"
msgstr ""

#: resources/assets/lang/temp.php:1195
msgid "Select problem type"
msgstr ""

#: resources/assets/lang/temp.php:1197
msgid "Select problem outcome"
msgstr ""

#: resources/assets/lang/temp.php:1198
msgid "Edit medical record"
msgstr ""

#: resources/assets/lang/temp.php:1201
msgid "DESCRIPTION"
msgstr ""

#: resources/assets/lang/temp.php:1202
msgid "END DATE"
msgstr ""

#: resources/assets/lang/temp.php:1203
msgid "OUTCOME"
msgstr ""

#: resources/assets/lang/temp.php:1204
msgid "PROBLEM TYPE"
msgstr ""

#: resources/assets/lang/temp.php:1205
#: resources/assets/lang/temp.php:1346
msgid "Sr. NO"
msgstr ""

#: resources/assets/lang/temp.php:1207
msgid "Medical record not found"
msgstr ""

#: resources/assets/lang/temp.php:1208
msgid "Enter Medical Record description"
msgstr ""

#: resources/assets/lang/temp.php:1211
#: utils/kc_helpers.php:6961
#: utils/kc_helpers.php:7098
#: utils/kc_helpers.php:7324
msgid "Reports"
msgstr ""

#: resources/assets/lang/temp.php:1212
msgid "Filter By"
msgstr ""

#: resources/assets/lang/temp.php:1213
msgid "Clinic Revenue (Overall)"
msgstr ""

#: resources/assets/lang/temp.php:1214
msgid "Clinic Revenue (Detail)"
msgstr ""

#: resources/assets/lang/temp.php:1215
msgid "Clinic Doctor Revenue"
msgstr ""

#: resources/assets/lang/temp.php:1217
msgid "Report is required."
msgstr ""

#: resources/assets/lang/temp.php:1220
msgid "Select Filter by"
msgstr ""

#: resources/assets/lang/temp.php:1221
msgid "Select"
msgstr ""

#: resources/assets/lang/temp.php:1226
msgid "Please fill in this form to create an account."
msgstr ""

#: resources/assets/lang/temp.php:1229
msgid "Service List"
msgstr ""

#: resources/assets/lang/temp.php:1230
msgid "Service category"
msgstr ""

#: resources/assets/lang/temp.php:1231
msgid "Service category is required"
msgstr ""

#: resources/assets/lang/temp.php:1232
msgid "Type and press enter to add new category"
msgstr ""

#: resources/assets/lang/temp.php:1233
msgid "Doctor is telemed not enabled"
msgstr ""

#: resources/assets/lang/temp.php:1234
msgid " category"
msgstr ""

#: resources/assets/lang/temp.php:1235
msgid " name"
msgstr ""

#: resources/assets/lang/temp.php:1236
msgid " charges"
msgstr ""

#: resources/assets/lang/temp.php:1237
msgid " doctor"
msgstr ""

#: resources/assets/lang/temp.php:1238
msgid "Service Name"
msgstr ""

#: resources/assets/lang/temp.php:1239
msgid "Service name is required"
msgstr ""

#: resources/assets/lang/temp.php:1240
msgid "Service name length should be between 2 to 100 character"
msgstr ""

#: resources/assets/lang/temp.php:1241
#: resources/assets/lang/temp.php:1267
msgid "Charges"
msgstr ""

#: resources/assets/lang/temp.php:1242
msgid "service charge from service module. "
msgstr ""

#: resources/assets/lang/temp.php:1243
msgid "Service charges is required"
msgstr ""

#: resources/assets/lang/temp.php:1244
msgid "Service charges should be between 0 to 10000000000"
msgstr ""

#: resources/assets/lang/temp.php:1245
msgid "Select all"
msgstr ""

#: resources/assets/lang/temp.php:1247
#: resources/assets/lang/temp.php:1248
msgid "Select service category"
msgstr ""

#: resources/assets/lang/temp.php:1249
msgid "Enter service name"
msgstr ""

#: resources/assets/lang/temp.php:1250
msgid "Enter charges"
msgstr ""

#: resources/assets/lang/temp.php:1253
msgid " Select status"
msgstr ""

#: resources/assets/lang/temp.php:1254
msgid " Sr."
msgstr ""

#: resources/assets/lang/temp.php:1255
msgid " Name"
msgstr ""

#: resources/assets/lang/temp.php:1256
msgid " Clinic Name"
msgstr ""

#: resources/assets/lang/temp.php:1261
msgid "Filter service by name"
msgstr ""

#: resources/assets/lang/temp.php:1262
msgid "Filter service by doctor"
msgstr ""

#: resources/assets/lang/temp.php:1263
msgid "Filter Service by price"
msgstr ""

#: resources/assets/lang/temp.php:1266
msgid "InActive"
msgstr ""

#: resources/assets/lang/temp.php:1269
msgid "Category"
msgstr ""

#: resources/assets/lang/temp.php:1272
msgid "Is this a telemed service ?"
msgstr ""

#: resources/assets/lang/temp.php:1273
msgid "Service Duration"
msgstr ""

#: resources/assets/lang/temp.php:1274
msgid "Telemed service is required"
msgstr ""

#: resources/assets/lang/temp.php:1277
msgid "Invoice id"
msgstr ""

#: resources/assets/lang/temp.php:1278
msgid "Created at"
msgstr ""

#: resources/assets/lang/temp.php:1279
msgid "Payment Status"
msgstr ""

#: resources/assets/lang/temp.php:1282
msgid "Patient details"
msgstr ""

#: resources/assets/lang/temp.php:1283
#: utils/kc_helpers.php:6059
msgid "Amount due"
msgstr ""

#: resources/assets/lang/temp.php:1285
msgid "Send to patient"
msgstr ""

#: resources/assets/lang/temp.php:1286
msgid "Service is required"
msgstr ""

#: resources/assets/lang/temp.php:1287
msgid "Price is required"
msgstr ""

#: resources/assets/lang/temp.php:1288
msgid "Price must be greater than or equal to zero"
msgstr ""

#: resources/assets/lang/temp.php:1289
msgid "Price must be between 0 to 1000000000000000000"
msgstr ""

#: resources/assets/lang/temp.php:1290
msgid "Quantity is required"
msgstr ""

#: resources/assets/lang/temp.php:1291
msgid "Please add bill items"
msgstr ""

#: resources/assets/lang/temp.php:1292
msgid "payment link"
msgstr ""

#: resources/assets/lang/temp.php:1293
msgid "Bill total is required"
msgstr ""

#: resources/assets/lang/temp.php:1294
#: utils/kc_helpers.php:6044
msgid "Discount"
msgstr ""

#: resources/assets/lang/temp.php:1295
msgid "Discount in amount"
msgstr ""

#: resources/assets/lang/temp.php:1296
msgid "Discount is required"
msgstr ""

#: resources/assets/lang/temp.php:1297
msgid "Discount must be greater than zero"
msgstr ""

#: resources/assets/lang/temp.php:1298
msgid "Discount must be less than total bill amount"
msgstr ""

#: resources/assets/lang/temp.php:1299
msgid "Payable Amount"
msgstr ""

#: resources/assets/lang/temp.php:1300
msgid "Bill title"
msgstr ""

#: resources/assets/lang/temp.php:1301
msgid "Bill title is required"
msgstr ""

#: resources/assets/lang/temp.php:1302
msgid "Bill items"
msgstr ""

#: resources/assets/lang/temp.php:1303
msgid "Grand total"
msgstr ""

#: resources/assets/lang/temp.php:1304
msgid "Grand total is required"
msgstr ""

#: resources/assets/lang/temp.php:1305
msgid "Print bill"
msgstr ""

#: resources/assets/lang/temp.php:1306
#: utils/kc_helpers.php:6954
#: utils/kc_helpers.php:7091
#: utils/kc_helpers.php:7186
#: utils/kc_helpers.php:7267
#: utils/kc_helpers.php:7317
msgid "Billing records"
msgstr ""

#: resources/assets/lang/temp.php:1307
msgid "Add bill"
msgstr ""

#: resources/assets/lang/temp.php:1308
msgid "Add new bill"
msgstr ""

#: resources/assets/lang/temp.php:1310
msgid "Close Encounter"
msgstr ""

#: resources/assets/lang/temp.php:1311
msgid "Close & Checkout"
msgstr ""

#: resources/assets/lang/temp.php:1312
msgid "Bill Details"
msgstr ""

#: resources/assets/lang/temp.php:1313
#: utils/kc_helpers.php:6198
msgid "Other information"
msgstr ""

#: resources/assets/lang/temp.php:1314
msgid "Patients Encounter List"
msgstr ""

#: resources/assets/lang/temp.php:1315
msgid "Patients Encounter List Is Empty"
msgstr ""

#: resources/assets/lang/temp.php:1316
msgid "Encounter Template List"
msgstr ""

#: resources/assets/lang/temp.php:1318
msgid "Encounter Template Name Required"
msgstr ""

#: resources/assets/lang/temp.php:1319
msgid "Bills"
msgstr ""

#: resources/assets/lang/temp.php:1320
msgid "Payment Setting"
msgstr ""

#: resources/assets/lang/temp.php:1321
msgid "Note: If you enable Woocommerce payment. This action may redirect appointments for payment on the default woocommerce cart page with selected appointment services. The appointment will be canceled automatically in case of an unsuccessful payment. (woocommerce redirection is for the patient role only)"
msgstr ""

#: resources/assets/lang/temp.php:1322
msgid "Woocommerce Payment Gateway"
msgstr ""

#: resources/assets/lang/temp.php:1323
msgid "Amount"
msgstr ""

#: resources/assets/lang/temp.php:1324
msgid "Items"
msgstr ""

#: resources/assets/lang/temp.php:1325
#: resources/assets/lang/temp.php:1496
#: utils/kc_helpers.php:5723
#: utils/kc_helpers.php:6143
msgid "Notes"
msgstr ""

#: resources/assets/lang/temp.php:1326
msgid "Services will be used for invoicing and other future payment related implementations"
msgstr ""

#: resources/assets/lang/temp.php:1327
msgid "Set currency prefix, postfix, and decimals points."
msgstr ""

#: resources/assets/lang/temp.php:1328
#: resources/assets/lang/temp.php:1332
#: resources/assets/lang/temp.php:1629
msgid "Select service"
msgstr ""

#: resources/assets/lang/temp.php:1336
msgid "Enter total_amount"
msgstr ""

#: resources/assets/lang/temp.php:1337
msgid "Enter discount"
msgstr ""

#: resources/assets/lang/temp.php:1339
msgid "Add item"
msgstr ""

#: resources/assets/lang/temp.php:1340
msgid "Add Bill Item"
msgstr ""

#: resources/assets/lang/temp.php:1341
#: resources/assets/lang/temp.php:1501
msgid "Close Form"
msgstr ""

#: resources/assets/lang/temp.php:1343
msgid "ACTUAL AMOUNT"
msgstr ""

#: resources/assets/lang/temp.php:1345
msgid "DISCOUNT"
msgstr ""

#: resources/assets/lang/temp.php:1351
msgid "Please Enable Payment Gateway And Add Items in Bills"
msgstr ""

#: resources/assets/lang/temp.php:1352
msgid "Generate invoice"
msgstr ""

#: resources/assets/lang/temp.php:1353
msgid "Invoice detail"
msgstr ""

#: resources/assets/lang/temp.php:1354
msgid "Local Payment"
msgstr ""

#: resources/assets/lang/temp.php:1355
msgid "Generate new bill"
msgstr ""

#: resources/assets/lang/temp.php:1356
msgid "No encounters found for billing"
msgstr ""

#: resources/assets/lang/temp.php:1359
msgid "General"
msgstr ""

#: resources/assets/lang/temp.php:1360
msgid "Holidays"
msgstr ""

#: resources/assets/lang/temp.php:1361
msgid "Configurations"
msgstr ""

#: resources/assets/lang/temp.php:1362
msgid "App Configurations"
msgstr ""

#: resources/assets/lang/temp.php:1363
msgid "One Signal App Configuration (Deprecated)"
msgstr ""

#: resources/assets/lang/temp.php:1364
msgid "Firebase Cloud Messeging Configuration"
msgstr ""

#: resources/assets/lang/temp.php:1366
msgid "App ID"
msgstr ""

#: resources/assets/lang/temp.php:1367
msgid "Enter App ID"
msgstr ""

#: resources/assets/lang/temp.php:1368
msgid "API Key"
msgstr ""

#: resources/assets/lang/temp.php:1369
msgid "Enter API Key"
msgstr ""

#: resources/assets/lang/temp.php:1371
msgid "Email Template"
msgstr ""

#: resources/assets/lang/temp.php:1372
msgid "SMS/WhatsApp Template"
msgstr ""

#: resources/assets/lang/temp.php:1373
msgid "Listings"
msgstr ""

#: resources/assets/lang/temp.php:1375
msgid "Payment"
msgstr ""

#: resources/assets/lang/temp.php:1376
msgid "New refined settings with various settings like email, invoice, currency, etc."
msgstr ""

#: resources/assets/lang/temp.php:1377
msgid " Pro Settings "
msgstr ""

#: resources/assets/lang/temp.php:1378
msgid " Permission Setting "
msgstr ""

#: resources/assets/lang/temp.php:1379
msgid "Language Settings"
msgstr ""

#: resources/assets/lang/temp.php:1380
#: utils/kc_helpers.php:884
msgid "Select Option"
msgstr ""

#: resources/assets/lang/temp.php:1382
msgid "Enter your ACCOUNT SID"
msgstr ""

#: resources/assets/lang/temp.php:1383
msgid "Enter your AUTH TOKEN"
msgstr ""

#: resources/assets/lang/temp.php:1384
msgid "Enter your to number"
msgstr ""

#: resources/assets/lang/temp.php:1385
msgid "Patient Setting"
msgstr ""

#: resources/assets/lang/temp.php:1386
msgid "Template Dynamic Keys List(click on button to copy)"
msgstr ""

#: resources/assets/lang/temp.php:1387
msgid "Click To Copy"
msgstr ""

#: resources/assets/lang/temp.php:1388
msgid "Key Copied"
msgstr ""

#: resources/assets/lang/temp.php:1389
msgid "Email Subject"
msgstr ""

#: resources/assets/lang/temp.php:1391
msgid "Firebase Server Key"
msgstr ""

#: resources/assets/lang/temp.php:1392
msgid "Client Email"
msgstr ""

#: resources/assets/lang/temp.php:1393
msgid "Private Key"
msgstr ""

#: resources/assets/lang/temp.php:1394
msgid "Project Id"
msgstr ""

#: resources/assets/lang/temp.php:1396
msgid "Content SID"
msgstr ""

#: resources/assets/lang/temp.php:1397
msgid "Fatch Twillio Template"
msgstr ""

#: resources/assets/lang/temp.php:1400
msgid "Theme Settings"
msgstr ""

#: resources/assets/lang/temp.php:1401
msgid "Site Logo"
msgstr ""

#: resources/assets/lang/temp.php:1402
msgid "Site Loader"
msgstr ""

#: resources/assets/lang/temp.php:1403
msgid "Language"
msgstr ""

#: resources/assets/lang/temp.php:1404
msgid "Theme color"
msgstr ""

#: resources/assets/lang/temp.php:1405
msgid "RTL Mode"
msgstr ""

#: resources/assets/lang/temp.php:1406
msgid "on"
msgstr ""

#: resources/assets/lang/temp.php:1407
msgid "SMS Configuration"
msgstr ""

#: resources/assets/lang/temp.php:1408
msgid "WhatsApp Configuration"
msgstr ""

#: resources/assets/lang/temp.php:1409
msgid "ACCOUNT SID"
msgstr ""

#: resources/assets/lang/temp.php:1410
msgid "AUTH TOKEN"
msgstr ""

#: resources/assets/lang/temp.php:1411
msgid "PHONE NUMBER"
msgstr ""

#: resources/assets/lang/temp.php:1412
msgid "Twilio Account Settings"
msgstr ""

#: resources/assets/lang/temp.php:1413
msgid "Twilo SMS guide"
msgstr ""

#: resources/assets/lang/temp.php:1414
msgid "Twilo Whatsapp guide"
msgstr ""

#: resources/assets/lang/temp.php:1415
msgid "Step 1:  You can sign up for a free Twilio trial account here"
msgstr ""

#: resources/assets/lang/temp.php:1416
msgid "Twilo SMS portal"
msgstr ""

#: resources/assets/lang/temp.php:1417
msgid "Step 2: To get the Twilio CLI connected to your account. Visit"
msgstr ""

#: resources/assets/lang/temp.php:1418
msgid "get console"
msgstr ""

#: resources/assets/lang/temp.php:1419
msgid "and you’ll find your unique Account SID and Auth Token to provide to the CLI."
msgstr ""

#: resources/assets/lang/temp.php:1420
msgid "Step 3: Copy and Paste ACCOUNT SID  and AUTH TOKEN and click on save button and here you go."
msgstr ""

#: resources/assets/lang/temp.php:1421
msgid "Step 4 (Optional): To get your first Twilio phone number for sending sms. Visit"
msgstr ""

#: resources/assets/lang/temp.php:1422
msgid "Important Note: Reciever(doctor/patient) Phone/contact No must be in twillo specific format ([+] [country code] [mobile number] )"
msgstr ""

#: resources/assets/lang/temp.php:1423
msgid "Please Refer here for more details"
msgstr ""

#: resources/assets/lang/temp.php:1424
msgid "head on over to the console"
msgstr ""

#: resources/assets/lang/temp.php:1425
msgid "and you will get phone number to send SMS if you dont want any particular number to send message use your SID"
msgstr ""

#: resources/assets/lang/temp.php:1426
msgid "Add New Langauge"
msgstr ""

#: resources/assets/lang/temp.php:1427
msgid "Translate"
msgstr ""

#: resources/assets/lang/temp.php:1428
msgid "Custom Langauge Translation"
msgstr ""

#: resources/assets/lang/temp.php:1429
msgid "translating..."
msgstr ""

#: resources/assets/lang/temp.php:1430
msgid "Select Color"
msgstr ""

#: resources/assets/lang/temp.php:1431
msgid "Google Account Settings"
msgstr ""

#: resources/assets/lang/temp.php:1432
msgid "Connect with google"
msgstr ""

#: resources/assets/lang/temp.php:1433
msgid "Connect with"
msgstr ""

#: resources/assets/lang/temp.php:1434
msgid "Please refer the following link for the setup."
msgstr ""

#: resources/assets/lang/temp.php:1435
msgid "Select Language"
msgstr ""

#: resources/assets/lang/temp.php:1436
msgid "Include Encounter Clinical Details in Prescription print"
msgstr ""

#: resources/assets/lang/temp.php:1437
msgid "Hide Encounter Clinical Details To Patient"
msgstr ""

#: resources/assets/lang/temp.php:1438
msgid "Include Encounter custom fields in Prescription print"
msgstr ""

#: resources/assets/lang/temp.php:1439
msgid "Copyright Text"
msgstr ""

#: resources/assets/lang/temp.php:1440
msgid "Change Copyright Text"
msgstr ""

#: resources/assets/lang/temp.php:1444
msgid "Label name is required"
msgstr ""

#: resources/assets/lang/temp.php:1445
msgid "Label name allows only alphabetic value"
msgstr ""

#: resources/assets/lang/temp.php:1446
msgid "Where it looks like"
msgstr ""

#: resources/assets/lang/temp.php:1447
msgid "It shows in doctor creation form"
msgstr ""

#: resources/assets/lang/temp.php:1448
msgid "It shows in patient encounter dashboard"
msgstr ""

#: resources/assets/lang/temp.php:1449
msgid "It shows in Appointment dashboard"
msgstr ""

#: resources/assets/lang/temp.php:1450
msgid "It shows in patient creation form"
msgstr ""

#: resources/assets/lang/temp.php:1451
msgid "Field name"
msgstr ""

#: resources/assets/lang/temp.php:1452
msgid "Invalid label name"
msgstr ""

#: resources/assets/lang/temp.php:1453
msgid "Label is required"
msgstr ""

#: resources/assets/lang/temp.php:1454
msgid "Field name is already used."
msgstr ""

#: resources/assets/lang/temp.php:1455
msgid "Input Type"
msgstr ""

#: resources/assets/lang/temp.php:1456
msgid "Input type is required"
msgstr ""

#: resources/assets/lang/temp.php:1457
msgid "Placeholder"
msgstr ""

#: resources/assets/lang/temp.php:1458
msgid "Options"
msgstr ""

#: resources/assets/lang/temp.php:1459
msgid "Validation"
msgstr ""

#: resources/assets/lang/temp.php:1460
msgid "Mandatory field"
msgstr ""

#: resources/assets/lang/temp.php:1461
msgid "Custom Field List"
msgstr ""

#: resources/assets/lang/temp.php:1462
msgid "Add custom field"
msgstr ""

#: resources/assets/lang/temp.php:1463
msgid "Module"
msgstr ""

#: resources/assets/lang/temp.php:1467
msgid "Enter field label"
msgstr ""

#: resources/assets/lang/temp.php:1468
msgid "Select input type"
msgstr ""

#: resources/assets/lang/temp.php:1469
msgid "Enter placeholder"
msgstr ""

#: resources/assets/lang/temp.php:1470
msgid "Add this as new option"
msgstr ""

#: resources/assets/lang/temp.php:1471
msgid "Search or add a option"
msgstr ""

#: resources/assets/lang/temp.php:1474
msgid "Field"
msgstr ""

#: resources/assets/lang/temp.php:1475
msgid "Filter custom field by name"
msgstr ""

#: resources/assets/lang/temp.php:1477
msgid "Filter custom field by type"
msgstr ""

#: resources/assets/lang/temp.php:1479
msgid "Add field"
msgstr ""

#: resources/assets/lang/temp.php:1480
msgid "Add new field"
msgstr ""

#: resources/assets/lang/temp.php:1482
msgid "Doctor profile data not found"
msgstr ""

#: resources/assets/lang/temp.php:1483
msgid "Edit custom field"
msgstr ""

#: resources/assets/lang/temp.php:1484
msgid "Edit field"
msgstr ""

#: resources/assets/lang/temp.php:1489
#: resources/assets/lang/temp.php:1645
msgid "Record not found"
msgstr ""

#: resources/assets/lang/temp.php:1490
msgid "Save field"
msgstr ""

#: resources/assets/lang/temp.php:1491
#: resources/assets/lang/temp.php:1564
msgid "Label"
msgstr ""

#: resources/assets/lang/temp.php:1494
#: utils/kc_helpers.php:5700
#: utils/kc_helpers.php:6125
msgid "Problems"
msgstr ""

#: resources/assets/lang/temp.php:1495
#: utils/kc_helpers.php:5711
#: utils/kc_helpers.php:6134
msgid "Observations"
msgstr ""

#: resources/assets/lang/temp.php:1498
msgid "Add Prescription"
msgstr ""

#: resources/assets/lang/temp.php:1500
#: utils/kc_helpers.php:6114
msgid "Clinical Detail"
msgstr ""

#: resources/assets/lang/temp.php:1502
msgid "Add encounter"
msgstr ""

#: resources/assets/lang/temp.php:1503
msgid "Enter Encounter Template Name"
msgstr ""

#: resources/assets/lang/temp.php:1504
msgid "Enter Template Name"
msgstr ""

#: resources/assets/lang/temp.php:1509
msgid "Session doctors"
msgstr ""

#: resources/assets/lang/temp.php:1511
msgid "No sessions found"
msgstr ""

#: resources/assets/lang/temp.php:1512
msgid "Time slot (in minute)"
msgstr ""

#: resources/assets/lang/temp.php:1513
msgid "Open time"
msgstr ""

#: resources/assets/lang/temp.php:1514
msgid "Close time"
msgstr ""

#: resources/assets/lang/temp.php:1515
msgid "Session demo"
msgstr ""

#: resources/assets/lang/temp.php:1516
msgid "Invalid time slot found. invalid slot time is "
msgstr ""

#: resources/assets/lang/temp.php:1517
msgid "Doctor list"
msgstr ""

#: resources/assets/lang/temp.php:1519
#: resources/assets/lang/temp.php:1772
msgid "Prev"
msgstr ""

#: resources/assets/lang/temp.php:1520
msgid "Enter your current password"
msgstr ""

#: resources/assets/lang/temp.php:1535
#: resources/assets/lang/temp.php:1632
msgid "Enter password"
msgstr ""

#: resources/assets/lang/temp.php:1544
msgid "Select start time"
msgstr ""

#: resources/assets/lang/temp.php:1545
msgid "Select end time"
msgstr ""

#: resources/assets/lang/temp.php:1548
msgid "Send test email"
msgstr ""

#: resources/assets/lang/temp.php:1549
msgid "Test sender email is required"
msgstr ""

#: resources/assets/lang/temp.php:1550
msgid "Test content"
msgstr ""

#: resources/assets/lang/temp.php:1551
msgid "Test content is required"
msgstr ""

#: resources/assets/lang/temp.php:1552
msgid "Enable/Disable email notification."
msgstr ""

#: resources/assets/lang/temp.php:1553
msgid "403 | forbidden"
msgstr ""

#: resources/assets/lang/temp.php:1554
msgid "Enter email Id"
msgstr ""

#: resources/assets/lang/temp.php:1558
msgid "Listing Data"
msgstr ""

#: resources/assets/lang/temp.php:1560
msgid "New Enhanced Filters and view"
msgstr ""

#: resources/assets/lang/temp.php:1561
msgid "The booking widget is updated"
msgstr ""

#: resources/assets/lang/temp.php:1562
msgid "Visiting Type is replaced with services (please check service tab)"
msgstr ""

#: resources/assets/lang/temp.php:1563
msgid "Appointment check-in and check-out flow updated"
msgstr ""

#: resources/assets/lang/temp.php:1565
msgid "Add List Data"
msgstr ""

#: resources/assets/lang/temp.php:1566
msgid "Enter data label"
msgstr ""

#: resources/assets/lang/temp.php:1567
#: resources/assets/lang/temp.php:1568
msgid "Select type"
msgstr ""

#: resources/assets/lang/temp.php:1572
msgctxt "static data label"
msgid "Name"
msgstr ""

#: resources/assets/lang/temp.php:1573
msgid "Filter by name"
msgstr ""

#: resources/assets/lang/temp.php:1575
msgid "Filter by type"
msgstr ""

#: resources/assets/lang/temp.php:1577
msgid "Filter by status"
msgstr ""

#: resources/assets/lang/temp.php:1579
msgid "Static data not found"
msgstr ""

#: resources/assets/lang/temp.php:1582
msgid "Doctor not found"
msgstr ""

#: resources/assets/lang/temp.php:1583
msgid "Zoom configuration"
msgstr ""

#: resources/assets/lang/temp.php:1586
msgid "File is required"
msgstr ""

#: resources/assets/lang/temp.php:1587
msgid "Current password"
msgstr ""

#: resources/assets/lang/temp.php:1588
msgid "Current password is required"
msgstr ""

#: resources/assets/lang/temp.php:1589
#: resources/assets/lang/temp.php:1753
msgid "New password"
msgstr ""

#: resources/assets/lang/temp.php:1590
msgid "Appointment info"
msgstr ""

#: resources/assets/lang/temp.php:1591
msgid "Available slots"
msgstr ""

#: resources/assets/lang/temp.php:1592
msgid "Service Detail"
msgstr ""

#: resources/assets/lang/temp.php:1593
msgid "No service detail found."
msgstr ""

#: resources/assets/lang/temp.php:1594
msgid "Book Now"
msgstr ""

#: resources/assets/lang/temp.php:1595
msgid "Registration successful please check your email"
msgstr ""

#: resources/assets/lang/temp.php:1596
msgid "more detail ..."
msgstr ""

#: resources/assets/lang/temp.php:1597
msgid "Username or email is required."
msgstr ""

#: resources/assets/lang/temp.php:1598
msgid "New password is required"
msgstr ""

#: resources/assets/lang/temp.php:1599
#: resources/assets/lang/temp.php:1748
msgid "Confirm password"
msgstr ""

#: resources/assets/lang/temp.php:1600
msgid "Confirm password is required"
msgstr ""

#: resources/assets/lang/temp.php:1601
msgid "New password and Confirm password doest match"
msgstr ""

#: resources/assets/lang/temp.php:1602
#: utils/kc_helpers.php:7005
#: utils/kc_helpers.php:7121
#: utils/kc_helpers.php:7209
#: utils/kc_helpers.php:7289
msgid "Home"
msgstr ""

#: resources/assets/lang/temp.php:1603
msgid "Change Password "
msgstr ""

#: resources/assets/lang/temp.php:1604
msgid "Logging out ...."
msgstr ""

#: resources/assets/lang/temp.php:1605
msgid "Total Visits"
msgstr ""

#: resources/assets/lang/temp.php:1606
msgid "Upcoming Visits"
msgstr ""

#: resources/assets/lang/temp.php:1607
msgid "Example Component"
msgstr ""

#: resources/assets/lang/temp.php:1608
msgid "Other than this many more fine-tunings and tweaks are done. Please email at"
msgstr ""

#: resources/assets/lang/temp.php:1609
msgid "<EMAIL>"
msgstr ""

#: resources/assets/lang/temp.php:1610
msgid "if you face any issues with the update."
msgstr ""

#: resources/assets/lang/temp.php:1611
msgid "After great user feedback, We have some major changes released in this update."
msgstr ""

#: resources/assets/lang/temp.php:1612
msgid "Important! Major Version update!! (V2.0.1)"
msgstr ""

#: resources/assets/lang/temp.php:1613
msgid "Replace appointment"
msgstr ""

#: resources/assets/lang/temp.php:1614
msgid "option as"
msgstr ""

#: resources/assets/lang/temp.php:1615
msgid "service type"
msgstr ""

#: resources/assets/lang/temp.php:1616
msgid "you have to add charges for"
msgstr ""

#: resources/assets/lang/temp.php:1617
msgid "Can Manage individual doctors"
msgstr ""

#: resources/assets/lang/temp.php:1618
msgid "Test email"
msgstr ""

#: resources/assets/lang/temp.php:1619
msgid "Send test Email"
msgstr ""

#: resources/assets/lang/temp.php:1620
msgid "Send test Sms"
msgstr ""

#: resources/assets/lang/temp.php:1621
msgid "Send test Whatsapp"
msgstr ""

#: resources/assets/lang/temp.php:1622
msgid "Morning"
msgstr ""

#: resources/assets/lang/temp.php:1623
msgid "Evening"
msgstr ""

#: resources/assets/lang/temp.php:1625
msgid "Enter description"
msgstr ""

#: resources/assets/lang/temp.php:1626
msgid "Clinics"
msgstr ""

#: resources/assets/lang/temp.php:1627
msgid "Roles"
msgstr ""

#: resources/assets/lang/temp.php:1630
msgid "Enter username or email"
msgstr ""

#: resources/assets/lang/temp.php:1631
msgid "Enter firstname"
msgstr ""

#: resources/assets/lang/temp.php:1633
msgid "Enter lastname"
msgstr ""

#: resources/assets/lang/temp.php:1636
msgid "Enter current password"
msgstr ""

#: resources/assets/lang/temp.php:1637
msgid "Enter new password"
msgstr ""

#: resources/assets/lang/temp.php:1638
#: resources/assets/lang/temp.php:1752
msgid "Enter confirm password"
msgstr ""

#: resources/assets/lang/temp.php:1639
msgid "Patient Info"
msgstr ""

#: resources/assets/lang/temp.php:1640
#: resources/assets/lang/temp.php:1740
msgid "Select doctor to get appointments slots."
msgstr ""

#: resources/assets/lang/temp.php:1641
#: resources/assets/lang/temp.php:1741
msgid "Sorry, No slots available of this doctor on selected day."
msgstr ""

#: resources/assets/lang/temp.php:1646
msgid "Admin can not view the widget. Only patients can view the widget. Please open this page in incognito mode or use another browser without an admin login."
msgstr ""

#: resources/assets/lang/temp.php:1647
msgid "Summary"
msgstr ""

#: resources/assets/lang/temp.php:1650
msgid "Back To Home Page"
msgstr ""

#: resources/assets/lang/temp.php:1653
msgid "Widget Setting"
msgstr ""

#: resources/assets/lang/temp.php:1656
msgid "Show clinic"
msgstr ""

#: resources/assets/lang/temp.php:1657
msgid "Show clinic image"
msgstr ""

#: resources/assets/lang/temp.php:1658
msgid "Show clinic address"
msgstr ""

#: resources/assets/lang/temp.php:1659
msgid "Contact Details"
msgstr ""

#: resources/assets/lang/temp.php:1660
msgid "Show doctor image"
msgstr ""

#: resources/assets/lang/temp.php:1661
msgid "Show doctor experience"
msgstr ""

#: resources/assets/lang/temp.php:1662
msgid "Show doctor speciality"
msgstr ""

#: resources/assets/lang/temp.php:1663
msgid "Show doctor degree"
msgstr ""

#: resources/assets/lang/temp.php:1664
msgid "Contact Detail is required"
msgstr ""

#: resources/assets/lang/temp.php:1666
msgid "Show service image"
msgstr ""

#: resources/assets/lang/temp.php:1667
msgid "Skip service when single is present"
msgstr ""

#: resources/assets/lang/temp.php:1668
msgid "Show service type"
msgstr ""

#: resources/assets/lang/temp.php:1669
msgid "Show service price"
msgstr ""

#: resources/assets/lang/temp.php:1670
msgid "Show service duration"
msgstr ""

#: resources/assets/lang/temp.php:1673
#: utils/kc_helpers.php:5105
msgid "Services from Category"
msgstr ""

#: resources/assets/lang/temp.php:1689
msgid "Signup"
msgstr ""

#: resources/assets/lang/temp.php:1691
msgid "Date And Time"
msgstr ""

#: resources/assets/lang/temp.php:1692
msgid "at"
msgstr ""

#: resources/assets/lang/temp.php:1696
msgid "Payment method"
msgstr ""

#: resources/assets/lang/temp.php:1697
msgid "PayPal"
msgstr ""

#: resources/assets/lang/temp.php:1698
#: utils/kc_helpers.php:5270
msgid "Pay Later"
msgstr ""

#: resources/assets/lang/temp.php:1699
msgid "VISA"
msgstr ""

#: resources/assets/lang/temp.php:1700
msgid "Pay"
msgstr ""

#: resources/assets/lang/temp.php:1702
msgid "Widget Order"
msgstr ""

#: resources/assets/lang/temp.php:1703
msgid "Widget Color"
msgstr ""

#: resources/assets/lang/temp.php:1704
msgid "Primary Color"
msgstr ""

#: resources/assets/lang/temp.php:1705
msgid "Primary Hover Color"
msgstr ""

#: resources/assets/lang/temp.php:1706
msgid "Secondary Color"
msgstr ""

#: resources/assets/lang/temp.php:1707
msgid "Secondary Hover Color"
msgstr ""

#: resources/assets/lang/temp.php:1708
msgid "Print Detail setting"
msgstr ""

#: resources/assets/lang/temp.php:1710
msgid "Show Phone & Email"
msgstr ""

#: resources/assets/lang/temp.php:1711
msgid "Show phone number"
msgstr ""

#: resources/assets/lang/temp.php:1712
msgid "Show email address"
msgstr ""

#: resources/assets/lang/temp.php:1713
msgid "Hide contact details"
msgstr ""

#: resources/assets/lang/temp.php:1714
msgid "Loader"
msgstr ""

#: resources/assets/lang/temp.php:1715
msgid "Select Loader"
msgstr ""

#: resources/assets/lang/temp.php:1716
msgid "Redirect to Print after payment"
msgstr ""

#: resources/assets/lang/temp.php:1717
msgid "Redirect to Print appointment after woocommerce payment complete."
msgstr ""

#: resources/assets/lang/temp.php:1718
msgid "Enable WooCommerce payment."
msgstr ""

#: resources/assets/lang/temp.php:1719
msgid "Enable Local payment."
msgstr ""

#: resources/assets/lang/temp.php:1720
msgid "Show doctor rating"
msgstr ""

#: resources/assets/lang/temp.php:1735
msgid "save"
msgstr ""

#: resources/assets/lang/temp.php:1738
msgid "No appointment found."
msgstr ""

#: resources/assets/lang/temp.php:1745
msgid "Personal Info"
msgstr ""

#: resources/assets/lang/temp.php:1749
msgid "Enter your old password"
msgstr ""

#: resources/assets/lang/temp.php:1750
msgid "Enter your new password"
msgstr ""

#: resources/assets/lang/temp.php:1751
msgid "Enter your confirm password"
msgstr ""

#: resources/assets/lang/temp.php:1756
msgid "Enter API key"
msgstr ""

#: resources/assets/lang/temp.php:1757
msgid "Enter API secret"
msgstr ""

#: resources/assets/lang/temp.php:1760
msgid "Question List"
msgstr ""

#: resources/assets/lang/temp.php:1762
msgid "Add Question"
msgstr ""

#: resources/assets/lang/temp.php:1763
msgid "Question"
msgstr ""

#: resources/assets/lang/temp.php:1764
msgid "Filter Health Question by Question"
msgstr ""

#: resources/assets/lang/temp.php:1765
msgid "Clinic ID"
msgstr ""

#: resources/assets/lang/temp.php:1766
msgid "Filter Health Question by Clinic ID"
msgstr ""

#: resources/assets/lang/temp.php:1767
msgid "Enter Question "
msgstr ""

#: resources/assets/lang/temp.php:1768
msgid "Question is Required"
msgstr ""

#: resources/assets/lang/temp.php:1773
msgid "Rows per page"
msgstr ""

#: resources/assets/lang/temp.php:1774
msgid "of"
msgstr ""

#: resources/assets/lang/temp.php:1775
msgid "ALL"
msgstr ""

#: resources/assets/lang/temp.php:1776
msgid "Search Table"
msgstr ""

#: resources/assets/lang/temp.php:1777
msgid "Page"
msgstr ""

#: resources/assets/lang/temp.php:1780
msgid "Google Event title"
msgstr ""

#: resources/assets/lang/temp.php:1781
msgid "Google Event Description"
msgstr ""

#: resources/assets/lang/temp.php:1787
msgid "Forget Password ?"
msgstr ""

#: resources/assets/lang/temp.php:1790
msgid "Google Meet"
msgstr ""

#: resources/assets/lang/temp.php:1791
msgid "Google Meet Configuration"
msgstr ""

#: resources/assets/lang/temp.php:1792
msgid "Google Meet Client ID"
msgstr ""

#: resources/assets/lang/temp.php:1793
msgid "Google Meet Client ID is required"
msgstr ""

#: resources/assets/lang/temp.php:1794
msgid "Google Meet Client Secret"
msgstr ""

#: resources/assets/lang/temp.php:1795
msgid "Google Meet Client Secret is required"
msgstr ""

#: resources/assets/lang/temp.php:1796
msgid "Guide to setup google GoogleMeet."
msgstr ""

#: resources/assets/lang/temp.php:1797
msgid "Google Meet Event Template"
msgstr ""

#: resources/assets/lang/temp.php:1798
msgid "Google Meet Integration"
msgstr ""

#: resources/assets/lang/temp.php:1799
msgid "Please connect with your google account to use google meet service."
msgstr ""

#: resources/assets/lang/temp.php:1803
msgid "Zoom Telemed"
msgstr ""

#: resources/assets/lang/temp.php:1804
msgid "Zoom Telemed Oauth"
msgstr ""

#: resources/assets/lang/temp.php:1805
msgid "Zoom Telemed Configuration"
msgstr ""

#: resources/assets/lang/temp.php:1806
msgid "Zoom Telemed Client ID"
msgstr ""

#: resources/assets/lang/temp.php:1807
msgid "Zoom Telemed Client ID is required"
msgstr ""

#: resources/assets/lang/temp.php:1808
msgid "Zoom Telemed Client Secret"
msgstr ""

#: resources/assets/lang/temp.php:1809
msgid "Zoom Telemed Client Secret is required"
msgstr ""

#: resources/assets/lang/temp.php:1810
msgid "Zoom Telemed Integration"
msgstr ""

#: resources/assets/lang/temp.php:1811
msgid "Guide to setup Zoom."
msgstr ""

#: resources/assets/lang/temp.php:1812
msgid "Please Connect With Your Zoom Account To Use Zoom Telemed Service."
msgstr ""

#: resources/assets/lang/temp.php:1813
msgid "You are connected with the Zoom."
msgstr ""

#: resources/assets/lang/temp.php:1814
msgid "Redirect URL."
msgstr ""

#: resources/assets/lang/temp.php:1815
msgid "Redirect URL is Required"
msgstr ""

#: resources/assets/lang/temp.php:1816
msgid "The Zoom JWT app type is being deprecated. Please switch to OAuth for your Zoom app to continue your Telemed Service."
msgstr ""

#: resources/assets/lang/temp.php:1817
msgid "Click Here To Goto Settings"
msgstr ""

#: resources/assets/lang/temp.php:1820
msgid "Today"
msgstr ""

#: resources/assets/lang/temp.php:1821
msgid "Day"
msgstr ""

#: resources/assets/lang/temp.php:1822
msgid "Month"
msgstr ""

#: resources/assets/lang/temp.php:1823
msgid "Week"
msgstr ""

#: resources/assets/lang/temp.php:1863
msgctxt "administrator-sidebar"
msgid "Webhooks"
msgstr ""

#: resources/assets/lang/temp.php:1869
msgctxt "administrator-sidebar"
msgid "Dashboard"
msgstr ""

#: resources/assets/lang/temp.php:1870
msgctxt "administrator-sidebar"
msgid "Appointments"
msgstr ""

#: resources/assets/lang/temp.php:1871
msgctxt "administrator-sidebar"
msgid "Encounters"
msgstr ""

#: resources/assets/lang/temp.php:1872
msgctxt "administrator-sidebar"
msgid "Encounter List"
msgstr ""

#: resources/assets/lang/temp.php:1873
msgctxt "administrator-sidebar"
msgid "Encounter Templates"
msgstr ""

#: resources/assets/lang/temp.php:1874
msgctxt "administrator-sidebar"
msgid "Clinic"
msgstr ""

#: resources/assets/lang/temp.php:1875
msgctxt "administrator-sidebar"
msgid "Patients"
msgstr ""

#: resources/assets/lang/temp.php:1876
msgctxt "administrator-sidebar"
msgid "Doctors"
msgstr ""

#: resources/assets/lang/temp.php:1877
msgctxt "administrator-sidebar"
msgid "Receptionist"
msgstr ""

#: resources/assets/lang/temp.php:1878
msgctxt "administrator-sidebar"
msgid "Services"
msgstr ""

#: resources/assets/lang/temp.php:1879
msgctxt "administrator-sidebar"
msgid "Doctor Sessions"
msgstr ""

#: resources/assets/lang/temp.php:1880
msgctxt "administrator-sidebar"
msgid "Taxes"
msgstr ""

#: resources/assets/lang/temp.php:1881
msgctxt "administrator-sidebar"
msgid "Billing records"
msgstr ""

#: resources/assets/lang/temp.php:1882
msgctxt "administrator-sidebar"
msgid "Reports"
msgstr ""

#: resources/assets/lang/temp.php:1883
msgctxt "administrator-sidebar"
msgid "Settings"
msgstr ""

#: resources/assets/lang/temp.php:1884
msgctxt "administrator-sidebar"
msgid "Get help"
msgstr ""

#: resources/assets/lang/temp.php:1885
msgctxt "administrator-sidebar"
msgid "Get Pro"
msgstr ""

#: resources/assets/lang/temp.php:1886
msgctxt "administrator-sidebar"
msgid "Request Features"
msgstr ""

#: resources/assets/lang/temp.php:1889
msgctxt "clinic-admin-sidebar"
msgid "Home"
msgstr ""

#: resources/assets/lang/temp.php:1890
msgctxt "clinic-admin-sidebar"
msgid "Dashboard"
msgstr ""

#: resources/assets/lang/temp.php:1891
msgctxt "clinic-admin-sidebar"
msgid "Appointments"
msgstr ""

#: resources/assets/lang/temp.php:1892
msgctxt "clinic-admin-sidebar"
msgid "Encounters"
msgstr ""

#: resources/assets/lang/temp.php:1893
msgctxt "clinic-admin-sidebar"
msgid "Encounters List"
msgstr ""

#: resources/assets/lang/temp.php:1894
msgctxt "clinic-admin-sidebar"
msgid "Encounter Templates"
msgstr ""

#: resources/assets/lang/temp.php:1895
msgctxt "clinic-admin-sidebar"
msgid "Patients"
msgstr ""

#: resources/assets/lang/temp.php:1896
msgctxt "clinic-admin-sidebar"
msgid "Doctors"
msgstr ""

#: resources/assets/lang/temp.php:1897
msgctxt "clinic-admin-sidebar"
msgid "Receptionist"
msgstr ""

#: resources/assets/lang/temp.php:1898
msgctxt "clinic-admin-sidebar"
msgid "Services"
msgstr ""

#: resources/assets/lang/temp.php:1899
msgctxt "clinic-admin-sidebar"
msgid "Doctor Sessions"
msgstr ""

#: resources/assets/lang/temp.php:1900
msgctxt "clinic-admin-sidebar"
msgid "Taxes"
msgstr ""

#: resources/assets/lang/temp.php:1901
msgctxt "clinic-admin-sidebar"
msgid "Billing records"
msgstr ""

#: resources/assets/lang/temp.php:1902
msgctxt "clinic-admin-sidebar"
msgid "Reports"
msgstr ""

#: resources/assets/lang/temp.php:1903
msgctxt "clinic-admin-sidebar"
msgid "Settings"
msgstr ""

#: resources/assets/lang/temp.php:1906
msgctxt "receptionist-sidebar"
msgid "Home"
msgstr ""

#: resources/assets/lang/temp.php:1907
msgctxt "receptionist-sidebar"
msgid "Dashboard"
msgstr ""

#: resources/assets/lang/temp.php:1908
msgctxt "receptionist-sidebar"
msgid "Appointments"
msgstr ""

#: resources/assets/lang/temp.php:1909
msgctxt "receptionist-sidebar"
msgid "Encounters"
msgstr ""

#: resources/assets/lang/temp.php:1910
msgctxt "receptionist-sidebar"
msgid "Encounter List"
msgstr ""

#: resources/assets/lang/temp.php:1911
msgctxt "receptionist-sidebar"
msgid "Encounter Templates"
msgstr ""

#: resources/assets/lang/temp.php:1912
msgctxt "receptionist-sidebar"
msgid "Patients"
msgstr ""

#: resources/assets/lang/temp.php:1913
msgctxt "receptionist-sidebar"
msgid "Doctors"
msgstr ""

#: resources/assets/lang/temp.php:1914
msgctxt "receptionist-sidebar"
msgid "Services"
msgstr ""

#: resources/assets/lang/temp.php:1915
msgctxt "receptionist-sidebar"
msgid "Billing records"
msgstr ""

#: resources/assets/lang/temp.php:1916
msgctxt "receptionist-sidebar"
msgid "Settings"
msgstr ""

#: resources/assets/lang/temp.php:1919
msgctxt "doctor-sidebar"
msgid "Home"
msgstr ""

#: resources/assets/lang/temp.php:1920
msgctxt "doctor-sidebar"
msgid "Dashboard"
msgstr ""

#: resources/assets/lang/temp.php:1921
msgctxt "doctor-sidebar"
msgid "Appointments"
msgstr ""

#: resources/assets/lang/temp.php:1922
msgctxt "doctor-sidebar"
msgid "Encounters"
msgstr ""

#: resources/assets/lang/temp.php:1923
msgctxt "doctor-sidebar"
msgid "Encounter List"
msgstr ""

#: resources/assets/lang/temp.php:1924
msgctxt "doctor-sidebar"
msgid "Encounter Templates"
msgstr ""

#: resources/assets/lang/temp.php:1925
msgctxt "doctor-sidebar"
msgid "Patients"
msgstr ""

#: resources/assets/lang/temp.php:1926
msgctxt "doctor-sidebar"
msgid "Services"
msgstr ""

#: resources/assets/lang/temp.php:1927
msgctxt "doctor-sidebar"
msgid "Billing records"
msgstr ""

#: resources/assets/lang/temp.php:1928
msgctxt "doctor-sidebar"
msgid "Settings"
msgstr ""

#: resources/assets/lang/temp.php:1931
msgctxt "patient-sidebar"
msgid "Home"
msgstr ""

#: resources/assets/lang/temp.php:1932
msgctxt "patient-sidebar"
msgid "Dashboard"
msgstr ""

#: resources/assets/lang/temp.php:1933
msgctxt "patient-sidebar"
msgid "Appointments"
msgstr ""

#: resources/assets/lang/temp.php:1934
msgctxt "patient-sidebar"
msgid "Encounters"
msgstr ""

#: resources/assets/lang/temp.php:1935
msgctxt "patient-sidebar"
msgid "Billing records"
msgstr ""

#: resources/assets/lang/temp.php:1936
msgctxt "patient-sidebar"
msgid "Reports"
msgstr ""

#: resources/assets/lang/temp.php:1937
msgctxt "patient-sidebar"
msgid "Clinic"
msgstr ""

#: resources/views/kc_notice.php:4
msgid "Language files not found - (404)"
msgstr ""

#: resources/views/kc_notice.php:5
msgid "Error info :"
msgstr ""

#: resources/views/kc_notice.php:5
msgid "Directory permission issue. Your setup does not have permission on"
msgstr ""

#: resources/views/kc_notice.php:5
msgid "wp-content/"
msgstr ""

#: resources/views/kc_notice.php:6
msgid "Solution :"
msgstr ""

#: resources/views/kc_notice.php:6
msgid "Create folder"
msgstr ""

#: resources/views/kc_notice.php:6
msgid "kiviCare_lang"
msgstr ""

#: resources/views/kc_notice.php:6
msgid "on mentioned path "
msgstr ""

#: resources/views/kc_notice.php:6
msgid "wp-content/uploads/"
msgstr ""

#: resources/views/kc_notice.php:7
msgid "And Copy all language json files from"
msgstr ""

#: resources/views/kc_notice.php:7
msgid "(Plugin) kivicare-clinic-management-system/resources/assets/lang"
msgstr ""

#: resources/views/kc_notice.php:7
msgid "and paste it to"
msgstr ""

#: resources/views/kc_notice.php:7
msgid "wp-content/uploads/kiviCare_lang "
msgstr ""

#: resources/views/kc_notice.php:9
msgid "Or"
msgstr ""

#: resources/views/kc_notice.php:10
msgid "Enable Loco Translation"
msgstr ""

#: utils/kc_helpers.php:38
msgid " field is required"
msgstr ""

#: utils/kc_helpers.php:39
msgid " has invalid email address"
msgstr ""

#: utils/kc_helpers.php:190
msgid "Welcome"
msgstr ""

#: utils/kc_helpers.php:200
msgid "Clinic Detail"
msgstr ""

#: utils/kc_helpers.php:210
msgid "Clinic Admin"
msgstr ""

#: utils/kc_helpers.php:855
msgid "Invalid file, Please select file size upto "
msgstr ""

#: utils/kc_helpers.php:2156
msgid "Doctor Registration"
msgstr ""

#: utils/kc_helpers.php:2159
msgid "Patient Registration"
msgstr ""

#: utils/kc_helpers.php:2162
msgid "Receptionist Registration"
msgstr ""

#: utils/kc_helpers.php:2165
msgid "Patient Appointment Booking"
msgstr ""

#: utils/kc_helpers.php:2168
msgid "Doctor Appointment Booking"
msgstr ""

#: utils/kc_helpers.php:2171
msgid "Clinic Appointment Booking"
msgstr ""

#: utils/kc_helpers.php:2175
msgid "Patient Telemed Appointment Booking"
msgstr ""

#: utils/kc_helpers.php:2178
msgid "Clinic Admin Registration"
msgstr ""

#: utils/kc_helpers.php:2181
msgid "Patient Appointment Payment"
msgstr ""

#: utils/kc_helpers.php:2185
msgid "Doctor Telemed Appointment Booking"
msgstr ""

#: utils/kc_helpers.php:2188
msgid "Patient Booked Appointment Reminder"
msgstr ""

#: utils/kc_helpers.php:2191
msgid "Patient Booked Appointment Reminder for Doctor"
msgstr ""

#: utils/kc_helpers.php:2194
msgid "Patient Prescription"
msgstr ""

#: utils/kc_helpers.php:2197
msgid "Patient Appointment Cancel"
msgstr ""

#: utils/kc_helpers.php:2200
msgid "Patient Report"
msgstr ""

#: utils/kc_helpers.php:2203
msgid "Patient Check In"
msgstr ""

#: utils/kc_helpers.php:2206
msgid "Admin New User Register"
msgstr ""

#: utils/kc_helpers.php:2209
msgid "Account Verified"
msgstr ""

#: utils/kc_helpers.php:2212
msgid "Welcome To Clinic "
msgstr ""

#: utils/kc_helpers.php:2324
msgid "There already exists an user registered with this email address,please use other email ID for clinic email"
msgstr ""

#: utils/kc_helpers.php:2324
msgid "There already exists an User registered with this email address,please use other email ID"
msgstr ""

#: utils/kc_helpers.php:2424
msgid "Meetings has email send"
msgstr ""

#: utils/kc_helpers.php:2640
msgid "All Clinic Have Doctor Session"
msgstr ""

#: utils/kc_helpers.php:2643
msgid "Clinic "
msgstr ""

#. translators: clinic name
#. translators: doctor name
#: utils/kc_helpers.php:2655
#: utils/kc_helpers.php:2665
msgid " do not have a doctor session"
msgstr ""

#. translators: timezone
#: utils/kc_helpers.php:3569
msgid "Current Timezone: "
msgstr ""

#. translators: timezone
#: utils/kc_helpers.php:3569
msgid "Your appointment slots work based on your current time zone."
msgstr ""

#: utils/kc_helpers.php:3703
msgid "No clinic Found"
msgstr ""

#: utils/kc_helpers.php:3723
msgid "No doctor found"
msgstr ""

#: utils/kc_helpers.php:3742
#: utils/kc_helpers.php:3972
msgid "Button Height"
msgstr ""

#: utils/kc_helpers.php:3753
#: utils/kc_helpers.php:3983
msgid "Button Width"
msgstr ""

#: utils/kc_helpers.php:3764
#: utils/kc_helpers.php:4165
#: utils/kc_helpers.php:4178
msgid "Button Radius"
msgstr ""

#: utils/kc_helpers.php:3776
#: utils/kc_helpers.php:4189
msgid "Margin"
msgstr ""

#: utils/kc_helpers.php:3798
#: utils/kc_helpers.php:4004
msgid "For hover in Button keep background type classic of button"
msgstr ""

#: utils/kc_helpers.php:3811
#: utils/kc_helpers.php:4017
msgid "Normal"
msgstr ""

#: utils/kc_helpers.php:3830
#: utils/kc_helpers.php:3905
#: utils/kc_helpers.php:4036
#: utils/kc_helpers.php:4111
msgid "Button Background"
msgstr ""

#: utils/kc_helpers.php:3839
#: utils/kc_helpers.php:3914
#: utils/kc_helpers.php:4045
#: utils/kc_helpers.php:4120
msgid "Button Border"
msgstr ""

#: utils/kc_helpers.php:3850
#: utils/kc_helpers.php:3925
#: utils/kc_helpers.php:4056
#: utils/kc_helpers.php:4131
msgid "Button Border style"
msgstr ""

#: utils/kc_helpers.php:3873
#: utils/kc_helpers.php:3948
#: utils/kc_helpers.php:4079
#: utils/kc_helpers.php:4154
msgid "Border Color"
msgstr ""

#: utils/kc_helpers.php:3886
#: utils/kc_helpers.php:4092
msgid "Hover"
msgstr ""

#: utils/kc_helpers.php:3964
msgid "Pagination Button style"
msgstr ""

#: utils/kc_helpers.php:4200
msgid "Padding"
msgstr ""

#: utils/kc_helpers.php:4223
msgid "Every 5 Minutes"
msgstr ""

#: utils/kc_helpers.php:4386
#: utils/kc_helpers.php:4392
msgid "appointment successfully booked, Please check your email "
msgstr ""

#: utils/kc_helpers.php:4389
msgid "appointment successfully booked, Please check your email for zoom meeting link."
msgstr ""

#. translators: doctor name
#: utils/kc_helpers.php:4716
msgid "Doctor Name : "
msgstr ""

#. translators: patient name
#: utils/kc_helpers.php:4724
msgid "Patient Name  : "
msgstr ""

#. translators: patient name
#: utils/kc_helpers.php:4733
msgid "Service Name  : "
msgstr ""

#. translators: Appointment
#: utils/kc_helpers.php:4741
msgid "Appointment Date : "
msgstr ""

#. translators: Appointment
#: utils/kc_helpers.php:4749
msgid "Appointment Time : "
msgstr ""

#. translators: Appointment
#: utils/kc_helpers.php:4758
msgid "Print Appointment Detail"
msgstr ""

#. translators: Appointment
#: utils/kc_helpers.php:4767
msgid "Loading....."
msgstr ""

#: utils/kc_helpers.php:4916
msgid "Doctor Name:"
msgstr ""

#: utils/kc_helpers.php:5107
msgid "Appointment Extra Data "
msgstr ""

#: utils/kc_helpers.php:5274
msgid "Woocommerce"
msgstr ""

#: utils/kc_helpers.php:5277
#: utils/kc_helpers.php:6407
msgid "Paypal"
msgstr ""

#: utils/kc_helpers.php:5285
#: utils/kc_helpers.php:6411
msgid "Stripe"
msgstr ""

#: utils/kc_helpers.php:5447
msgid "Date:"
msgstr ""

#: utils/kc_helpers.php:5462
msgid "Contact No: "
msgstr ""

#: utils/kc_helpers.php:5468
#: utils/kc_helpers.php:5671
msgid "Address: "
msgstr ""

#: utils/kc_helpers.php:5472
#: utils/kc_helpers.php:5516
#: utils/kc_helpers.php:5667
msgid "Email: "
msgstr ""

#: utils/kc_helpers.php:5478
msgid "Invoice Id: "
msgstr ""

#: utils/kc_helpers.php:5489
msgid "Patient Name:"
msgstr ""

#: utils/kc_helpers.php:5494
msgid "Age:"
msgstr ""

#: utils/kc_helpers.php:5502
msgid "Email:"
msgstr ""

#: utils/kc_helpers.php:5508
msgid "Gender:"
msgstr ""

#: utils/kc_helpers.php:5546
#: utils/kc_helpers.php:5931
msgid "Doctor Signature"
msgstr ""

#: utils/kc_helpers.php:5660
msgid "Encounter Details"
msgstr ""

#: utils/kc_helpers.php:5665
msgid "Name: "
msgstr ""

#: utils/kc_helpers.php:5669
msgid "Encounter Date: "
msgstr ""

#: utils/kc_helpers.php:5676
msgid "Clinic Name: "
msgstr ""

#: utils/kc_helpers.php:5678
msgid "Doctor Name: "
msgstr ""

#: utils/kc_helpers.php:5681
#: utils/kc_helpers.php:6329
msgid "Description: "
msgstr ""

#: utils/kc_helpers.php:5696
msgid "Clinical Details"
msgstr ""

#: utils/kc_helpers.php:5769
msgid "Other Information"
msgstr ""

#: utils/kc_helpers.php:5973
msgid "SR NO"
msgstr ""

#: utils/kc_helpers.php:5976
msgid "ITEM NAME"
msgstr ""

#: utils/kc_helpers.php:5979
msgid "PRICE"
msgstr ""

#: utils/kc_helpers.php:5982
msgid "QUANTITY"
msgstr ""

#: utils/kc_helpers.php:5985
msgid "TOTAL"
msgstr ""

#: utils/kc_helpers.php:6053
msgid "Payment Status: "
msgstr ""

#: utils/kc_helpers.php:6075
msgid "Prescriptions"
msgstr ""

#: utils/kc_helpers.php:6099
msgid " day"
msgstr ""

#: utils/kc_helpers.php:6258
msgid "Check Out"
msgstr ""

#: utils/kc_helpers.php:6261
msgid "Check In"
msgstr ""

#: utils/kc_helpers.php:6270
msgid "Appointment Date: "
msgstr ""

#: utils/kc_helpers.php:6274
msgid "Appointment Time: "
msgstr ""

#: utils/kc_helpers.php:6279
msgid "Appointment Status: "
msgstr ""

#: utils/kc_helpers.php:6285
msgid "Payment Mode: "
msgstr ""

#: utils/kc_helpers.php:6289
msgid "Service: "
msgstr ""

#: utils/kc_helpers.php:6293
msgid "Total Bill Payment: "
msgstr ""

#: utils/kc_helpers.php:6321
msgid "Other Info"
msgstr ""

#: utils/kc_helpers.php:6353
msgid "Telemed Meeting Info"
msgstr ""

#: utils/kc_helpers.php:6360
msgid "Meeting Start Link: "
msgstr ""

#: utils/kc_helpers.php:6375
msgid "Meeting Join Link: "
msgstr ""

#: utils/kc_helpers.php:6394
msgid "Manual"
msgstr ""

#: utils/kc_helpers.php:6982
msgid "Get Pro"
msgstr ""

#: utils/kc_helpers.php:7033
#: utils/kc_helpers.php:7420
msgid "Encounters List"
msgstr ""
