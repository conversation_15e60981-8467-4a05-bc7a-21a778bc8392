<template>
  <div
    v-if="showFollowupModal"
    class="overflow-y-auto overflow-x-hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
  >
    <div class="bg-white rounded-lg w-full max-w-md max-h-[90vh] flex flex-col">
      <!-- Header -->
      <div class="flex justify-between items-center p-4 border-b">
        <h3 class="text-lg font-medium">Schedule Follow-up</h3>
        <button
          @click="closeModal"
          type="button"
          aria-label="Close modal"
          class="text-gray-500 hover:text-gray-700"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="lucide lucide-x w-5 h-5"
          >
            <path d="M18 6 6 18"></path>
            <path d="m6 6 12 12"></path>
          </svg>
        </button>
      </div>

      <form @submit.prevent="handleSchedule">
        <!-- Scrollable Content -->
        <div class="overflow-y-auto flex-1 p-4">
          <!-- Form content remains the same -->
          <div class="space-y-4">
            <!-- Service Selection -->
            <div>
              <label
                class="block text-sm font-medium text-gray-700"
                for="appointment-type"
              >
                {{ formTranslation?.common?.service || "Service" }}
                <span class="text-red-500">*</span>
              </label>
              <select
                id="appointment-type"
                v-model="appointmentFormObj.visit_type"
                @change="appointmentTypeChangeSelect"
                :disabled="disabledServiceField"
                class="w-full rounded-md border border-gray-300 px-3 py-2 bg-white mt-1"
                required
              >
                <option value="">
                  {{
                    formTranslation?.appointments?.tag_visit_type_plh ||
                    "Select visit type"
                  }}
                </option>
                <option
                  v-for="type in appointmentTypes"
                  :key="type.id"
                  :value="type.id"
                >
                  {{ type.name }}
                </option>
              </select>
              <p
                v-if="submitted && !appointmentFormObj.visit_type"
                class="text-sm text-red-500 mt-1"
              >
                {{
                  formTranslation?.patient_bill?.service_required ||
                  "Service is required"
                }}
              </p>
            </div>

            <!-- Date Selection -->
            <div>
              <label
                for="appointment-date"
                class="block text-sm font-medium mb-1"
                >Select Date</label
              >
              <div class="relative">
                <input
                  id="appointment-date"
                  class="w-full px-3 py-2 border rounded cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  type="date"
                  v-model="selectedDate"
                  :min="minDate"
                  required
                />
                <div
                  class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700"
                >
                  <svg
                    class="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                    />
                  </svg>
                </div>
              </div>
            </div>

            <!-- Time Slots -->
            <div>
              <label class="block text-sm font-medium mb-2"
                >Available Slots</label
              >
              <div v-if="loadingSlots" class="text-center py-4">
                <div
                  class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"
                ></div>
                <p class="mt-2 text-sm text-gray-600">
                  Loading available slots...
                </p>
              </div>
              <div v-else-if="timeSlots.length === 0" class="text-center py-4">
                <p class="text-sm text-gray-600">
                  {{
                    selectedDate
                      ? "No available slots for selected date"
                      : "Please select a date to view available slots"
                  }}
                </p>
              </div>
              <div
                v-else
                class="max-h-[200px] overflow-y-auto rounded-md border border-gray-200"
              >
                <div class="grid grid-cols-4 gap-2 p-2" role="radiogroup">
                  <button
                    v-for="(slot, index) in timeSlots"
                    :key="`${slot.time}-${index}`"
                    type="button"
                    @click="selectedSlot = slot"
                    :class="[
                      'px-3 py-2 text-sm border rounded',
                      'hover:bg-blue-50 hover:border-blue-200',
                      selectedSlot === slot ? 'bg-blue-50 border-blue-200' : '',
                      'transition-colors duration-150',
                    ]"
                    :aria-pressed="selectedSlot === slot"
                    role="radio"
                  >
                    {{ slot.time }}
                  </button>
                </div>
              </div>
            </div>

            <!-- Reason -->
            <div>
              <label
                for="followup-reason"
                class="block text-sm font-medium mb-1"
                >Follow-up Reason</label
              >
              <textarea
                id="followup-reason"
                class="w-full px-3 py-2 border rounded resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                style="height: 100px"
                placeholder="Enter reason for follow-up..."
                v-model="reason"
                required
              ></textarea>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="border-t p-4">
          <div class="flex justify-end gap-2">
            <button
              type="button"
              @click="closeModal"
              class="px-4 py-2 border rounded text-sm hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="px-4 py-2 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
              :disabled="!isValid || loading"
            >
              {{ loading ? "Scheduling..." : "Schedule" }}
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import { get, post } from "../../config/request";

export default {
  name: "ScheduleFollowupModal",
  props: {
    showFollowupModal: { type: Boolean, required: true },
    patientDetails: { type: Object, required: true },
    disabledServiceField: { type: Boolean, default: false },
  },
  data() {
    return {
      loading: false,
      submitted: false,
      loadingSlots: false,
      appointmentFormObj: { visit_type: "" },
      selectedDate: null,
      selectedSlot: null,
      reason: "",
      timeSlots: [],
      appointmentTypes: [],
    };
  },
  watch: {
    showFollowupModal: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.fetchServices();
          // Add event listener for escape key
          document.addEventListener('keydown', this.handleEscapeKey);
        } else {
          // Remove event listener when modal is closed
          document.removeEventListener('keydown', this.handleEscapeKey);
        }
      },
    },
    selectedDate: "fetchTimeSlots",
    "appointmentFormObj.visit_type": "fetchTimeSlots",
  },
  computed: {
    minDate() {
      return new Date().toISOString().split("T")[0];
    },
    maxDate() {
      const maxDate = new Date();
      maxDate.setFullYear(maxDate.getFullYear() + 1);
      return maxDate.toISOString().split("T")[0];
    },
    isValid() {
      return (
        this.appointmentFormObj.visit_type &&
        this.selectedDate &&
        !isNaN(new Date(this.selectedDate).getTime()) &&
        this.selectedSlot &&
        this.reason.trim()
      );
    },
  },
  beforeDestroy() {
    // Clean up event listener
    document.removeEventListener('keydown', this.handleEscapeKey);
  },
  methods: {
    appointmentTypeChangeSelect() {
      this.$emit("typeChange", this.appointmentFormObj.visit_type);
    },
    // closeModal() {
    //   this.$emit("update:showFollowupModal", false);
    //   this.resetForm();
    // },

    handleBackdropClick(event) {
      // Only close if clicking the backdrop
      if (event.target === event.currentTarget) {
        this.closeModal();
      }
    },

    handleEscapeKey(event) {
      if (event.key === 'Escape') {
        this.closeModal();
      }
    },
    closeModal() {
      // Prevent multiple close attempts
      if (this.isClosing) return;
      this.isClosing = true;
      this.resetAndClose();
    },

    resetAndClose() {
      this.$emit("update:show", false);
      this.resetForm();
      this.isClosing = false;
    },

    hasUnsavedChanges() {
      return (
        this.appointmentFormObj.visit_type ||
        this.selectedDate ||
        this.selectedSlot ||
        this.reason.trim()
      );
    },

    resetForm() {
      this.appointmentFormObj.visit_type = "";
      this.selectedDate = null;
      this.selectedSlot = null;
      this.reason = "";
      this.submitted = false;
    },
    // In the handleSchedule method, update the appointmentData:
    async handleSchedule() {
      this.loading = true;
      this.submitted = true;
      try {
        const appointmentData = {
          patient_id: { id: this.patientDetails.patient_id },
          doctor_id: { id: this.patientDetails.doctor_id },
          clinic_id: { id: this.patientDetails.clinic_id },
          visit_type: [{ id: this.appointmentFormObj.visit_type }], // Changed from service_id
          appointment_start_date: this.selectedDate,
          appointment_start_time: this.selectedSlot.time,
          description: this.reason.trim(),
          status: 1,
          taxes: [],
          payment_mode: "paymentOffline",
          is_dashboard: "true",
          _ajax_nonce:
            document.querySelector('meta[name="kivicare-ajax-nonce"]')
              ?.content || "",
        };

        const response = await post("appointment_save", appointmentData);
        if (response?.data?.status) {
          this.$swal.fire({
            icon: "success",
            title:
              response.data.message || "Appointment scheduled successfully",
            text: "",
          });
          // this.$toast?.success(
          //   response.data.message || "Appointment scheduled successfully"
          // );
          this.$emit("schedule", response.data.data);
          this.closeModal();
        } else {
          throw new Error(
            response?.data?.message || "Failed to schedule appointment"
          );
        }
      } catch (error) {
        this.$toast?.error(error.message || "Internal server error");
      } finally {
        this.loading = false;
      }
    },
    async fetchServices() {
      try {
        const response = await get("service_list", {
          doctor_id: this.patientDetails.doctor_id,
          clinic_id: this.patientDetails.clinic_id,
        });
        this.appointmentTypes = response?.data?.data || [];
      } catch (error) {
        console.error("Error fetching services:", error);
      }
    },
    async fetchTimeSlots() {
      if (!this.selectedDate || !this.appointmentFormObj.visit_type) return;
      try {
        this.loadingSlots = true;
        const response = await get("get_time_slots", {
          doctor_id: this.patientDetails.doctor_id,
          clinic_id: this.patientDetails.clinic_id,
          date: this.selectedDate,
          service: [{ id: this.appointmentFormObj.visit_type }],
        });
        this.timeSlots = response?.data?.data?.[0] || [];
      } catch (error) {
        this.timeSlots = [];
        console.error("Error fetching time slots:", error);
      } finally {
        this.loadingSlots = false;
      }
    },
  },
};
</script>

<style scoped>
input[type="date"]::-webkit-calendar-picker-indicator {
  position: absolute;
  right: 0;
  top: 0;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  opacity: 0;
  cursor: pointer;
}
</style>
