// resources/js/views/Appointment/NewAppointment.vue
<template>
  <div class="min-h-screen">
    <!-- Header Section -->
    <div class="bg-white border-b">
      <div class="flex justify-between items-center p-3">
        <div>
          <h1 class="text-lg font-semibold flex items-center gap-2">
            {{ patientDetails.patient_name }}
            <span class="text-sm font-normal text-gray-500">
              {{ patientDetails.patient_unique_id }}
            </span>
            <button @click="showEditPatient" class="text-sm font-normal text-gray-500">
              <i class="fa fa-pen"></i>
            </button>
          </h1>
          <p class="text-sm text-gray-600">
            {{
              patientMetaData.dob
                ? moment(patientMetaData.dob).format("MMM DD, YYYY") + ", "
                : ""
            }}{{ capitalizeFirstLetter(patientMetaData.gender) }}
          </p>
        </div>

        <div class="flex gap-2 text-sm">
          <!-- Back Button -->
          <button @click="handleBack" class="px-3 py-1.5 bg-black text-white rounded flex items-center gap-1">
            <i class="fa fa-angle-double-left"></i>
            Back
          </button>
          <!-- Save All Tabs Button -->
          <button @click="saveAllTabs" :disabled="isSaving"
            class="px-3 py-1.5 bg-black text-white rounded flex items-center gap-1 hover:bg-black">
            <i :class="isSaving ? 'fa fa-spinner fa-spin' : 'fa fa-save'"></i>
            {{ isSaving ? "Saving..." : "Save All" }}
          </button>

          <!-- Print Button -->
          <button v-if="userData?.addOns?.kiviPro && !isEncounterTemp" @click="printEncounter"
            :disabled="isButtonDisabled" class="px-3 py-1.5 border rounded flex items-center gap-1">
            <i :class="iconClass"></i>
            {{ formTranslation.patient_bill.print }}
          </button>

          <!-- Body Chart Link -->
          <router-link v-if="
            userData?.addOns?.bodyChart &&
            kcCheckPermission('body_chart_list')
          " :to="{
            name: 'patient-encounter.body-chart',
            params: { encounter_id: encounterId },
          }" class="px-3 py-1.5 border rounded flex items-center gap-1">
            <i class="fas fa-x-ray"></i>
            {{ formTranslation.common.body_chart }}
          </router-link>

          <!-- Close Consultation Button -->
          <!-- <button
            v-if="
              kcCheckPermission('patient_bill_add') &&
              encounterData?.status != 0 &&
              !isEncounterTemp
            "
            @click="handleEncounterStatus('0')"
            class="px-3 py-1.5 text-red-500 bg-white border-red-200 border rounded flex items-center gap-1"
          >
            <i class="fa fa-check"></i>
            {{ formTranslation.patient_bill.encounter_close }}
          </button> -->

          <!-- Close & Checkout Button -->
          <button v-if="
            kcCheckPermission('patient_bill_add') &&
            kcCheckPermission('patient_appointment_status_change') &&
            encounterData?.status != 0 &&
            encounterData?.appointment_id != null
          " @click="handleEncounterStatus('1')"
            class="px-3 py-1.5 text-red-500 border-red-200 border rounded flex items-center gap-1">
            <i class="fa fa-check"></i>
            {{ formTranslation.patient_bill.encounter_close_checkout }}
          </button>

          <!-- Bill Details Button -->
          <button v-if="encounterData?.status == 0 && checkEnableModule('billing')" @click="handleBillDetails"
            class="px-3 py-1.5 bg-black text-white rounded flex items-center gap-1">
            <i class="fa fa-file-invoice"></i>
            {{ formTranslation.patient_bill.bill_details }}
          </button>
        </div>
      </div>
    </div>

    <!-- Timer and AIscribe Section -->
    <div class="py-4">
      <!-- Animated background wrapper -->
      <div
        class="relative overflow-hidden rounded-lg border border-[#F8E7FF]/30 shadow-[0_0_1px_rgba(248,231,255,0.4)]">
        <!-- Base layer -->
        <div class="absolute inset-0 bg-[#F8E7FF]"></div>

        <!-- Moving gradient overlay -->
        <div class="absolute inset-0">
          <div class="absolute inset-0 wave-gradient"></div>
        </div>

        <!-- Content -->
        <div class="relative p-3">
          <div class="flex justify-between items-center">
            <!-- Timer and Auto-save Status Section -->
            <div class="flex items-center gap-4">
              <!-- Timer -->
              <div class="flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                  class="lucide lucide-clock w-4 h-4 text-gray-500">
                  <circle cx="12" cy="12" r="10"></circle>
                  <polyline points="12 6 12 12 16 14"></polyline>
                </svg>
                <span class="text-sm text-gray-500">{{ formattedTime }}</span>
              </div>

              <!-- Auto-save status indicator -->
              <div class="flex items-center gap-2">
                <span v-if="isSaving" class="text-sm text-blue-500 flex items-center gap-1">
                  <svg class="animate-spin h-3 w-3" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"
                      fill="none"></circle>
                    <path class="opacity-75" fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                    </path>
                  </svg>
                  Saving...
                </span>
                <span v-else-if="lastSaved" class="text-sm text-green-500 flex items-center gap-1">
                  <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-check">
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                  Saved {{ formatTimeAgo(lastSaved) }}
                </span>
              </div>
            </div>

            <div class="flex items-center gap-3">
              <!-- AI Scribe component -->
              <AIScribe v-if="encounterId" :encounter-id="encounterId" @records-updated="fetchAllData"
                @ai-populate="handleAIPopulate" />

              <!-- Schedule Follow-up Button -->
              <button @click="scheduleFollowUp"
                class="flex items-center gap-2 px-3 py-1.5 bg-black text-white rounded text-sm hover:bg-gray-800">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
                  <path d="M8 2v4"></path>
                  <path d="M16 2v4"></path>
                  <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                  <path d="M3 10h18"></path>
                </svg>
                Schedule Follow-up
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-3 gap-3">
      <div class="col-span-2">
        <div v-for="(instance, index) in vitalSignsInstances" :key="instance.id">
          <VitalSigns v-if="encounterId" :instance-id="instance.id" :is-first-instance="index === 0"
            :encounter-id="encounterId" :vitalSignsData="vitalSignsData[instance.id] || {}"
            @update:vitals="handleVitalsUpdate" @save:success="handleVitalsSaveSuccess" @clone="handleVitalsClone"
            @remove="handleVitalsRemove" />
        </div>

        <div class="grid grid-cols-1 gap-3 mb-3">
          <!-- Main Forms -->
          <div v-for="instance in mainFormsComputed" :key="instance.instanceId" class="relative">
            <MedicalRecordForm :form="getMedicalFormData(instance)" :displayPlusBtn="true"
              :aiData="getAIDataForForm(instance.type)" 
              :approvedInstances="approvedAIInstances"
              @update:content="handleContentUpdate" @clone="handleClone"
              @ai-approved="handleAIApproval" @save="handleManualSave" />
          </div>
        </div>

        <div class="mb-3">
          <div class="flex flex-wrap gap-2 mb-2">
            <div v-for="form in medicalForms" :key="form.type">
              <button @click="toggleForm(form.type)"
                class="flex items-center gap-1.5 px-3 py-1.5 rounded text-sm relative group" :class="[
                  isFormActive(form.type)
                    ? 'bg-black text-white border border-black'
                    : 'bg-white border hover:bg-gray-50',
                ]">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                  class="w-4 h-4 lucide" :class="[form.icon]">
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" x2="12" y1="8" y2="12"></line>
                  <line x1="12" x2="12.01" y1="16" y2="16"></line>
                </svg>
                <span>{{ form.title }}</span>
                <span v-if="!isFormActive(form.type)"
                  class="absolute -top-2 -right-2 w-5 h-5 bg-black text-white rounded-full flex items-center justify-center text-xs group-hover:bg-black">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-plus w-3 h-3">
                    <path d="M5 12h14"></path>
                    <path d="M12 5v14"></path>
                  </svg>
                </span>
              </button>
            </div>
          </div>

          <div class="grid grid-cols-3 gap-3">
            <!-- Other Medical Record Forms -->
            <div v-for="instance in activeMedicalForms" :key="instance.instanceId" class="relative">
              <div class="absolute top-2 right-2 z-10">
                <button @click="removeForm(instance.type, instance.instanceId)" class="p-1 hover:bg-gray-100 rounded">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="lucide lucide-x w-4 h-4 text-gray-400 hover:text-gray-600">
                    <path d="M18 6 6 18"></path>
                    <path d="m6 6 12 12"></path>
                  </svg>
                </button>
              </div>
              <MedicalRecordForm :form="getMedicalFormData(instance)" :displayPlusBtn="false"
              :approvedInstances="approvedAIInstances"
                :aiData="getAIDataForForm(instance.type)" @update:content="handleContentUpdate" @clone="handleClone"
                @ai-approved="handleAIApproval" @save="handleManualSave" />
            </div>
          </div>
        </div>

        <div class="py-2">
          <!-- Plans -->
          <MedicalRecordForm :form="planForm" :displayPlusBtn="false" :aiData="getAIDataForForm('plan')"
          :approvedInstances="approvedAIInstances"
            @update:content="handleContentUpdate" @ai-approved="handleAIApproval" @save="handleManualSave" />
        </div>

        <ApptPrescription v-if="encounterId" :encounter-id="encounterId" :is-encounter-temp="isEncounterTemp" />
      </div>
      <div class="col-span-1">
        <!-- Recent vitals on right -->
        <RecentVitals v-if="encounterId && patientDetails" :encounter-id="encounterId"
          :patient-id="patientDetails.patient_id" />

        <!-- Past Consultations -->
        <PreviousVisits :patient-id="patientDetails.patient_id" />

        <!-- File Management -->
        <FileManagement v-if="encounterId && patientDetails" :encounter-id="encounterId" :canUpload="true"
          :canDelete="true" :consultation-data="getAllMedicalData()" :active-form-types="activeFormTypes"
          :patient-details="patientDetails" :patient-meta-data="patientMetaData"
          :patient-id="patientDetails.patient_id" />
      </div>
    </div>

    <EditPatientModal v-if="showEditPatientModal" :showEditPatientModal="showEditPatientModal"
      :patient-details="patientDetails" :patient-meta-data="patientMetaData" :is-encounter-temp="isEncounterTemp"
      @update:showEditPatientModal="showEditPatientModal = $event" @patient-updated="handlePatientUpdated" />

    <ScheduleFollowupModal v-if="showFollowupModal" :showFollowupModal="showFollowupModal"
      :patient-details="patientDetails" @update:show="showFollowupModal = $event" @schedule="handleScheduleFollowup" />

    <BillDetailsModal v-if="encounterId" :showBillDetailsModal="billDetailsModal"
      @update:showBillDetailsModal="billDetailsModal = $event" :encounter-id="encounterId" :clinic_extra="encounterData?.clinic_extra !== undefined
        ? encounterData?.clinic_extra
        : {}
        " />

    <!-- Bill Generation Modal -->
    <BillFormModal v-if="encounterId && encounterData" :showGenerateBillModal="generateBillModal"
      @update:showGenerateBillModal="generateBillModal = $event" :encounter-id="encounterId"
      :check-out-val="checkOutVal" @onBillSaved="handleBillSave" :patientBillData="patientBillData"
      :appointment-data="encounterData" :clinic_extra="encounterData?.clinic_extra !== undefined
        ? encounterData?.clinic_extra
        : {}
        " :doctorId="encounterData?.doctor_id" :userData="userData"  :isFromEncounter="true" />
  </div>
</template>

<script>
import { post, get } from "../../config/request";
import VitalSigns from "../../components/NewAppointment/VitalSigns.vue";
import MedicalRecordForm from "../../components/NewAppointment/MedicalRecordForm.vue";
import RecentVitals from "../../components/NewAppointment/RecentVitals.vue";
import PreviousVisits from "../../components/NewAppointment/PreviousVisits.vue";
import FileManagement from "../../components/NewAppointment/FileManagement.vue";
import ApptPrescription from "../../components/NewAppointment/ApptPrescription.vue";
import { getBasicDetails } from "../../services/appointment.js";
import EditPatientModal from "../../components/Patient/EditPatientModal.vue";
import ScheduleFollowupModal from "../../components/NewAppointment/ScheduleFollowupModal.vue";
import BillDetailsModal from "../../components/PatientBill/BillDetailsModal.vue";
import BillFormModal from "../../components/PatientBill/BillFormModal.vue";
import AIScribe from "../../components/NewAppointment/AIScribe.vue";
// import ModalPopup from "../../components/Modal/Index";
import { displayMessage, displayErrorMessage } from "../../utils/message";
import moment from "moment";

export default {
  name: "NewAppointment",
  components: {
    AIScribe,
    VitalSigns,
    MedicalRecordForm,
    RecentVitals,
    PreviousVisits,
    FileManagement,
    ApptPrescription,
    EditPatientModal,
    ScheduleFollowupModal,
    BillDetailsModal,
    BillFormModal,
  },

  data() {
    return {
      moment: moment,
      isLoading: false,
      error: null,
      encounterId: null,
      isButtonDisabled: false,
      isEncounterTemp: false,
      showEditPatientModal: false,
      showFollowupModal: false,
      activeFormTypes: [],
      lastInstanceId: 0,
      iconClass: "fa fa-print",
      patientMetaData: {},
      saveTimers: {},
      generateBillModal: false,
      billDetailsModal: false,
      encounterData: null,
      saveDebounceTimers: {},
      isSaving: false,
      approvedAIInstances: {},
      unsavedChanges: {
        vitals: {},
        forms: {},
        plan: false,
      },
      vitalSignsInstances: [
        { id: "vitals-1" }, // Initial instance
      ],
      vitalSignsData: {}, // Store vital signs data by instance ID
      elapsedTime: 0,
      timer: null,
      refreshRecentVitalsKey: 0,
      checkOutVal: 0,
      patientBillData: null,
      lastSaved: null,
      patientDetails: {
        patient_id: "",
        patient_name: "",
        patient_unique_id: "",
        doctor_name: "",
      },
      // Add AI extracted data property to store the data from AIScribe
      aiExtractedData: null,
      planForm: {
        title: "Plan",
        type: "plan",
        templates: [],
        content: "",
        tabId: null,
        metadata: {},
      },
      mainForms: [
        {
          title: "Present Concerns",
          type: "concerns",
          content: "",
          tabId: null,
          templates: [],
          isMainForm: true,
        },
        {
          title: "Present History",
          type: "history",
          content: "",
          tabId: null,
          templates: [],
          isMainForm: true,
        },
        {
          title: "Examination",
          type: "examination",
          content: "",
          tabId: null,
          templates: ["CVS", "Resp", "Gastro", "Neuro", "MSK", "Derm", "ENT"],
          isMainForm: true,
        },
      ],
      medicalForms: [
        {
          tabId: null,
          title: "Safeguarding",
          type: "safeguarding",
          templates: [],
          content: "",
          icon: "lucide-circle-alert",
        },
        {
          tabId: null,
          title: "Notes",
          type: "notes",
          templates: [],
          content: "",
          icon: "lucide-stethoscope",
        },
        {
          tabId: null,
          title: "Comments",
          type: "comments",
          templates: [],
          content: "",
          icon: "lucide-stethoscope",
        },
        {
          tabId: null,
          title: "Allergies",
          type: "allergies",
          templates: [],
          content: "",
          icon: "lucide-hand",
        },
        {
          tabId: null,
          title: "Family History",
          type: "family_history",
          templates: [],
          content: "",
          icon: "lucide-users",
        },
        {
          tabId: null,
          title: "Safety Netting",
          type: "safety_netting",
          templates: [],
          content: "",
          icon: "lucide-shield",
        },
        {
          tabId: null,
          title: "Past Medical History",
          type: "medical_history",
          templates: [],
          content: "",
          icon: "lucide-file-text",
        },
        {
          tabId: null,
          title: "Medications",
          type: "medications",
          templates: [],
          content: "",
          icon: "lucide-file-text",
        },
        {
          tabId: null,
          title: "Social History",
          type: "social_history",
          templates: [],
          content: "",
          icon: "lucide-users",
        },
        {
          tabId: null,
          title: "Systems Review",
          type: "systems_review",
          templates: [],
          content: "",
          icon: "lucide-stethoscope",
        },
        {
          tabId: null,
          title: "Preventative Care",
          type: "preventative_care",
          templates: [],
          content: "",
          icon: "lucide-shield",
        },
        {
          tabId: null,
          title: "Mental Health",
          type: "mental_health",
          templates: [],
          content: "",
          icon: "lucide-circle-alert",
        },
        {
          tabId: null,
          title: "Lifestyle",
          type: "lifestyle",
          templates: [],
          content: "",
          icon: "lucide-file-text",
        },
      ],
    };
  },

  computed: {
    formattedTime() {
      const minutes = Math.floor(this.elapsedTime / 60);
      const seconds = this.elapsedTime % 60;
      return `${minutes}:${seconds.toString().padStart(2, "0")}`;
    },

    mainFormsComputed() {
      return this.activeFormTypes.filter((form) => form.isMainForm);
    },

    activeMedicalForms() {
      return this.activeFormTypes.filter((form) => !form.isMainForm);
    },

    /**
     * Retrieves user data from the Vuex store.
     * @returns {Object} - The user data.
     */
    userData() {
      return this.$store.state.userDataModule.user;
    },
  },

  watch: {
    encounterId: {
      immediate: true,
      handler(newId) {
        if (newId) {
          this.fetchAllData();
        }
      },
    },
  },

  async mounted() {
    await this.init();
    this.initializeMainForms();
    this.startTimer();
  },

  beforeDestroy() {
    this.stopTimer();
    // Clear all save timers
    Object.values(this.saveTimers).forEach((timer) => clearTimeout(timer));
  },

  methods: {
    // Handle AI population data from AIScribe component
    handleAIPopulate({ extractedData, rawData }) {
      console.log('Received AI extracted data:', extractedData);

      // Store the AI data for use in the forms
      this.aiExtractedData = extractedData;

      // First, update main forms with relevant AI data
      this.updateMainFormsWithAIData(extractedData);

      // Update plan if AI data available
      if (extractedData.plan && extractedData.plan.trim()) {
        if (!this.planForm.content || this.planForm.content.trim().length < 10) {
          console.log('Updating plan with AI data');
          this.planForm.content = extractedData.plan.trim();
          this.unsavedChanges.plan = true;
        }
      }

      // Then add tabs for any extracted data that doesn't have a form yet
      this.addTabsForExtractedData(extractedData);

      // Handle vital signs separately if present
      if (extractedData.vitals) {
        this.processAIVitalSigns(extractedData.vitals);
      }

      // Display a message to the user
      try {
        displayMessage('AI has extracted medical data. Saving changes...');
      } catch (e) {
        console.warn('Could not display message:', e);
        alert('AI has extracted medical data. Saving changes...');
      }

      // Automatically save all tabs with a slight delay to ensure data is ready
      setTimeout(() => {
        this.saveAllTabs();
      }, 500);
    },

    // Add new tabs for AI-extracted data that doesn't have existing tabs
    addTabsForExtractedData(extractedData) {
      if (!extractedData) return;

      console.log('Adding tabs for extracted data:', extractedData);

      // Get a list of all existing tab types to prevent duplicates
      const existingTabTypes = this.activeFormTypes.map(form => form.type);
      console.log('Existing tab types:', existingTabTypes);

      // Iterate through all medical forms
      this.medicalForms.forEach(formConfig => {
        const formType = formConfig.type;

        // Check if we have AI data for this form type
        if (extractedData[formType] && extractedData[formType].trim()) {
          // Skip if this tab type already exists - more careful checking
          if (existingTabTypes.includes(formType)) {
            console.log(`Tab of type ${formType} already exists, updating content instead of creating new tab`);

            // Instead of creating new tab, update the existing one with AI data
            const existingTab = this.activeFormTypes.find(form => form.type === formType);
            if (existingTab) {
              // Only update if the existing tab doesn't have content or if AI data is better
              if (!existingTab.content || existingTab.content.trim() === '') {
                existingTab.content = extractedData[formType].trim();
                this.unsavedChanges.forms[existingTab.instanceId] = true;
                console.log(`Updated existing tab ${formType} with AI data`);
              }
            }
          } else {
            // Create new tab instance using the existing medical form config
            const newTab = {
              type: formType,
              tabId: null,
              instanceId: this.generateInstanceId(),
              content: extractedData[formType].trim(), // Will be populated by aiData prop in the component
              metadata: {},
              title: formConfig.title,
              icon: formConfig.icon,
              templates: formConfig.templates || [],
              isMainForm: false
            };

            console.log('Adding new tab for AI data:', newTab);
            this.activeFormTypes.push(newTab);

            // Add this type to our existingTabTypes to prevent duplicates in the same batch
            existingTabTypes.push(formType);

            // Auto-save the new tab with AI data
            this.unsavedChanges.forms[newTab.instanceId] = true;
          }
        }
      });
    },

    // Update main forms with relevant AI data
    updateMainFormsWithAIData(extractedData) {
      if (!extractedData) return;

      // Define mappings between AI data fields and main form types
      const mainFormMappings = {
        'concerns': ['concerns', 'problems', 'present_concerns'],
        'history': ['history', 'present_history'],
        'examination': ['examination']
      };

      // Go through each main form type
      Object.entries(mainFormMappings).forEach(([mainFormType, aiDataFields]) => {
        // Find an existing main form of this type
        const mainForm = this.activeFormTypes.find(form => form.type === mainFormType && form.isMainForm);

        if (mainForm) {
          // Check if we have AI data for any of the mapped fields
          for (const aiField of aiDataFields) {
            if (extractedData[aiField] && extractedData[aiField].trim()) {
              // Only update if the main form is empty or has minimal content
              if (!mainForm.content || mainForm.content.trim().length < 10) {
                console.log(`Updating main form ${mainFormType} with AI data from ${aiField}`);
                mainForm.content = extractedData[aiField].trim();
                this.unsavedChanges.forms[mainForm.instanceId] = true;
                break; // Use the first available AI data field
              }
            }
          }
        }
      });
    },

    // Process AI extracted vital signs data
    processAIVitalSigns(vitalSignsText) {
      if (!vitalSignsText) return;

      console.log('Processing AI vital signs data:', vitalSignsText);

      // Parse the vital signs text to extract measurements
      // Expected format is like:
      // Temperature: 98.6 F
      // Pulse: 72 bpm
      // Blood Pressure: 120/80 mmHg
      // etc.

      const vitalData = {};

      // Extract temperature
      const tempMatch = vitalSignsText.match(/Temperature:\s*([0-9.]+)/i);
      if (tempMatch && tempMatch[1]) {
        vitalData.temperature = tempMatch[1];
      }

      // Extract pulse
      const pulseMatch = vitalSignsText.match(/Pulse:\s*([0-9]+)/i);
      if (pulseMatch && pulseMatch[1]) {
        vitalData.pulse = pulseMatch[1];
      }

      // Extract blood pressure
      const bpMatch = vitalSignsText.match(/Blood Pressure:\s*([0-9]+)\/([0-9]+)/i);
      if (bpMatch && bpMatch[1] && bpMatch[2]) {
        vitalData.systolic = bpMatch[1];
        vitalData.diastolic = bpMatch[2];
      }

      // Extract respiratory rate
      const rrMatch = vitalSignsText.match(/Respiratory Rate:\s*([0-9]+)/i);
      if (rrMatch && rrMatch[1]) {
        vitalData.respiratoryRate = rrMatch[1];
      }

      // Extract oxygen saturation
      const satMatch = vitalSignsText.match(/Saturation:\s*([0-9]+)/i);
      if (satMatch && satMatch[1]) {
        vitalData.oxygenSaturation = satMatch[1];
      }

      // If we have vital signs data, update the first vital signs instance
      if (Object.keys(vitalData).length > 0) {
        // Add AI flag for visual indication
        vitalData.aiPopulated = true;

        // Store in vitalSignsData for the first instance
        const firstInstanceId = this.vitalSignsInstances[0].id;
        this.vitalSignsData[firstInstanceId] = {
          ...this.vitalSignsData[firstInstanceId],
          ...vitalData
        };

        // Mark as having unsaved changes
        this.unsavedChanges.vitals[firstInstanceId] = true;

        console.log('Updated vital signs data with AI extracted values:', this.vitalSignsData);

        // Auto-save the vitals data
        // this.saveVitalSignsData(firstInstanceId, this.vitalSignsData[firstInstanceId]);
      }
    },

    // Get the appropriate AI data for a specific form type
    getAIDataForForm(formType) {
      if (!this.aiExtractedData) return null;

      // Map form types to their corresponding AI data
      // return this.aiExtractedData[formType] || null;

      if (this.aiExtractedData[formType]) {
        return {
          content: this.aiExtractedData[formType],
          isAIGenerated: true
        };
      }

      return null;

    },

    // Handle AI data approval
    handleAIApproval(formData) {
      console.log(`AI data approved for ${formData.type}:`, formData);

      // Store this instance as approved
      this.approvedAIInstances[formData.instanceId] = true;

      // Find the form in activeFormTypes
      const form = this.activeFormTypes.find(
        (f) => f.type === formData.type && f.instanceId === formData.instanceId
      );

      if (form) {
        // Save the form data immediately when AI content is approved
        this.saveTab(form, true);
      }
    },

    async init() {
      this.encounterId = this.$route.params.encounter_id ?
        (Number.isNaN(parseInt(this.$route.params.encounter_id)) ?
          this.$route.params.encounter_id :
          parseInt(this.$route.params.encounter_id)) :
        null;

      // Add a debug line to check the encounterId value
      console.log("Encounter ID initialized:", this.encounterId);
      try {
        this.isLoading = true;
        this.error = null;

        if (this.$route?.query?.isEncounterTemp !== undefined) {
          this.isEncounterTemp = this.$route.query.isEncounterTemp === "1";
        }

        this.encounterId = this.$route.params.encounter_id;
        console.log("Encounter ID initialized:", this.encounterId); // Add this debug line

        if (!this.encounterId) {
          throw new Error("No encounter ID provided");
        }
      } catch (error) {
        this.error = error.message;
        console.error("Error initializing dashboard:", error);
      } finally {
        this.isLoading = false;
      }
    },

    initializeMainForms() {
      // Map mainForms to activeFormTypes format
      const initialMainForms = this.mainForms.map((form) => ({
        type: form.type,
        tabId: null,
        instanceId: this.generateInstanceId(),
        content: "",
        metadata: {},
        templates: form.templates,
        title: form.title,
        isMainForm: true,
      }));

      // Add to activeFormTypes
      this.activeFormTypes = [...initialMainForms];
    },

    async fetchAllData() {
      await Promise.all([this.fetchBasicDetails(), this.fetchEncounterTabs()]);
      this.getEncounterCustomField();
      this.getEncounterBill();
      if (this.userData.addOns.kiviPro) {
        this.getEncounterTemplates();
      }
    },

    /**
     * Fetches billing details associated with the encounter.
     */
    getEncounterBill() {
      if (parseInt(this.encounterId) !== 0 && !this.isEncounterTemp) {
        get("patient_bill_detail", {
          encounter_id: this.encounterId,
        })
          .then((response) => {
            if (
              response.data.status !== undefined &&
              response.data.status === true
            ) {
              if (response.data.data.length === 0) {
                this.createBillButton = true;
              } else {
                this.createBillButton = false;
                this.patientBillData = response.data.data;
              }
            }
          })
          .catch((error) => {
            console.log(error);
            displayErrorMessage(this.formTranslation.widgets.record_not_found);
          });
      }
    },

    async fetchBasicDetails() {
      try {
        const result = await getBasicDetails(
          this.encounterId,
          this.isEncounterTemp
        );

        if (result?.status) {
          this.encounterData = result.encounterData;
          this.patientDetails = result.patientDetails;
          this.patientMetaData = result.patientMetaData;
        } else {
          throw new Error(result.message || "Failed to fetch basic details");
        }
      } catch (error) {
        console.error("Error fetching basic details:", error);
        throw error;
      }
    },

    async fetchEncounterTabs() {
      try {
        let response = await get("get_encounter_tabs", {
          encounter_id: this.encounterId,
        });

        if (!response?.data?.status) {
          throw new Error(response?.data?.message || "Failed to load tabs");
        }

        this.processTabsData(response.data.data);
      } catch (error) {
        console.error("Error loading tabs:", error);
        throw error;
      }
    },

    processTabsData(tabs) {
      if (!Array.isArray(tabs)) return;

      console.log('Processing tabs data received from server:', tabs);

      // Reset activeFormTypes with initial main forms
      this.activeFormTypes = this.mainForms.map((form) => ({
        type: form.type,
        tabId: null,
        instanceId: this.generateInstanceId(),
        content: "",
        metadata: {},
        templates: form.templates,
        title: form.title,
        isMainForm: true,
      }));

      // Track existing tab types to prevent duplicates
      const existingTypes = new Set(this.activeFormTypes.map(form => form.type));

      tabs.forEach((tab) => {
        if (!tab || !tab.type) {
          console.warn('Skipping invalid tab entry:', tab);
          return;
        }

        if (tab.type === "plan") {
          this.planForm = {
            ...this.planForm,
            content: tab.content || '',
            tabId: tab.id,
            metadata: tab.metadata || {},
          };
          console.log('Updated plan tab with saved content:', this.planForm.content.substring(0, 50) + '...');
          return;
        }

        const isMainForm = this.mainForms.some((f) => f.type === tab.type);

        const instance = {
          type: tab.type,
          tabId: tab.id,
          instanceId: `existing_${tab.id}`,
          content: tab.content || '',
          metadata: tab.metadata || {},
          title: tab.title || this.getTabTitle(tab.type)
        };

        if (isMainForm) {
          // Update existing main form instead of adding new one
          const existingFormIndex = this.activeFormTypes.findIndex(
            (f) => f.type === tab.type && f.isMainForm
          );
          if (existingFormIndex !== -1) {
            this.activeFormTypes[existingFormIndex] = {
              ...this.activeFormTypes[existingFormIndex],
              ...instance,
              isMainForm: true,
            };
            console.log(`Updated main form "${tab.type}" with saved content: "${tab.content?.substring(0, 50)}..."`);
          }
        } else {
          // Add supplementary form only if type doesn't already exist
          if (!existingTypes.has(tab.type)) {
            const formConfig = this.medicalForms.find(f => f.type === tab.type) || {};

            this.activeFormTypes.push({
              ...instance,
              isMainForm: false,
              templates: formConfig.templates || [],
              icon: formConfig.icon,
              title: formConfig.title || tab.title || this.getTabTitle(tab.type)
            });

            // Add to tracking set
            existingTypes.add(tab.type);

            console.log(`Added supplementary form "${tab.type}" with saved content: "${tab.content?.substring(0, 50)}..."`);
          } else {
            console.log(`Skipping duplicate tab type: ${tab.type}`);
          }
        }
      });
    },

    // Helper method to get a human-readable title for a tab type
    getTabTitle(type) {
      // First check medicalForms
      const medicalForm = this.medicalForms.find(f => f.type === type);
      if (medicalForm?.title) {
        return medicalForm.title;
      }

      // Then check mainForms
      const mainForm = this.mainForms.find(f => f.type === type);
      if (mainForm?.title) {
        return mainForm.title;
      }

      // Default: capitalize and format the type
      return type.split('_').map(word => this.capitalizeFirstLetter(word)).join(' ');
    },

    /**
     * Fetches encounter templates from the backend.
     */
    getEncounterTemplates() {
      get("get_encounter_templates", { encounter_id: this.encounterId }).then(
        (res) => {
          if (res?.data?.success) {
            this.encounterTemplates = res?.data?.data.list;
            this.encounterTemplate = this.encounterTemplates?.find(
              (obj) => obj.id === res?.data?.data?.default
            );
          }
          this.encounterTemplateLoader = false;
        }
      );
    },

    generateInstanceId() {
      this.lastInstanceId += 1;
      return `new_${this.lastInstanceId}`;
    },

    isFormActive(formType) {
      return this.activeFormTypes.some((form) => form.type === formType);
    },

    startTimer() {
      this.timer = setInterval(() => {
        this.elapsedTime++;
      }, 1000);
    },

    stopTimer() {
      if (this.timer) {
        clearInterval(this.timer);
      }
    },

    // Update handleContentUpdate to only track changes
    handleContentUpdate({ type, content, instanceId }) {
      if (type === "plan") {
        this.planForm.content = content;
        this.unsavedChanges.plan = true;
      } else {
        const instance = this.activeFormTypes.find(
          (i) => i.type === type && i.instanceId === instanceId
        );
        if (instance) {
          instance.content = content;
          this.unsavedChanges.forms[instanceId] = true;
        }
      }
    },

    // Add window beforeunload warning if there are unsaved changes
    beforeRouteLeave(to, from, next) {
      if (this.hasUnsavedChanges()) {
        const answer = window.confirm(
          "You have unsaved changes. Do you really want to leave?"
        );
        if (answer) {
          next();
        } else {
          next(false);
        }
      } else {
        next();
      }
    },

    // Helper method to check if there are any unsaved changes
    hasUnsavedChanges() {
      return (
        Object.keys(this.unsavedChanges.vitals).length > 0 ||
        Object.keys(this.unsavedChanges.forms).length > 0 ||
        this.unsavedChanges.plan
      );
    },

    // Replace the existing handleClone method in NewAppointment.vue
    handleClone(formData) {
      // Create a new instance with empty content
      const newInstance = {
        type: formData.type,
        tabId: null,
        instanceId: this.generateInstanceId(),
        content: "", // Empty content for new instance
        metadata: {}, // Fresh metadata
        isMainForm: formData.isMainForm,
      };

      // If it's a main form, get the templates and title from original form
      if (formData.isMainForm) {
        const baseForm = this.mainForms.find((f) => f.type === formData.type);
        if (baseForm) {
          newInstance.templates = [...baseForm.templates];
          newInstance.title = baseForm.title;
        }
      }

      // If it's a main form, find the correct position to insert
      if (formData.isMainForm) {
        const insertIndex = this.activeFormTypes.findIndex(
          (f) => f.type === formData.type && f.isMainForm
        );
        if (insertIndex !== -1) {
          // Insert after the last instance of this type
          let lastIndex = insertIndex;
          for (let i = insertIndex + 1; i < this.activeFormTypes.length; i++) {
            if (
              this.activeFormTypes[i].type === formData.type &&
              this.activeFormTypes[i].isMainForm
            ) {
              lastIndex = i;
            } else {
              break;
            }
          }
          this.activeFormTypes.splice(lastIndex + 1, 0, newInstance);
        } else {
          this.activeFormTypes.push(newInstance);
        }
      } else {
        this.activeFormTypes.push(newInstance);
      }
    },

    async confirmAction(title, text) {
      const result = await this.$swal.fire({
        title,
        text,
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes",
        cancelButtonText: "Cancel",
      });
      return result.isConfirmed;
    },

    handleInput(form, showMessage = false) {
      // Clear any existing timer for this form
      const timerId = form.type === "plan" ? "plan" : form.instanceId;
      if (this.saveDebounceTimers[timerId]) {
        clearTimeout(this.saveDebounceTimers[timerId]);
      }

      // Set new timer
      this.saveDebounceTimers[timerId] = setTimeout(() => {
        this.saveTab(form, showMessage);
      }, 1000);
    },

    // In NewAppointment.vue, add this method:
    getAllMedicalData() {
      // Get data from main forms
      const mainFormData = this.activeFormTypes
        .filter((form) => form.isMainForm)
        .reduce((acc, form) => {
          if (form.type === "concerns") {
            acc.problems = [...(acc.problems || []), { content: form.content }];
          } else if (form.type === "history") {
            acc.observations = [
              ...(acc.observations || []),
              { content: form.content },
            ];
          } else if (form.type === "examination") {
            acc.notes = [...(acc.notes || []), { content: form.content }];
          }
          return acc;
        }, {});

      // Get data from prescription
      const prescriptionData = this.$refs.prescription_ref
        ? {
          medicines: this.$refs.prescription_ref.prescriptionList || [],
          notes: this.$refs.prescription_ref.notes || "",
        }
        : {};

      // // Combine all data
      // return {
      //   consultationData: {
      //     encounter_id: this.encounterId,
      //     ...this.activeFormTypes.reduce((acc, form) => {
      //       acc[form.type] = [{ content: form.content }];
      //       return acc;
      //     }, {}),
      //   },
      // };

      // Get all form data including active forms and plan
      const allFormData = this.activeFormTypes.reduce((acc, form) => {
        acc[form.type] = [{ content: form.content }];
        return acc;
      }, {});

      // Add plan data if it exists
      if (this.planForm?.content) {
        allFormData.plan = [{ content: this.planForm.content }];
      }

      return {
        consultationData: {
          encounter_id: this.encounterId,
          ...allFormData,
        },
      };
    },

    // Update the saveAllTabs method to show better progress
    async saveAllTabs() {
      if (this.isSaving) return;

      try {
        this.isSaving = true;

        // Show saving toast
        displayMessage("Saving changes...", { timeout: false });

        // Collect all unsaved changes
        const savePromises = [];

        // Save vitals if changed
        // Object.entries(this.vitalSignsData).forEach(([instanceId, data]) => {
        //   if (this.unsavedChanges.vitals[instanceId]) {
        //     savePromises.push(this.saveVitalSignsData(instanceId, data));
        //   }
        // });

        // Save forms if changed
        this.activeFormTypes.forEach((form) => {
          if (this.unsavedChanges.forms[form.instanceId]) {
            savePromises.push(this.saveTab(form, false));
          }
        });

        // Save plan if changed
        if (this.unsavedChanges.plan) {
          savePromises.push(this.saveTab(this.planForm, false));
        }

        // Wait for all saves to complete
        await Promise.all(savePromises);

        // Reset unsaved changes
        this.unsavedChanges = {
          vitals: {},
          forms: {},
          plan: false,
        };

        // Update last saved timestamp
        this.lastSaved = new Date();

        // Show success message
        displayMessage("All changes saved successfully");
      } catch (error) {
        console.error("Error saving changes:", error);
        displayErrorMessage("Failed to save some changes. Please try again.");
      } finally {
        this.isSaving = false;
      }
    },

    async saveTab(form, showMessage = false) {
      if (!form?.type || !form?.content?.trim()) return;

      // For plan form, we don't need instanceId check
      if (form.type !== "plan" && !form?.instanceId) return;

      // Clear any existing timer
      const timerId = form.type === "plan" ? "plan" : form.instanceId;
      if (this.saveDebounceTimers[timerId]) {
        clearTimeout(this.saveDebounceTimers[timerId]);
        delete this.saveDebounceTimers[timerId];
      }

      try {
        // Set saving state
        this.isSaving = true;

        const payload = {
          encounter_id: this.encounterId,
          tab_id: form.tabId,
          type: form.type,
          content: form.content.trim(),
          metadata: form.metadata || {},
        };

        const response = await post("save_encounter_tab", payload);

        if (showMessage) {
          if (response?.data?.status) {
            displayMessage("Details Saved Successfully");
          }
        }

        if (!response?.data?.status) {
          throw new Error(response?.data?.message || "Save failed");
        }

        // Always update the tab ID if available from response
        if (response.data.data?.tab_id) {
          if (form.type === "plan") {
            this.planForm.tabId = response.data.data.tab_id;
          } else {
            form.tabId = response.data.data.tab_id;
          }
        }

        // Clear unsaved changes flag
        if (form.type === "plan") {
          this.unsavedChanges.plan = false;
        } else if (form.instanceId) {
          delete this.unsavedChanges.forms[form.instanceId];
        }

        // Update last saved timestamp
        this.lastSaved = new Date();

        console.log(`Tab ${form.type} saved successfully with ID ${form.tabId}`);

        return response.data;
      } catch (error) {
        console.error("Error saving tab:", error);
        throw error;
      } finally {
        this.isSaving = false;
      }
    },

    capitalizeFirstLetter(string) {
      if (!string) return "";
      return string.charAt(0).toUpperCase() + string.slice(1).toLowerCase();
    },

    // Format relative time for "last saved" indicator
    formatTimeAgo(date) {
      if (!date) return '';

      const seconds = Math.floor((new Date() - date) / 1000);

      // Less than a minute
      if (seconds < 60) {
        return 'just now';
      }

      // Less than an hour
      const minutes = Math.floor(seconds / 60);
      if (minutes < 60) {
        return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
      }

      // Less than a day
      const hours = Math.floor(minutes / 60);
      if (hours < 24) {
        return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
      }

      // Otherwise use standard date format
      return moment(date).format('MMM DD, h:mm A');
    },

    toggleForm(formType) {
      // Create a new instance every time the button is clicked
      const newInstance = {
        type: formType,
        tabId: null,
        instanceId: this.generateInstanceId(),
        content: "", // Empty content for new instance
        metadata: {},
      };

      // Find the base form to copy metadata
      const baseForm = this.medicalForms.find((f) => f.type === formType);
      if (baseForm) {
        newInstance.title = baseForm.title;
        newInstance.templates = [...baseForm.templates];
        newInstance.icon = baseForm.icon;
      }

      this.activeFormTypes.push(newInstance);
    },

    // Update isFormActive to return true if the form type exists (for button styling)
    isFormActive(formType) {
      return this.activeFormTypes.some((form) => form.type === formType);
    },

    // Handle manual save action from MedicalRecordForm
    handleManualSave(formData) {
      console.log('Manual save triggered for:', formData.type);

      if (formData.type === "plan") {
        // Handle plan form save
        this.planForm.content = formData.content;
        this.unsavedChanges.plan = true;
        this.saveTab(this.planForm, true);
      } else {
        // Handle other forms
        const instance = this.activeFormTypes.find(
          (i) => i.type === formData.type && i.instanceId === formData.instanceId
        );

        if (instance) {
          instance.content = formData.content;
          this.unsavedChanges.forms[formData.instanceId] = true;
          this.saveTab(instance, true);
        }
      }
    },

    /**
     * Handles the status update of the encounter.
     * @param {Number|String} status - The new status to set.
     */
    async handleEncounterStatus(status) {
      // Set checkOutVal before any other operations
      this.checkOutVal = status;
      console.log("checkOutVal set in NewAppointment:", this.checkOutVal); // Debug

      const actionText =
        status === "0" ? "close this encounter" : "close and checkout";
      const confirmed = await this.confirmAction(
        "Confirm Action",
        `Are you sure you want to ${actionText}?`
      );

      if (!confirmed) return;

      if (this.isBillModuleActive) {
        // Pass checkOutVal to BillFormModal
        this.generateBillModal = true;
      } else {
        this.handleEncounterstatusUpdate({
          id: this.encounterId,
          status: status,
        });
      }
    },

    async handleBack() {
      if (
        await this.confirmAction(
          "Go Back?",
          "Are you sure you want to go back? Any unsaved changes will be lost."
        )
      ) {
        this.$router.go(-1);
      }
    },

    /**
     * Opens the bill generation modal.
     */
    handleGenerateBill() {
      console.log("encounterData", this.encounterData);
      console.log("patientBillData", this.patientBillData);
      this.generateBillModal = true;
    },

    /**
     * Opens the bill details modal.
     */
    handleBillDetails() {
      this.billDetailsModal = true;
    },

    /**
     * Updates the status of the encounter in the backend.
     * @param {Object} requestData - The data containing encounter ID and new status.
     */
    handleEncounterstatusUpdate(requestData) {
      post("patient_encounter_update_status", {
        id: requestData.id,
        status: requestData.status,
        checkOutVal: this.checkOutVal,
      })
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.encounterData.status = requestData.status;
            displayMessage(response.data.message);
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },

    async removeForm(formType, instanceId) {
      const confirmed = await this.confirmAction(
        "Are you sure ?",
        "You want to remove this"
      );

      if (!confirmed) return;

      const index = this.activeFormTypes.findIndex(
        (item) => item.type === formType && item.instanceId === instanceId
      );

      if (index === -1) return;

      const instance = this.activeFormTypes[index];

      try {
        if (instance.tabId) {
          await post("delete_encounter_tab", {
            tab_id: instance.tabId,
            encounter_id: this.encounterId,
          });
        }

        this.activeFormTypes.splice(index, 1);
      } catch (error) {
        console.error("Error removing form:", error);
      }
    },

    getMedicalFormData(instance) {
      const mainForm = this.mainForms.find((f) => f.type === instance.type);
      if (mainForm) {
        return {
          ...mainForm,
          tabId: instance.tabId,
          instanceId: instance.instanceId,
          content: instance.content,
          metadata: instance.metadata,
        };
      }

      const medicalForm = this.medicalForms.find(
        (f) => f.type === instance.type
      );
      if (medicalForm) {
        return {
          ...medicalForm,
          tabId: instance.tabId,
          instanceId: instance.instanceId,
          content: instance.content,
          metadata: instance.metadata,
        };
      }

      return null;
    },

    // handleVitalsUpdate(vitals) {
    //   console.log("Vitals updated:", vitals);
    // },

    // Modify handleVitalsUpdate to track changes
    handleVitalsUpdate({ instanceId, data }) {
      this.vitalSignsData[instanceId] = data;
      this.unsavedChanges.vitals[instanceId] = true;
    },

    handlePatientUpdated(updatedDetails) {
      this.patientDetails = updatedDetails;
    },

    async handleScheduleFollowup(appointmentData) {
      try {
        console.log("Scheduling follow-up:", appointmentData);
        this.showFollowupModal = false;
      } catch (error) {
        console.error("Error scheduling follow-up:", error);
      }
    },

    async handleBack() {
      if (
        await this.confirmAction(
          "Go Back?",
          "Are you sure you want to go back? Any unsaved changes will be lost."
        )
      ) {
        this.$router.go(-1);
      }
    },

    scheduleFollowUp() {
      this.showFollowupModal = true;
    },

    showEditPatient() {
      this.showEditPatientModal = true;
    },

    // handleSave({ type, content, instanceId = null }) {
    //   if (type === "plan") {
    //     // For plan, update the planForm data
    //     this.planForm.content = content;
    //     this.handleInput(this.planForm, true);
    //   } else {
    //     // Existing logic for other form types
    //     const instance = this.activeFormTypes.find(
    //       (i) => i.type === type && i.instanceId === instanceId
    //     );
    //     if (instance) {
    //       instance.content = content;
    //       this.handleInput(instance, true);
    //     }
    //   }
    // },

    async saveAllTabs_delete() {
      if (this.isSaving) return;

      try {
        this.isSaving = true;

        // Collect all unsaved changes
        const savePromises = [];

        // Save vitals if changed
        Object.entries(this.vitalSignsData).forEach(([instanceId, data]) => {
          if (this.unsavedChanges.vitals[instanceId]) {
            savePromises.push(this.saveVitals(instanceId, data));
          }
        });

        // Save forms if changed
        this.activeFormTypes.forEach((form) => {
          if (this.unsavedChanges.forms[form.instanceId]) {
            savePromises.push(this.saveTab(form, false));
          }
        });

        // Save plan if changed
        if (this.unsavedChanges.plan) {
          savePromises.push(this.saveTab(this.planForm, false));
        }

        // Wait for all saves to complete
        await Promise.all(savePromises);

        // Reset unsaved changes
        this.unsavedChanges = {
          vitals: {},
          forms: {},
          plan: false,
        };

        displayMessage("All changes saved successfully");
      } catch (error) {
        console.error("Error saving changes:", error);
        displayErrorMessage("Failed to save some changes. Please try again.");
      } finally {
        this.isSaving = false;
      }
    },

    handleVitalsClone() {
      const newId = `vitals-${this.vitalSignsInstances.length + 1}`;
      this.vitalSignsInstances.push({ id: newId });
    },

    handleVitalsRemove(instanceId) {
      const index = this.vitalSignsInstances.findIndex(
        (instance) => instance.id === instanceId
      );
      if (index !== -1) {
        this.vitalSignsInstances.splice(index, 1);
        // Clean up the data
        delete this.vitalSignsData[instanceId];
      }
    },

    handleVitalsUpdate({ instanceId, data }) {
      this.vitalSignsData[instanceId] = data;
      this.unsavedChanges.vitals[instanceId] = true;
    },

    // Save vital signs data to the server
    // saveVitalSignsData(instanceId, data) {
    //   if (!this.encounterId || !instanceId || !data) return;

    //   // Create a payload with the vital signs data and encounter ID
    //   const payload = {
    //     encounter_id: this.encounterId,
    //     ...data,
    //     instanceId: instanceId
    //   };

    //   // Save to the server
    //   post("save_encounter_vitals", payload)
    //     .then(response => {
    //       if (response.data.status) {
    //         console.log("Auto-saved vital signs data successfully");

    //         // Update the data with any ID returned from the server
    //         if (response.data.data && response.data.data.id) {
    //           this.vitalSignsData[instanceId] = {
    //             ...this.vitalSignsData[instanceId],
    //             id: response.data.data.id
    //           };
    //         }

    //         // Clear the unsaved changes flag
    //         delete this.unsavedChanges.vitals[instanceId];

    //         // Update last saved timestamp
    //         this.lastSaved = new Date();

    //         // Show success message if enabled
    //         // displayMessage("Vital signs saved successfully");
    //       } else {
    //         console.error("Failed to save vital signs:", response.data.message);
    //       }
    //     })
    //     .catch(error => {
    //       console.error("Error saving vital signs:", error);
    //     });
    // },

    handleVitalsSaveSuccess({ instanceId, data, id, showSwal }) {
      if (showSwal) {
        displayMessage("Vitals Added Successfully");
      }

      // Update the local data with the saved ID
      this.vitalSignsData[instanceId] = {
        ...data,
        id,
      };

      this.refreshRecentVitalsKey++;
    },

    /**
     * Determines if a custom form should be displayed based on conditions.
     * @param {Object} props - The encounter or appointment properties.
     * @param {Object} custom_form_data - The custom form data.
     * @returns {Boolean} - Whether the custom form should be displayed.
     */
    customFormCondition(props, custom_form_data) {
      return (
        props.custom_forms &&
        props.custom_forms.length &&
        (custom_form_data.clinic_ids.length === 0 ||
          custom_form_data.clinic_ids.includes(props.clinic_id)) &&
        ((custom_form_data.module_type === "appointment_module" &&
          props.appointment_id) ||
          custom_form_data.module_type === "patient_encounter_module")
      );
    },

    /**
     * Checks if the billing module is active.
     * @returns {Boolean} - Whether the billing module is active.
     */
    isBillModuleActive() {
      // Check if billing module exists and is enabled in user's module config
      return (
        this.userData?.module?.module_config?.some(
          (module) => module.name === "billing" && module.status === "1"
        ) ?? false
      );
    },

    /**
     * Retrieves enabled encounter modules from user data.
     * @returns {Array} - The enabled encounter modules.
     */
    getEnableEncounter() {
      if (this.userData.encounter_enable_module !== undefined) {
        return this.userData.encounter_enable_module;
      }
      return [];
    },

    /**
     * Determines the number of columns to display based on user add-ons.
     * @returns {Number|String} - The number of columns.
     */
    getEnableCount() {
      if (this.userData.addOns.kiviPro == true) {
        return this.userData.encounter_enable_count;
      } else {
        return "4";
      }
    },

    /**
     * Fetches medical records related to the encounter.
     * @param {Number|String} encounter_id - The ID of the encounter.
     */
    getMedicalRecords(encounter_id) {
      get(
        this.isEncounterTemp
          ? "medical_history_list_from_template"
          : "medical_history_list",
        {
          encounter_id: encounter_id,
        }
      )
        .then((response) => {
          try {
            if (this.$refs.medical_history_note !== undefined) {
              if (this.userData.addOns.kiviPro !== true) {
                this.$refs.medical_history_note.medicalHistoryListLoader = false;
              } else if (this.getEnableEncounter[2]["status"] == 1) {
                this.$refs.medical_history_note.medicalHistoryListLoader = false;
              }
            }
            if (this.$refs.medical_history_observation !== undefined) {
              if (this.userData.addOns.kiviPro !== true) {
                this.$refs.medical_history_observation.medicalHistoryListLoader = false;
              } else if (this.getEnableEncounter[1]["status"] == 1) {
                this.$refs.medical_history_observation.medicalHistoryListLoader = false;
              }
            }
            if (this.$refs.medical_history_problems !== undefined) {
              if (this.userData.addOns.kiviPro !== true) {
                this.$refs.medical_history_problems.medicalHistoryListLoader = false;
              } else if (this.getEnableEncounter[0]["status"] == 1) {
                this.$refs.medical_history_problems.medicalHistoryListLoader = false;
              }
            }

            this.extraClinicalData.forEach((section) => {
              const ref = this.$refs[section.ref];
              if (ref && ref.length > 0) {
                ref[0].medicalHistoryListLoader = false;
              }
            });
          } catch (error) {
            console.log(error);
          }
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            if (
              response.data.data.problem !== undefined &&
              response.data.data.problem.length > 0
            ) {
              if (this.userData.addOns.kiviPro !== true) {
                this.$refs.medical_history_problems.medicalHistoryList =
                  response.data.data.problem;
              } else if (this.getEnableEncounter[0]["status"] == 1) {
                this.$refs.medical_history_problems.medicalHistoryList =
                  response.data.data.problem;
              }
            }
            if (
              response.data.data.observation !== undefined &&
              response.data.data.observation.length > 0
            ) {
              if (this.userData.addOns.kiviPro !== true) {
                this.$refs.medical_history_observation.medicalHistoryList =
                  response.data.data.observation;
              } else if (this.getEnableEncounter[1]["status"] == 1) {
                this.$refs.medical_history_observation.medicalHistoryList =
                  response.data.data.observation;
              }
            }
            if (
              response.data.data.note !== undefined &&
              response.data.data.note.length > 0
            ) {
              if (this.userData.addOns.kiviPro !== true) {
                this.$refs.medical_history_note.medicalHistoryList =
                  response.data.data.note;
              } else if (this.getEnableEncounter[2]["status"] == 1) {
                this.$refs.medical_history_note.medicalHistoryList =
                  response.data.data.note;
              }
            }
            this.extraClinicalData.forEach((section) => {
              if (
                response.data.data[section.type] !== undefined &&
                response.data.data[section.type].length > 0
              ) {
                this.$refs[section.ref][0].medicalHistoryList =
                  response.data.data[section.type];
              }
            });
          }
        })
        .catch((error) => {
          try {
            if (this.$refs.medical_history_note) {
              this.$refs.medical_history_note.medicalHistoryListLoader = false;
            }
            if (this.$refs.medical_history_observation) {
              this.$refs.medical_history_observation.medicalHistoryListLoader = false;
            }
            if (this.$refs.medical_history_problems) {
              this.$refs.medical_history_problems.medicalHistoryListLoader = false;
            }
          } catch (error) {
            console.log(error);
          }
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },

    /**
     * Initiates the print process for the encounter details.
     */
    async printEncounter() {
      if (this.isButtonDisabled) return;

      const confirmed = await this.confirmAction(
        "Print Consultation",
        "Do you want to print this consultation?"
      );

      if (!confirmed) return;

      this.isButtonDisabled = true;
      this.iconClass = "fa fa-spinner fa-spin";

      try {
        // Collect all the data
        const printData = {
          encounter_id: this.encounterId,
          problems: this.$refs.medical_history_problems
            ? this.$refs.medical_history_problems.medicalHistoryList
            : [],
          observations: this.$refs.medical_history_observation
            ? this.$refs.medical_history_observation.medicalHistoryList
            : [],
          notes: this.$refs.medical_history_note
            ? this.$refs.medical_history_note.medicalHistoryList
            : [],
          prescription: this.$refs.prescription_ref
            ? {
              medicines: this.$refs.prescription_ref.prescriptionList || [],
              notes: this.$refs.prescription_ref.notes || "",
            }
            : null,
        };

        const response = await get("get_encounter_print", {
          ...printData,
        });

        if (response?.data?.status && response?.data?.data) {
          // Create a temporary iframe for printing
          const printFrame = document.createElement("iframe");
          printFrame.style.display = "none";
          document.body.appendChild(printFrame);

          const frameDoc = printFrame.contentWindow.document;
          frameDoc.open();
          frameDoc.write(`
                <!DOCTYPE html>
                <html>
                    <head>
                        <title>Print</title>
                        <style>
                            @media print {
                                @page { margin: 2cm; }
                                body { font-family: Arial, sans-serif; }
                                .print-container { width: 100%; }
                                .header { text-align: center; margin-bottom: 20px; }
                                .patient-info { margin-bottom: 20px; }
                                .section { margin-bottom: 15px; }
                                table { width: 100%; border-collapse: collapse; }
                                th, td { padding: 8px; border: 1px solid #ddd; }
                                .signature { margin-top: 50px; text-align: right; }
                            }
                        </style>
                    </head>
                    <body>${response.data.data}</body>
                </html>
            `);
          frameDoc.close();

          // Wait for content and images to load
          setTimeout(() => {
            printFrame.contentWindow.print();
            // Remove the frame after printing
            setTimeout(() => {
              document.body.removeChild(printFrame);
            }, 500);
          }, 500);
        } else {
          throw new Error(
            response?.data?.message || "Failed to generate print data"
          );
        }
      } catch (error) {
        console.error("Print error:", error);
        this.$bvToast.toast("Failed to prepare print data. Please try again.", {
          title: "Error",
          variant: "danger",
          solid: true,
        });
      } finally {
        this.isButtonDisabled = false;
        this.iconClass = "fa fa-print";
      }
    },

    /**
     * Handles the successful saving of a bill.
     * @param {Object} data - The data returned after saving the bill.
     */
    handleBillSave(data) {
      this.billModel = false;
      this.createBillButton = false;
      // Update encounter status based on payment
      this.encounterData.status =
        data.payment_status && data.payment_status === "paid" ? 0 : 1;
      this.getEncounterBill();
    },

    /**
     * Fetches custom fields related to the encounter from the backend.
     */
    getEncounterCustomField() {
      get("get_custom_fields", {
        module_type: "patient_encounter_module",
        module_id: this.encounterId,
        doctor_id: this.encounterData?.doctor_id,
      })
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.customFieldsObj = response.data.data;
            this.customFieldsLength = response.data.data.length;
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
  },
};
</script>
<style scoped>
.wave-gradient {
  background: linear-gradient(45deg,
      rgba(248, 231, 255, 1) 0%,
      rgba(255, 255, 255, 0.9) 50%,
      rgba(248, 231, 255, 1) 100%);
  background-size: 200% 200%;
  animation: waveMove 6s ease infinite;
}

@keyframes waveMove {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}
</style>