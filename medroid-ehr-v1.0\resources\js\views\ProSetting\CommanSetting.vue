<template>
  <div class="bg-white rounded-lg shadow-sm">
    <NotificationTestModal ref="NotificationTestModal"></NotificationTestModal>

    <div v-if="formLoader" class="w-full h-64 flex items-center justify-center">
      <loader-component-2></loader-component-2>
    </div>

    <div v-else class="w-full">
      <!-- Pro Settings Overlay -->
      <div
        class="relative"
        :class="{ 'pointer-events-none': userData.addOns.kiviPro != true }"
      >
        <div
          v-if="userData.addOns.kiviPro != true"
          class="absolute inset-0 bg-white bg-opacity-75 z-10"
        >
          <overlay-message addon_type="pro"></overlay-message>
        </div>

        <!-- Header Section -->
        <div class="px-6 py-4">
          <div class="flex items-center justify-between">
            <h2 class="text-2xl font-semibold">
              {{ formTranslation.settings.pro_settings }}
              <a
                v-if="request_status == 'off'"
                href="https://apps.medroid.ai/docs/product/kivicare/pro-version/"
                target="_blank"
                class="text-gray-500 hover:text-gray-700"
              >
                <i class="fa fa-question-circle"></i>
              </a>
            </h2>
          </div>
        </div>

        <hr class="border-gray-200" />

        <!-- Theme Settings -->
        <form id="uploadFile" class="p-6" @submit.prevent>
          <div class="mb-6">
            <h2 class="text-xl font-semibold mb-4">
              {{ formTranslation.pro_setting.theme_setting }}
            </h2>

            <!-- Site Logo Upload Section -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
              <!-- Header -->
              <div class="flex items-center mb-4">
                <div class="bg-blue-600 p-2 rounded-lg">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-6 w-6 text-white"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                    />
                  </svg>
                </div>
                <div class="ml-4">
                  <h2 class="text-lg font-semibold text-gray-900">
                    Site Logo Upload
                  </h2>
                  <p class="text-sm text-gray-500">
                    Configure your clinic's logo settings
                  </p>
                </div>
              </div>

              <!-- Upload Section -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-gray-50 rounded-lg p-6">
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    Upload Logo
                  </label>
                  <div class="flex items-center">
                    <div class="relative">
                      <input
                        v-if="isMediaUpload === undefined"
                        type="file"
                        id="siteLogo"
                        class="hidden"
                        accept="image/*"
                        @change="uploadsiteLogo"
                      />
                      <button
                        type="button"
                        class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        @click="
                          isMediaUpload !== undefined
                            ? uploadsiteLogo()
                            : document.getElementById('siteLogo').click()
                        "
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-5 w-5 mr-2"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"
                          />
                        </svg>
                        {{ formTranslation.common.choose_file }}
                      </button>
                    </div>
                    <span
                      v-if="isMediaUpload !== undefined"
                      class="ml-3 text-sm text-gray-500 bg-white px-3 py-2 rounded-lg border border-gray-200"
                    >
                      {{ new_site_logo }}
                    </span>
                  </div>
                </div>

                <!-- Logo Preview -->
                <div
                  class="bg-gray-50 rounded-lg p-6 flex items-center justify-center"
                >
                  <div class="w-full max-w-xs">
                    <img
                      v-if="
                        getSiteLogo != -1 &&
                        getSiteLogo != null &&
                        getSiteLogo != ''
                      "
                      :src="getSiteLogo"
                      class="max-h-24 w-auto mx-auto"
                      alt="Site Logo"
                    />
                    <img
                      v-else
                      :src="logoURL"
                      class="max-h-24 w-auto mx-auto"
                      alt="Default Logo"
                    />
                  </div>
                </div>
              </div>

              <!-- Save Button -->
              <div class="mt-6 flex justify-end">
                <button
                  type="button"
                  class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 mr-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                  Save
                </button>
              </div>
            </div>

            <!-- Site Loader Upload Section -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
              <!-- Header -->
              <div class="flex items-center mb-4">
                <div class="bg-blue-600 p-2 rounded-lg">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-6 w-6 text-white"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                    />
                  </svg>
                </div>
                <div class="ml-4">
                  <h2 class="text-lg font-semibold text-gray-900">
                    Site Loader Upload
                  </h2>
                  <p class="text-sm text-gray-500">
                    Configure your site's loading animation
                  </p>
                </div>
              </div>

              <!-- Upload Section -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-gray-50 rounded-lg p-6">
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    {{ formTranslation.pro_setting.set_site_loader }}
                  </label>
                  <div class="flex items-center">
                    <div class="relative">
                      <input
                        v-if="isMediaUpload === undefined"
                        type="file"
                        id="siteLoader"
                        class="hidden"
                        accept="image/*"
                        @change="uploadSiteLoader"
                      />
                      <button
                        type="button"
                        class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        @click="
                          isMediaUpload !== undefined
                            ? uploadSiteLoader()
                            : document.getElementById('siteLoader').click()
                        "
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-5 w-5 mr-2"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"
                          />
                        </svg>
                        {{ formTranslation.common.choose_file }}
                      </button>
                    </div>
                    <span
                      v-if="isMediaUpload !== undefined"
                      class="ml-3 text-sm text-gray-500 bg-white px-3 py-2 rounded-lg border border-gray-200"
                    >
                      {{ new_site_loader }}
                    </span>
                  </div>
                </div>

                <!-- Loader Preview -->
                <div
                  class="bg-gray-50 rounded-lg p-6 flex items-center justify-center"
                >
                  <div class="w-full max-w-xs">
                    <img
                      :src="loaderURL"
                      class="max-h-24 w-auto mx-auto"
                      alt="Site Loader"
                    />
                  </div>
                </div>
              </div>

              <!-- Save Button -->
              <div class="mt-6 flex justify-end">
                <button
                  type="button"
                  class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5 mr-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                  Save
                </button>
              </div>
            </div>
            <!-- Theme Color Picker Section -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
              <!-- Header -->
              <div class="flex items-center mb-4">
                <div class="bg-black p-2 rounded-lg">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-6 w-6 text-white"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01"
                    />
                  </svg>
                </div>
                <div class="ml-4">
                  <h2 class="text-lg font-semibold text-gray-900">
                    Theme Color
                  </h2>
                  <p class="text-sm text-gray-500">
                    Configure your site's color scheme
                  </p>
                </div>
              </div>

              <!-- Color Picker -->
              <div class="bg-gray-50 rounded-lg p-6">
                <label class="block text-sm font-medium text-gray-700 mb-4">
                  {{ formTranslation.pro_setting.set_theme_color }}
                </label>
                <div class="flex items-center gap-4">
                  <div class="overflow-hidden rounded-lg">
                    <input
                      type="color"
                      v-model="themeColor"
                      @change="setSiteThemeColor"
                      class="h-12 w-24 cursor-pointer border-0 bg-transparent p-0"
                    />
                  </div>
                  <span class="text-sm text-gray-600">
                    Selected color: {{ themeColor }}
                  </span>
                </div>
              </div>
            </div>

            <!-- RTL Mode Toggle -->
            <div class="mt-6">
              <label class="inline-flex items-center">
                <input
                  type="checkbox"
                  v-model="rtlMode"
                  :true-value="'true'"
                  :false-value="'false'"
                  @change="setSiteRTLMode"
                  class="form-checkbox h-5 w-5 text-primary rounded"
                />
                <span class="ml-2 text-sm font-medium text-gray-700">
                  {{ formTranslation.pro_setting.rtl_mode }}
                </span>
              </label>
            </div>
          </div>
        </form>

        <hr class="border-gray-200" />

        <!-- WordPress Logo Section -->
        <form id="wordpress-logo" class="p-6" @submit.prevent>
          <h2 class="text-xl font-semibold mb-4">WordPress Logo</h2>

          <div class="space-y-4">
            <!-- WordPress Logo Toggle -->
            <label class="inline-flex items-center">
              <input
                type="checkbox"
                v-model="EnableWordpressStatus"
                @change="setWordpressLogoStatus"
                class="form-checkbox h-5 w-5 text-primary rounded"
                :true-value="'1'"
                :false-value="'0'"
              />
              <span class="ml-2 text-sm font-medium text-gray-700">
                {{ formTranslation.common.enable_wordpress_logo_status }}
              </span>
            </label>

            <!-- WordPress Logo Upload -->
            <div
              v-if="EnableWordpressStatus === '1'"
              class="grid grid-cols-1 md:grid-cols-2 gap-6"
            >
              <div>
                <div class="flex">
                  <button
                    type="button"
                    class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark"
                    @click.prevent="saveWordpressLogoImage"
                  >
                    {{ formTranslation.common.choose_file }}
                  </button>
                  <span class="ml-2 px-3 py-2 bg-gray-100 rounded-lg">
                    {{ new_wordpress_logo }}
                  </span>
                </div>
              </div>

              <!-- WordPress Logo Preview -->
              <div class="flex items-center">
                <img
                  :src="wordpressLogoImagePreview"
                  class="max-h-24 w-auto"
                  alt="WordPress Logo"
                />
              </div>
            </div>
          </div>
        </form>

        <!-- Custom Notification Section -->
        <hr class="border-gray-200" />
        <div class="p-6">
          <h2 class="text-xl font-semibold mb-4">
            {{ formTranslation.common.custom_notification }}
          </h2>

          <div class="space-y-4">
            <!-- SMS Toggle -->
            <label class="inline-flex items-center">
              <input
                type="checkbox"
                v-model="customNotification.enableSMS"
                @change="saveCustomNotification('sms')"
                class="form-checkbox h-5 w-5 text-primary rounded"
                value="yes"
                :false-value="'no'"
              />
              <span class="ml-2 text-sm font-medium text-gray-700">
                {{ formTranslation.common.enable_sms }}
              </span>
            </label>

            <!-- WhatsApp Toggle -->
            <label class="inline-flex items-center">
              <input
                type="checkbox"
                v-model="customNotification.enableWhatsapp"
                @change="saveCustomNotification('whatsapp')"
                class="form-checkbox h-5 w-5 text-primary rounded"
                value="yes"
                :false-value="'no'"
              />
              <span class="ml-2 text-sm font-medium text-gray-700">
                {{ formTranslation.common.enable_whatsapp }}
              </span>
            </label>
          </div>
        </div>

        <!-- Twilio Account Settings -->
        <hr class="border-gray-200" />
        <div class="p-6">
          <h2 class="text-xl font-semibold mb-4">
            {{ formTranslation.pro_setting.twilio_account_setting }}
          </h2>

          <div class="grid grid-cols-1 lg:grid-cols-5 gap-6">
            <!-- SMS Configuration -->
            <div class="lg:col-span-2">
              <form
                id="smsForm"
                @submit.prevent="saveSmsConfigData('submit')"
                novalidate
              >
                <!-- SMS Toggle -->
                <label class="inline-flex items-center mb-4">
                  <input
                    type="checkbox"
                    v-model="smsConfigData.enableSMS"
                    @change="saveSmsConfigData('checkbox')"
                    class="form-checkbox h-5 w-5 text-primary rounded"
                    :true-value="'true'"
                    :false-value="'false'"
                  />
                  <span class="ml-2 text-sm font-medium text-gray-700">
                    {{ formTranslation.pro_setting.twilo_sms_configration }}
                  </span>
                </label>

                <div
                  v-if="smsConfigData.enableSMS === 'true'"
                  class="space-y-4"
                >
                  <!-- Account SID -->
                  <div>
                    <label
                      for="account_id"
                      class="block text-sm font-medium text-gray-700 mb-1"
                    >
                      {{ formTranslation.pro_setting.account_sid }}
                    </label>
                    <input
                      type="text"
                      id="account_id"
                      v-model="smsConfigData.account_id"
                      :placeholder="formTranslation.settings.plh_enter_acc_sid"
                      :class="{
                        'border-red-500':
                          smsSubmitted && $v.smsConfigData.account_id.$error,
                      }"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary"
                    />
                    <p
                      v-if="smsSubmitted && $v.smsConfigData.account_id.$error"
                      class="mt-1 text-sm text-red-600"
                    >
                      {{
                        formTranslation.pro_setting.account_sid +
                        " " +
                        formTranslation.common.required
                      }}
                    </p>
                  </div>

                  <!-- Auth Token -->
                  <div>
                    <label
                      for="auth_token"
                      class="block text-sm font-medium text-gray-700 mb-1"
                    >
                      {{ formTranslation.pro_setting.auth_token }}
                    </label>
                    <input
                      type="text"
                      id="auth_token"
                      v-model="smsConfigData.auth_token"
                      :placeholder="formTranslation.settings.plh_auth_token"
                      :class="{
                        'border-red-500':
                          smsSubmitted && $v.smsConfigData.auth_token.$error,
                      }"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary"
                    />
                    <p
                      v-if="smsSubmitted && $v.smsConfigData.auth_token.$error"
                      class="mt-1 text-sm text-red-600"
                    >
                      {{
                        formTranslation.pro_setting.auth_token +
                        " " +
                        formTranslation.common.required
                      }}
                    </p>
                  </div>

                  <!-- Phone Number -->
                  <div>
                    <label
                      for="to_number"
                      class="block text-sm font-medium text-gray-700 mb-1"
                    >
                      {{ formTranslation.pro_setting.phone_number }}
                    </label>
                    <input
                      type="text"
                      id="to_number"
                      v-model="smsConfigData.to_number"
                      :placeholder="formTranslation.settings.plh_enter_number"
                      :class="{
                        'border-red-500':
                          smsSubmitted && $v.smsConfigData.to_number.$error,
                      }"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary"
                    />
                    <p
                      v-if="smsSubmitted && $v.smsConfigData.to_number.$error"
                      class="mt-1 text-sm text-red-600"
                    >
                      {{
                        formTranslation.pro_setting.phone_number +
                        " " +
                        formTranslation.common.required
                      }}
                    </p>
                  </div>

                  <!-- Action Buttons -->
                  <div class="flex justify-end space-x-2">
                    <button
                      type="button"
                      class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark"
                      @click="
                        $refs.NotificationTestModal.modalOpen = true;
                        $refs.NotificationTestModal.notificationType = 'sms';
                      "
                    >
                      <i class="fas fa-sms mr-1"></i>
                      {{ formTranslation.widgets.send_test_sms }}
                    </button>
                    <!-- Previous code remains the same -->

                    <button
                      v-if="!smsConfigDataLoading"
                      type="submit"
                      class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark"
                    >
                      <i class="fa fa-save mr-1"></i>
                      {{ formTranslation.common.save }}
                    </button>
                    <button
                      v-else
                      type="submit"
                      disabled
                      class="px-4 py-2 bg-gray-400 text-white rounded-lg"
                    >
                      <i class="fa fa-sync fa-spin mr-1"></i>
                      {{ formTranslation.common.loading }}
                    </button>
                  </div>
                </div>
              </form>
            </div>

            <!-- Twilio Guide -->
            <div
              v-if="smsConfigData.enableSMS === 'true'"
              class="lg:col-span-3"
            >
              <twillo-guide twillo-type="sms"></twillo-guide>
            </div>
          </div>

          <!-- WhatsApp Configuration -->
          <div
            v-if="userData.pro_version >= '1.2.0'"
            class="grid grid-cols-1 lg:grid-cols-5 gap-6 mt-6"
          >
            <div class="lg:col-span-2">
              <form
                id="kcwhatsAppForm"
                @submit.prevent="saveWhatsAppConfigData('submit')"
                novalidate
              >
                <!-- WhatsApp Toggle -->
                <label class="inline-flex items-center mb-4">
                  <input
                    type="checkbox"
                    v-model="whatsAppConfigData.enableWhatsApp"
                    @change="saveWhatsAppConfigData('checkbox')"
                    class="form-checkbox h-5 w-5 text-primary rounded"
                    :true-value="'true'"
                    :false-value="'false'"
                  />
                  <span class="ml-2 text-sm font-medium text-gray-700">
                    {{
                      formTranslation.pro_setting.twilo_whatsapp_configration
                    }}
                  </span>
                </label>

                <div
                  v-if="whatsAppConfigData.enableWhatsApp === 'true'"
                  class="space-y-4"
                >
                  <!-- WhatsApp Account SID -->
                  <div>
                    <label
                      for="wa_account_id"
                      class="block text-sm font-medium text-gray-700 mb-1"
                    >
                      {{ formTranslation.pro_setting.account_sid }}
                    </label>
                    <input
                      type="text"
                      id="wa_account_id"
                      v-model="whatsAppConfigData.wa_account_id"
                      :placeholder="formTranslation.settings.plh_enter_acc_sid"
                      :class="{
                        'border-red-500':
                          whatSubmitted &&
                          $v.whatsAppConfigData.wa_account_id.$error,
                      }"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary"
                    />
                    <p
                      v-if="
                        whatSubmitted &&
                        $v.whatsAppConfigData.wa_account_id.$error
                      "
                      class="mt-1 text-sm text-red-600"
                    >
                      {{
                        formTranslation.pro_setting.account_sid +
                        " " +
                        formTranslation.common.required
                      }}
                    </p>
                  </div>

                  <!-- WhatsApp Auth Token -->
                  <div>
                    <label
                      for="wa_auth_token"
                      class="block text-sm font-medium text-gray-700 mb-1"
                    >
                      {{ formTranslation.pro_setting.auth_token }}
                    </label>
                    <input
                      type="text"
                      id="wa_auth_token"
                      v-model="whatsAppConfigData.wa_auth_token"
                      :placeholder="formTranslation.settings.plh_auth_token"
                      :class="{
                        'border-red-500':
                          whatSubmitted &&
                          $v.whatsAppConfigData.wa_auth_token.$error,
                      }"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary"
                    />
                    <p
                      v-if="
                        whatSubmitted &&
                        $v.whatsAppConfigData.wa_auth_token.$error
                      "
                      class="mt-1 text-sm text-red-600"
                    >
                      {{
                        formTranslation.pro_setting.auth_token +
                        " " +
                        formTranslation.common.required
                      }}
                    </p>
                  </div>

                  <!-- WhatsApp Phone Number -->
                  <div>
                    <label
                      for="wa_to_number"
                      class="block text-sm font-medium text-gray-700 mb-1"
                    >
                      {{ formTranslation.pro_setting.phone_number }}
                    </label>
                    <input
                      type="text"
                      id="wa_to_number"
                      v-model="whatsAppConfigData.wa_to_number"
                      :placeholder="formTranslation.settings.plh_enter_number"
                      :class="{
                        'border-red-500':
                          whatSubmitted &&
                          $v.whatsAppConfigData.wa_to_number.$error,
                      }"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary"
                    />
                    <p
                      v-if="
                        whatSubmitted &&
                        $v.whatsAppConfigData.wa_to_number.$error
                      "
                      class="mt-1 text-sm text-red-600"
                    >
                      {{
                        formTranslation.pro_setting.phone_number +
                        " " +
                        formTranslation.common.required
                      }}
                    </p>
                  </div>

                  <!-- Action Buttons -->
                  <div class="flex justify-end space-x-2">
                    <button
                      type="button"
                      class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark"
                      @click="
                        $refs.NotificationTestModal.modalOpen = true;
                        $refs.NotificationTestModal.notificationType =
                          'whatsapp';
                      "
                    >
                      <i class="fab fa-whatsapp mr-1"></i>
                      {{ formTranslation.widgets.send_test_whatsapp }}
                    </button>
                    <button
                      v-if="!whatsappConfigDataLoading"
                      type="submit"
                      class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark"
                    >
                      <i class="fa fa-save mr-1"></i>
                      {{ formTranslation.common.save }}
                    </button>
                    <button
                      v-else
                      type="submit"
                      disabled
                      class="px-4 py-2 bg-gray-400 text-white rounded-lg"
                    >
                      <i class="fa fa-sync fa-spin mr-1"></i>
                      {{ formTranslation.common.loading }}
                    </button>
                  </div>
                </div>
              </form>
            </div>

            <!-- WhatsApp Guide -->
            <div
              v-if="whatsAppConfigData.enableWhatsApp === 'true'"
              class="lg:col-span-3"
            >
              <twillo-guide twillo-type="whatapps"></twillo-guide>
            </div>
          </div>
        </div>

        <!-- Google Calendar Settings -->
        <hr class="border-gray-200" />
        <div class="p-6">
          <h2 class="text-xl font-semibold mb-4">
            {{ formTranslation.pro_setting.google_account_setting }}
          </h2>

          <div class="grid grid-cols-1 lg:grid-cols-5 gap-6">
            <div class="lg:col-span-2">
              <form
                id="googleCalform"
                @submit.prevent="saveGoogleCalenderConfigData('submit')"
                novalidate
              >
                <!-- Google Calendar Toggle -->
                <label class="inline-flex items-center mb-4">
                  <input
                    type="checkbox"
                    v-model="googleCalData.enableCal"
                    @change="saveGoogleCalenderConfigData('checkbox')"
                    class="form-checkbox h-5 w-5 text-primary rounded"
                    :true-value="'true'"
                    :false-value="'false'"
                  />
                  <span class="ml-2 text-sm font-medium text-gray-700">
                    {{ formTranslation.common.google_calendar_configuration }}
                  </span>
                </label>

                <div
                  v-if="googleCalData.enableCal === 'true'"
                  class="space-y-4"
                >
                  <!-- Client ID -->
                  <div>
                    <label
                      for="client_id"
                      class="block text-sm font-medium text-gray-700 mb-1"
                    >
                      {{ formTranslation.common.google_calendar_client_id }}
                    </label>
                    <input
                      type="text"
                      id="client_id"
                      v-model="googleCalData.client_id"
                      :class="{
                        'border-red-500':
                          googlecalendarSubmitted &&
                          $v.googleCalData.client_id.$error,
                      }"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary"
                    />
                    <p
                      v-if="
                        googlecalendarSubmitted &&
                        $v.googleCalData.client_id.$error
                      "
                      class="mt-1 text-sm text-red-600"
                    >
                      {{
                        formTranslation.common.google_calendar_client_id +
                        " " +
                        formTranslation.common.required
                      }}
                    </p>
                  </div>

                  <!-- Client Secret -->
                  <div>
                    <label
                      for="client_secret"
                      class="block text-sm font-medium text-gray-700 mb-1"
                    >
                      {{ formTranslation.common.google_calendar_client_secret }}
                    </label>
                    <input
                      type="text"
                      id="client_secret"
                      v-model="googleCalData.client_secret"
                      :class="{
                        'border-red-500':
                          googlecalendarSubmitted &&
                          $v.googleCalData.client_secret.$error,
                      }"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary"
                    />
                    <p
                      v-if="
                        googlecalendarSubmitted &&
                        $v.googleCalData.client_secret.$error
                      "
                      class="mt-1 text-sm text-red-600"
                    >
                      {{
                        formTranslation.common.google_calendar_client_secret +
                        " " +
                        formTranslation.common.required
                      }}
                    </p>
                  </div>

                  <!-- App Name -->
                  <div>
                    <label
                      for="app_name"
                      class="block text-sm font-medium text-gray-700 mb-1"
                    >
                      {{ formTranslation.common.app_name }}
                    </label>
                    <input
                      type="text"
                      id="app_name"
                      v-model="googleCalData.app_name"
                      :class="{
                        'border-red-500':
                          googlecalendarSubmitted &&
                          $v.googleCalData.app_name.$error,
                      }"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary"
                    />
                    <p
                      v-if="
                        googlecalendarSubmitted &&
                        $v.googleCalData.app_name.$error
                      "
                      class="mt-1 text-sm text-red-600"
                    >
                      {{
                        formTranslation.common.app_name +
                        " " +
                        formTranslation.common.required
                      }}
                    </p>
                  </div>

                  <!-- Save Button -->
                  <div class="flex justify-end">
                    <button
                      v-if="!loading"
                      type="submit"
                      class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark"
                    >
                      <i class="fa fa-save mr-1"></i>
                      {{ formTranslation.common.save }}
                    </button>
                    <button
                      v-else
                      type="submit"
                      disabled
                      class="px-4 py-2 bg-gray-400 text-white rounded-lg"
                    >
                      <i class="fa fa-sync fa-spin mr-1"></i>
                      {{ formTranslation.common.loading }}
                    </button>
                  </div>
                </div>
              </form>
            </div>

            <!-- Google Calendar Guide -->
            <div
              v-if="googleCalData.enableCal === 'true'"
              class="lg:col-span-3"
            >
              <h4 class="text-lg font-medium mb-3">
                {{ formTranslation.common.guide_to_setup_google_calender }}
              </h4>
              <div class="bg-white rounded-lg border border-gray-200">
                <div class="p-4">
                  {{ "Step:1" }}
                  <a
                    href="https://apps.medroid.ai/docs/product/kivicare/google-calendar/"
                    target="_blank"
                    class="text-primary hover:text-primary-dark ml-1"
                  >
                    {{ formTranslation.pro_setting.please_refer_link }}
                  </a>
                </div>
              </div>
            </div>
          </div>

          <!-- Patient Calendar Setting -->
          <div class="mt-6">
            <label class="inline-flex items-center">
              <input
                type="checkbox"
                v-model="patientCalOn"
                @change="savePatientCalendarStatus"
                class="form-checkbox h-5 w-5 text-primary rounded"
              />
              <span class="ml-2 text-sm font-medium text-gray-700">
                {{
                  formTranslation.common
                    .setting_for_add_event_in_calendar_for_patient
                }}
              </span>
            </label>
          </div>
        </div>

        <!-- Consultations Settings -->
        <hr class="border-gray-200" />
        <div class="p-6">
          <h2 class="text-xl font-semibold mb-4">
            {{
              formTranslation.patient_encounter.encounters +
              " " +
              formTranslation.common.settings
            }}
          </h2>

          <div class="space-y-4">
            <!-- Clinical Details Toggle -->
            <label class="inline-flex items-center">
              <input
                type="checkbox"
                v-model="clinicalDetailInclude"
                @change="saveEncounterClinicalDetailsIncludeInPrescriptionPrint"
                class="form-checkbox h-5 w-5 text-primary rounded"
              />
              <span class="ml-2 text-sm font-medium text-gray-700">
                {{ formTranslation.pro_setting.clinical_detail }}
              </span>
            </label>

            <!-- Custom Fields Toggle -->
            <label class="inline-flex items-center">
              <input
                type="checkbox"
                v-model="encounterCustomFieldInclude"
                @change="saveEncounterCustomFieldIncludeInPrescriptionPrint"
                class="form-checkbox h-5 w-5 text-primary rounded"
              />
              <span class="ml-2 text-sm font-medium text-gray-700">
                {{
                  formTranslation.pro_setting
                    .include_encounter_custom_fields_in_print
                }}
              </span>
            </label>

            <!-- Hide Clinical Details Toggle -->
            <label class="inline-flex items-center">
              <!-- Previous code remains the same -->
              <input
                type="checkbox"
                v-model="clinicalDetailHideInPatient"
                @change="saveEncounterClinicalDetailsHideInPatientDashboard"
                class="form-checkbox h-5 w-5 text-primary rounded"
              />
              <span class="ml-2 text-sm font-medium text-gray-700">
                {{ formTranslation.pro_setting.clinical_detail_patient_hide }}
              </span>
            </label>
          </div>
        </div>

        <!-- Copyright Text Settings -->
        <hr class="border-gray-200" />
        <div class="p-6">
          <h2 class="text-xl font-semibold mb-4">
            {{ formTranslation.pro_setting.copy_right_text }}
          </h2>

          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                {{ formTranslation.pro_setting.change_copy_right_text }}
              </label>
              <input
                type="text"
                v-model="copyrighttext"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary"
              />
            </div>

            <div class="flex justify-end">
              <button
                type="button"
                @click="saveSiteCopyrightText"
                :disabled="copyrighttextLoading"
                class="px-4 py-2 bg-black text-white rounded-lg hover:bg-black disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                <i
                  :class="
                    copyrighttextLoading
                      ? 'fa fa-spinner fa-spin'
                      : 'fa fa-save'
                  "
                  class="mr-1"
                ></i>
                {{ formTranslation.common.save }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { post, get } from "../../config/request";
import { Chrome } from "vue-color";
import {
  required,
  numeric,
  alpha,
  email,
  minLength,
  maxLength,
} from "vuelidate/lib/validators";
import twilloGuide from "./twilloGuide";
import { phoneNumber } from "../../config/helper";
import NotificationTestModal from "../Notification/NotificationTestModal";
export default {
  name: "CommanSetting",
  components: {
    "chrome-color-picker": Chrome,
    twilloGuide,
    NotificationTestModal,
  },
  data: () => {
    return {
      isMediaUpload: undefined,
      smsSubmitted: false,
      request_status: "off",
      whatSubmitted: false,
      on: false,
      themeColor: "#000000",
      hex: "",
      isOpen: false,
      isEnabled: "",
      imagePreview:
        window.request_data.kiviCarePluginURL + "assets/images/logo-banner.png",
      rtlMode: false,
      loading: false,
      smsConfigDataLoading: false,
      whatsappConfigDataLoading: false,
      submitted: false,
      smsConfigData: {},
      whatsAppConfigData: {},
      googleCalData: {},
      clinicalDetailHideInPatient: false,
      langsOption: [],
      patientCalOn: false,
      clinicalDetailInclude: false,
      encounterCustomFieldInclude: false,
      logoURL:
        window.request_data.kiviCarePluginURL + "assets/images/logo-banner.png",
      loaderURL: window.request_data.loaderImage,
      EnableWordpressStatus: false,
      wordpressLogoImagePreview: window.request_data.wordpress_logo,
      copyrighttext: window.request_data.copyrightText,
      copyrighttextLoading: false,
      formLoader: true,
      googlecalendarSubmitted: false,
      smsConfigSubmitted: false,
      whatsappConfigSubmitted: false,
      new_site_logo: "",
      new_site_loader: "",
      wordpress_logo: "",
      customNotification: {
        enableSMS: "no",
        enableWhatsapp: "no",
      },
    };
  },
  validations: {
    smsConfigData: {
      to_number: {
        required,
      },
      account_id: {
        required,
      },
      auth_token: {
        required,
      },
    },
    whatsAppConfigData: {
      wa_to_number: {
        required,
      },
      wa_account_id: {
        required,
      },
      wa_auth_token: {
        required,
      },
    },
    googleCalData: {
      enableCal: {
        required,
      },
      client_id: {
        required,
      },
      client_secret: {
        required,
      },
      app_name: {
        required,
      },
    },
  },
  mounted() {
    this.new_site_logo = this.formTranslation.common.no_file_chosen;
    this.new_site_loader = this.formTranslation.common.no_file_chosen;
    this.new_wordpress_logo = this.formTranslation.common.no_file_chosen;
    this.smsConfigData = this.defaultSMSData();
    this.whatsAppConfigData = this.defaultWhatsAppData();
    this.googleCalData = this.defaultGoogleData();
    this.getAllProSettingsData();
    this.themeColor = this.getColor;
    this.hex = this.themeColor;
    document.documentElement.style.setProperty("--primary", this.getColor);
    if (this.getMode == "true") {
      this.rtlMode = true;
      document.body.classList.add("rtl");
      var h1 = document.getElementsByTagName("html")[0]; // Get the first <h1> element in the document
      var att = document.createAttribute("dir"); // Create a "class" attribute
      att.value = "rtl"; // Set the value of the class attribute
      h1.setAttributeNode(att);
    } else {
      document.body.classList.remove("rtl");
      document.getElementsByTagName("html")[0].removeAttribute("dir");
    }
    this.getModule();
    this.isMediaUpload = window.wp.media();
  },
  methods: {
    defaultSMSData() {
      return {
        account_id: "",
        auth_token: "",
        to_number: "",
        enableSMS: false,
      };
    },
    defaultWhatsAppData() {
      return {
        wa_account_id: "",
        wa_auth_token: "",
        wa_to_number: "",
        enableWhatsApp: false,
      };
    },
    defaultGoogleData() {
      return {
        client_id: "",
        client_secret: "",
        app_name: "",
        enableCal: false,
      };
    },
    getAllProSettingsData() {
      if (this.userData.addOns.kiviPro !== true) {
        this.formLoader = false;
        return;
      }
      this.formLoader = true;
      get("get_all_pro_settings_value", {})
        .then((response) => {
          this.formLoader = false;

          if (
            response.data.whatsapp.status !== undefined &&
            response.data.whatsapp.status === true
          ) {
            this.whatsAppConfigData = response.data.whatsapp.data;
          }
          if (
            response.data.sms.status !== undefined &&
            response.data.sms.status === true
          ) {
            this.smsConfigData = response.data.sms.data;
          }

          if (
            response.data.google_calendar !== undefined &&
            response.data.google_calendar.status !== undefined &&
            response.data.google_calendar.status === true
          ) {
            this.googleCalData = response.data.google_calendar.data;
          }
          if (
            response.data.patient_calendar !== undefined &&
            response.data.patient_calendar.status !== undefined &&
            response.data.patient_calendar.status === true
          ) {
            this.patientCalOn = response.data.patient_calendar.data;
          }

          if (
            response.data.encounter_clinical_detail !== undefined &&
            response.data.encounter_clinical_detail.status !== undefined &&
            response.data.encounter_clinical_detail.status === true
          ) {
            this.clinicalDetailInclude =
              response.data.encounter_clinical_detail.data == "true" ||
              response.data.encounter_clinical_detail.data == true
                ? true
                : false;
            this.clinicalDetailHideInPatient =
              response.data.encounter_clinical_detail.hideInPatient == "true" ||
              response.data.encounter_clinical_detail.hideInPatient == true
                ? true
                : false;
            this.encounterCustomFieldInclude =
              response.data.encounter_clinical_detail.custom_field == "true" ||
              response.data.encounter_clinical_detail.custom_field == true
                ? true
                : false;
          }
          if (
            response.data.wordpress_logo_data !== undefined &&
            response.data.wordpress_logo_data.status !== undefined &&
            response.data.wordpress_logo_data.logo !== undefined
          ) {
            this.EnableWordpressStatus =
              response.data.wordpress_logo_data.status;
            this.wordpressLogoImagePreview =
              response.data.wordpress_logo_data.logo;
          }
          if (response.data.custom_notification) {
            this.customNotification = response.data.custom_notification;
          }
        })
        .catch((error) => {
          this.formLoader = false;
          console.log(error);
          displayErrorMessage(this.formTranslation.widgets.record_not_found);
        });
    },
    uploadsiteLogo(event) {
      if (this.userData.addOns.kiviPro !== true) {
        return;
      }
      let _this = this;
      if (this.isMediaUpload === undefined) {
        const file = event.target.files[0];
        const formData = new FormData();
        formData.append("file", file);
        post("upload_logo", formData)
          .then((response) => {
            if (
              response.data.status !== undefined &&
              response.data.status === true
            ) {
              displayMessage(response.data.message);
              _this.imagePreview = response.data.data;
              _this.$store.dispatch("userDataModule/fetchUserData", {});
            } else {
              displayErrorMessage(response.data.message);
            }
          })
          .catch((error) => {
            console.log(error);
          });
      } else {
        var custom_uploader = kivicareCustomImageUploader(this.formTranslation);

        custom_uploader.on("select", function () {
          var attachment = custom_uploader
            .state()
            .get("selection")
            .first()
            .toJSON();
          _this.imagePreview = attachment.url;
          _this.new_site_logo = attachment.filename;
          post("upload_logo", { site_logo: attachment.id })
            .then((response) => {
              if (
                response.data.status !== undefined &&
                response.data.status === true
              ) {
                displayMessage(response.data.message);
                _this.imagePreview = response.data.data;
                _this.$store.dispatch("userDataModule/fetchUserData", {});
              } else {
                displayErrorMessage(response.data.message);
              }
            })
            .catch((error) => {
              console.log(error);
            });
        });

        //Open the uploader dialog
        custom_uploader.open();
      }
    },
    uploadSiteLoader(event) {
      if (this.userData.addOns.kiviPro !== true) {
        return;
      }

      let _this = this;
      if (this.isMediaUpload === undefined) {
        const file = event.target.files[0];
        const formData = new FormData();
        formData.append("file", file);
        post("upload_loader", formData)
          .then((response) => {
            if (
              response.data.status !== undefined &&
              response.data.status === true
            ) {
              _this.loaderURL = response.data.data;
              displayMessage(response.data.message);
            } else {
              displayErrorMessage(response.data.message);
              _this.loaderURL = window.request_data.loaderImage;
            }
          })
          .catch((error) => {
            _this.loaderURL = window.request_data.loaderImage;
            console.log(error);
          });
      } else {
        var custom_uploader = kivicareCustomImageUploader(this.formTranslation);

        custom_uploader.on("select", function () {
          var attachment = custom_uploader
            .state()
            .get("selection")
            .first()
            .toJSON();
          _this.loaderURL = attachment.url;
          _this.new_site_loader = attachment.filename;
          post("upload_loader", { site_loader: attachment.id })
            .then((response) => {
              if (
                response.data.status !== undefined &&
                response.data.status === true
              ) {
                _this.loaderURL = response.data.data;
                displayMessage(response.data.message);
              } else {
                displayErrorMessage(response.data.message);
                _this.loaderURL = window.request_data.loaderImage;
              }
            })
            .catch((error) => {
              _this.loaderURL = window.request_data.loaderImage;
              console.log(error);
            });
        });

        // Open the uploader dialog
        custom_uploader.open();
      }
    },
    setSiteThemeColor() {
      if (this.userData.addOns.kiviPro !== true) {
        return;
      }
      document.documentElement.style.setProperty("--primary", this.themeColor);
      post("update_theme_color", { color: this.themeColor })
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
            this.themeColor = response.data.data;
            localStorage.setItem("temp_color", response.data.data);
            // window.location.reload()
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    setSiteRTLMode() {
      if (this.userData.addOns.kiviPro !== true) {
        return;
      }
      var state = this.rtlMode;
      if (state == true) {
        document.body.classList.add("rtl");
        var h1 = document.getElementsByTagName("html")[0];
        var att = document.createAttribute("dir");
        att.value = "rtl";
        h1.setAttributeNode(att);
      } else {
        document.body.classList.remove("rtl");
        document.getElementsByTagName("html")[0].removeAttribute("dir");
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      }
      get("update_theme_rtl", { rtl: state })
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.isEnabled = response.data.data;
            displayMessage(response.data.message);
            this.$store.dispatch("userDataModule/fetchUserData", {});
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    setWordpressLogoStatus: function () {
      if (this.userData.addOns.kiviPro !== true) {
        return;
      }
      post("save_wordpress_logo", { status: this.EnableWordpressStatus })
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.EnableWordpressStatus = response.data.data ? "1" : "0";
            this.$store.commit("FETCH_WORDPRESS_LOGO", {
              data: {
                logo: this.wordpressLogoImagePreview,
                status: this.EnableWordpressStatus,
              },
            });
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    saveWordpressLogoImage: function () {
      if (this.userData.addOns.kiviPro !== true) {
        return;
      }

      let _this = this;

      var custom_uploader = kivicareCustomImageUploader(this.formTranslation);

      custom_uploader.on("select", function () {
        var attachment = custom_uploader
          .state()
          .get("selection")
          .first()
          .toJSON();
        _this.wordpressLogoImagePreview = attachment.url;
        _this.new_wordpress_logo = attachment.filename;
        post("save_wordpress_logo", { wordpress_logo: attachment.id })
          .then((response) => {
            if (
              response.data.status !== undefined &&
              response.data.status === true
            ) {
              _this.wordpressLogoImagePreview = response.data.data;
              _this.$store.commit("FETCH_WORDPRESS_LOGO", {
                data: {
                  logo: response.data.data,
                  status: _this.EnableWordpressStatus,
                },
              });
              displayMessage(response.data.message);
            } else {
              displayErrorMessage(response.data.message);
              _this.loaderURL = window.request_data.loaderImage;
            }
          })
          .catch((error) => {
            _this.loaderURL = window.request_data.loaderImage;
            console.log(error);
          });
      });

      //Open the uploader dialog
      custom_uploader.open();
    },
    saveSmsConfigData(type) {
      if (this.userData.addOns.kiviPro !== true) {
        return;
      }

      let configOk =
        type === "checkbox" && this.smsConfigData.enableSMS === "false";
      if (!configOk) {
        // validation part
        this.smsConfigDataLoading = true;
        this.smsSubmitted = true;
        this.$v.$touch();
        if (this.$v.smsConfigData.$invalid) {
          this.smsConfigDataLoading = false;
          return;
        }
      }

      if (
        !configOk &&
        this.customNotification.enableSMS &&
        ["sms"].includes(this.customNotification.enableSMS)
      ) {
        this.$swal
          .fire({
            title: this.formTranslation.clinic_schedule.dt_are_you_sure,
            text: this.formTranslation
              .enabling_twilio_sms_will_disable_custom_notification_sms,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#d33",
            cancelButtonColor: "#3085d6",
            confirmButtonText: this.formTranslation.common.yes,
            cancelButtonText: this.formTranslation.common.cancel,
          })
          .then((result) => {
            if (result.isConfirmed) {
              this.customNotification.enableSMS = "no";
              this.saveCustomNotificationApi("sms", "no");
              this.saveSmsConfigDataApi();
            } else {
              // Revert enableSMS to false if canceled
              this.smsConfigData.enableSMS = "false";
            }
          });
      } else {
        this.saveSmsConfigDataApi();
      }
    },
    saveSmsConfigDataApi() {
      post("sms_config_save", this.smsConfigData)
        .then((response) => {
          this.smsSubmitted = false;
          this.smsConfigDataLoading = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          console.log(error);
          this.smsSubmitted = false;
          this.smsConfigDataLoading = false;
          this.submitted = false;
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    saveWhatsAppConfigData(type) {
      if (this.userData.addOns.kiviPro !== true) {
        return;
      }

      let configOk =
        type === "checkbox" &&
        this.whatsAppConfigData.enableWhatsApp === "false";

      if (!configOk) {
        // validation part
        this.whatsappConfigDataLoading = true;
        this.whatSubmitted = true;
        this.$v.$touch();

        if (this.$v.whatsAppConfigData.$invalid) {
          this.whatsappConfigDataLoading = false;
          return;
        }
      }

      if (
        !configOk &&
        this.customNotification.enableWhatsapp &&
        ["yes"].includes(this.customNotification.enableWhatsapp)
      ) {
        this.$swal
          .fire({
            title: this.formTranslation.clinic_schedule.dt_are_you_sure,
            text: this.formTranslation
              .enabling_twilio_whatsApp_will_disable_custom_notification_WhatsApp,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#d33",
            cancelButtonColor: "#3085d6",
            confirmButtonText: this.formTranslation.common.yes,
            cancelButtonText: this.formTranslation.common.cancel,
          })
          .then((result) => {
            if (result.isConfirmed) {
              this.customNotification.enableWhatsapp = "no";
              this.saveCustomNotificationApi("whatsapp", "no");
              this.saveWhatsappApiCall();
            } else {
              // Revert enableWhatsApp to false if canceled
              this.whatsAppConfigData.enableWhatsApp = "false";
            }
          });
      } else {
        this.saveWhatsappApiCall();
      }
    },
    saveWhatsappApiCall() {
      post("whatsapp_config_save", this.whatsAppConfigData)
        .then((response) => {
          this.whatSubmitted = false;
          this.whatsappConfigDataLoading = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          console.log(error);
          this.whatSubmitted = false;
          this.whatsappConfigDataLoading = false;
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    saveGoogleCalenderConfigData(type) {
      if (this.userData.addOns.kiviPro !== true) {
        return;
      }
      this.loading = true;
      let configOk =
        type === "checkbox" && this.googleCalData.enableCal === "false";
      if (!configOk) {
        this.googlecalendarSubmitted = true;
        this.$v.$touch();
        if (this.$v.googleCalData.$invalid) {
          this.loading = false;
          return;
        }
      }
      post("google_calender_config", this.googleCalData)
        .then((response) => {
          this.loading = false;
          this.googlecalendarSubmitted = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
            this.$router.push({ name: "setting.comman_settings" });
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          this.loading = false;
          this.googlecalendarSubmitted = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    savePatientCalendarStatus() {
      if (this.userData.addOns.kiviPro !== true) {
        return;
      }
      var state = this.patientCalOn;
      get("save_patient_google_cal", { pCal: state })
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            // this.patientCalOn = response.data.data;
            displayMessage(response.data.message);
            this.$store.dispatch("userDataModule/fetchUserData", {});
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    saveEncounterClinicalDetailsIncludeInPrescriptionPrint: function (event) {
      get("edit_clinical_detail_include", {
        status: this.clinicalDetailInclude,
      })
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(this.formTranslation.widgets.record_not_found);
        });
    },
    saveEncounterCustomFieldIncludeInPrescriptionPrint: function (event) {
      get("edit_encounter_custom_field_include", {
        status: this.encounterCustomFieldInclude,
      })
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(this.formTranslation.widgets.record_not_found);
        });
    },
    saveEncounterClinicalDetailsHideInPatientDashboard: function (event) {
      get("edit_clinical_detail_hide_in_patient", {
        status: this.clinicalDetailHideInPatient,
      })
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(this.formTranslation.widgets.record_not_found);
        });
    },
    saveSiteCopyrightText() {
      if (this.userData.addOns.kiviPro !== true) {
        return;
      }
      this.copyrighttextLoading = true;
      get("save_copy_right_text", { text: this.copyrighttext })
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.$store.commit("FETCH_FOOTER_COPYRIGHT_TEXT", {
              data: this.copyrighttext,
            });
            this.copyrighttextLoading = false;

            displayMessage(response.data.message);
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          this.copyrighttextLoading = true;
          console.log(error);
        });
    },
    getModule: function () {
      if (
        window.request_data.link_show_hide !== undefined &&
        window.request_data.link_show_hide !== ""
      ) {
        this.request_status = window.request_data.link_show_hide;
      }
    },
    saveCustomNotification: function (type) {
      let status = "";
      if (type === "sms") {
        status = this.customNotification.enableSMS;
        if (
          this.smsConfigData.enableSMS &&
          [true, "true"].includes(this.smsConfigData.enableSMS)
        ) {
          this.$swal
            .fire({
              title: this.formTranslation.clinic_schedule.dt_are_you_sure,
              text: this.formTranslation
                .enabling_custom_notification_sms_will_disable_twillo_sms,
              icon: "warning",
              showCancelButton: true,
              confirmButtonColor: "#d33",
              cancelButtonColor: "#3085d6",
              confirmButtonText: this.formTranslation.common.yes,
              cancelButtonText: this.formTranslation.common.cancel,
            })
            .then((result) => {
              if (result.isConfirmed) {
                this.saveSmsConfigData("checkbox");
                this.saveCustomNotificationApi(type, status);
              }
            });
        } else {
          this.saveCustomNotificationApi(type, status);
        }
      } else {
        status = this.customNotification.enableWhatsapp;
        if (
          this.whatsAppConfigData.enableWhatsapp &&
          [true, "true"].includes(this.whatsAppConfigData.enableWhatsapp)
        ) {
          this.$swal
            .fire({
              title: this.formTranslation.clinic_schedule.dt_are_you_sure,
              text: this.formTranslation
                .enabling_custom_notification_sms_will_disable_twillo_sms,
              icon: "warning",
              showCancelButton: true,
              confirmButtonColor: "#d33",
              cancelButtonColor: "#3085d6",
              confirmButtonText: this.formTranslation.common.yes,
              cancelButtonText: this.formTranslation.common.cancel,
            })
            .then((result) => {
              if (result.isConfirmed) {
                this.whatsAppConfigData.enableWhatsapp = "false";
                this.saveWhatsAppConfigData("checkbox");
                this.saveCustomNotificationApi(type, status);
              }
            });
        } else {
          this.saveCustomNotificationApi(type, status);
        }
      }
    },
    saveCustomNotificationApi: function (type, status) {
      post("save_custom_notification_setting", { type: type, status: status })
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
  },
  computed: {
    userData() {
      return this.$store.state.userDataModule.user;
    },
    getColor() {
      if (this.userData.addOns.kiviPro !== true) {
        return "#000000";
      } else {
        return this.userData.theme_color;
      }
    },
    getMode() {
      return this.userData.theme_mode;
    },
    getSiteLogo() {
      return this.userData.site_logo;
    },
  },
  watch: {
    getMode(oldMode, newMode) {
      this.rtlMode = newMode;
    },
  },
};
</script>
<style scoped>
.kc-thm-st {
  display: grid;
}
</style>
