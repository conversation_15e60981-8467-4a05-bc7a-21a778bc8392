<?php
global $wpdb;

$clinic_id = kcGetDefaultClinicId();
if (isKiviCareProActive()) {
    $clinic_id = collect($wpdb->get_results("SELECT id FROM {$wpdb->prefix}kc_clinics"))->pluck('id')->implode(",");
}
if (!empty($this->getLoginUserRole())) {
    $user_id = get_current_user_id();
    if (isKiviCareProActive()) {
        if ($this->getLoginUserRole() === 'KiviCare_receptionist') {
            $clinic_id = $wpdb->get_var("SELECT clinic_id FROM {$wpdb->prefix}kc_receptionist_clinic_mappings WHERE receptionist_id={$user_id}");
        }
        if ($this->getLoginUserRole() === 'kiviCare_doctor') {
            unset($userList['KiviCare_doctor']);
            unset($userList['KiviCare_receptionist']);
            $clinic_id = collect($wpdb->get_results("SELECT clinic_id FROM {$wpdb->prefix}kc_doctor_clinic_mappings WHERE doctor_id = {$user_id}"))->pluck('clinic_id')->implode(",");
        }
        if ($this->getLoginUserRole() === 'kiviCare_clinic_admin') {
            $clinic_id = $wpdb->get_var("SELECT id FROM {$wpdb->prefix}kc_clinics WHERE clinic_admin_id = {$user_id}");
        }
    }
}
$clinic = [];

if (empty($clinic_id_param)) {
    $clinic = collect($wpdb->get_results("SELECT id,name FROM {$wpdb->prefix}kc_clinics WHERE id IN ({$clinic_id}) AND status = 1"))->toArray();
}

$theme_mode = get_option(KIVI_CARE_PREFIX . 'theme_mode');
$rtl_attr = in_array($theme_mode, ['1', 'true']) ? 'rtl' : '';

$site_logo = !empty($config_options[KIVI_CARE_PREFIX . 'site_logo']) ? wp_get_attachment_url($config_options[KIVI_CARE_PREFIX . 'site_logo']) : $this->plugin_url . 'assets/images/logo-banner.png';

?>

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<script src="https://cdn.tailwindcss.com"></script>
<script>
    tailwind.config = {
        theme: {
            extend: {
                colors: {
                    'true-black': '#000000',
                }
            }
        }
    }
</script>

<div class="wp-block-kivi-care-register-login">
    <div class="flex flex-col lg:flex-row w-full min-h-screen font-sans">
        <!-- Left Side - Hidden on Mobile -->
        <div class="hidden lg:block lg:w-1/2 min-h-screen bg-gradient-to-br from-pink-50 via-purple-50 to-orange-50 relative">
            <!-- Logo -->
            <div class="absolute top-8 left-8 lg:top-16 lg:left-16">
                <img src="<?php echo esc_url($site_logo ?? "#") ?>" alt="logo" class="w-30 lg:w-40">
            </div>

            <!-- Centered Content Container -->
            <div class="min-h-screen flex items-center px-8 lg:px-16">
                <div class="max-w-[520px]">
                    <h1 class="text-4xl lg:text-5xl font-semibold mb-4">
                        <div class="bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-pink-600">Your Health Passport</div>
                        <div class="text-gray-900 mt-2">Next-Gen Care Portal
                        </div>
                    </h1>
                    <p class="text-gray-600 text-base lg:text-lg mb-12 leading-relaxed max-w-[460px]">
                    Take control of your healthcare journey. With Medroid, access your medical history, schedule appointments, communicate with providers, and manage your health information all in one secure place.

                    </p>

                    <!-- Stats -->
                    <div class="flex gap-8 lg:gap-12">
                        <div>
                            <div class="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">95%</div>
                            <div class="text-gray-600 mt-1 text-sm lg:text-base">Less time spent on paperwork
                            </div>
                        </div>
                        <div>
                            <div class="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">2x</div>
                            <div class="text-gray-600 mt-1 text-sm lg:text-base">Faster access to your health records
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Side - Full Width on Mobile -->
        <div class="w-full lg:w-1/2 min-h-screen p-8 lg:p-16 flex items-center justify-center bg-white">
            <!-- Mobile Logo -->
            <div class="absolute top-8 left-8 lg:hidden">
                <img src="<?php echo esc_url($site_logo ?? "#") ?>" alt="logo" class="w-24">
            </div>

            <div class="w-full max-w-[500px] mt-16 lg:mt-0">
                <h2 class="text-2xl lg:text-3xl font-semibold text-gray-900 mb-3">Access Your Health Portal
                </h2>
                <p class="text-gray-600 mb-8 text-sm lg:text-base">
                Sign in to view your medical records, manage appointments, message your healthcare team, and access personalized health resources designed for your wellbeing.

                </p>

                <?php if (isset($login) && $login) { ?>
                    <form id="kivicare-login-form" class="space-y-6">
                        <div id="login" class="iq-fade authActive">
                            <div id="kivicare-login">
                                <!-- Username/Email Field -->
                                <div class="mb-6">
                                    <label class="block text-sm font-medium text-gray-700 mb-2" for="loginUsername">
                                        <?php echo esc_html__('Username or Email', 'kc-lang'); ?> 
                                        <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" 
                                        name="username" 
                                        class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white transition-all"
                                        id="loginUsername" 
                                        placeholder="Enter your email" 
                                        required>
                                </div>

                                <!-- Password Field -->
                                <div class="mb-6">
                                    <label class="block text-sm font-medium text-gray-700 mb-2" for="loginPassword">
                                        <?php echo esc_html__('Password', 'kc-lang'); ?> 
                                        <span class="text-red-500">*</span>
                                    </label>
                                    <div class="relative">
                                        <input type="password" 
                                            name="password" 
                                            class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white transition-all"
                                            id="loginPassword" 
                                            placeholder="Enter your password" 
                                            required>
                                        <i class="password-toggle fas fa-eye absolute top-1/2 right-4 transform -translate-y-1/2 cursor-pointer text-gray-400 hover:text-gray-600" 
                                            id="togglePassword"></i>
                                    </div>
                                </div>

                                <!-- Remember Me & Forgot Password -->
                                <div class="flex items-center justify-between mb-6">
                                    <div class="flex items-center">
                                        <input type="checkbox" 
                                            id="remember-me" 
                                            class="w-4 h-4 border-gray-300 rounded text-purple-600 focus:ring-purple-500"
                                            name="Remember Me">
                                        <label class="ml-2 text-sm text-gray-600" for="remember-me">
                                            <?php echo esc_html__('Remember Me', 'kc-lang'); ?>
                                        </label>
                                    </div>
                                    <a href="<?php echo esc_url(wp_lostpassword_url()); ?>" 
                                        class="text-sm text-purple-600 hover:text-purple-700 font-medium">
                                        <?php echo esc_html__('Forgot Password?', 'kc-lang'); ?>
                                    </a>
                                </div>

                                <!-- Login Button -->
                                <button type="submit" 
                                    name="submit" 
                                    value="submit"
                                    class="ring-offset-background focus-visible:ring-ring whitespace-nowrap text-sm hover:opacity-50 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary hover:bg-primary/90 h-10 w-full px-4 py-2.5 rounded-lg bg-gradient-to-r from-purple-600 to-pink-600 text-white hover:from-purple-700 hover:to-pink-700 font-semibold flex items-center justify-center gap-2 transition-all duration-300 ease-in-out transform hover:-translate-y-1 hover:shadow-lg">
                                    <span class="mr-2"><?php echo esc_html__("Login"); ?></span>
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                                    </svg>
                                </button>

                                <!-- Error/Success Messages -->
                                <div id="kivicare_server_error_msg" 
                                    class="mt-4 p-4 bg-red-50 text-red-600 rounded-lg text-sm" style="display: none;"></div>
                                <div id="kivicare_success_msg" 
                                    class="mt-4 p-4 bg-green-50 text-green-600 rounded-lg text-sm" style="display: none;"></div>
                            </div>
                        </div>
                    </form>
                <?php } ?>
            </div>
        </div>

        <!-- Loader -->
        <div id="kivi-main-loader-overlay" class="hidden fixed inset-0 bg-white/75 backdrop-blur-sm flex items-center justify-center z-50">
            <?php if (isLoaderCustomUrl()) { ?>
                <img src="<?php echo esc_url(kcAppointmentWidgetLoader()); ?>" alt="Loading...">
            <?php } else { ?>
                <div class="w-12 h-12 border-4 border-purple-600 border-t-transparent rounded-full animate-spin"></div>
            <?php } ?>
        </div>
    </div>
</div>

<script>
    <?php
    $widgetSetting = json_decode(get_option(KIVI_CARE_PREFIX . 'widgetSetting'), true);
    ?>
    if ('<?php echo !empty($widgetSetting['primaryColor']); ?>') {
        document.documentElement.style.setProperty("--iq-primary", '<?php echo esc_js(!empty($widgetSetting['primaryColor']) ? $widgetSetting['primaryColor'] : '#7093e5'); ?>');
    }

    if ('<?php echo !empty($widgetSetting['primaryHoverColor']); ?>') {
        document.documentElement.style.setProperty("--iq-primary-dark", '<?php echo esc_js(!empty($widgetSetting['primaryHoverColor']) ? $widgetSetting['primaryHoverColor'] : '#4367b9'); ?>');
    }

    if ('<?php echo !empty($widgetSetting['secondaryColor']); ?>') {
        document.documentElement.style.setProperty("--iq-secondary", '<?php echo esc_js(!empty($widgetSetting['secondaryColor']) ? $widgetSetting['secondaryColor'] : '#f68685'); ?>');
    }

    if ('<?php echo !empty($widgetSetting['secondaryHoverColor']); ?>') {
        document.documentElement.style.setProperty("--iq-secondary-dark", '<?php echo esc_js(!empty($widgetSetting['secondaryHoverColor']) ? $widgetSetting['secondaryHoverColor'] : '#df504e'); ?>');
    }

    function kivicareFileUploadSizeCheck(event) {
        const allowedSize = <?php echo wp_max_upload_size(); ?>; //(in bytes)
        if (event.target.files && event.target.files.length > 0
            && event.target.files[0].size > allowedSize) {
            jQuery(event.target).css('border', '1px solid var(--iq-secondary-dark)');
            jQuery(event.target).siblings('div').css('display', 'block');
            event.target.value = ''; // Clear the file input field
        } else {
            jQuery(event.target).css('border', '1px solid #eee');
            jQuery(event.target).siblings('div').css('display', 'none');
        }
    }

    document.addEventListener('readystatechange', event => {
        if (event.target.readyState === "complete") {
            jQuery('#CountryCode').select2();
            jQuery('#userRole').select2({
                minimumResultsForSearch: Infinity
            });
            jQuery('#userClinic').select2({
                minimumResultsForSearch: Infinity
            });
            'use strict';
            (function ($) {

                const post = (route, data = {}, frontEnd = false, headers = {
                    headers: { 'Content-Type': 'application/json' }
                }) => {

                    window.ajaxurl = '<?php echo esc_url(admin_url('admin-ajax.php')); ?>';
                    window.nonce = '<?php echo esc_js(wp_create_nonce('ajax_post')); ?>';

                    let url = window.ajaxurl;
                    if (data.action === undefined) {
                        url = ajaxurl + '?action=ajax_post';
                    }

                    if (route === undefined) {
                        return false
                    }

                    if (data.append !== undefined) {
                        data.append('route_name', route);
                        data.append('_ajax_nonce', window.nonce)
                    } else {
                        data.route_name = route;
                        data._ajax_nonce = window.nonce;
                    }

                    return new Promise((resolve, reject, headers) => {
                        axios.post(url, data, headers)
                            .then((data) => {
                                if (data.data.status_code !== undefined && data.data.status_code === 403) {
                                    kcShowErrorMessage('<?php echo esc_html__("Route not found", "kc-lang"); ?>');
                                }
                                resolve(data)
                            })
                            .catch((error) => {
                                reject(error)
                                kcShowErrorMessage('<?php echo esc_html__("Internal server error", "kc-lang"); ?>');
                            });
                    })
                }

                const get = (route, data, frontEnd = false) => {
                    data._ajax_nonce = '<?php echo esc_js(wp_create_nonce('ajax_get')); ?>';
                    let url = '<?php echo esc_url(admin_url('admin-ajax.php')); ?>';
                    if (data.action === undefined) {
                        url = url + '?action=ajax_get';
                    }

                    if (route === undefined) {
                        return false
                    }

                    url = url + '&route_name=' + route;
                    return new Promise((resolve, reject) => {
                        axios.get(url, { params: data })
                            .then((data) => {
                                if (data.data.status_code !== undefined && data.data.status_code === 403) {
                                    kcShowErrorMessage('<?php echo esc_html__("Route not found", "kc-lang"); ?>');
                                }
                                resolve(data)
                            })
                            .catch((error) => {
                                reject(error)
                                kcShowErrorMessage('<?php echo esc_html__("Internal server error", "kc-lang"); ?>');
                            });
                    })
                }

                if ($(document).find('#userRole').val()) {
                    kcGetRegisterPageCustomField($(document).find('#userRole').val())
                }
                var element = document.getElementById('CountryCode');
                if (element !== null) {
                    getCountryCodeData();
                }

                getUserRegistrationFormData();

                if ('<?php echo !is_user_logged_in(); ?>') {
                    $('.kivi-widget ul li.tab-item a').on('click', function (e) {
                        e.preventDefault();
                        const tab_id = $(this).attr('href');
                        $('.kivi-widget li.tab-item').removeClass('active');
                        $(this).parent().addClass('active');
                        $('.kivi-widget form').addClass('d-none');
                        $(tab_id).parent().removeClass('d-none')
                    });
                }

                if ('<?php echo kcGoogleCaptchaData('status') === 'on'; ?>') {
                    grecaptcha.ready(function () {
                        kcCreateRecaptcha();
                    });
                }

                function kcCreateRecaptcha() {
                    grecaptcha.execute('<?php echo esc_html(kcGoogleCaptchaData('site_key')); ?>', { action: 'validate_captcha' })
                        .then(function (token) {
                            // add token value to form
                            document.getElementById('g-recaptcha-response').value = token;
                        });
                }
                $(document).on('submit', '#kivicare-register-form', function (event) {
                    $('#kcCustomFieldsList .kivicare-required').prop('required', true);
                    $.each($('#kcCustomFieldsList').find(':input:checkbox').parent().parent(), function (key, value) {
                        let cbx_group = $(value).find(':input:checkbox');
                        if (cbx_group.is(":checked")) {
                            cbx_group.prop('required', false);
                        }
                    });
                    var result = {};
                    $.each($('#register :input').serializeArray(), function () {
                        result[this.name] = this.value;
                    });
                    var custom_fields = kivicareCustomFieldsData('kcCustomFieldsList');
                    kivicareButtonTextChange(this, '<?php echo esc_html__("Loading..."); ?>', true)
                    event.preventDefault();
                    $('#kivi-content').addClass('kc-position-relative')
                    $('#kivi-main-loader-overlay').removeClass('d-none')
                    $('#kivi-main-loader-overlay').addClass('kc-relative-center')
                    const registerData = { ...result, ...{ custom_fields } };
                    let formData = new FormData(this)
                    $.each(registerData, function (key, value) {
                        if (typeof (value) === 'object') {
                            value = JSON.stringify(value)
                        }
                        formData.append(key, value)
                    });
                    post('register_new_user', formData, true)
                        .then((response) => {
                            $('#kivi-content').removeClass('kc-position-relative')
                            $('#kivi-main-loader-overlay').addClass('d-none')
                            $('#kivi-main-loader-overlay').removeClass('kc-relative-center')
                            if ('<?php echo kcGoogleCaptchaData('status') === 'on'; ?>') {
                                kcCreateRecaptcha();
                            }
                            kivicareButtonTextChange(this, '<?php echo esc_html__("Register"); ?>', false)
                            if (response.data.status !== undefined && response.data.status === true) {
                                $('#kivicare-register-form').trigger("reset");
                                $('#kcCustomFieldsList').find('.appointment_widget_multiselect').each(function () {
                                    $(this).val([]).trigger('change');
                                });
                                if (response.data.redirect !== undefined && response.data.redirect !== '') {
                                    setTimeout(() => {
                                        location.href = response.data.redirect
                                    }, 1000)
                                } else {
                                    kivicareShowSuccessMessage(response.data.message)
                                }
                            } else {
                                kcShowErrorMessage(response.data.message)
                            }
                        }).catch((error) => {
                            $('#kivi-content').removeClass('kc-position-relative')
                            $('#kivi-main-loader-overlay').addClass('d-none')
                            $('#kivi-main-loader-overlay').removeClass('kc-relative-center')
                            if ('<?php echo kcGoogleCaptchaData('status') === 'on'; ?>') {
                                kcCreateRecaptcha();
                            }
                            kivicareButtonTextChange(this, '<?php echo esc_html__("Register"); ?>', false)
                            kcShowErrorMessage('<?php echo esc_html__("Internal server error", "kc-lang"); ?>')
                            console.log(error);
                        })
                })

                $(document).on('submit', '#kivicare-login-form', function (event) {
                    var result = {};
                    $.each($('#login :input').serializeArray(), function () {
                        result[this.name] = this.value;
                    });
                    event.preventDefault();
                    kivicareButtonTextChange(this, '<?php echo esc_html__("Loading..."); ?>', true)
                    $('#kivi-content').addClass('kc-position-relative')
                    $('#kivi-main-loader-overlay').removeClass('d-none')
                    $('#kivi-main-loader-overlay').addClass('kc-relative-center')
                    post('login_new_user', result, true)
                        .then((response) => {
                            $('#kivi-content').removeClass('kc-position-relative')
                            $('#kivi-main-loader-overlay').addClass('d-none')
                            $('#kivi-main-loader-overlay').removeClass('kc-relative-center')
                            kivicareButtonTextChange(this, '<?php echo esc_html__("Login"); ?>', false)
                            if (response.data.status !== undefined && response.data.status === true) {
                                setTimeout(() => {
                                    location.href = response.data.login_redirect_url;
                                }, 1000)
                            } else {
                                kcShowErrorMessage(response.data.message)
                            }
                        }).catch((error) => {
                            $('#kivi-content').removeClass('kc-position-relative')
                            $('#kivi-main-loader-overlay').addClass('d-none')
                            $('#kivi-main-loader-overlay').removeClass('kc-relative-center')
                            kivicareButtonTextChange(this, '<?php echo esc_html__("Login"); ?>', false)
                            console.log(error);
                            kcShowErrorMessage('<?php echo esc_html__("Internal server error", "kc-lang"); ?>')
                        })
                })

                $(document).on('change', '#userRole', function (event) {
                    kcGetRegisterPageCustomField(this.value);
                })

                $('#kivi-main-loader').css('display', 'none');
                $('#kivi-content').css('display', '');

                function kcGetRegisterPageCustomField(type) {
                    let customFieldEle = document.getElementById('kcCustomFieldsList')
                    customFieldEle.classList.add("kivi-center");
                    customFieldEle.innerHTML = `<div class="double-lines-spinner"></div>`
                    get('get_appointment_custom_field', { user_role: type }, true)
                        .then((res) => {
                            customFieldEle.classList.remove("kivi-center");
                            customFieldEle.innerHTML = ''
                            if (res.data.status !== undefined && res.data.status) {
                                customFieldEle.innerHTML = res.data.data;
                                $('#kcCustomFieldsList').find('.appointment_widget_multiselect').each(function () {
                                    $(this).select2({
                                        placeholder: $(this).attr('placeholder'),
                                        allowClear: true,
                                        dropdownCssClass: 'kivicare-custom-dropdown-width'
                                    });
                                });
                            }
                        }).catch((error) => {
                            customFieldEle.classList.remove("kivi-center");
                            customFieldEle.innerHTML = ''
                            console.log(error);
                        })
                }
                function getCountryCodeData() {
                    get('get_country_code_settings_data', {})
                        .then((response) => {
                            if (response.data.status !== undefined && response.data.status === true) {
                                var valueString = '{"countryCallingCode":"+' + response.data.data.country_calling_code + '","countryCode":"' + response.data.data.country_code + '"}';
                                jQuery('#CountryCode').val(valueString).trigger('change');
                            }
                        })
                        .catch((error) => {
                            console.log(error);
                            displayErrorMessage(this.formTranslation.common.internal_server_error);
                        })
                }
                function getUserRegistrationFormData() {
                    get('get_user_registration_form_settings_data', {})
                        .then((response) => {
                            console.log(response.data.data.userRegistrationFormSettingData);
                            if (response.data.status !== undefined && response.data.status === true) {
                                let userRegistrationFormSettingData = response.data.data.userRegistrationFormSettingData;
                                if (userRegistrationFormSettingData === 'on') {
                                    $('#otherGenderOption').show();
                                } else {

                                    $('#otherGenderOption').hide();
                                }
                                return response.data.data.userRegistrationFormSettingData;
                            }
                        })
                        .catch((error) => {
                            console.log(error);
                            displayErrorMessage(this.formTranslation.common.internal_server_error);
                        })
                }
                function kivicareCustomFieldsData(ele) {
                    var custom_fields = {};
                    $.each($('#' + ele).find('select, textarea, :input:not(:checkbox)').serializeArray(), function () {
                        custom_fields[this.name] = this.value;
                    });
                    var temp = [];
                    var temp2 = '';
                    $.each($('#' + ele).find(':input:checkbox').serializeArray(), function (key, value) {
                        if (temp2 !== value.name) {
                            temp = [];
                        }
                        temp.push(value.value)
                        custom_fields[value.name] = temp;
                        temp2 = value.name;
                    });
                    $('#' + ele).find('.appointment_widget_multiselect').each(function () {
                        custom_fields[$(this).attr('name')] = $(this).val().map((index) => {
                            return { 'id': index, 'text': index }
                        });
                    });
                    return custom_fields;
                }

                function kcShowErrorMessage(message) {
                    let ele = $('#kivicare_server_error_msg');
                    ele.css('display', 'block');
                    ele.empty();
                    ele.append(message)
                    setTimeout(() => {
                        ele.css('display', 'none');
                    }, 6000);
                }

                function kivicareShowSuccessMessage(message) {
                    let ele = $('#kivicare_success_msg');
                    ele.css('display', 'block');
                    ele.text('')
                    ele.text(message)
                    setTimeout(() => {
                        ele.css('display', 'none');
                    }, 6000);
                }

                function kivicareButtonTextChange(ele, txt, disabled) {
                    $(ele).find('button').text(txt);
                    $(ele).find('button').prop('disabled', disabled);
                }

                $('#togglePassword').on('click', function (e) {
                    var passwordInput = document.getElementById("loginPassword");
                    var toggleIcon = document.getElementById("togglePassword");

                    if (passwordInput.type === "password") {
                        passwordInput.type = "text";
                        toggleIcon.classList.remove("fa-eye");
                        toggleIcon.classList.add("fa-eye-slash");
                    } else {
                        passwordInput.type = "password";
                        toggleIcon.classList.remove("fa-eye-slash");
                        toggleIcon.classList.add("fa-eye");
                    }
                });

                if (jQuery('select').length > 0) {
                    jQuery('select').each(function () {
                        jQuery(this).select2({
                            width: '100%'
                        });
                    });
                    jQuery('.select2-container').addClass('wide');
                }

            })(window.jQuery)
        }
    });
</script>