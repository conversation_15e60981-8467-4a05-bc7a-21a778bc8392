// pdfService.js
import jsPDF from 'jspdf';
import 'jspdf-autotable';

export const generatePDF = (content, clinicData = {}, patientData = {}) => {
  try {
    const doc = new jsPDF();
    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();

    // Styling constants
    const colors = {
      primary: [0, 83, 156],
      secondary: [41, 128, 185],
      text: [51, 51, 51]
    };

    const clinic = {
      name: clinicData.clinic_name || 'Medical Clinic',
      address: clinicData.clinic_address || '',
      phone: clinicData.clinic_phone || '',
      email: clinicData.clinic_email || ''
    };

    const patient = {
      name: patientData.patient_name || '',
      id: patientData.patient_id || '',
      date: new Date().toLocaleDateString()
    };

    // Header Design
    const addHeader = () => {
      // Header background
      doc.setFillColor(...colors.primary);
      doc.roundedRect(0, 0, pageWidth, 40, 0, 0, 'F');

      // Add a decorative line
      doc.setDrawColor(...colors.secondary);
      doc.setLineWidth(1);
      doc.line(20, 42, pageWidth - 20, 42);

      // Clinic name
      doc.setTextColor(255, 255, 255);
      doc.setFont('helvetica', 'bold');
      doc.setFontSize(24);
      doc.text(clinic.name, 20, 20);

      // Clinic details
      doc.setFontSize(10);
      doc.setFont('helvetica', 'normal');
      doc.text(clinic.address, 20, 30);
      doc.text(`Tel: ${clinic.phone}`, pageWidth - 60, 30);
    };

    // Patient Info Section
    

    // Footer Design
    const addFooter = () => {
      const footerTop = pageHeight - 35;
      
      // Footer line
      doc.setDrawColor(...colors.primary);
      doc.setLineWidth(0.5);
      doc.line(20, footerTop, pageWidth - 20, footerTop);

      // Signature section
      doc.setTextColor(...colors.text);
      doc.setFontSize(10);
      doc.text("Doctor's Signature:", 20, footerTop + 10);
      doc.line(20, footerTop + 20, 80, footerTop + 20);

      // Page number
      doc.setTextColor(...colors.primary);
      doc.setFontSize(8);
      doc.text(
        `Page ${doc.internal.getCurrentPageInfo().pageNumber}`,
        pageWidth - 40,
        pageHeight - 10
      );

      // Clinic info in footer
      doc.setTextColor(...colors.secondary);
      doc.setFontSize(8);
      doc.text(clinic.email, 20, pageHeight - 10);
    };

    // Add initial header
    addHeader();
    // addPatientInfo();

    // Content
    const processedContent = content.replace(/<[^>]*>/g, '');
    const splitContent = doc.splitTextToSize(processedContent, 170);

    doc.autoTable({
      startY: 38,
      head: [],
      body: [[{ content: splitContent.join('\n'), styles: { cellPadding: 5 } }]],
      theme: 'plain',
      styles: {
        fontSize: 10,
        cellPadding: 5,
        textColor: [51, 51, 51]
      },
      margin: { left: 20, right: 20, bottom: 40 },
      didDrawPage: (data) => {
        if (doc.internal.getCurrentPageInfo().pageNumber > 1) {
          addHeader();
        }
        addFooter();
      }
    });

    // Add footer to first page
    addFooter();

    return doc;
  } catch (error) {
    console.error('PDF Generation Error:', error);
    throw error;
  }
};