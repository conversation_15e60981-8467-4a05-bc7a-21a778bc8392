import { get } from "../config/request";
import { displayErrorMessage } from "../utils/message";

export const getBasicDetails = async (encounterId, isEncounterTemp = false) => {
  try {
    let response = await get(
      isEncounterTemp
        ? "patient_encounter_template_details"
        : "patient_encounter_details",
      {
        id: encounterId,
      }
    );

    if (response?.data?.status === true) {
      let encounterData = response.data.data;
      let patientDetails = response.data.patientDetails;
      let patientMetaData = response.data.patientMetaData;

      if (encounterData.clinic_extra) {
        encounterData.clinic_extra = JSON.parse(encounterData.clinic_extra);
      }

      return {
        status: true,
        encounterData,
        patientDetails,
        patientMetaData,
        hideInPatient: response.data.hideInPatient,
      };
    } else {
      return { status: false };
    }
  } catch (error) {
    console.error(error);
    displayErrorMessage("An error occurred while fetching details.");
    throw error;
  }
};
