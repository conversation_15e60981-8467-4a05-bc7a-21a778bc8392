<template>
  <div>
    <div class="bg-white rounded-lg shadow-lg">
      <!-- Header -->
      <div class="p-4 border-b">
        <div class="grid grid-cols-12 gap-4">
          <div class="col-span-12 md:col-span-8">
            <h2 class="text-xl font-bold m-0">
              {{ formTranslation.settings.patient_setting }}
              <a
                v-if="request_status == 'off'"
                href="https://apps.medroid.ai/docs/product/kivicare/clinic-admin/settings/#patient-setting"
                target="_blank"
                class="text-gray-500 hover:text-gray-700"
              >
                <i class="fa fa-question-circle"></i>
              </a>
            </h2>
          </div>
        </div>
      </div>

      <!-- Loader -->
      <div class="p-4" v-if="formLoader">
        <loader-component-2></loader-component-2>
      </div>

      <!-- Form -->
      <form
        v-else
        id="patient form"
        name="patient form"
        @submit.prevent="handleSubmit"
        :novalidate="true"
        enctype="multipart/form-data"
        class="p-4"
      >
        <div class="space-y-6">
          <!-- Patient Unique Setting Toggle -->
          <div>
            <div class="pl-4">
              <!-- Toggle button -->
              <toggle-switch
                :value="patientdata.enable"
                @input="(value) => (patientdata.enable = value)"
                @change="handleSubmit"
                :label="formTranslation.patient.patient_unique_setting"
                :on-value="true"
                :off-value="false"
              />
            </div>
          </div>

          <!-- Only Number Toggle -->
          <div v-if="patientdata.enable">
            <div class="pl-4">
              <toggle-switch
                :value="patientdata.only_number"
                @input="(value) => (patientdata.only_number = value)"
                @change="handleSubmit"
                :label="
                  formTranslation.patient.only_number_in_patient_unique_id
                "
                :on-value="true"
                :off-value="false"
              />
            </div>
          </div>

          <!-- Prefix and Postfix Inputs -->
          <div
            v-if="patientdata.enable"
            class="grid grid-cols-1 md:grid-cols-2 gap-6"
          >
            <!-- Prefix Input -->
            <div>
              <label
                for="prefix_value"
                class="block text-sm font-medium text-gray-700 mb-1"
              >
                {{ formTranslation.patient.lbl_prefix }}
              </label>
              <input
                type="text"
                id="prefix_value"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                name="prefix_value"
                v-model="patientdata.prefix_value"
              />
            </div>

            <!-- Postfix Input -->
            <div>
              <label
                for="postfix_value"
                class="block text-sm font-medium text-gray-700 mb-1"
              >
                {{ formTranslation.patient.lbl_postfix }}
              </label>
              <input
                type="text"
                id="postfix_value"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                name="postfix_value"
                v-model="patientdata.postfix_value"
              />
            </div>
          </div>

          <!-- Submit Button -->
          <div v-if="patientdata.enable" class="flex justify-end">
            <button
              type="submit"
              :disabled="loading"
              class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              <i :class="loading ? 'fa fa-sync fa-spin' : 'fa fa-save'"></i>
              <span>{{
                loading
                  ? formTranslation.common.loading
                  : formTranslation.common.save
              }}</span>
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>
<script>
import { post, get } from "../../config/request";
export default {
  name: "UniqueIdSetting",
  components: {},
  data: () => {
    return {
      patientdata: {},
      formLoader: true,
      loading: false,
      request_status: "off",
    };
  },
  mounted() {
    if (!["administrator"].includes(this.getUserRole())) {
      this.$router.push({ name: "403" });
    }
    this.patientdata = this.defaultData();
    this.edit();
    this.getModule();
  },
  methods: {
    defaultData() {
      return {
        prefix_value: "",
        postfix_value: "",
        enable: false,
        only_number: false,
      };
    },
    handleSubmit() {
      this.loading = true;
      post("patient_id_config", this.patientdata)
        .then((response) => {
          this.loading = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            // location.reload();
            displayMessage(response.data.message);
            // this.$router.push({ name: "setting.patient_setting" });
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          this.loading = false;
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },
    edit() {
      this.formLoader = true;
      get("edit_patient_id_config", {})
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.patientdata = response.data.data;
          }
          this.formLoader = false;
        })
        .catch((error) => {
          this.formLoader = false;
          console.log(error);
          displayErrorMessage(this.formTranslation.widgets.record_not_found);
        });
    },
    getModule: function () {
      if (
        window.request_data.link_show_hide !== undefined &&
        window.request_data.link_show_hide !== ""
      ) {
        this.request_status = window.request_data.link_show_hide;
      }
    },
  },
  computed: {
    userData() {
      return this.$store.state.userDataModule.user;
    },
  },
  watch: {},
  formTranslation: function () {
    return this.$store.state.staticDataModule.langTranslateData;
  },
};
</script>
