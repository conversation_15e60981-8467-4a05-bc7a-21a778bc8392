<?php

use App\baseClasses\KCBase;

use App\Models\KCTask;
use App\Models\KCTaskAssignee;

/**
 * Send task notification emails to assignees
 * 
 * @param int $task_id The task ID
 * @param array $assignee_ids Array of user IDs to notify
 * @param string $action_type Action that triggered notification (created, updated, deleted, status_changed, comment_added, comment_updated, attachment_added, attachment_updated, reminder, overdue, etc.)
 * @param string $attachment_path Optional attachment file path
 * @param array $additional_data Optional additional data for specialized notifications (like comments or status changes)
 * @return bool Whether all emails were sent successfully
 */
function sendTaskNotificationEmails($task_id, $assignee_ids, $action_type = 'created', $attachment_path = '', $additional_data = []) {
    if (empty($task_id) || empty($assignee_ids) || !is_array($assignee_ids)) {
        return false;
    }
    
    global $wpdb;
    $users_table = $wpdb->base_prefix . 'users';
    
    // Get assignee emails from user IDs
    $placeholders = implode(',', array_fill(0, count($assignee_ids), '%d'));
    $query = $wpdb->prepare(
        "SELECT ID, user_email, display_name FROM $users_table WHERE ID IN ($placeholders)",
        $assignee_ids
    );
    
    $assignees = $wpdb->get_results($query);
    
    if (empty($assignees)) {
        return false;
    }
    
    $emails = array_map(function($user) {
        return $user->user_email;
    }, $assignees);
    
    // Get task details
    $task = (new KCTask)->getTaskById($task_id);
    
    if (empty($task)) {
        return false;
    }
    
    // Create subject line based on action type
    $subject_prefix = '[' . get_bloginfo('name') . '] ';
    $subject_action = '';
    
    switch ($action_type) {
        case 'created':
            $subject_action = __('New Task:', 'kc-lang');
            break;
        case 'updated':
            $subject_action = __('Task Updated:', 'kc-lang');
            break;
        case 'deleted':
            $subject_action = __('Task Deleted:', 'kc-lang');
            break;
        case 'status_changed':
            $subject_action = __('Task Status Changed:', 'kc-lang');
            break;
        case 'comment_added':
            $subject_action = __('New Comment on Task:', 'kc-lang');
            break;
        case 'comment_updated':
            $subject_action = __('Comment Updated on Task:', 'kc-lang');
            break;
        case 'attachment_added':
            $subject_action = __('New Attachment on Task:', 'kc-lang');
            break;
        case 'attachment_updated':
            $subject_action = __('Attachment Updated on Task:', 'kc-lang');
            break;
        case 'reminder':
            $subject_action = __('Task Reminder:', 'kc-lang');
            break;
        case 'overdue':
            $subject_action = __('OVERDUE Task:', 'kc-lang');
            break;
        default:
            $subject_action = __('Task Notification:', 'kc-lang');
    }
    
    $email_subject = $subject_prefix . $subject_action . ' ' . $task->title;
    
    // Get email body
    $email_body = getTaskEmailBody($task, $action_type, $additional_data);
    
    // Set up attachments
    $attachments = [];
    if (!empty($attachment_path) && file_exists($attachment_path)) {
        $attachments[] = $attachment_path;
    }
    
    $headers = ['Content-Type: text/html; charset=UTF-8'];
    
    // Send emails
    $success = true;
    foreach ($emails as $email) {
        if (function_exists('wp_mail')) {
            $result = wp_mail(
                $email,
                $email_subject,
                $email_body,
                $headers,
                $attachments
            );
            
            // Track if any email fails
            if (!$result) {
                $success = false;
            }
        } else {
            $success = false;
        }
    }
    
    return $success;
}

/**
 * Generate email body for task notifications
 * 
 * @param object $task The task object with all details
 * @param string $action_type The action type (created, updated, reminder, etc.)
 * @return string HTML email body
 */
function getTaskEmailBody($task, $action_type = 'created', $additional_data = []) {
    // For overdue tasks, use the specialized template
    if ($action_type === 'overdue') {
        return getTaskOverdueEmailBody($task);
    }
    
    // Get action-specific header and description
    $header = esc_html__('Task Notification', 'kc-lang');
    $description = '';
    $action_color = '#0073aa';
    $highlighted_section = '';
    
    // Set action-specific content
    switch ($action_type) {
        case 'created':
            $description = esc_html__('A new task has been created and assigned to you:', 'kc-lang');
            $action_color = '#0073aa'; // Blue
            break;
            
        case 'updated':
            $description = esc_html__('A task assigned to you has been updated:', 'kc-lang');
            $action_color = '#0073aa'; // Blue
            break;
            
        case 'deleted':
            $header = esc_html__('Task Deleted', 'kc-lang');
            $deleted_by = isset($additional_data['deleted_by']) ? $additional_data['deleted_by'] : esc_html__('Someone', 'kc-lang');
            
            $description = sprintf(
                esc_html__('%s has deleted a task that was assigned to you:', 'kc-lang'),
                esc_html($deleted_by)
            );
            $action_color = '#d32f2f'; // Red
            break;
            
        case 'status_changed':
            $header = esc_html__('Task Status Changed', 'kc-lang');
            $new_status = isset($additional_data['new_status']) ? $additional_data['new_status'] : '';
            $old_status = isset($additional_data['old_status']) ? $additional_data['old_status'] : '';
            $changed_by = isset($additional_data['changed_by_name']) ? $additional_data['changed_by_name'] : esc_html__('Someone', 'kc-lang');
            
            $description = sprintf(
                esc_html__('%s has changed the status of a task from "%s" to "%s":', 'kc-lang'),
                esc_html($changed_by),
                esc_html(ucfirst($old_status)),
                esc_html(ucfirst($new_status))
            );
            
            // Choose color based on new status
            if ($new_status === 'completed') {
                $action_color = '#2e7d32'; // Green
            } elseif ($new_status === 'cancelled') {
                $action_color = '#d32f2f'; // Red
            } elseif ($new_status === 'in-progress') {
                $action_color = '#1976d2'; // Blue
            } else {
                $action_color = '#ff9800'; // Orange for pending
            }
            
            $highlighted_section = '<div style="background: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid ' . $action_color . ';">
                <p><strong>' . esc_html__('Status Change:', 'kc-lang') . '</strong> 
                    ' . esc_html(ucfirst($old_status)) . ' → 
                    <span style="font-weight: bold; color: ' . $action_color . ';">' . esc_html(ucfirst($new_status)) . '</span>
                </p>
            </div>';
            break;
            
        case 'comment_added':
            $header = esc_html__('New Comment on Task', 'kc-lang');
            $comment_text = isset($additional_data['comment_text']) ? $additional_data['comment_text'] : '';
            $comment_by = isset($additional_data['comment_by']) ? $additional_data['comment_by'] : esc_html__('Someone', 'kc-lang');
            
            $description = sprintf(
                esc_html__('%s added a new comment on a task you are assigned to:', 'kc-lang'),
                esc_html($comment_by)
            );
            
            $action_color = '#673ab7'; // Purple
            
            if (!empty($comment_text)) {
                $highlighted_section = '<div style="background: #f0f0f0; padding: 15px; margin: 10px 0; border-left: 3px solid ' . $action_color . ';">
                    <p><strong>' . esc_html__('New Comment:', 'kc-lang') . '</strong></p>
                    <p style="font-style: italic;">' . nl2br(esc_html($comment_text)) . '</p>
                    <p style="font-size: 12px; color: #666; margin: 5px 0 0;">' . esc_html__('Added by', 'kc-lang') . ' ' . esc_html($comment_by) . '</p>
                </div>';
            }
            break;
            
        case 'comment_updated':
            $header = esc_html__('Comment Updated on Task', 'kc-lang');
            $comment_text = isset($additional_data['comment_text']) ? $additional_data['comment_text'] : '';
            $comment_by = isset($additional_data['comment_by']) ? $additional_data['comment_by'] : esc_html__('Someone', 'kc-lang');
            
            $description = sprintf(
                esc_html__('%s updated a comment on a task you are assigned to:', 'kc-lang'),
                esc_html($comment_by)
            );
            
            $action_color = '#673ab7'; // Purple
            
            if (!empty($comment_text)) {
                $highlighted_section = '<div style="background: #f0f0f0; padding: 15px; margin: 10px 0; border-left: 3px solid ' . $action_color . ';">
                    <p><strong>' . esc_html__('Updated Comment:', 'kc-lang') . '</strong></p>
                    <p style="font-style: italic;">' . nl2br(esc_html($comment_text)) . '</p>
                    <p style="font-size: 12px; color: #666; margin: 5px 0 0;">' . esc_html__('Updated by', 'kc-lang') . ' ' . esc_html($comment_by) . '</p>
                </div>';
            }
            break;
            
        case 'comment_deleted':
            $header = esc_html__('Comment Deleted from Task', 'kc-lang');
            $comment_text = isset($additional_data['comment_text']) ? $additional_data['comment_text'] : '';
            $comment_by = isset($additional_data['comment_by']) ? $additional_data['comment_by'] : esc_html__('Someone', 'kc-lang');
            $deleted_by = isset($additional_data['deleted_by']) ? $additional_data['deleted_by'] : esc_html__('Someone', 'kc-lang');
            
            $description = sprintf(
                esc_html__('%s deleted a comment from a task you are assigned to:', 'kc-lang'),
                esc_html($deleted_by)
            );
            
            $action_color = '#d32f2f'; // Red
            
            if (!empty($comment_text)) {
                $highlighted_section = '<div style="background: #fff0f0; padding: 15px; margin: 10px 0; border-left: 3px solid ' . $action_color . ';">
                    <p><strong>' . esc_html__('Deleted Comment:', 'kc-lang') . '</strong></p>
                    <p style="font-style: italic; text-decoration: line-through;">' . nl2br(esc_html($comment_text)) . '</p>
                    <p style="font-size: 12px; color: #666; margin: 5px 0 0;">' . esc_html__('Originally by', 'kc-lang') . ' ' . esc_html($comment_by) . '</p>
                    <p style="font-size: 12px; color: #666; margin: 0;">' . esc_html__('Deleted by', 'kc-lang') . ' ' . esc_html($deleted_by) . '</p>
                </div>';
            }
            break;
            
        case 'attachment_added':
            $header = esc_html__('New Attachment on Task', 'kc-lang');
            $file_name = isset($additional_data['file_name']) ? $additional_data['file_name'] : '';
            $uploaded_by = isset($additional_data['uploaded_by']) ? $additional_data['uploaded_by'] : esc_html__('Someone', 'kc-lang');
            
            $description = sprintf(
                esc_html__('%s added a new attachment to a task you are assigned to:', 'kc-lang'),
                esc_html($uploaded_by)
            );
            
            $action_color = '#2196f3'; // Lighter blue
            
            if (!empty($file_name)) {
                $highlighted_section = '<div style="background: #f0f0f0; padding: 15px; margin: 10px 0; border-left: 3px solid ' . $action_color . ';">
                    <p><strong>' . esc_html__('New Attachment:', 'kc-lang') . '</strong></p>
                    <p style="font-weight: bold;">' . esc_html($file_name) . '</p>
                    <p style="font-size: 12px; color: #666; margin: 5px 0 0;">' . esc_html__('Added by', 'kc-lang') . ' ' . esc_html($uploaded_by) . '</p>
                </div>';
            }
            break;
            
        case 'attachment_updated':
            $header = esc_html__('Attachment Updated on Task', 'kc-lang');
            $file_name = isset($additional_data['file_name']) ? $additional_data['file_name'] : '';
            $uploaded_by = isset($additional_data['uploaded_by']) ? $additional_data['uploaded_by'] : esc_html__('Someone', 'kc-lang');
            
            $description = sprintf(
                esc_html__('%s updated an attachment on a task you are assigned to:', 'kc-lang'),
                esc_html($uploaded_by)
            );
            
            $action_color = '#2196f3'; // Lighter blue
            
            if (!empty($file_name)) {
                $highlighted_section = '<div style="background: #f0f0f0; padding: 15px; margin: 10px 0; border-left: 3px solid ' . $action_color . ';">
                    <p><strong>' . esc_html__('Updated Attachment:', 'kc-lang') . '</strong></p>
                    <p style="font-weight: bold;">' . esc_html($file_name) . '</p>
                    <p style="font-size: 12px; color: #666; margin: 5px 0 0;">' . esc_html__('Updated by', 'kc-lang') . ' ' . esc_html($uploaded_by) . '</p>
                </div>';
            }
            break;
            
        case 'attachment_deleted':
            $header = esc_html__('Attachment Deleted from Task', 'kc-lang');
            $file_name = isset($additional_data['file_name']) ? $additional_data['file_name'] : '';
            $deleted_by = isset($additional_data['deleted_by']) ? $additional_data['deleted_by'] : esc_html__('Someone', 'kc-lang');
            
            $description = sprintf(
                esc_html__('%s deleted an attachment from a task you are assigned to:', 'kc-lang'),
                esc_html($deleted_by)
            );
            
            $action_color = '#d32f2f'; // Red
            
            if (!empty($file_name)) {
                $highlighted_section = '<div style="background: #fff0f0; padding: 15px; margin: 10px 0; border-left: 3px solid ' . $action_color . ';">
                    <p><strong>' . esc_html__('Deleted Attachment:', 'kc-lang') . '</strong></p>
                    <p style="font-weight: bold; text-decoration: line-through;">' . esc_html($file_name) . '</p>
                    <p style="font-size: 12px; color: #666; margin: 5px 0 0;">' . esc_html__('Deleted by', 'kc-lang') . ' ' . esc_html($deleted_by) . '</p>
                </div>';
            }
            break;
            
        case 'reminder':
            $header = esc_html__('Task Reminder', 'kc-lang');
            $description = esc_html__('This is a reminder about your assigned task:', 'kc-lang');
            $action_color = '#ff9800'; // Orange
            break;
            
        default:
            $description = sprintf(esc_html__('A task has been %s:', 'kc-lang'), $action_type);
    }
    
    ob_start();
    ?>
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: <?php echo $action_color; ?>;"><?php echo $header; ?></h2>
        
        <p><?php echo $description; ?></p>
        
        <?php if (!empty($highlighted_section)): ?>
            <?php echo $highlighted_section; ?>
        <?php endif; ?>
        
        <div style="background: #f7f7f7; padding: 15px; border-left: 4px solid <?php echo $action_color; ?>; margin: 20px 0;">
            <h3 style="margin-top: 0;"><?php echo esc_html($task->title); ?></h3>
            
            <?php if (!empty($task->description)): ?>
                <p><strong><?php echo esc_html__('Description:', 'kc-lang'); ?></strong><br>
                <?php echo nl2br(esc_html($task->description)); ?></p>
            <?php endif; ?>
            
            <p><strong><?php echo esc_html__('Priority:', 'kc-lang'); ?></strong> 
                <?php echo esc_html(ucfirst($task->priority)); ?>
            </p>
            
            <p><strong><?php echo esc_html__('Status:', 'kc-lang'); ?></strong> 
                <?php echo esc_html(ucfirst($task->status)); ?>
            </p>
            
            <?php if (!empty($task->due_date)): ?>
                <p><strong><?php echo esc_html__('Due Date:', 'kc-lang'); ?></strong> 
                    <?php 
                    $due_date = date_i18n(get_option('date_format'), strtotime($task->due_date));
                    $is_overdue = strtotime($task->due_date) < strtotime(current_time('Y-m-d'));
                    
                    if ($is_overdue) {
                        echo '<span style="color: #d32f2f; font-weight: bold;">' . esc_html($due_date) . ' (' . esc_html__('Overdue', 'kc-lang') . ')</span>';
                    } else {
                        echo esc_html($due_date);
                    }
                    ?>
                </p>
            <?php endif; ?>
            
            <?php if (!empty($task->category)): ?>
                <p><strong><?php echo esc_html__('Category:', 'kc-lang'); ?></strong> 
                    <?php echo esc_html($task->category); ?>
                </p>
            <?php endif; ?>
            
            <?php if (!empty($task->clinic_name)): ?>
                <p><strong><?php echo esc_html__('Clinic:', 'kc-lang'); ?></strong> 
                    <?php echo esc_html($task->clinic_name); ?>
                </p>
            <?php endif; ?>
            
            <?php if (!empty($task->patient_name)): ?>
                <p><strong><?php echo esc_html__('Patient:', 'kc-lang'); ?></strong> 
                    <?php echo esc_html($task->patient_name); ?>
                </p>
            <?php endif; ?>
        </div>
        
        <p>
            <?php echo esc_html__('You can view and manage this task by logging into your account.', 'kc-lang'); ?>
        </p>
        
        <p style="margin-top: 30px; font-size: 12px; color: #666;">
            <?php echo esc_html__('This is an automated message. Please do not reply directly to this email.', 'kc-lang'); ?>
        </p>
    </div>
    <?php
    return ob_get_clean();
}

/**
 * Send task status update notifications
 * 
 * @param int $task_id The task ID
 * @param string $new_status The updated status
 * @param int $updated_by User ID who updated the status
 * @return bool Whether all emails were sent successfully
 */
function sendTaskStatusUpdateEmails($task_id, $new_status, $updated_by) {
    $task = (new KCTask)->getTaskById($task_id);
    
    if (empty($task)) {
        return false;
    }
    
    // Get task creator and other assignees
    $notify_users = [$task->creator_id]; // Always notify creator
    
    // Get all assignees except the one who updated the status
    $assignees = (new KCTaskAssignee)->getTaskAssignees($task_id);
    
    foreach ($assignees as $assignee) {
        if ($assignee->assignee_id != $updated_by) {
            $notify_users[] = $assignee->assignee_id;
        }
    }
    
    // Remove duplicates
    $notify_users = array_unique($notify_users);
    
    global $wpdb;
    $users_table = $wpdb->base_prefix . 'users';
    
    // Get user who updated the task
    if (function_exists('get_userdata')) {
        $updater = get_userdata($updated_by);
        $updater_name = $updater ? $updater->display_name : __('A user', 'kc-lang');
    } else {
        $updater_name = __('A user', 'kc-lang');
    }
    
    // Prepare email
    $email_subject = sprintf(
        __('[%s] Task Status Updated: %s', 'kc-lang'),
        get_bloginfo('name'),
        $task->title
    );
    
    // Build email body
    ob_start();
    ?>
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;"><?php echo esc_html__('Task Status Update', 'kc-lang'); ?></h2>
        
        <p><?php echo sprintf(
            esc_html__('%s has updated the status of a task to "%s":', 'kc-lang'),
            esc_html($updater_name),
            esc_html(ucfirst($new_status))
        ); ?></p>
        
        <div style="background: #f7f7f7; padding: 15px; border-left: 4px solid #0073aa; margin: 20px 0;">
            <h3 style="margin-top: 0;"><?php echo esc_html($task->title); ?></h3>
            
            <?php if (!empty($task->description)): ?>
                <p><strong><?php echo esc_html__('Description:', 'kc-lang'); ?></strong><br>
                <?php echo nl2br(esc_html($task->description)); ?></p>
            <?php endif; ?>
            
            <p><strong><?php echo esc_html__('New Status:', 'kc-lang'); ?></strong> 
                <span style="font-weight: bold; color: <?php echo ($new_status === 'completed') ? '#2e7d32' : '#0073aa'; ?>">
                    <?php echo esc_html(ucfirst($new_status)); ?>
                </span>
            </p>
            
            <?php if (!empty($task->due_date)): ?>
                <p><strong><?php echo esc_html__('Due Date:', 'kc-lang'); ?></strong> 
                    <?php echo esc_html(date_i18n(get_option('date_format'), strtotime($task->due_date))); ?>
                </p>
            <?php endif; ?>
        </div>
        
        <p>
            <?php echo esc_html__('You can view and manage this task by logging into your account.', 'kc-lang'); ?>
        </p>
        
        <p style="margin-top: 30px; font-size: 12px; color: #666;">
            <?php echo esc_html__('This is an automated message. Please do not reply directly to this email.', 'kc-lang'); ?>
        </p>
    </div>
    <?php
    $email_body = ob_get_clean();
    
    // Get emails for notification
    $placeholders = implode(',', array_fill(0, count($notify_users), '%d'));
    $query = $wpdb->prepare(
        "SELECT user_email FROM $users_table WHERE ID IN ($placeholders)",
        $notify_users
    );
    
    $recipient_emails = $wpdb->get_col($query);
    
    if (empty($recipient_emails)) {
        return false;
    }
    
    $headers = ['Content-Type: text/html; charset=UTF-8'];
    
    // Send emails
    $success = true;
    
    foreach ($recipient_emails as $email) {
        if (function_exists('wp_mail')) {
            $result = wp_mail(
                $email,
                $email_subject,
                $email_body,
                $headers
            );
            
            if (!$result) {
                $success = false;
            }
        } else {
            $success = false;
        }
    }
    
    return $success;
}

/**
 * Send notifications for overdue tasks
 * 
 * @param int $task_id The task ID (optional, if specific task)
 * @return bool Whether all emails were sent successfully
 */
function sendTaskOverdueEmails($task_id = null) {
    global $wpdb;
    $task_model = new KCTask();
    $task_table = $task_model->get_table_name();
    
    // Current date in system format
    $today = current_time('Y-m-d');
    
    // Build query to get overdue tasks
    if ($task_id) {
        // If specific task ID is provided
        $query = $wpdb->prepare(
            "SELECT id FROM $task_table 
            WHERE id = %d 
            AND due_date < %s 
            AND status IN ('pending', 'in-progress')
            AND is_archived = 0",
            $task_id, $today
        );
    } else {
        // Get all overdue tasks that haven't been notified today
        $query = $wpdb->prepare(
            "SELECT id FROM $task_table 
            WHERE due_date < %s 
            AND status IN ('pending', 'in-progress')
            AND is_archived = 0
            AND (last_overdue_notification IS NULL OR last_overdue_notification < %s)",
            $today, $today
        );
    }
    
    $overdue_task_ids = $wpdb->get_col($query);
    
    if (empty($overdue_task_ids)) {
        return false;
    }
    
    $success = true;
    $assignee_model = new KCTaskAssignee();
    
    // Process each overdue task
    foreach ($overdue_task_ids as $overdue_id) {
        $task = $task_model->getTaskById($overdue_id);
        
        if (!$task) {
            continue;
        }
        
        // Get all assignees for this task
        $assignees = $assignee_model->getTaskAssignees($overdue_id);
        $assignee_ids = array_map(function($a) {
            return $a->assignee_id;
        }, $assignees);
        
        // Also notify task creator
        $assignee_ids[] = $task->creator_id;
        $assignee_ids = array_unique($assignee_ids);
        
        // Send the overdue notification
        $result = sendTaskNotificationEmails($overdue_id, $assignee_ids, 'overdue');
        
        // Also send system notification for overdue task
        if (function_exists('kc_send_notification_to_multiple')) {
            $task = $task_model->getTaskById($overdue_id);
            if ($task) {
                $notification_title = sprintf(__('OVERDUE Task: %s', 'kc-lang'), $task->title);
                $notification_message = __('This task is overdue. Please complete it as soon as possible.', 'kc-lang');
                
                kc_send_notification_to_multiple(
                    $assignee_ids,
                    $notification_title,
                    $notification_message,
                    'error',
                    $overdue_id,
                    'task'
                );
            }
        }
        
        // Update the last notification timestamp for this task
        if ($result) {
            $wpdb->update(
                $task_table,
                ['last_overdue_notification' => current_time('mysql')],
                ['id' => $overdue_id]
            );
        } else {
            $success = false;
        }
    }
    
    return $success;
}

/**
 * Check for overdue tasks and send notifications
 * This function is meant to be called by a WordPress cron job
 */
function kc_check_overdue_tasks() {
    // Log the execution
    error_log('Running KiviCare overdue tasks check: ' . current_time('mysql'));
    
    // Send notifications for overdue tasks
    $result = sendTaskOverdueEmails();
    
    // Log the result
    if ($result) {
        error_log('KiviCare overdue task notifications sent successfully');
        
        // Also send system notifications for overdue tasks
        if (function_exists('kc_send_notification_to_multiple')) {
            global $wpdb;
            $task_model = new KCTask();
            $task_table = $task_model->get_table_name();
            
            // Get tasks that were just notified
            $today = current_time('Y-m-d');
            $overdue_tasks = $wpdb->get_results($wpdb->prepare(
                "SELECT id, title, creator_id FROM $task_table 
                WHERE due_date < %s 
                AND status IN ('pending', 'in-progress')
                AND is_archived = 0
                AND last_overdue_notification = %s",
                $today, current_time('mysql', true)
            ));
            
            if (!empty($overdue_tasks)) {
                foreach ($overdue_tasks as $task) {
                    // Get assignees for this task
                    $assignee_model = new KCTaskAssignee();
                    $assignees = $assignee_model->getTaskAssignees($task->id);
                    $assignee_ids = array_map(function($a) {
                        return $a->assignee_id;
                    }, $assignees);
                    
                    // Also notify task creator
                    if (!in_array($task->creator_id, $assignee_ids)) {
                        $assignee_ids[] = $task->creator_id;
                    }
                    
                    // Remove duplicates
                    $assignee_ids = array_unique($assignee_ids);
                    
                    if (!empty($assignee_ids)) {
                        $notification_title = sprintf(__('OVERDUE Task: %s', 'kc-lang'), $task->title);
                        $notification_message = __('This task is overdue. Please complete it as soon as possible.', 'kc-lang');
                        
                        kc_send_notification_to_multiple(
                            $assignee_ids,
                            $notification_title,
                            $notification_message,
                            'error',
                            $task->id,
                            'task'
                        );
                    }
                }
            }
        }
    } else {
        error_log('No overdue task notifications to send or some notifications failed');
    }
}

/**
 * Get email body for overdue task notifications
 * Extends the getTaskEmailBody function for overdue notifications
 * 
 * @param object $task The task object
 * @return string HTML email body
 */
function getTaskOverdueEmailBody($task) {
    ob_start();
    ?>
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #d32f2f;"><?php echo esc_html__('Task Overdue Notification', 'kc-lang'); ?></h2>
        
        <p style="color: #d32f2f; font-weight: bold;">
            <?php echo esc_html__('A task assigned to you is now overdue:', 'kc-lang'); ?>
        </p>
        
        <div style="background: #fff3f3; padding: 15px; border-left: 4px solid #d32f2f; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #d32f2f;"><?php echo esc_html($task->title); ?></h3>
            
            <?php if (!empty($task->description)): ?>
                <p><strong><?php echo esc_html__('Description:', 'kc-lang'); ?></strong><br>
                <?php echo nl2br(esc_html($task->description)); ?></p>
            <?php endif; ?>
            
            <p><strong><?php echo esc_html__('Priority:', 'kc-lang'); ?></strong> 
                <span style="font-weight: bold; color: <?php echo ($task->priority === 'high') ? '#d32f2f' : '#0073aa'; ?>">
                    <?php echo esc_html(ucfirst($task->priority)); ?>
                </span>
            </p>
            
            <p><strong><?php echo esc_html__('Status:', 'kc-lang'); ?></strong> 
                <?php echo esc_html(ucfirst($task->status)); ?>
            </p>
            
            <p><strong><?php echo esc_html__('Due Date:', 'kc-lang'); ?></strong> 
                <span style="color: #d32f2f; font-weight: bold;">
                    <?php echo esc_html(date_i18n(get_option('date_format'), strtotime($task->due_date))); ?>
                    (<?php echo esc_html__('Overdue', 'kc-lang'); ?>)
                </span>
            </p>
            
            <?php if (!empty($task->category)): ?>
                <p><strong><?php echo esc_html__('Category:', 'kc-lang'); ?></strong> 
                    <?php echo esc_html($task->category); ?>
                </p>
            <?php endif; ?>
            
            <?php if (!empty($task->clinic_name)): ?>
                <p><strong><?php echo esc_html__('Clinic:', 'kc-lang'); ?></strong> 
                    <?php echo esc_html($task->clinic_name); ?>
                </p>
            <?php endif; ?>
        </div>
        
        <p style="font-weight: bold;">
            <?php echo esc_html__('Please log in and complete this task as soon as possible or update its status.', 'kc-lang'); ?>
        </p>
        
        <p style="margin-top: 30px; font-size: 12px; color: #666;">
            <?php echo esc_html__('This is an automated message. Please do not reply directly to this email.', 'kc-lang'); ?>
        </p>
    </div>
    <?php
    return ob_get_clean();
}