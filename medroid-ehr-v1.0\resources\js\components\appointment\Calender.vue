<template>
  <div class="flex flex-row">
    <div class="w-full">
      <div class="rounded-lg shadow">
        <!-- Header -->
        <div class="p-4 border-b">
          <div class="flex justify-between items-center">
            <h3 class="text-xl font-semibold">
              {{ formTranslation.appointments.appointment }}
            </h3>
            <button
              class="px-3 py-1 text-sm text-white bg-blue-500 hover:bg-blue-600 rounded-md flex items-center"
              @click="filterOpenClose"
              type="button"
            >
              <template v-if="!filterOpen">
                <svg
                  class="w-4 h-4 mr-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                </svg>
                {{ formTranslation.common.add_filter }}
              </template>
              <template v-else>
                <svg
                  class="w-4 h-4 mr-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M20 12H4"
                  />
                </svg>
                {{ formTranslation.common.close_filter }}
              </template>
            </button>
          </div>

          <!-- Filter Section -->
          <div v-if="filterOpen" class="mt-4">
            <hr class="my-2" />
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
              <!-- Doctor Filter -->
              <div v-if="getUserRole() !== 'doctor'">
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  {{ formTranslation.patient_encounter.tag_select_doctor }}
                </label>
                <multi-select
                  v-model="filterData.doctor_id"
                  @input="updateData"
                  @remove="updateData"
                  deselect-label=""
                  select-label=""
                  :placeholder="
                    formTranslation.patient_encounter.tag_select_doctor
                  "
                  :tag-placeholder="
                    formTranslation.patient_encounter.tag_select_doctor
                  "
                  id="doctors"
                  label="label"
                  track-by="id"
                  :loading="doctorMultiselectLoader"
                  :disabled="doctorMultiselectLoader"
                  :options="doctors"
                  :multiple="true"
                  class="w-full"
                />
              </div>

              <!-- Patient Filter -->
              <div v-if="getUserRole() !== 'patient'">
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  {{ formTranslation.appointments.select_patient }}
                </label>
                <multi-select
                  v-model="filterData.patient_id"
                  @input="updateData"
                  @remove="updateData"
                  deselect-label=""
                  select-label=""
                  :placeholder="formTranslation.appointments.select_patient"
                  :tag-placeholder="formTranslation.appointments.select_patient"
                  id="patient_id"
                  label="label"
                  track-by="id"
                  :loading="patientMultiselectLoader"
                  :disabled="patientMultiselectLoader"
                  :options="patients"
                  :multiple="true"
                  class="w-full"
                />
              </div>

              <!-- Service Filter -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  {{ formTranslation.patient_bill.tag_plh_service }}
                </label>
                <multi-select
                  v-model="filterData.service"
                  @input="updateData"
                  @remove="updateData"
                  deselect-label=""
                  select-label=""
                  :placeholder="formTranslation.patient_bill.tag_plh_service"
                  :tag-placeholder="
                    formTranslation.patient_bill.tag_plh_service
                  "
                  id="service"
                  label="name"
                  track-by="service_id"
                  :options="allservice"
                  :multiple="true"
                  class="w-full"
                />
              </div>

              <!-- Status Filter -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  {{ formTranslation.appointments.select_status }}
                </label>
                <multi-select
                  v-model="filterData.status"
                  @input="updateData"
                  @remove="updateData"
                  deselect-label=""
                  select-label=""
                  :placeholder="formTranslation.appointments.select_status"
                  :tag-placeholder="formTranslation.appointments.select_status"
                  id="filter_status"
                  label="label"
                  track-by="value"
                  :options="allStatus"
                  :multiple="true"
                  class="w-full"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Calendar Section -->
        <div v-show="calendarLoader" class="p-4">
          <FullCalendar
            defaultView="dayGridMonth"
            ref="appointmentCalendar"
            :header="headerConfig"
            :plugins="calendarPlugins"
            :event-sources="eventSources"
            :selectable="true"
            id="kivicare-appointment-calendar"
            :eventRender="fullcalendarRender"
            @eventClick="handleEventClick"
            :fixedWeekCount="false"
            :eventLimit="2"
            :eventLimitText="formTranslation.common.more"
            :eventTimeFormat="{
              hour: '2-digit',
              minute: '2-digit',
              hourCycle: getTimeFormat(),
            }"
            :dir="
              userData.theme_mode !== undefined &&
              [true, 'true'].includes(userData.theme_mode)
                ? 'rtl'
                : 'ltr'
            "
            :locale="calenderLang"
            :buttonText="{
              today: this.formTranslation.fullcalendar.today,
              month: this.formTranslation.fullcalendar.month,
              week: this.formTranslation.fullcalendar.week,
              day: this.formTranslation.fullcalendar.day,
              list: 'list',
            }"
            :schedulerLicenseKey="userData.fullcalendar_key"
            :firstDay="start_of_week"
          />
        </div>

        <!-- Loading State -->
        <div class="p-4" v-if="!calendarLoader">
          <div class="flex justify-center">
            <loader-component-2></loader-component-2>
          </div>
        </div>
      </div>

      <!-- Appointment Details Modal -->
      <ModalPopup
        v-if="appointmentDetailsModel"
        @close="appointmentDetailsModel = false"
      >
        <div class="p-4 border-b">
          <h3 class="text-lg font-semibold">
            {{ formTranslation.appointments.appointment_details }}
          </h3>
        </div>

        <!-- Content -->
        <div class="p-6 space-y-4">
          <!-- Date & Time -->
          <div
            class="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-white rounded-lg border border-gray-100"
          >
            <div>
              <p class="flex items-center space-x-2">
                <span class="text-gray-600 font-medium"
                  >{{ formTranslation.common.date }}:</span
                >
                <span class="text-gray-800">{{
                  appointmentDetails.appointment_start_date
                }}</span>
              </p>
            </div>
            <div>
              <p class="flex items-center space-x-2">
                <span class="text-gray-600 font-medium"
                  >{{ formTranslation.common.time }}:</span
                >
                <span class="text-gray-800">{{
                  appointmentDetails.appointment_start_time
                }}</span>
              </p>
            </div>
          </div>

          <!-- Doctor & Patient -->
          <div
            class="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-white rounded-lg border border-gray-100"
          >
            <div>
              <p class="flex items-center space-x-2">
                <span class="text-gray-600 font-medium"
                  >{{ formTranslation.common.doctor }}:</span
                >
                <span class="text-gray-800">{{
                  appointmentDetails.doctor_id.label
                }}</span>
              </p>
            </div>
            <div>
              <p class="flex items-center space-x-2">
                <span class="text-gray-600 font-medium"
                  >{{ formTranslation.common.patient }}:</span
                >
                <span class="text-gray-800">{{
                  appointmentDetails.patient_id.label
                }}</span>
              </p>
            </div>
          </div>

          <!-- Clinic & Status -->
          <div
            class="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-white rounded-lg border border-gray-100"
          >
            <div>
              <p class="flex items-center space-x-2">
                <span class="text-gray-600 font-medium"
                  >{{ formTranslation.common.clinic }}:</span
                >
                <span class="text-gray-800">{{
                  appointmentDetails.clinic_id.label
                }}</span>
              </p>
            </div>
            <div
              v-if="
                getUserRole() === 'patient' || !appointmentDetails.is_edit_able
              "
            >
              <p class="flex items-center space-x-2">
                <span class="text-gray-600 font-medium"
                  >{{ formTranslation.common.status }}:</span
                >
                <span
                  :class="{
                    'text-blue-600 font-medium':
                      appointmentDetails.status == '1',
                    'text-gray-600 font-medium':
                      appointmentDetails.status == '4',
                    'text-red-600 font-medium':
                      appointmentDetails.status == '0',
                    'text-yellow-600 font-medium':
                      appointmentDetails.status == '2',
                    'text-green-600 font-medium':
                      appointmentDetails.status == '3',
                  }"
                >
                  {{ getStatusText(appointmentDetails.status) }}
                </span>
              </p>
            </div>
          </div>

          <!-- Appointment Type -->
          <div class="p-4 bg-white rounded-lg border border-gray-100">
            <p class="flex items-center space-x-2">
              <span class="text-gray-600 font-medium"
                >{{ formTranslation.appointments.appointment_type }}:</span
              >
              <span class="text-gray-800">{{
                appointmentDetails.visit_type
              }}</span>
            </p>
          </div>

          <!-- Description (if exists) -->
          <div
            v-if="appointmentDetails.description"
            class="p-4 bg-white rounded-lg border border-gray-100"
          >
            <p class="flex items-start space-x-2">
              <span class="text-gray-600 font-medium"
                >{{ formTranslation.appointments.description }}:</span
              >
              <span class="text-gray-800">{{
                appointmentDetails.description
              }}</span>
            </p>
          </div>

          <!-- Telemed Link -->
          <div
            v-if="
              appointmentDetails.telemed_service &&
              appointmentDetails.status != '0'
            "
            class="p-4 bg-white rounded-lg border border-gray-100"
          >
            <p class="flex items-center space-x-2 mb-2">
              <span class="text-gray-600 font-medium">{{
                formTranslation.common.telemed_link
              }}</span>
              <i
                id="link-copy"
                :title="formTranslation.settings.click_to_copy"
                class="fas fa-copy cursor-pointer text-blue-600 hover:text-blue-700"
                @click.prevent="
                  copyLink(appointmentDetails.telemed_meeting_link)
                "
                @mouseout="copyLinkMouseOut"
              ></i>
            </p>
            <div class="overflow-x-auto">
              <a
                :href="appointmentDetails.telemed_meeting_link"
                class="text-blue-600 hover:text-blue-700 hover:underline"
                target="_blank"
              >
                {{ appointmentDetails.telemed_meeting_link }}
              </a>
            </div>
          </div>

          <!-- Status Update (for non-patients) -->
          <div
            v-if="getUserRole() != 'patient' && appointmentDetails.is_edit_able"
            class="p-4 bg-white rounded-lg border border-gray-100"
          >
            <div class="flex items-center space-x-4">
              <span class="text-gray-600 font-medium">{{
                formTranslation.common.status
              }}</span>
              <b-select
                v-model="appointmentStatus"
                name="status"
                id="app_status"
                class="flex-1 rounded-lg border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                :disabled="
                  appointmentDetails.less ||
                  appointmentDetails.status == '0' ||
                  appointmentDetails.status == '3'
                "
              >
                <option selected>
                  {{ formTranslation.appointments.select_status }}
                </option>
                <option value="1">
                  {{ formTranslation.appointments.booked }}
                </option>
                <option value="2">
                  {{ formTranslation.appointments.pending }}
                </option>
                <option
                  value="4"
                  v-if="
                    currentDate ===
                    calendarAppointmentFormatDate(
                      appointmentDetails.appointment_start_date
                    )
                  "
                >
                  {{ formTranslation.appointments.check_in }}
                </option>
                <option value="3">
                  {{ formTranslation.appointments.completed }}
                </option>
                <option value="0">
                  {{ formTranslation.appointments.cancelled }}
                </option>
              </b-select>
            </div>
          </div>

          <!-- Action Buttons -->
          <div
            v-if="getUserRole() != 'patient' && appointmentDetails.is_edit_able"
            class="flex justify-end space-x-3 pt-4 border-t border-gray-200"
          >
            <button
              @click="appointmentDetailsModel = false"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              {{ formTranslation.common.close }}
            </button>
            <button
              v-if="
                appointmentDetails.status != '0' &&
                appointmentDetails.status != '3'
              "
              @click="handleStatusUpdate"
              :disabled="statusChangeLoading"
              class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {{
                statusChangeLoading
                  ? formTranslation.common.loading
                  : formTranslation.common.save_changes
              }}
            </button>
          </div>
        </div>
      </ModalPopup>
    </div>
  </div>
</template>

<script>
import FullCalendar from "@fullcalendar/vue";
import resourceTimelinePlugin from "@fullcalendar/resource-timeline";
import interactionPlugin from "@fullcalendar/interaction";
import dayGridPlugin from "@fullcalendar/daygrid";
import { get, post } from "../../config/request";
import { required } from "vuelidate/lib/validators";
import { validateForm } from "../../config/helper";
import appointmentData from "../../store/appointmentData";
import ModalPopup from "../Modal/Index";

export default {
  name: "AppointmentCalendar",
  components: {
    FullCalendar,
    ModalPopup,
  },
  validations: {
    appointmentData: {
      appointment_start_date: { required },
      appointment_start_time: { required },
      visit_type: { required },
      clinic_id: { required },
      doctor_id: { required },
      patient_id: { required },
      description: { required },
      status: { required },
    },
  },
  data() {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const minDate = new Date(today);
    minDate.setMonth(minDate.getMonth() - 2);
    minDate.setDate(15);

    return {
      appointmentDetailsModel: false,
      appointmentDetails: {},
      loading: false,
      submitted: false,
      appointmentData: {},
      timeSlotLoader: false,
      min: minDate,
      clinics: [],
      doctors: [],
      patients: [],
      appointmentTypes: [],
      calendarPlugins: [
        resourceTimelinePlugin,
        dayGridPlugin,
        interactionPlugin,
      ],
      timeSlots: [],
      displayTimeSlots: false,
      headerConfig: {
        left: "prev,next",
        center: "title",
        right: "today dayGridMonth,dayGridDay,dayGridWeek,dayGridList",
      },
      editAppointment: false,
      appointmentStatus: "",
      calenderLang: window.request_data.current_wordpress_lang,
      eventSources: [],
      calendarLoader: false,
      statusChangeLoading: false,
      filterOpen: false,
      patientMultiselectLoader: true,
      doctorMultiselectLoader: true,
      serviceMultiselectLoader: true,
      filterData: {
        status: "",
        doctor_id: "",
        patient_id: "",
        service: "",
      },
      allStatus: [
        { label: "all", value: "all" },
        { label: "upcoming", value: "1" },
        { label: "completed", value: "3" },
        { label: "cancelled", value: "0" },
        { label: "checkin", value: "4" },
        { label: "pending", value: "2" },
      ],
      allservice: [],
      firstTimeFilterEnable: 0,
      start_of_week: 0,
    };
  },
  // Rest of the script section remains largely the same since it contains the business logic
  mounted() {
    this.init();
    this.updateData();
    this.allStatus = this.allStatus.map((item) => {
      if (item.label === "pending") {
        item.label = this.formTranslation.appointments[item.label];
      } else {
        item.label = this.formTranslation.common[item.label];
      }
      return item;
    });
  },
  methods: {
    updateData() {
      let _this = this;
      _this.calendarLoader = false;
      this.eventSources = [
        {
          events({ start, end, timeZone }, callback) {
            let route_data = {
              start_date: moment(start).format("YYYY-MM-DD"),
              end_date: moment(end).format("YYYY-MM-DD"),
            };
            route_data = { ...route_data, ..._this.filterData };
            get("appointment_list", route_data)
              .then((response) => {
                _this.calendarLoader = true;
                if (
                  response.data.status !== undefined &&
                  response.data.status === true
                ) {
                  callback(response.data.data);
                } else {
                  callback([]);
                }
              })
              .catch((error) => {
                _this.calendarLoader = true;
                console.log(error);
              });
          },
        },
      ];
    },
    init() {
      this.appointmentData = this.defaultAppointmentData();
      this.appointmentDetails = this.defaultAppointmentData(true);

      if (this.getUserRole() === "doctor") {
        this.appointmentData.doctor_id = {
          id: this.userData.ID,
          label: this.userData.display_name,
        };
      }
    },
    fullcalendarRender(info) {
      if (info.event.extendedProps.telemed_service) {
        info.el.querySelector(".fc-title").innerHTML =
          info.event.title +
          '<i class="fas fa-video float-right text-blue-500"></i>';
      }
      document
        .querySelectorAll("#kivicare-appointment-calendar .fc-button")
        .forEach((button) => {
          button.classList.add(
            "px-4",
            "py-2",
            "text-sm",
            "font-medium",
            "text-white",
            "bg-blue-500",
            "rounded-md",
            "hover:bg-blue-600"
          );
        });

      document
        .querySelectorAll(
          "#kivicare-appointment-calendar table span:not(.fc-content)"
        )
        .forEach((span) => {
          span.classList.add("text-blue-500");
        });
    },
    copyLink(link) {
      const elem = document.getElementById("modal-hidden-field");
      elem.value = link;
      elem.select();
      document.execCommand("copy");
      document
        .querySelector("#link-copy")
        .classList.replace("fa-copy", "fa-clipboard");
    },
    copyLinkMouseOut() {
      document
        .querySelector("#link-copy")
        .classList.replace("fa-clipboard", "fa-copy");
    },
    calendarLoading(isLoading, view) {
      this.calendarLoader = isLoading;
    },
    calendarAppointmentFormatDate(date) {
      return moment(date).format("YYYY-MM-DD");
    },
    handleEventClick(data) {
      if (!this.kcCheckPermission("appointment_view")) {
        return false;
      }
      const appointment = data.event;
      this.appointmentDetails = {
        id: appointment.id,
        appointment_start_date:
          appointment.extendedProps.appointment_start_date,
        appointment_start_time:
          appointment.extendedProps.appointment_start_time,
        clinic_id: appointment.extendedProps.clinic_id,
        patient_id: appointment.extendedProps.patient_id,
        doctor_id: appointment.extendedProps.doctor_id,
        visit_type: appointment.extendedProps.all_services,
        description: appointment.extendedProps.description,
        status: appointment.extendedProps.status,
        telemed_service: appointment.extendedProps.telemed_service,
        telemed_meeting_link: appointment.extendedProps.telemed_meeting_link,
        is_edit_able: appointment.extendedProps.is_edit_able,
      };

      this.appointmentStatus = String(this.appointmentDetails.status);
      this.appointmentDetailsModel = true;

      const d1 = new Date(
        appointment.extendedProps.appointment_start_date + " 00:00"
      );
      d1.setHours(0, 0, 0, 0);
      const d2 = new Date();
      d2.setHours(0, 0, 0, 0);
      this.appointmentDetails.less = d1 < d2;
    },
    defaultAppointmentData(details = false) {
      const temp = {
        appointment_start_date: "",
        appointment_start_time: "",
        clinic_id: {},
        patient_id: "",
        doctor_id: "",
        visit_type: "",
        description: "",
        status: 1,
        telemed_service: false,
        telemed_meeting_link: "",
        is_edit_able: false,
      };

      if (details) {
        temp.id = "";
      }

      return temp;
    },
    handleStatusUpdate() {
      if (this.appointmentStatus !== this.appointmentDetails.status) {
        if (confirm(this.formTranslation.common.update_appointment_status)) {
          this.updateAppointmentStatus();
        }
      } else {
        this.updateAppointmentStatus();
      }
    },
    getStatusText(status) {
      const statusTexts = {
        1: this.formTranslation.appointments.booked,
        0: this.formTranslation.appointments.cancelled,
        2: this.formTranslation.appointments.pending,
        4: this.formTranslation.appointments.check_in,
        3: this.formTranslation.appointments.check_out,
      };
      return statusTexts[status] || "";
    },
    updateAppointmentStatus() {
      this.statusChangeLoading = true;
      get("appointment_update_status", {
        appointment_id: this.appointmentDetails.id,
        appointment_status: this.appointmentStatus,
      })
        .then((response) => {
          this.statusChangeLoading = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.appointmentDetails.status = String(this.appointmentStatus);
            this.appointmentDetailsModel = false;
            this.updateData();
          }
        })
        .catch((error) => {
          this.statusChangeLoading = false;
          console.log(error);
        });
    },
    filterOpenClose() {
      this.firstTimeFilterEnable++;
      this.filterOpen = !this.filterOpen;
      if (this.firstTimeFilterEnable === 1) {
        if (this.getUserRole() !== "patient") {
          this.getClinicPatients("");
        }
        if (this.getUserRole() !== "doctor") {
          this.doctorListDropDown("");
        }
        this.allServiceList();
      }
    },
    getTimeFormat() {
      const wpTimeFormat = window.request_data.appointment_time_format;
      return wpTimeFormat === "H:i" ? "h23" : "h12";
    },
    doctorListDropDown(clinic_id) {
      this.doctorMultiselectLoader = true;
      get("get_static_data", {
        data_type: "clinic_doctors",
        clinic_id: clinic_id,
        module_type: "appointment_filter",
      })
        .then((response) => {
          this.doctorMultiselectLoader = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.doctors = response.data.data;
          }
        })
        .catch((error) => {
          this.doctorMultiselectLoader = false;
          console.log(error);
        });
    },
    getClinicPatients(clinic_id) {
      this.patientMultiselectLoader = true;
      get("get_static_data", {
        data_type: "users",
        user_type: this.patientRoleName,
        request_clinic_id: clinic_id,
      })
        .then((response) => {
          this.patientMultiselectLoader = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.patients = response.data.data;
          }
        })
        .catch((error) => {
          this.patientMultiselectLoader = false;
          console.log(error);
        });
    },
    allServiceList() {
      this.serviceMultiselectLoader = true;
      get("service_list", {
        widgetType: "phpWidget",
      })
        .then((response) => {
          this.serviceMultiselectLoader = false;
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.allservice = response.data.data;
          }
        })
        .catch((error) => {
          this.serviceMultiselectLoader = false;
          console.log(error);
        });
    },
  },
  watch: {
    appointmentDetailsModel(val) {
      if (val === false) {
        this.appointmentDetails = this.defaultAppointmentData(true);
      }
    },
    "appointmentData.appointment_start_date"(val) {
      if (val === null) {
        this.appointmentData.appointment_start_time = "";
      }
    },
  },
  computed: {
    userData() {
      const user = this.$store.state.userDataModule.user;
      this.start_of_week = user.start_of_week;

      if (this.getUserRole() === "doctor") {
        this.appointmentData.doctor_id = {
          id: user.ID,
          label: user.display_name,
        };
      }
      if (this.getUserRole() === "patient") {
        this.appointmentData.patient_id = {
          id: user.ID,
          label: user.display_name,
        };
      }
      return user;
    },
    currentDate() {
      return moment().format("YYYY-MM-DD");
    },
  },
};
</script>

<style lang="scss">
@import "~@fullcalendar/core/main.css";
@import "~@fullcalendar/daygrid/main.css";

.fc-button {
  text-transform: capitalize;
}

#kivicare-appointment-calendar .fc-today {
  background: #ebe9eb;
}
.fc-toolbar {
  flex-wrap: wrap;
}

.fc-license-message{
  display: none;
}
</style>
