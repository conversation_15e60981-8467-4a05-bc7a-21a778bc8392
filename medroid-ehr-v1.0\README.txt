Medroid - Clinic & Patient Management System (EHR)
Contributors: Medroid AI Inc
Tags: medroid,clinic,patient,hospital,management,clinic management,patient management,doctor,doctor management,appointment,appointment management,vuejs,saas,webpack
Requires PHP: 8.0
Requires at least: 3.0.1
Tested up to: 6.4.1
Stable tag: 3.5.1
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

Medroid is an impressive clinic and patient management plugin (EHR).

== Description ==

Medroid is an impressive clinic and patient management plugin. It is developed for doctors and clinics to handle multiple doctors appointment and encounters at the clinic. It allows you to make, manage and track appointment booking of patients for doctors. Add holidays, receptions, set reminders for appointment and set the email notification for doctors. Keeping privacy on fore front, each doctor’s appointment can be kept private to others.

Medroid is a flexible plugin that lets you manage doctor’s professional practices like of the related background like Orthopedic surgeons, Dentists, Cardiologists, Paediatricians, and so on.

Medroid plugin also allows you to accommodate different modules for doctors and patients. Each doctor will have their individual dashboard. Super admin on Medroid will have an aerial view. It has got everything you need to manage your patients and bookings effortlessly. Medroid plugin is backed with an intuitive user interface. It is a simple, functional, and powerful plugin for your site.

It is built on the cutting-edge frameworks like Vuejs, Webpack, Saas and many others. If you are looking for a high-performance plugin, then this is for you.

Benefits:
Hassle-free 24x7 appointment bookings
Add holidays and important events and notices
Provide customizable templates for clinic and patient management
Quick view of important clinic module
Super Admin can view complete activity log for all doctors
Super admin can create doctors, patients and other users
Keeping track of appointments
Manage day-to-day appointment & encounters
Create and check invoices/encounters
Set the reminders by the system on important events
Manage patient registration, appointment and encounters
Check on the status of clinic session changes
Sent email with links and redirect to the dashboard
Access to super admin module
Add static data and maintain profile settings
== Screenshots ==

Welcome Screen
Configuration Screen
Clinic session Detail
Dashboard
Patient Add
Patient List
Appointments List
Appointments Booking
Patient Encounter
Invoice
Email Templates
Clinic Profile
== Frequently Asked Questions ==

= Does it work with any theme? =

Of course, you can use it with any theme.

= How to send email notification =

You need to configure the SMTP or any other mailer option.

== Changelog ==

= 1.0.0 - 22/10/2020 =

Initial stable release
