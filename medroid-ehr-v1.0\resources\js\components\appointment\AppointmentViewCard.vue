<template>
  <div>
    <!-- <PERSON><PERSON> Backdrop with smooth transition -->
    <div v-if="appointmentDetailsModel"
      class="fixed inset-0 bg-black opacity-50 backdrop-blur-sm transition-opacity duration-300 z-40"
      @click="closeModal"></div>

    <!-- Modal Container -->
    <div v-if="appointmentDetailsModel"
      class="overflow-y-auto overflow-x-hidden fixed inset-0 z-50 flex items-start justify-center p-4 sm:p-6 md:p-8">
      <div
        class="bg-white rounded-xl shadow-2xl w-full max-w-3xl max-h-[90vh] overflow-hidden flex flex-col transform transition-all">
        <!-- Modal Header -->
        <div class="relative bg-indigo-50 px-6 py-5 flex items-center">
          <h2 class="text-xl text-gray-900 font-semibold flex items-center gap-3">
            <span class="p-2 bg-indigo-100 rounded-lg">
              <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </span>
            {{ formTranslation.appointments.appointment_details }}
            <span class="mt-2 mr-2 px-3 py-1 rounded-full text-sm font-medium" :class="statusClass">{{ getStatusText
              }}</span>
          </h2>

          <button
            class="absolute right-6 -translate-y-1/2 p-2 bg-gray-100 hover:bg-indigo-100 rounded-lg transition-colors"
            @click="closeModal">
            <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <!-- Modal Body -->
        <div class="flex-1 overflow-y-auto p-6">
          <div class="space-y-8">
            <!-- Time and Date Section -->
            <div class="flex items-center gap-6 p-4 bg-gray-50 rounded-xl">
              <div class="flex items-center gap-3">
                <div class="p-2 bg-white rounded-lg shadow-sm">
                  <svg class="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <p class="text-sm text-gray-500">Time</p>
                  <p class="font-medium">
                    {{ appointmentDetails.appointment_start_time }}
                  </p>
                </div>
              </div>

              <div class="w-px h-10 bg-gray-200"></div>

              <div class="flex items-center gap-3">
                <div class="p-2 bg-white rounded-lg shadow-sm">
                  <svg class="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
                <div>
                  <p class="text-sm text-gray-500">Date</p>
                  <p class="font-medium">
                    {{ appointmentDetails.appointment_formated_start_date }}
                  </p>
                </div>
              </div>
            </div>

            <!-- People Section -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Doctor Info -->
              <div class="p-4 bg-white border border-gray-100 rounded-xl shadow-sm">
                <div class="flex items-start gap-4">
                  <div class="p-3 bg-blue-50 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <div>
                    <p class="text-sm text-gray-500">Doctor</p>
                    <p class="font-medium">
                      {{ appointmentDetails.doctor_id.label }}
                    </p>
                    <p class="text-sm text-gray-500 mt-1">
                      {{ appointmentDetails.clinic_id.label }}
                    </p>
                  </div>
                </div>
              </div>

              <!-- Patient Info -->
              <div class="p-4 bg-white border border-gray-100 rounded-xl shadow-sm">
                <div class="flex items-start gap-4">
                  <div class="p-3 bg-green-50 rounded-lg">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>
                  <div>
                    <p class="text-sm text-gray-500">Patient</p>
                    <p class="font-medium">
                      {{ appointmentDetails.patient_id.label }}
                    </p>
                    <p class="text-sm text-gray-500 mt-1">
                      {{ appointmentDetails.patient_contact_no }}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Services and Payment Section -->
            <div class="bg-gray-50 rounded-xl overflow-hidden">
              <div class="px-4 py-3 bg-gray-100">
                <h3 class="text-sm font-medium text-gray-900">
                  Service Details
                </h3>
              </div>
              <div class="divide-y divide-gray-200">

                <!-- description -->
                <div class="px-4 py-3 flex justify-between items-center">
                  <div class="flex items-center gap-2">
                    <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                    <span class="text-sm text-gray-500">Description</span>
                  </div>
                  <span class="text-sm font-medium">
                    {{ appointmentDetails.description ?? 'Not provided' }}
                  </span>
                </div>

                <!-- reason -->
                <div class="px-4 py-3 flex justify-between items-center">
                  <div class="flex items-center gap-2">
                    <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                    <span class="text-sm text-gray-500">Reason for Visit</span>
                  </div>
                  <span class="text-sm font-medium">
                    {{ getCustomFieldValue(8) }}
                  </span>
                </div>

                <!-- Services -->
                <div class="px-4 py-3 flex justify-between items-center">
                  <div class="flex items-center gap-2">
                    <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                    <span class="text-sm text-gray-500">Services</span>
                  </div>
                  <span class="text-sm font-medium">{{
                    appointmentDetails.all_services
                    }}</span>
                </div>

                <!-- Charges -->
                <div class="px-4 py-3 flex justify-between items-center">
                  <div class="flex items-center gap-2">
                    <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span class="text-sm text-gray-500">Charges</span>
                  </div>
                  <span class="text-sm font-medium">
                    {{ appointmentDetails.clinic_prefix
                    }}{{ appointmentDetails.all_service_charges || "0"
                    }}{{ appointmentDetails.clinic_postfix }}
                  </span>
                </div>

                <!-- Payment Mode -->
                <div class="px-4 py-3 flex justify-between items-center">
                  <div class="flex items-center gap-2">
                    <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                    </svg>
                    <span class="text-sm text-gray-500">Payment Method</span>
                  </div>
                  <span class="text-sm font-medium">{{
                    appointmentDetails.payment_mode
                    }}</span>
                </div>
              </div>
            </div>

            <!-- Description if available -->
            <div v-if="appointmentDetails.description" class="bg-yellow-50 border border-yellow-100 rounded-xl p-4">
              <h3 class="text-sm font-medium text-yellow-900 mb-2">
                Additional Notes
              </h3>
              <p class="text-sm text-yellow-800">
                {{ appointmentDetails.description }}
              </p>
            </div>

            <!-- Medical Reports if available -->
            <div v-if="hasReports" class="space-y-3">
              <h3 class="text-sm font-medium text-gray-900">Medical Reports</h3>
              <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <a v-for="(
report, index
                  ) in appointmentDetails.appointment_report" :key="index" :href="report.url" target="_blank"
                  class="flex items-center gap-2 p-3 bg-white border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-colors group">
                  <div class="p-2 bg-indigo-50 rounded-lg group-hover:bg-white">
                    <svg class="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <span class="text-sm font-medium text-gray-700 group-hover:text-indigo-600">
                    {{ report.name }}
                  </span>
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- Modal Footer -->
        <!-- <div class="border-t px-6 py-4">
          <button
            @click="closeModal"
            class="w-full sm:w-auto px-4 py-2.5 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg font-medium text-sm transition-colors"
          >
            Close
          </button>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "AppointmentViewCard",
  props: {
    appointmentDetailsModel: {
      type: Boolean,
      default: false,
    },
    appointmentDetails: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    hasReports() {
      return this.appointmentDetails.appointment_report?.length > 0;
    },
    getStatusText() {
      const status = this.appointmentDetails.status;
      return (
        {
          1: this.formTranslation.appointments.booked,
          0: this.formTranslation.appointments.cancelled,
          2: this.formTranslation.appointments.pending,
          4: this.formTranslation.appointments.check_in,
          3: this.formTranslation.appointments.check_out,
        }[status] || ""
      );
    },
    statusClass() {
      const status = this.appointmentDetails.status;
      return (
        {
          1: "bg-green-100 text-green-800",
          0: "bg-red-100 text-red-800",
          2: "bg-yellow-100 text-yellow-800",
          4: "bg-blue-100 text-blue-800",
          3: "bg-indigo-100 text-indigo-800",
        }[status] || "bg-gray-100 text-gray-800"
      );
    },
  },
  methods: {
    closeModal() {
      this.$emit("closeModal");
    },
    getCustomFieldValue(fieldId) {
      if (!this.appointmentDetails || !this.appointmentDetails?.custom_fields) {
        return 'N/A';
      }

      // Find the custom field with the specified ID
      let field = this.appointmentDetails?.custom_fields.find(field => field.id === fieldId.toString());

      // Return the field_data if found, or a default message
      return field ? (field.field_data || 'Not provided') : 'Not available';
    },
  },
};
</script>
