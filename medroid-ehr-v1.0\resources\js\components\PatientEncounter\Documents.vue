<template>
  <div class="documents-section">
    <b-overlay :show="loading" rounded="sm">
      <!-- Documents List -->
      <div v-if="documents.length > 0" class="documents-list">
        <div
          v-for="doc in documents"
          :key="doc.id"
          class="document-item p-3 border-bottom"
        >
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <p><i :class="getDocIcon(doc.type)" class="mr-2"></i>{{ doc.name }}</p>
              <small class="text-muted ml-2">
                {{ formatDate(doc.created_at) }}
              </small>
            </div>
            <div class="actions">
              <b-button-group size="sm">
                <b-button variant="outline-primary" @click="openShareModel(doc)" >
                  <i class="fas fa-share-alt"></i>
                </b-button>
                <b-button variant="outline-primary" @click="viewDocument(doc)">
                  <i class="fas fa-eye"></i>
                </b-button>
                <b-button
                  v-if="canDelete"
                  variant="outline-danger"
                  @click="deleteDocument(doc)"
                >
                  <i class="fas fa-trash"></i>
                </b-button>
              </b-button-group>
            </div>
          </div>
        </div>
      </div>

      <!-- No Documents Found -->
      <div v-else class="text-center py-4">
        <i class="fas fa-file-medical fa-3x text-muted mb-2"></i>
        <p class="text-muted">No documents found</p>
      </div>

      <!-- Upload Button -->
      <div class="text-right mt-3" v-if="canUpload">
        <b-button variant="primary" @click="showUploadModal">
          <i class="fas fa-upload"></i> Upload Document
        </b-button>
      </div>
    </b-overlay>

    <!-- Upload Document Modal -->
    <b-modal
      v-model="showModal"
      title="Upload Document"
      @hidden="resetForm"
      @ok.prevent="handleUpload"
      ok-title="Upload"
      cancel-title="Cancel"
      :ok-disabled="!isUploadFormValid || uploading"
      :busy="uploading"
    >
      <b-form>
        <!-- Document Type Selection -->
        <b-form-group label="Document Type" label-for="document-type">
          <b-form-select
            id="document-type"
            v-model="uploadForm.type"
            :options="documentTypes"
            required
          ></b-form-select>
        </b-form-group>

        <!-- File Upload -->
        <b-form-group label="File" label-for="document-file">
          <b-form-file
            id="document-file"
            ref="fileInput"
            v-model="uploadForm.file"
            :accept="allowedFileTypes"
            placeholder="Choose file..."
            drop-placeholder="Drop file here..."
            required
            @change="handleFileChange"
          ></b-form-file>
        </b-form-group>

        <!-- Description -->
        <b-form-group label="Description" label-for="document-description">
          <b-form-textarea
            id="document-description"
            v-model="uploadForm.description"
            rows="3"
            placeholder="Enter description..."
          ></b-form-textarea>
        </b-form-group>
      </b-form>
    </b-modal>

    <!-- Share Model -->
    <b-modal
      v-model="showShareModel"
      title="Share Document"
      @ok.prevent="handleShareDoc"
      @hidden="hiddenShareModel"
      ok-title="Share"
      cancel-title="Cancel"
      :busy="is_sending_email"
    >
      <b-form>
        <label for="select_emails" class="col-md-12 form-control-label">
          Select emails which you want to send.
        </label>
        <multi-select
          id="select_emails"
          v-model="selectedOptions"
          :options="options"
          :multiple="true"
          :searchable="true"
          :loading="isLoading"
          :clear-on-select="false"
          :close-on-select="true"
          :preserve-search="true"
          :options-limit="50"
          :internal-search="false"
          placeholder="Search..."
          :taggable="true"
          @search-change="fetchOptions"
          @tag="addCustomOption"
        >
          <template slot="noResult">No results found</template>
        </multi-select>
      </b-form>
    </b-modal>
  </div>
</template>

<script>
import { post, get } from '../../config/request';
import moment from 'moment';
import { displayErrorMessage, displayMessage } from '../../utils/message';

export default {
  name: 'Documents',

  props: {
    encounterId: {
      type: [String, Number],
      required: true,
    },
    canUpload: {
      type: Boolean,
      default: true,
    },
    canDelete: {
      type: Boolean,
      default: true,
    },
    defaultPatientEmail: {
      type: Object,
      required: true,
    }
  },

  data() {
    return {
      documents: [],
      loading: false,
      uploading: false,
      showModal: false,
      documentRefreshInterval: null,
      showShareModel:false,
      ShareModelDoc:null,
      is_sending_email:false,
      uploadForm: {
        type: '',
        file: null,
        description: '',
      },
      documentTypes: [
        { value: 'lab_report', text: 'Lab Report' },
        { value: 'prescription', text: 'Prescription' },
        { value: 'scan', text: 'Scan' },
        { value: 'other', text: 'Other' },
      ],
      allowedFileTypes: '.pdf,.doc,.docx,.jpg,.jpeg,.png',
      searchQuery: "",
      options: [],
      selectedOptions: [this.defaultPatientEmail],
      isLoading: false,
      timeout: null,

    };
  },

  computed: {
    isUploadFormValid() {
      return this.uploadForm.type && this.uploadForm.file;
    },
  },

  mounted() {
    this.loadDocuments();
    this.startDocumentRefresh();
  },

  beforeDestroy() {
    if (this.documentRefreshInterval) {
      clearInterval(this.documentRefreshInterval);
    }
  },

  methods: {
    /**
     * Load Documents from the Server
     */
    loadDocuments() {
      this.loading = true;
      try {
        // ***custom note*** this needs to load once file upload done endpoint method not found as of now
        get('get_encounter_documents', {
          encounter_id: this.encounterId
        }).then(response => {
          if (response?.data?.status) {
            this.documents = response.data.data || [];
          } else {
            throw new Error(response?.data?.message || 'Failed to load documents');
          }
        });

     
      } catch (error) {
        console.error('Error loading documents:', error);
        this.$bvToast.toast(error.message || 'Failed to load documents', {
          title: 'Error',
          variant: 'danger',
          solid: true,
        });
      } finally {
        this.loading = false;
      }
    },

    /**
     * Show the Upload Document Modal
     */
    showUploadModal() {
      this.showModal = true;
    },

    /**
     * Reset the Upload Form
     */
    resetForm() {
      this.uploadForm = {
        type: '',
        file: null,
        description: '',
      };
      if (this.$refs.fileInput) {
        this.$refs.fileInput.reset();
      }
    },

    /**
     * Handle File Selection
     */
    handleFileChange(event) {
      const file = event.target.files ? event.target.files[0] : null;
      if (file) {
        this.uploadForm.file = file;
      } else {
        this.uploadForm.file = null;
      }
    },

    /**
     * Handle Document Upload
     */
    async handleUpload() {
      if (!this.isUploadFormValid) {
        this.$bvToast.toast('Please select a file and document type', {
          title: 'Error',
          variant: 'danger',
          solid: true,
        });
        return;
      }

      this.uploading = true;

      try {
        const formData = new FormData();
        formData.append('encounter_id', this.encounterId);
        formData.append('document_type', this.uploadForm.type);
        formData.append('document', this.uploadForm.file);
        formData.append('description', this.uploadForm.description || '');
        formData.append('action', 'upload_encounter_document'); // Added action parameter

        // Updated the endpoint URL to use WordPress admin-ajax.php
        const response = await post('/wp-admin/admin-ajax.php', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        });

        if (response?.data?.status) {
          this.$bvToast.toast('Document uploaded successfully', {
            title: 'Success',
            variant: 'success',
            solid: true,
          });
          this.showModal = false;
          this.resetForm();
          await this.loadDocuments();
        } else {
          throw new Error(response?.data?.message || 'Upload failed');
        }
      } catch (error) {
        console.error('Upload error:', error);
        this.$bvToast.toast(error.message || 'Failed to upload document', {
          title: 'Error',
          variant: 'danger',
          solid: true,
        });
      } finally {
        this.uploading = false;
      }
    },

    /**
     * Get Document Icon Based on Type
     */
    getDocIcon(type) {
      const icons = {
        lab_report: 'fas fa-flask',
        prescription: 'fas fa-prescription',
        scan: 'fas fa-x-ray',
        other: 'fas fa-file',
      };
      return icons[type] || icons.other;
    },

    /**
     * Format Date Using Moment.js
     */
    formatDate(date) {
      return moment(date).format('DD/MM/YYYY HH:mm');
    },

    /**
     * Construct Full Document URL
     */
    getDocumentUrl(url) {
      if (!url) return '';
      if (!url.startsWith('http')) {
        const baseUrl = window.location.origin;
        return `${baseUrl}/${url.replace(/^\//, '')}`;
      }
      return url;
    },

    /**
     * View Document in a New Tab
     */
    viewDocument(doc) {
      if (doc.url) {
        window.open(doc.url, '_blank');
      } else {
        this.$bvToast.toast('Document URL not available', {
          title: 'Error',
          variant: 'danger',
          solid: true,
        });
      }
    },

    /**
     * Delete a Document
     */
    async deleteDocument(doc) {
      if (!confirm('Are you sure you want to delete this document?')) return;

      this.loading = true;
      try {
        const response = await post('delete_encounter_document', {
          id: doc.id,
          encounter_id: this.encounterId,
        });
        if (response?.data?.status) {
          displayMessage('Document deleted successfully');
           this.loadDocuments();
        } else {
          throw new Error(response?.data?.message || 'Delete failed');
        }
      } catch (error) {
        displayErrorMessage('Failed to delete document')
      } finally {
        this.loading = false;
      }
    },

    /**
     * Start Automatic Document Refresh
     */
    startDocumentRefresh() {
      this.documentRefreshInterval = setInterval(() => {
        this.loadDocuments();
      }, 5000);
    },
    openShareModel(doc){
      this.ShareModelDoc=doc
      this.showShareModel = true  
    },
    hiddenShareModel(){
      this.showShareModel = false  
      this.ShareModelDoc=null
      this.selectedOptions=[this.defaultPatientEmail]
    },
    handleShareDoc(){
      this.is_sending_email=true
      post('send_document_summery_to',{doc:this.ShareModelDoc,emails:this.selectedOptions}).then(res=>{
        displayMessage(res.data.message)	
        this.is_sending_email=false
        this.hiddenShareModel()
      })
    },
    fetchOptions(query) {
      this.searchQuery = query;
      if (this.timeout) clearTimeout(this.timeout);
      this.timeout = setTimeout(async () => {
        if (this.searchQuery.trim() === "") {
          this.options = [];
          return;
        }
        this.isLoading = true;
        try {
          const response = await get('get_patients_by_email', {
            search:this.searchQuery ,
          });
          this.options = response.data.data;
        } catch (error) {
          console.error("Error fetching options:", error);
        } finally {
          this.isLoading = false;
        }
      },300);
    },
    addCustomOption(newTag) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(newTag)) {
        return null;
      }
      this.options.push(newTag);
      this.selectedOptions.push(newTag);
    },
  },
};
</script>

<style scoped>
.documents-section {
  min-height: 200px;
}
.document-item {
  transition: background-color 0.2s;
}
.document-item:hover {
  background-color: #f8f9fa;
}
.actions {
  opacity: 0.2;
  transition: opacity 0.2s;
}
.document-item:hover .actions {
  opacity: 1;
}
</style>
