<template>
  <div>
    <!-- Loader Section -->
    <div
      v-if="formLoader"
      class="w-full h-full flex items-center justify-center"
    >
      <loader-component-2></loader-component-2>
    </div>

    <div v-else class="space-y-6">
      <!-- Woocommerce Payment Gateway -->
<div
  v-if="getUserRole() === 'administrator'"
  class="bg-white rounded-2xl shadow-sm"
>
  <div class="p-6">
    <div class="flex items-center gap-4 mb-6">
      <div class="h-14 w-14 bg-blue-500 rounded-2xl flex items-center justify-center">
        <i class="fa fa-credit-card text-white text-xl"></i>
      </div>
      <div>
        <h2 class="text-xl font-semibold text-gray-900">Payment Setting</h2>
        <p class="text-gray-500 text-sm mt-1">Configure your payment gateway settings</p>
      </div>
    </div>

    <div class="border-t border-gray-200 my-6"></div>

    <div class="bg-gray-50 rounded-xl p-6">
      <h2 class="text-lg font-semibold text-gray-900 mb-6">
        {{ formTranslation.patient_bill.woocommerce_payment_gateway }}
      </h2>

      <div class="bg-white rounded-lg p-4">
        <toggle-switch
          :value="status"
          :on-value="'on'"
          :off-value="'off'"
          @input="(value) => (status = value)"
          @change="onPaymentStatusChange"
          :disabled="
            userData.addOns.kiviPro != true &&
            userData.addOns.telemed != true &&
            userData.addOns.googlemeet != true
          "
          :label="formTranslation.widget_setting.enable_woocommerce"
        >
          <template v-slot:default>
            <span class="ml-3 font-medium text-gray-900">
              {{ formTranslation.widget_setting.enable_woocommerce }}
            </span>
            <span
              v-if="
                userData.addOns.kiviPro != true &&
                userData.addOns.telemed != true &&
                userData.addOns.googlemeet != true
              "
              v-html="kivicareProAndAddonIcon"
              class="ml-2"
            >
            </span>
          </template>
        </toggle-switch>
      </div>

      <div class="mt-4 bg-blue-50 text-blue-700 p-4 rounded-lg text-sm leading-relaxed">
        {{ formTranslation.patient_bill.woocommerce_payment_notice }}
      </div>
    </div>
  </div>
</div>

      <!-- PayPal Settings -->
<div
  v-if="getUserRole() === 'administrator'"
  class="bg-white rounded-2xl shadow-sm"
>
  <div class="p-6">
    <div class="flex items-center gap-4 mb-6">
      <div class="h-14 w-14 bg-blue-500 rounded-2xl flex items-center justify-center">
        <i class="fa fa-paypal text-white text-xl"></i>
      </div>
      <div>
        <h2 class="text-xl font-semibold text-gray-900">
          {{ formTranslation.paypal.paypal_setting }}
        </h2>
        <p class="text-gray-500 text-sm mt-1">Configure your PayPal payment settings</p>
      </div>
    </div>

    <div class="border-t border-gray-200 my-6"></div>

    <form @submit.prevent="handleSubmit" :novalidate="true" class="space-y-6">
      <div class="bg-gray-50 rounded-xl p-6">
        <!-- Toggle Switch -->
        <div class="bg-white rounded-lg p-4 mb-6">
          <toggle-switch
            :value="paypalConfigData.enablePaypal"
            :on-value="true"
            :off-value="false"
            @input="(value) => (paypalConfigData.enablePaypal = value)"
            @change="handleSubmit"
            :label="formTranslation.paypal.paypal_status"
          >
            <template v-slot:default>
              <span class="ml-3 font-medium text-gray-900">
                {{ formTranslation.paypal.paypal_status }}
              </span>
            </template>
          </toggle-switch>
        </div>

        <!-- PayPal Configuration Fields -->
        <div v-if="paypalConfigData.enablePaypal">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <!-- Mode Selection -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                {{ formTranslation.paypal.mode }}
                <span class="text-red-500">*</span>
              </label>
              <multi-select
                v-model="paypalConfigData.mode"
                :options="mode_options"
                label="label"
                track-by="id"
                class="w-full"
              />
              <div
                v-if="submitted && !$v.paypalConfigData.mode.required"
                class="mt-1 text-sm text-red-600"
              >
                {{ formTranslation.paypal.paypal_mode_required }}
              </div>
            </div>

            <!-- Client ID -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                {{ formTranslation.paypal.client_id }}
              </label>
              <input
                type="text"
                v-model="paypalConfigData.client_id"
                class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
              <div
                v-if="submitted && !$v.paypalConfigData.client_id.required"
                class="mt-1 text-sm text-red-600"
              >
                {{ formTranslation.paypal.paypal_client_id_required }}
              </div>
            </div>

            <!-- Client Secret -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                {{ formTranslation.paypal.client_secret }}
              </label>
              <input
                type="text"
                v-model="paypalConfigData.client_secret"
                class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
              <div
                v-if="submitted && !$v.paypalConfigData.client_secret.required"
                class="mt-1 text-sm text-red-600"
              >
                {{ formTranslation.paypal.paypal_client_secret_required }}
              </div>
            </div>

            <!-- Currency -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                {{ formTranslation.paypal.currency }}
                <span class="text-red-500">*</span>
              </label>
              <multi-select
                v-model="paypalConfigData.currency"
                :options="currency_list"
                label="label"
                track-by="id"
                class="w-full"
              />
              <div
                v-if="submitted && !$v.paypalConfigData.currency.required"
                class="mt-1 text-sm text-red-600"
              >
                {{ formTranslation.paypal.paypal_currency_required }}
              </div>
            </div>
          </div>

          <!-- Notice -->
          <div class="mt-6 bg-blue-50 text-blue-700 p-4 rounded-lg text-sm leading-relaxed">
            {{ formTranslation.paypal.paypal_currency_notice }}
          </div>

          <!-- Save Button -->
          <div class="flex justify-end mt-6">
            <button
              type="submit"
              :disabled="loading"
              class="px-6 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors disabled:opacity-50 flex items-center gap-2"
            >
              <i class="fa fa-save"></i>
              {{ formTranslation.common.save }}
            </button>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>

      <!-- Local Payment Gateway -->
      <div
        v-if="getUserRole() === 'administrator'"
        class="bg-white rounded-lg shadow-md"
      >
        <div class="p-6">
          <h2 class="text-xl font-semibold mb-4">
            {{ formTranslation.patient_bill.local_payment_gateway }}
          </h2>

          <div class="ml-4">
            <!-- Toggle button -->
            <toggle-switch
              :value="local_payment_status"
              :on-value="'on'"
              :off-value="'off'"
              @input="(value) => (local_payment_status = value)"
              @change="localPaymentStatusChange"
              :label="formTranslation.widget_setting.enable_local_payment"
            >
              <template v-slot:default>
                <span class="ml-3 font-semibold">
                  {{ formTranslation.widget_setting.enable_local_payment }}
                </span>
              </template>
            </toggle-switch>
          </div>
        </div>
      </div>

      <!-- Razorpay Settings -->
      <div
        v-if="getUserRole() === 'administrator'"
        class="bg-white rounded-lg shadow-md"
      >
        <div class="p-6">
          <div
            class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4"
          >
            <h2 class="text-xl font-semibold">
              {{
                formTranslation.common.razorpay +
                " " +
                formTranslation.common.settings
              }}
              <a href="#" target="_blank" v-if="!userData.addOns.razorpay">
                <span
                  v-html="
                    kivicareProFeatureIcon(
                      'Razorpay',
                      'Available In Razorpay Addon'
                    )
                  "
                ></span>
              </a>
            </h2>
          </div>

          <form
            v-if="userData.addOns.razorpay"
            @submit.prevent="handleRazorpaySubmit"
            :novalidate="true"
            class="space-y-6"
          >
            <div class="ml-4">
              <!-- Toggle button -->
              <toggle-switch
                :value="razorPayConfigData.enable"
                :on-value="true"
                :off-value="false"
                @input="(value) => (razorPayConfigData.enable = value)"
                @change="handleRazorpaySubmit"
                :label="
                  formTranslation.common.razorpay +
                  ' ' +
                  formTranslation.receptionist.dt_lbl_status
                "
              >
                <template v-slot:default>
                  <span class="ml-3 font-semibold">
                    {{
                      formTranslation.common.razorpay +
                      " " +
                      formTranslation.receptionist.dt_lbl_status
                    }}
                  </span>
                </template>
              </toggle-switch>
            </div>

            <div
              v-if="razorPayConfigData.enable"
              class="grid grid-cols-1 md:grid-cols-3 gap-6"
            >
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  {{ formTranslation.doctor.api_key }}
                </label>
                <input
                  type="text"
                  v-model="razorPayConfigData.api_key"
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  :placeholder="formTranslation.doctor.api_key"
                />
                <div
                  v-if="
                    razorpaySubmitted && !$v.razorPayConfigData.api_key.required
                  "
                  class="mt-1 text-sm text-red-600"
                >
                  {{ formTranslation.doctor.api_key_required }}
                </div>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  {{ formTranslation.doctor.api_secret }}
                </label>
                <input
                  type="text"
                  v-model="razorPayConfigData.secret_key"
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  :placeholder="formTranslation.doctor.api_secret"
                />
                <div
                  v-if="
                    razorpaySubmitted &&
                    !$v.razorPayConfigData.secret_key.required
                  "
                  class="mt-1 text-sm text-red-600"
                >
                  {{ formTranslation.doctor.api_secret_required }}
                </div>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  {{ formTranslation.paypal.currency }}
                  <span class="text-red-500">*</span>
                </label>
                <multi-select
                  v-model="razorPayConfigData.currency"
                  :options="currency_list"
                  label="label"
                  track-by="id"
                  :disabled="true"
                  class="w-full"
                />
                <div
                  v-if="
                    razorpaySubmitted &&
                    !$v.razorPayConfigData.currency.required
                  "
                  class="mt-1 text-sm text-red-600"
                >
                  {{ formTranslation.paypal.paypal_currency_required }}
                </div>
              </div>
            </div>

            <div v-if="razorPayConfigData.enable" class="text-gray-600 mt-4">
              {{ formTranslation.common.razorpay_currency_notice }}
            </div>

            <div v-if="razorPayConfigData.enable" class="flex justify-end mt-6">
              <button
                type="submit"
                :disabled="razorpayLoading"
                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
              >
                <i class="fa fa-save mr-2"></i>
                {{ formTranslation.common.save }}
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Stripe Payment Settings -->
<div
  v-if="getUserRole() === 'administrator' || getUserRole() === 'clinic_admin'"
  class="bg-white rounded-lg shadow-sm"
>
  <div class="p-6">
    <!-- Header -->
    <div class="flex items-center gap-4 mb-6">
      <div class="h-14 w-14 bg-purple-500 rounded-xl flex items-center justify-center">
        <i class="fa fa-credit-card text-white text-xl"></i>
      </div>
      <div>
        <h2 class="text-xl font-semibold text-gray-900">
          {{ formTranslation.common.stripe_payment + " " + formTranslation.common.settings }}
        </h2>
        <p class="text-gray-500 text-sm">Configure your Stripe payment gateway settings</p>
      </div>
    </div>

    <form
      v-if="userData.addOns.stripepay"
      @submit.prevent="handleStripepaySubmit"
      :novalidate="true"
      class="space-y-6"
    >
      <div class="bg-gray-50 rounded-lg p-6">
        <!-- Toggle Switch -->
        <div class="mb-6">
          <toggle-switch
            :value="stripePayConfigData.enable"
            :on-value="true"
            :off-value="false"
            @input="(value) => (stripePayConfigData.enable = value)"
            @change="handleStripepaySubmit"
            :label="formTranslation.common.stripe_payment + ' ' + formTranslation.receptionist.dt_lbl_status"
          >
            <template v-slot:default>
              <span class="ml-3 font-medium">
                {{ formTranslation.common.stripe_payment + " " + formTranslation.receptionist.dt_lbl_status }}
              </span>
            </template>
          </toggle-switch>
        </div>

        <div v-if="stripePayConfigData.enable" class="space-y-6">
          <!-- Mode Banner -->
          <div class="bg-indigo-50 p-4 rounded-lg flex justify-between items-center">
            <div class="text-sm">
              Current Mode: <span class="text-blue-600 font-medium">{{ stripePayConfigData.mode === 'sandbox' ? 'Test Mode' : 'Live Mode' }}</span>
            </div>
            <span class="px-3 py-1 text-xs bg-green-100 text-green-800 rounded-full">
              {{ stripePayConfigData.mode === 'sandbox' ? 'Testing' : 'Production' }}
            </span>
          </div>

          <!-- Form Fields -->
          <div class="grid grid-cols-1 gap-6">
            <!-- Mode -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                {{ formTranslation.paypal.mode }} <span class="text-red-500">*</span>
              </label>
              <select
                v-model="stripePayConfigData.mode"
                class="w-full h-11 px-3 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
              >
                <option value="sandbox">{{ formTranslation.paypal.sandbox }}</option>
                <option value="live">{{ formTranslation.paypal.live }}</option>
              </select>
              <div
                v-if="stripepaySubmitted && !$v.stripePayConfigData.mode.required"
                class="mt-1 text-sm text-red-600"
              >
                {{ formTranslation.paypal.paypal_mode_required }}
              </div>
            </div>

            <!-- Currency -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                {{ formTranslation.paypal.currency }} <span class="text-red-500">*</span>
              </label>
              <multi-select
                v-model="stripePayConfigData.currency"
                :options="stripeCurrencyOptions"
                label="label"
                track-by="id"
                class="w-full"
              />
              <div
                v-if="stripepaySubmitted && !$v.stripePayConfigData.currency.required"
                class="mt-1 text-sm text-red-600"
              >
                {{ formTranslation.common.stripe_currency_required }}
              </div>
            </div>

            <!-- Publishable Key -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                {{ formTranslation.common.stripe_publishable_key }} <span class="text-red-500">*</span>
              </label>
              <div class="relative">
                <input
                  type="text"
                  v-model="stripePayConfigData.publishable_key"
                  class="w-full h-11 pl-3 pr-10 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                  :placeholder="formTranslation.common.enter_publishable_key"
                />
                <button 
                  type="button" 
                  class="absolute right-3 top-[13px] text-gray-400 hover:text-gray-600"
                >
                  <i class="fa fa-key text-lg"></i>
                </button>
              </div>
              <div
                v-if="stripepaySubmitted && !$v.stripePayConfigData.publishable_key.required"
                class="mt-1 text-sm text-red-600"
              >
                {{ formTranslation.common.publishable_key_required }}
              </div>
            </div>

            <!-- API Secret Key -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                {{ formTranslation.common.stripe_secret_key }} <span class="text-red-500">*</span>
              </label>
              <div class="relative">
                <input
                  type="text"
                  v-model="stripePayConfigData.api_key"
                  class="w-full h-11 pl-3 pr-10 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                  :placeholder="formTranslation.common.enter_stripe_secret_key"
                />
                <button 
                  type="button" 
                  class="absolute right-3 top-[13px] text-gray-400 hover:text-gray-600"
                >
                  <i class="fa fa-lock text-lg"></i>
                </button>
              </div>
              <div
                v-if="stripepaySubmitted && !$v.stripePayConfigData.api_key.required"
                class="mt-1 text-sm text-red-600"
              >
                {{ formTranslation.doctor.api_secret_required }}
              </div>
            </div>
          </div>

          <!-- Notice -->
          <div class="bg-blue-50 text-blue-700 p-4 rounded-lg text-sm">
            {{ formTranslation.common.stripe_payment_currency_match }}
          </div>

          <!-- Save Button -->
          <div class="flex justify-end">
            <button
              type="submit"
              :disabled="stripepayLoading"
              class="px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors disabled:opacity-50 flex items-center gap-2"
            >
              <i class="fa fa-save"></i>
              {{ formTranslation.common.save }}
            </button>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>
    </div>
  </div>
</template>

<script>
import { post, get } from "../../config/request";
import { required } from "vuelidate/lib/validators";
import { alphaSpace, validateForm } from "../../config/helper";
export default {
  data: () => {
    return {
      status: "off",
      formLoader: true,
      request_status: "off",
      paypalConfigData: {
        enablePaypal: false,
        mode: 0,
        client_id: "",
        client_secret: "",
        currency: [{ id: "USD", label: "USD" }],
      },
      razorPayConfigData: {},
      stripePayConfigData: {},
      local_payment_status: "on",
      submitted: false,
      loading: false,
      razorpaySubmitted: false,
      razorpayLoading: false,
      stripepayLoading: false,
      stripepaySubmitted: false,
      stripeCurrencyOptions: [],
      mode_options: [
        { id: 1, label: "Live" },
        { id: 0, label: "Sandbox" },
      ],
      currency_list: [
        { id: "AUD", label: "AUD" },
        { id: "BRL", label: "BRL" },
        { id: "CAD", label: "CAD" },
        { id: "CNY", label: "CNY" },
        { id: "CZK", label: "CZK" },
        { id: "DKK", label: "DKK" },
        { id: "EUR", label: "EUR" },
        { id: "HKD", label: "HKD" },
        { id: "HUF", label: "HUF" },
        { id: "ILS", label: "ILS" },
        { id: "JPY", label: "JPY" },
        { id: "MYR", label: "MYR" },
        { id: "MXN", label: "MXN" },
        { id: "TWD", label: "TWD" },
        { id: "NZD", label: "NZD" },
        { id: "NOK", label: "NOK" },
        { id: "PHP", label: "PHP" },
        { id: "PLN", label: "PLN" },
        { id: "GBP", label: "GBP" },
        { id: "RUB", label: "RUB" },
        { id: "SGD", label: "SGD" },
        { id: "SEK", label: "SEK" },
        { id: "CHF", label: "CHF" },
        { id: "THB", label: "THB" },
        { id: "USD", label: "USD" },
      ],
    };
  },
  mounted() {
    if (!["administrator", "clinic_admin"].includes(this.getUserRole())) {
      this.$router.push({ name: "403" });
    }
    if (this.userData.addOns.stripepay) {
      const currencyCodeToName = {
        USD: "United States Dollar",
        AED: "United Arab Emirates Dirham",
        AFN: "Afghan Afghani",
        ALL: "Albanian Lek",
        AMD: "Armenian Dram",
        ANG: "Netherlands Antillean Guilder",
        AOA: "Angolan Kwanza",
        ARS: "Argentine Peso",
        AUD: "Australian Dollar",
        AWG: "Aruban Florin",
        AZN: "Azerbaijani Manat",
        BAM: "Bosnia and Herzegovina Convertible Mark",
        BBD: "Barbadian Dollar",
        BDT: "Bangladeshi Taka",
        BGN: "Bulgarian Lev",
        BIF: "Burundian Franc",
        BMD: "Bermudian Dollar",
        BND: "Brunei Dollar",
        BOB: "Bolivian Boliviano",
        BRL: "Brazilian Real",
        BSD: "Bahamian Dollar",
        BWP: "Botswana Pula",
        BYN: "Belarusian Ruble",
        BZD: "Belize Dollar",
        CAD: "Canadian Dollar",
        CDF: "Congolese Franc",
        CHF: "Swiss Franc",
        CLP: "Chilean Peso",
        CNY: "Chinese Yuan",
        COP: "Colombian Peso",
        CRC: "Costa Rican Colón",
        CVE: "Cape Verdean Escudo",
        CZK: "Czech Republic Koruna",
        DJF: "Djiboutian Franc",
        DKK: "Danish Krone",
        DOP: "Dominican Peso",
        DZD: "Algerian Dinar",
        EGP: "Egyptian Pound",
        ETB: "Ethiopian Birr",
        EUR: "Euro",
        FJD: "Fijian Dollar",
        FKP: "Falkland Islands Pound",
        GBP: "British Pound Sterling",
        GEL: "Georgian Lari",
        GIP: "Gibraltar Pound",
        GMD: "Gambian Dalasi",
        GNF: "Guinean Franc",
        GTQ: "Guatemalan Quetzal",
        GYD: "Guyanaese Dollar",
        HKD: "Hong Kong Dollar",
        HNL: "Honduran Lempira",
        HTG: "Haitian Gourde",
        HUF: "Hungarian Forint",
        IDR: "Indonesian Rupiah",
        ILS: "Israeli New Shekel",
        INR: "Indian Rupee",
        ISK: "Icelandic Króna",
        JMD: "Jamaican Dollar",
        JPY: "Japanese Yen",
        KES: "Kenyan Shilling",
        KGS: "Kyrgystani Som",
        KHR: "Cambodian Riel",
        KMF: "Comorian Franc",
        KRW: "South Korean Won",
        KYD: "Cayman Islands Dollar",
        KZT: "Kazakhstani Tenge",
        LAK: "Laotian Kip",
        LBP: "Lebanese Pound",
        LKR: "Sri Lankan Rupee",
        LRD: "Liberian Dollar",
        LSL: "Lesotho Loti",
        MAD: "Moroccan Dirham",
        MDL: "Moldovan Leu",
        MGA: "Malagasy Ariary",
        MKD: "Macedonian Denar",
        MMK: "Myanmar Kyat",
        MNT: "Mongolian Tugrik",
        MOP: "Macanese Pataca",
        MUR: "Mauritian Rupee",
        MVR: "Maldivian Rufiyaa",
        MWK: "Malawian Kwacha",
        MXN: "Mexican Peso",
        MYR: "Malaysian Ringgit",
        MZN: "Mozambican Metical",
        NAD: "Namibian Dollar",
        NGN: "Nigerian Naira",
        NIO: "Nicaraguan Córdoba",
        NOK: "Norwegian Krone",
        NPR: "Nepalese Rupee",
        NZD: "New Zealand Dollar",
        PAB: "Panamanian Balboa",
        PEN: "Peruvian Nuevo Sol",
        PGK: "Papua New Guinean Kina",
        PHP: "Philippine Peso",
        PKR: "Pakistani Rupee",
        PLN: "Polish Złoty",
        PYG: "Paraguayan Guarani",
        QAR: "Qatari Rial",
        RON: "Romanian Leu",
        RSD: "Serbian Dinar",
        RUB: "Russian Ruble",
        RWF: "Rwandan Franc",
        SAR: "Saudi Riyal",
        SBD: "Solomon Islands Dollar",
        SCR: "Seychellois Rupee",
        SEK: "Swedish Krona",
        SGD: "Singapore Dollar",
        SHP: "Saint Helena Pound",
        SLE: "Sierra Leonean Leone",
        SOS: "Somali Shilling",
        SRD: "Surinamese Dollar",
        STD: "São Tomé and Príncipe Dobra",
        SZL: "Swazi Lilangeni",
        THB: "Thai Baht",
        TJS: "Tajikistani Somoni",
        TOP: "Tongan Paʻanga",
        TRY: "Turkish Lira",
        TTD: "Trinidad and Tobago Dollar",
        TWD: "New Taiwan Dollar",
        TZS: "Tanzanian Shilling",
        UAH: "Ukrainian Hryvnia",
        UGX: "Ugandan Shilling",
        UYU: "Uruguayan Peso",
        UZS: "Uzbekistan Som",
        VND: "Vietnamese Đồng",
        VUV: "Vanuatu Vatu",
        WST: "Samoan Tala",
        XAF: "Central African CFA Franc",
        XCD: "East Caribbean Dollar",
        XOF: "West African CFA Franc",
        XPF: "CFP Franc",
        YER: "Yemeni Rial",
        ZAR: "South African Rand",
        ZMW: "Zambian Kwacha",
      };
      // Create an array of objects with id and label properties
      this.stripeCurrencyOptions = Object.keys(currencyCodeToName).map(
        (currency) => ({
          id: currency,
          label: currencyCodeToName[currency] || currency, // Use the full name if available, otherwise use the code
        })
      );
    }
    this.paypalConfigData = this.defaultPaypalData();
    this.razorPayConfigData = this.defaultRazorpayData();
    this.stripePayConfigData = this.defaultStripePayData();
    this.getPaymentStatusAll();
    this.kivicareProAndAddonIcon = this.kivicareProFeatureIcon("pro");
    this.getModule();
  },
  validations: {
    paypalConfigData: {
      mode: { required },
      client_id: { required },
      client_secret: { required },
      currency: { required },
    },
    razorPayConfigData: {
      api_key: { required },
      secret_key: { required },
      currency: { required },
    },
    stripePayConfigData: {
      api_key: { required },
      publishable_key: { required },
      enable: { required },
      mode: { required },
      currency: { required },
    },
  },
  methods: {
    getPaymentStatusAll: function (value) {
      this.formLoader = true;
      get("get_payment_status_all", { status: "" })
        .then((response) => {
          //woocommerce setting get
          this.status = response.data.data;
          //paypal setting get
          if (response.data.paypal == "off") {
            this.paypalConfigData.enablePaypal = false;
          } else {
            this.paypalConfigData = response.data.paypal;
          }
          //local payment setting get
          this.local_payment_status = response.data.local_payment;
          //razorpay setting
          if (
            response.data.razorpay !== undefined &&
            response.data.razorpay.enable !== undefined
          ) {
            this.razorPayConfigData = response.data.razorpay;
            this.razorPayConfigData.enable = ["on", true, "true"].includes(
              this.razorPayConfigData.enable
            );
          }
          //razorpay setting
          if (
            response.data.stripepay !== undefined &&
            response.data.stripepay.enable !== undefined
          ) {
            this.stripePayConfigData = response.data.stripepay;
            this.stripePayConfigData.enable = ["on", true, "true"].includes(
              this.stripePayConfigData.enable
            );
          }
          this.formLoader = false;
        })
        .catch((error) => {
          this.formLoader = false;
          console.log("error", error);
        });
    },
    onPaymentStatusChange: function () {
      get("change_woocommerce_payment_status", { status: this.status })
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          } else {
            this.status = this.status === "on" ? "off" : "on";
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          this.status = this.status === "on" ? "off" : "on";
          console.log("error", error);
        });
    },
    localPaymentStatusChange: function () {
      get("change_local_payment_status", { status: this.local_payment_status })
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            displayMessage(response.data.message);
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          } else {
            this.local_payment_status =
              this.local_payment_status === "on" ? "off" : "on";
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          this.status = this.local_payment_status === "on" ? "off" : "on";
          console.log("error", error);
        });
    },
    defaultPaypalData() {
      return {
        mode: "",
        client_id: "",
        client_secret: "",
        enablePaypal: false,
      };
    },
    defaultStripePayData() {
      return {
        api_key: "",
        publishable_key: "",
        enable: false,
        mode: "sandbox",
        currency: {
          id: "INR",
          value: "Indian Rupees",
        },
      };
    },
    defaultRazorpayData() {
      return {
        api_key: "",
        secret_key: "",
        enable: false,
        currency: {
          id: "INR",
          value: "Indian Rupees",
        },
      };
    },
    handleRazorpaySubmit() {
      this.razorpayLoading = true;
      this.razorpaySubmitted = true;
      // stop here if form is invalid
      this.$v.$touch();
      if (this.$v.razorPayConfigData.$invalid) {
        this.razorpayLoading = false;
        return;
      }
      var element = $("#btn-razorpay-submit").find("i");
      element.removeClass("fa fa-save ");
      element.addClass("fa fa-spinner fa-spin");
      this.razorpaySubmitted = true;

      if (validateForm("razorpayDataForm")) {
        post("razorpay_config_save", { data: this.razorPayConfigData })
          .then((response) => {
            this.razorpayLoading = false;
            element.removeClass("fa fa-spinner fa-spin");
            element.addClass("fa fa-save");
            if (
              response.data.status !== undefined &&
              response.data.status === true
            ) {
              displayMessage(response.data.message);
              setTimeout(() => {
                window.location.reload();
              }, 1000);
            } else {
              this.razorPayConfigData.enable =
                this.razorPayConfigData.enable === true ? false : true;
              displayErrorMessage(response.data.message);
            }
          })
          .catch((error) => {
            console.log(error);
            this.razorpayLoading = false;
            displayErrorMessage(
              this.formTranslation.common.internal_server_error
            );
          });
      }
    },
    handleStripepaySubmit() {
      this.stripepayLoading = true;
      this.stripepaySubmitted = true;
      // stop here if form is invalid
      this.$v.$touch();
      if (this.$v.stripePayConfigData.$invalid) {
        this.stripepayLoading = false;
        return;
      }
      var element = $("#btn-stripepay-submit").find("i");
      element.removeClass("fa fa-save ");
      element.addClass("fa fa-spinner fa-spin");
      this.stripepaySubmitted = true;

      if (validateForm("stripepayDataForm")) {
        post("stripepay_config_save", { data: this.stripePayConfigData })
          .then((response) => {
            this.stripepayLoading = false;
            element.removeClass("fa fa-spinner fa-spin");
            element.addClass("fa fa-save");
            if (
              response.data.status !== undefined &&
              response.data.status === true
            ) {
              displayMessage(response.data.message);
              setTimeout(() => {
                window.location.reload();
              }, 1000);
            } else {
              this.stripePayConfigData.enable = !["on", true, "true"].includes(
                this.stripePayConfigData.enable
              );
              displayErrorMessage(response.data.message);
            }
          })
          .catch((error) => {
            console.log(error);
            this.stripepayLoading = false;
            displayErrorMessage(
              this.formTranslation.common.internal_server_error
            );
          });
      }
    },
    handleSubmit() {
      this.loading = true;
      this.submitted = true;
      // stop here if form is invalid
      this.$v.$touch();
      if (this.$v.paypalConfigData.$invalid) {
        this.loading = false;
        return;
      }
      var element = $("#btn-paypal-submit").find("i");
      element.removeClass("fa fa-save ");
      element.addClass("fa fa-spinner fa-spin");
      this.isSubmitedPaypal = true;

      if (validateForm("paypalDataForm")) {
        get("paypal_config_save", { data: this.paypalConfigData })
          .then((response) => {
            this.isSubmitedPaypal = false;
            this.submitted = false;
            this.loading = false;
            element.removeClass("fa fa-spinner fa-spin");
            element.addClass("fa fa-save");
            if (
              response.data.status !== undefined &&
              response.data.status === true
            ) {
              displayMessage(response.data.message);
              setTimeout(() => {
                window.location.reload();
              }, 1000);
            } else {
              displayErrorMessage(response.data.message);
            }
          })
          .catch((error) => {
            console.log(error);
            this.loading = false;
            this.submitted = false;
            displayErrorMessage(
              this.formTranslation.common.internal_server_error
            );
          });
      }
    },
    getModule: function () {
      if (
        window.request_data.link_show_hide !== undefined &&
        window.request_data.link_show_hide !== ""
      ) {
        this.request_status = window.request_data.link_show_hide;
      }
    },
  },
  computed: {
    userData() {
      if (
        this.$store.state.userDataModule !== undefined &&
        this.$store.state.userDataModule.user !== undefined
      ) {
        return this.$store.state.userDataModule.user;
      } else {
        return [];
      }
    },
    teleMedEn() {
      return this.userData.addOns.telemed;
    },
  },
};
</script>
<style scoped>
header.card-header {
  min-height: unset;
}
</style>